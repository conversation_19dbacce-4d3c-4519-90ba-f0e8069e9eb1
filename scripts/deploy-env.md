# Environment Deployment Guide

## Setup für mehrere Umgebungen

### 1. Supabase-Projekte erstellen

Erstelle separate Supabase-Projekte für jede Umgebung:

**Development (aktuell)**
- URL: https://qotcxsnnuzaupxqjihsw.supabase.co
- Bereits konfiguriert

**Test Environment**
1. <PERSON><PERSON><PERSON> zu https://supabase.com/dashboard
2. Erstelle neues Projekt: `tennisclub-test`
3. Kopiere URL und Anon Key
4. Ersetze in `src/config/environments.ts` die Test-Werte

**Production Environment**
1. Erstelle neues Projekt: `tennisclub-prod`
2. <PERSON>piere URL und Anon Key
3. Ersetze in `src/config/environments.ts` die Prod-Werte

### 2. Datenbank-Migrationen

Für jede neue Umgebung:
```bash
# Migrationen auf Test/Prod anwenden
supabase db push --project-ref YOUR_PROJECT_REF
```

### 3. Deployment-Konfiguration

**Test Environment:**
- Domain: `test-tennisclub.lovable.app`
- Automatische Erkennung über Hostname

**Production Environment:**
- Domain: `tennisclub.yourdomain.com`
- Automatische Erkennung über Hostname

### 4. Environment-spezifische Features

- **Development**: Debug-Modus aktiviert, erweiterte Logs
- **Test**: Debug-Modus aktiviert, Test-Daten
- **Production**: Debug-Modus deaktiviert, optimierte Performance

### 5. Nächste Schritte

1. Erstelle Test- und Prod-Supabase-Projekte
2. Aktualisiere `environments.ts` mit echten URLs/Keys
3. Deploye auf verschiedene Domains
4. Teste Umgebungen einzeln