import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.53.0'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface FormatConfig {
  id?: string
  club_id: string
  tournament_id?: string
  format_type: string
  custom_rules?: Record<string, any>
  custom_description?: string
  custom_name?: string
  time_config?: Record<string, any>
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Get the authorization header from the request
    const authHeader = req.headers.get('Authorization')!
    supabaseClient.auth.setSession({ 
      access_token: authHeader.replace('Bearer ', ''),
      refresh_token: ''
    })

    const url = new URL(req.url)
    const clubId = url.searchParams.get('club_id')
    const tournamentId = url.searchParams.get('tournament_id')
    const formatType = url.searchParams.get('format_type')

    switch (req.method) {
      case 'GET':
        return await handleGet(supabaseClient, clubId, tournamentId, formatType)
      case 'POST':
      case 'PUT':
        const config: FormatConfig = await req.json()
        return await handleSave(supabaseClient, config)
      case 'DELETE':
        const configId = url.searchParams.get('config_id')
        return await handleDelete(supabaseClient, configId)
      default:
        throw new Error(`Unsupported method: ${req.method}`)
    }
  } catch (error) {
    console.error('Error:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }, 
        status: 400 
      }
    )
  }
})

async function handleGet(supabaseClient: any, clubId: string | null, tournamentId: string | null, formatType: string | null) {
  let query = supabaseClient
    .from('tournament_format_configs')
    .select('*')

  if (clubId) query = query.eq('club_id', clubId)
  if (tournamentId) query = query.eq('tournament_id', tournamentId)
  if (formatType) query = query.eq('format_type', formatType)

  // Order by specificity: tournament-specific first, then club-wide
  query = query.order('tournament_id', { nullsLast: false })

  const { data, error } = await query

  if (error) {
    throw new Error(`Failed to fetch format configs: ${error.message}`)
  }

  return new Response(
    JSON.stringify({ data }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function handleSave(supabaseClient: any, config: FormatConfig) {
  // Validate required fields
  if (!config.club_id || !config.format_type) {
    throw new Error('club_id and format_type are required')
  }

  let result
  if (config.id) {
    // Update existing config
    const { data, error } = await supabaseClient
      .from('tournament_format_configs')
      .update({
        custom_rules: config.custom_rules || {},
        custom_description: config.custom_description,
        custom_name: config.custom_name,
        time_config: config.time_config || {},
        updated_at: new Date().toISOString()
      })
      .eq('id', config.id)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update format config: ${error.message}`)
    }
    result = data
  } else {
    // Create new config
    const { data, error } = await supabaseClient
      .from('tournament_format_configs')
      .insert({
        club_id: config.club_id,
        tournament_id: config.tournament_id || null,
        format_type: config.format_type,
        custom_rules: config.custom_rules || {},
        custom_description: config.custom_description,
        custom_name: config.custom_name,
        time_config: config.time_config || {}
      })
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to create format config: ${error.message}`)
    }
    result = data
  }

  return new Response(
    JSON.stringify({ data: result }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function handleDelete(supabaseClient: any, configId: string | null) {
  if (!configId) {
    throw new Error('config_id is required for deletion')
  }

  const { error } = await supabaseClient
    .from('tournament_format_configs')
    .delete()
    .eq('id', configId)

  if (error) {
    throw new Error(`Failed to delete format config: ${error.message}`)
  }

  return new Response(
    JSON.stringify({ success: true }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}