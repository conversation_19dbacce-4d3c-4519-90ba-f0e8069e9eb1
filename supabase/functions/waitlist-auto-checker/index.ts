import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.53.0'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });

    // Parse request body
    const { action, club_id } = await req.json();
    console.log('Waitlist Auto-Checker invoked:', { action, club_id });

    if (action === 'check_all_pending_entries') {
      return await checkAllPendingEntries(supabase, club_id);
    } else if (action === 'check_immediate_entry') {
      return await checkImmediateEntry(supabase, club_id);
    } else if (action === 'cron_check') {
      return await cronAutoBookingCheck(supabase);
    } else {
      return new Response(
        JSON.stringify({ 
          success: false, 
          message: `Unknown action: ${action}` 
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

  } catch (error) {
    console.error('Error in waitlist-auto-checker:', error);
    return new Response(
      JSON.stringify({ 
        success: false, 
        message: error.message || 'Internal server error',
        error: error.stack 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
})

async function checkAllPendingEntries(supabaseClient: any, clubId: string) {
  console.log('Checking all pending entries for club:', clubId);
  
  try {
    // Call the database function to check all pending auto-bookings
    const { data, error } = await supabaseClient.rpc('check_all_pending_auto_bookings', {
      p_club_id: clubId
    });

    if (error) {
      console.error('Database function error:', error);
      throw error;
    }

    console.log('Auto-booking check result:', data);

    // If there were successful bookings, we might want to send notifications
    if (data.successful_bookings && data.successful_bookings.length > 0) {
      console.log(`Successfully created ${data.successful_bookings.length} auto-bookings`);
      
      // Send notifications for successful bookings
      for (const booking of data.successful_bookings) {
        try {
          await supabaseClient.functions.invoke('send-waitlist-notification', {
            body: {
              type: 'auto_booking_success',
              user_id: booking.user_id,
              booking_id: booking.booking_id,
              player_name: booking.player_name,
              partner_name: booking.partner_name,
              club_timezone: data.club_timezone
            }
          });
        } catch (notificationError) {
          console.error('Failed to send auto-booking notification:', notificationError);
        }
      }
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: data.message,
        results: {
          processed_entries: data.processed_entries,
          successful_bookings: data.successful_count || 0,
          failed_attempts: data.failed_count || 0,
          club_timezone: data.club_timezone
        }
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );

  } catch (error) {
    console.error('Error checking pending entries:', error);
    throw error;
  }
}

async function checkImmediateEntry(supabaseClient: any, clubId: string) {
  console.log('Checking immediate entry for club:', clubId);
  
  // For immediate entries, we check the audit logs for recent triggers
  const { data: recentTriggers, error: triggerError } = await supabaseClient
    .from('audit_logs')
    .select('*')
    .eq('resource_type', 'waitlist_auto_booking_trigger')
    .eq('action', 'immediate_check_needed')
    .eq('club_id', clubId)
    .gte('created_at', new Date(Date.now() - 60000).toISOString()) // Last minute
    .order('created_at', { ascending: false })
    .limit(5);

  if (triggerError) {
    throw triggerError;
  }

  console.log(`Found ${recentTriggers?.length || 0} recent auto-booking triggers`);

  // Process recent triggers by calling the general check function
  return await checkAllPendingEntries(supabaseClient, clubId);
}

async function cronAutoBookingCheck(supabaseClient: any) {
  console.log('Running cron auto-booking check for all active clubs');
  
  // Get all active clubs
  const { data: clubs, error: clubsError } = await supabaseClient
    .from('clubs')
    .select('id, name, timezone')
    .eq('is_active', true);

  if (clubsError) {
    throw clubsError;
  }

  const results = [];
  for (const club of clubs || []) {
    try {
      console.log(`Processing club: ${club.name} (${club.id})`);
      
      const { data } = await supabaseClient.rpc('check_all_pending_auto_bookings', {
        p_club_id: club.id
      });

      results.push({
        club_id: club.id,
        club_name: club.name,
        success: true,
        ...data
      });

    } catch (error) {
      console.error(`Error processing club ${club.id}:`, error);
      results.push({
        club_id: club.id,
        club_name: club.name,
        success: false,
        error: error.message
      });
    }
  }

  const totalSuccessful = results.reduce((sum, r) => sum + (r.successful_count || 0), 0);
  const totalProcessed = results.reduce((sum, r) => sum + (r.processed_entries || 0), 0);

  return new Response(
    JSON.stringify({
      success: true,
      message: 'Cron auto-booking check completed',
      total_clubs_processed: results.length,
      total_entries_processed: totalProcessed,
      total_successful_bookings: totalSuccessful,
      club_results: results
    }),
    { 
      headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
    }
  );
}