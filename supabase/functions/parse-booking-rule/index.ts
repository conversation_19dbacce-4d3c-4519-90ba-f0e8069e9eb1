import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const openAIApiKey = Deno.env.get('OPENAI_API_KEY');

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { description } = await req.json();

    if (!description) {
      throw new Error('Description is required');
    }

    console.log('Parsing booking rule description:', description);

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: `Du bist ein Experte für Tennisplatz-Buchungsregeln. Analysiere die gegebene Beschreibung und extrahiere die Regel-Parameter.

Verfügbare Regel-Typen:
- advance_booking_limit: Wie weit im Voraus gebucht werden kann
- max_duration: Maximale Buchungsdauer
- min_duration: Minimale Buchungsdauer  
- max_bookings_per_user: Maximale Buchungen pro Benutzer
- booking_window: Zeitfenster für Buchungen
- custom: Benutzerdefiniert

Antworte NUR mit einem gültigen JSON-Objekt in diesem Format:
{
  "rule_type": "advance_booking_limit|max_duration|min_duration|max_bookings_per_user|booking_window|custom",
  "name": "Kurzer Regelname",
  "description": "Bereinigte Beschreibung der Regel",
  "parameters": {
    "max_advance_days": 7,
    "max_duration_hours": 2,
    "min_duration_hours": 1,
    "max_bookings_per_user": 3,
    "booking_start_hour": 8,
    "booking_end_hour": 22
  }
}

Beispiele:
- "Mitglieder können maximal 14 Tage im Voraus buchen" → rule_type: "advance_booking_limit", parameters: {"max_advance_days": 14}
- "Buchungen sollen mindestens 90 Minuten dauern" → rule_type: "min_duration", parameters: {"min_duration_hours": 1.5}
- "Buchungen sind nur zwischen 8 und 20 Uhr möglich" → rule_type: "booking_window", parameters: {"booking_start_hour": 8, "booking_end_hour": 20}`
          },
          {
            role: 'user',
            content: description
          }
        ],
        temperature: 0.1,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to parse rule');
    }

    const data = await response.json();
    const aiResponse = data.choices[0].message.content;

    console.log('AI Response:', aiResponse);

    // Parse the JSON response
    let parsedRule;
    try {
      parsedRule = JSON.parse(aiResponse);
    } catch (parseError) {
      console.error('Failed to parse AI response as JSON:', parseError);
      throw new Error('KI konnte die Regel nicht korrekt interpretieren');
    }

    // Validate the response structure
    if (!parsedRule.rule_type || !parsedRule.name || !parsedRule.parameters) {
      throw new Error('Unvollständige Regel-Struktur von der KI');
    }

    console.log('Parsed rule:', parsedRule);

    return new Response(
      JSON.stringify({ 
        success: true,
        rule: parsedRule 
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      },
    );
  } catch (error) {
    console.error('Error in parse-booking-rule function:', error);
    return new Response(
      JSON.stringify({ 
        success: false,
        error: error.message 
      }),
      {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      },
    );
  }
});