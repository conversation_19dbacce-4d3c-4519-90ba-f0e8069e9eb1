import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.53.0'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });

    console.log('🕐 Waitlist Cron Checker started at:', new Date().toISOString());

    // Get all active clubs
    const { data: clubs, error: clubsError } = await supabase
      .from('clubs')
      .select('id, name, timezone, is_active')
      .eq('is_active', true);

    if (clubsError) {
      console.error('Error fetching clubs:', clubsError);
      throw clubsError;
    }

    console.log(`Found ${clubs?.length || 0} active clubs to process`);

    const results = [];
    let totalProcessed = 0;
    let totalSuccessful = 0;

    // Process each club
    for (const club of clubs || []) {
      try {
        console.log(`🏟️ Processing club: ${club.name} (${club.id}) - Timezone: ${club.timezone}`);
        
        // Call the database function to check all pending auto-bookings for this club
        const { data: checkResult, error: checkError } = await supabase.rpc('check_all_pending_auto_bookings', {
          p_club_id: club.id
        });

        if (checkError) {
          console.error(`Error checking club ${club.id}:`, checkError);
          results.push({
            club_id: club.id,
            club_name: club.name,
            success: false,
            error: checkError.message,
            processed_entries: 0,
            successful_bookings: 0
          });
          continue;
        }

        console.log(`✅ Club ${club.name} processed:`, {
          processed: checkResult.processed_entries || 0,
          successful: checkResult.successful_count || 0,
          failed: checkResult.failed_count || 0
        });

        totalProcessed += checkResult.processed_entries || 0;
        totalSuccessful += checkResult.successful_count || 0;

        // Send notifications for successful bookings
        if (checkResult.successful_bookings && checkResult.successful_bookings.length > 0) {
          console.log(`📧 Sending notifications for ${checkResult.successful_bookings.length} auto-bookings`);
          
          for (const booking of checkResult.successful_bookings) {
            try {
              await supabase.functions.invoke('send-waitlist-notification', {
                body: {
                  type: 'auto_booking_success',
                  user_id: booking.user_id,
                  booking_id: booking.booking_id,
                  player_name: booking.player_name,
                  partner_name: booking.partner_name,
                  club_timezone: checkResult.club_timezone || club.timezone
                }
              });
            } catch (notificationError) {
              console.error('Failed to send auto-booking notification:', notificationError);
            }
          }
        }

        results.push({
          club_id: club.id,
          club_name: club.name,
          club_timezone: club.timezone,
          success: true,
          processed_entries: checkResult.processed_entries || 0,
          successful_bookings: checkResult.successful_count || 0,
          failed_attempts: checkResult.failed_count || 0
        });

      } catch (error) {
        console.error(`Error processing club ${club.name}:`, error);
        results.push({
          club_id: club.id,
          club_name: club.name,
          success: false,
          error: error.message,
          processed_entries: 0,
          successful_bookings: 0
        });
      }
    }

    const summary = {
      timestamp: new Date().toISOString(),
      total_clubs_processed: results.length,
      total_entries_processed: totalProcessed,
      total_successful_bookings: totalSuccessful,
      successful_clubs: results.filter(r => r.success).length,
      failed_clubs: results.filter(r => !r.success).length
    };

    console.log('🎯 Cron job completed:', summary);

    // Log the cron run in audit_logs for monitoring
    await supabase
      .from('audit_logs')
      .insert({
        resource_type: 'waitlist_cron_job',
        resource_id: 'system',
        action: 'completed',
        user_id: null,
        club_id: null,
        new_values: {
          summary,
          club_results: results
        }
      });

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Waitlist cron check completed successfully',
        summary,
        club_results: results
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );

  } catch (error) {
    console.error('❌ Critical error in waitlist cron checker:', error);
    
    return new Response(
      JSON.stringify({ 
        success: false, 
        message: 'Cron job failed',
        error: error.message,
        timestamp: new Date().toISOString()
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
})