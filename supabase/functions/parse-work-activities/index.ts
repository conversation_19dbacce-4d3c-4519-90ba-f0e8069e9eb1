import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const openAIApiKey = Deno.env.get('OPENAI_API_KEY');

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { mode, input } = await req.json();

    if (!mode || !input) {
      throw new Error('Mode and input are required');
    }

    console.log('Parsing work activities:', { mode, input });

    let systemPrompt = '';
    
    switch (mode) {
      case 'suggest':
        systemPrompt = `Du bist ein Experte für Tennisverein-Arbeitsdienste. Schlage basierend auf der Beschreibung passende Arbeitsdienst-Tätigkeiten für einen Tennisverein vor.

Berücksichtige folgende Kategorien:
- Platzpflege: Linien erneuern, Sand harken, Platz kehren, Netze reparieren/spannen, Windschutzzäune reinigen
- Wartung & Instandhaltung: Zäune reparieren, Beleuchtung prüfen, Sprinkleranlage warten, Geräte reparieren
- Events & Turniere: Turnierorganisation, Vereinsfeier, Jugendtraining betreuen, Schiedsrichter
- Verwaltung: Kassenwart, Schriftführer, Mitgliederbetreuung, Website pflegen
- Saisonale Arbeiten: Wintervorbereitung, Saisonstart, Herbstpflege, Schnee räumen

Antworte NUR mit einem gültigen JSON-Array von Tätigkeiten:`;
        break;
        
      case 'import':
        systemPrompt = `Du bist ein Experte für Tennisverein-Arbeitsdienste. Analysiere die eingefügte Liste und extrahiere alle Arbeitsdienst-Tätigkeiten.

Die Liste kann aus Excel, Word oder anderen Quellen stammen. Erkenne:
- Tätigkeitsname
- Beschreibung (falls vorhanden)
- Stundenanzahl (falls angegeben, sonst schätze basierend auf typischen Werten)

Typische Stundenwerte für Tennisvereine:
- Einfache Platzpflege: 1-2 Stunden
- Komplexe Wartung: 2-4 Stunden
- Verwaltungsaufgaben: 0.5-2 Stunden
- Event-Organisation: 3-8 Stunden

Antworte NUR mit einem gültigen JSON-Array von Tätigkeiten:`;
        break;
        
      case 'analyze':
        systemPrompt = `Du bist ein Experte für Tennisverein-Arbeitsdienste. Analysiere die gegebene Tätigkeitsbeschreibung und strukturiere sie.

Extrahiere:
- Name der Tätigkeit (prägnant)
- Detaillierte Beschreibung
- Geschätzte Stundenanzahl basierend auf Aufwand

Berücksichtige dabei typische Arbeitsdienste im Tennisverein.

Antworte NUR mit einem gültigen JSON-Array mit einer Tätigkeit:`;
        break;
        
      case 'seasonal':
        systemPrompt = `Du bist ein Experte für Tennisverein-Arbeitsdienste. Schlage saisonale Arbeitsdienst-Tätigkeiten für einen Tennisverein vor.

Berücksichtige die aktuelle Saison und typische saisonale Arbeiten:
- Frühjahr: Plätze herrichten, Saisonvorbereitung, Winterschäden beheben
- Sommer: Regelmäßige Platzpflege, Bewässerung, Events
- Herbst: Wintervorbereitung, Laub entfernen, Geräte einlagern
- Winter: Wartungsarbeiten, Planung, Vereinsarbeit

Antworte NUR mit einem gültigen JSON-Array von saisonalen Tätigkeiten:`;
        break;
        
      default:
        throw new Error('Invalid mode');
    }

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: `${systemPrompt}

[
  {
    "name": "Tätigkeitsname",
    "description": "Detaillierte Beschreibung der Tätigkeit",
    "hourly_rate": 2.0
  }
]

Wichtige Regeln:
- Namen sollen prägnant und verständlich sein
- hourly_rate als Dezimalzahl (z.B. 1.5 für 1,5 Stunden)
- Beschreibungen sollen hilfreich aber nicht zu lang sein
- Keine Duplikate
- Realistische Stundenwerte für Tennisvereine`
          },
          {
            role: 'user',
            content: input
          }
        ],
        temperature: 0.3,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error?.message || 'Failed to parse activities');
    }

    const data = await response.json();
    const aiResponse = data.choices[0].message.content;

    console.log('AI Response:', aiResponse);

    // Parse the JSON response
    let parsedActivities;
    try {
      parsedActivities = JSON.parse(aiResponse);
    } catch (parseError) {
      console.error('Failed to parse AI response as JSON:', parseError);
      throw new Error('KI konnte die Tätigkeiten nicht korrekt interpretieren');
    }

    // Validate the response structure
    if (!Array.isArray(parsedActivities)) {
      throw new Error('KI-Antwort ist kein gültiges Array');
    }

    // Validate each activity
    for (const activity of parsedActivities) {
      if (!activity.name || typeof activity.hourly_rate !== 'number') {
        throw new Error('Unvollständige Tätigkeits-Struktur von der KI');
      }
    }

    console.log('Parsed activities:', parsedActivities);

    return new Response(
      JSON.stringify({ 
        success: true,
        activities: parsedActivities 
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      },
    );
  } catch (error) {
    console.error('Error in parse-work-activities function:', error);
    return new Response(
      JSON.stringify({ 
        success: false,
        error: error.message 
      }),
      {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      },
    );
  }
});