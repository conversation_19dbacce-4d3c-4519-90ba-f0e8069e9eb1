import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface TimeConfiguration {
  match_duration_minutes: number;
  warmup_minutes: number;
  changeover_minutes: number;
  set_break_minutes: number;
  between_match_buffer_minutes: number;
  court_preparation_minutes: number;
}

const handler = async (req: Request): Promise<Response> => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const { action, tournament_id, time_config, start_date, start_time } = await req.json();
    
    console.log('Tournament scheduling action:', action);

    if (action === 'generate_with_timezone') {
      // Get tournament and club information with timezone
      const { data: tournament, error: tournamentError } = await supabaseClient
        .from('tournaments')
        .select(`
          *,
          clubs (
            id,
            name,
            timezone
          )
        `)
        .eq('id', tournament_id)
        .single();

      if (tournamentError || !tournament) {
        throw new Error('Tournament not found');
      }

      const clubTimezone = tournament.clubs?.timezone || 'Europe/Berlin';
      console.log('Using club timezone:', clubTimezone);

      // Calculate total match duration including all buffers
      const totalMatchDuration = Object.values(time_config as TimeConfiguration)
        .reduce((sum: number, minutes: number) => sum + minutes, 0);

      // Get tournament courts
      const { data: tournamentCourts, error: courtsError } = await supabaseClient
        .from('tournament_courts')
        .select(`
          court_id,
          courts (
            id,
            number,
            club_id
          )
        `)
        .eq('tournament_id', tournament_id);

      if (courtsError || !tournamentCourts || tournamentCourts.length === 0) {
        throw new Error('No courts assigned to tournament');
      }

      // Get court availability considering timezone
      const { data: availability, error: availabilityError } = await supabaseClient
        .from('court_availability')
        .select('*')
        .in('court_id', tournamentCourts.map(tc => tc.court_id));

      if (availabilityError) {
        console.error('Error fetching court availability:', availabilityError);
      }

      // Create schedule considering club timezone
      const scheduleStartDate = new Date(start_date || new Date());
      const scheduleStartTime = start_time || '09:00';

      // Convert start time to club timezone
      const startDateTime = new Date(`${scheduleStartDate.toISOString().split('T')[0]}T${scheduleStartTime}:00`);
      
      // Use Supabase database function to convert timezone-aware times
      const { data: convertedTime, error: conversionError } = await supabaseClient
        .rpc('club_time_to_utc', {
          _local_time: startDateTime.toISOString().split('T')[0] + ' ' + scheduleStartTime + ':00',
          _club_id: tournament.clubs?.id
        });

      if (conversionError) {
        console.error('Timezone conversion error:', conversionError);
      }

      console.log('Timezone conversion:', {
        localTime: `${scheduleStartDate.toISOString().split('T')[0]} ${scheduleStartTime}:00`,
        utcTime: convertedTime,
        clubTimezone
      });

      // Calculate matches per court per day based on availability
      let dailyHours = 10; // Default fallback
      if (availability && availability.length > 0) {
        dailyHours = Math.max(...availability.map(a => {
          const start = new Date(`2000-01-01T${a.start_time}`);
          const end = new Date(`2000-01-01T${a.end_time}`);
          return (end.getTime() - start.getTime()) / (1000 * 60 * 60);
        }));
      }

      const matchesPerCourtPerDay = Math.floor((dailyHours * 60) / totalMatchDuration);

      return new Response(
        JSON.stringify({
          success: true,
          timezone_info: {
            club_timezone: clubTimezone,
            start_time_local: `${scheduleStartDate.toISOString().split('T')[0]} ${scheduleStartTime}:00`,
            start_time_utc: convertedTime,
            total_match_duration: totalMatchDuration,
            matches_per_court_per_day: matchesPerCourtPerDay,
            available_courts: tournamentCourts.length,
            daily_capacity: matchesPerCourtPerDay * tournamentCourts.length
          }
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        }
      );
    }

    return new Response(
      JSON.stringify({ error: 'Invalid action' }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    );

  } catch (error: any) {
    console.error('Tournament scheduling error:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    );
  }
};

serve(handler);