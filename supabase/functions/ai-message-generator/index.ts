import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface AIMessageRequest {
  prompt: string;
  tone?: 'friendly' | 'formal' | 'urgent' | 'energetic';
  persona?: 'friendly' | 'formal' | 'urgent' | 'energetic';
  targetGroup?: 'all' | 'active' | 'tournament' | 'junior';
  channels?: string[];
  availableVariables?: string[];
  clubContext?: string;
  clubId?: string;
}

const handler = async (req: Request): Promise<Response> => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    console.log("🤖 AI Message Generator called");

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Get OpenAI API key
    const openAIApiKey = Deno.env.get('OPENAI_API_KEY');
    if (!openAIApiKey) {
      throw new Error('OpenAI API key not configured');
    }

    const { prompt, tone, persona, targetGroup, channels, availableVariables, clubContext: requestClubContext, clubId }: AIMessageRequest = await req.json();
    
    const activeTone = persona || tone || 'friendly';
    console.log(`🎯 Generating template with ${activeTone} persona`);

    // Use provided club context or fetch from database
    let clubContext = requestClubContext || "";
    if (clubId && !requestClubContext) {
      const { data: club } = await supabase
        .from('clubs')
        .select('name, description')
        .eq('id', clubId)
        .single();
      
      if (club) {
        clubContext = `Club: ${club.name}${club.description ? ` - ${club.description}` : ''}`;
      }
    }

    // Handle template generation (new functionality)
    if (!channels || channels.length === 0) {
      return await generateTemplate(prompt, activeTone, availableVariables, clubContext, openAIApiKey);
    }

    // Handle multi-channel message generation (existing functionality)
    return await generateMultiChannelMessages(prompt, activeTone, targetGroup, channels, clubContext, openAIApiKey, supabase, clubId);

  } catch (error: any) {
    console.error('💥 Error in AI message generator:', error);
    return new Response(JSON.stringify({ 
      error: error.message,
      success: false 
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
};

async function generateTemplate(prompt: string, tone: string, availableVariables: string[] = [], clubContext: string, openAIApiKey: string) {
  // Define persona characteristics
  const personaPrompts = {
    friendly: "Schreibe in einem warmen, einladenden und persönlichen Ton. Verwende emotionale Sprache und erstelle eine Verbindung zu den Lesern.",
    formal: "Schreibe in einem professionellen, sachlichen und respektvollen Ton. Halte die Sprache klar und direkt.",
    urgent: "Schreibe in einem dringenden, wichtigen Ton. Betone die Zeitkritikalität und verwende aufmerksamkeitsstarke Worte.",
    energetic: "Schreibe in einem motivierenden, dynamischen und begeisterten Ton. Verwende Emojis und energiegeladene Sprache."
  };

  const systemPrompt = `Du bist ein Experte für Vereinskommunikation im deutschen Tennis-Sport. 

AUFGABE: Erstelle ein komplettes Nachrichten-Template basierend auf der Benutzereingabe.

PERSONA: ${personaPrompts[tone as keyof typeof personaPrompts] || personaPrompts.friendly}

VERFÜGBARE VARIABLEN: ${availableVariables.join(', ') || 'Keine spezifischen Variablen'}

KONTEXT: ${clubContext || 'Tennis-Verein'}

ANFORDERUNGEN:
1. Erstelle einen passenden Template-Namen (max. 50 Zeichen)
2. Schreibe einen aussagekräftigen Betreff/Titel
3. Verfasse eine vollständige Nachricht
4. Verwende passende Variablen aus der Liste natürlich im Text
5. Schlage einen geeigneten Kanal-Typ vor (email, push, dashboard, chat)

AUSGABE-FORMAT (JSON):
{
  "templateName": "Name des Templates",
  "subject": "Betreff oder Titel",
  "content": "Vollständiger Nachrichteninhalt mit {{variablen}}",
  "suggestedType": "email|push|dashboard|chat"
}

WICHTIG: 
- Verwende deutsche Sprache
- Integriere passende Variablen natürlich in den Text
- Achte auf die gewählte Persona
- Halte E-Mail-Betreff unter 60 Zeichen
- Push-Nachrichten sollten kurz und prägnant sein`;

  console.log(`🔄 Generating template with OpenAI...`);

  const response = await fetch('https://api.openai.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${openAIApiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      model: 'gpt-4o-mini',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: prompt }
      ],
      max_tokens: 1000,
      temperature: 0.7,
    }),
  });

  if (!response.ok) {
    const errorData = await response.text();
    console.error('OpenAI API error:', errorData);
    throw new Error(`OpenAI API error: ${response.status}`);
  }

  const data = await response.json();
  const generatedText = data.choices[0]?.message?.content;

  if (!generatedText) {
    throw new Error('No content generated from OpenAI');
  }

  // Try to parse JSON response
  let parsedResponse;
  try {
    parsedResponse = JSON.parse(generatedText);
  } catch (parseError) {
    console.error('JSON parsing error:', parseError);
    // Fallback: extract content manually
    parsedResponse = {
      templateName: "AI-generiertes Template",
      subject: "Neue Nachricht",
      content: generatedText,
      suggestedType: "email"
    };
  }

  console.log('✅ Generated template successfully');

  return new Response(JSON.stringify(parsedResponse), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' },
  });
}

async function generateMultiChannelMessages(prompt: string, tone: string, targetGroup: string = 'all', channels: string[], clubContext: string, openAIApiKey: string, supabase: any, clubId?: string) {
  // Define tone characteristics
  const tonePrompts = {
    friendly: "freundlich, einladend und persönlich",
    formal: "professionell, respektvoll und offiziell", 
    urgent: "dringend, aber höflich und klar",
    energetic: "enthusiastisch, motivierend und lebhaft"
  };

  // Define target group characteristics
  const groupPrompts = {
    all: "alle Vereinsmitglieder",
    active: "aktive Spieler",
    tournament: "Turnierspieler", 
    junior: "Jugendliche und deren Eltern"
  };

  // Generate messages for each requested channel
  const messages: Record<string, any> = {};

    for (const channel of channels) {
      let channelSpecificPrompt = "";
      let maxLength = "";

      switch (channel) {
        case 'email':
          channelSpecificPrompt = "Erstelle eine vollständige E-Mail mit Betreff und strukturiertem Inhalt.";
          maxLength = "Die E-Mail sollte informativ aber nicht zu lang sein.";
          break;
        case 'push':
          channelSpecificPrompt = "Erstelle eine kurze Push-Benachrichtigung.";
          maxLength = "Maximal 100 Zeichen, prägnant und handlungsfördernd.";
          break;
        case 'dashboard':
          channelSpecificPrompt = "Erstelle eine Dashboard-News mit Titel und kurzem Text.";
          maxLength = "Titel unter 60 Zeichen, Text unter 200 Zeichen.";
          break;
        case 'chat':
          channelSpecificPrompt = "Erstelle eine informelle Chat-Nachricht.";
          maxLength = "Kurz und direkt, wie eine WhatsApp-Nachricht.";
          break;
      }

      const systemPrompt = `Du bist ein Experte für Vereinskommunikation im Tennis-Bereich. 
${clubContext}

Aufgabe: ${channelSpecificPrompt}
Ton: ${tonePrompts[tone]}
Zielgruppe: ${groupPrompts[targetGroup]}
${maxLength}

Verwende angemessene Tennis-Emojis und gestalte die Nachricht ansprechend.
Antworte nur mit dem gewünschten Inhalt, ohne zusätzliche Erklärungen.`;

      console.log(`🔄 Generating ${channel} content for OpenAI...`);

      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${openAIApiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'gpt-4o-mini',
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: prompt }
          ],
          max_tokens: channel === 'push' ? 50 : 500,
          temperature: 0.7,
        }),
      });

      if (!response.ok) {
        throw new Error(`OpenAI API error: ${response.status}`);
      }

      const data = await response.json();
      const generatedContent = data.choices[0].message.content;

      // Parse content based on channel
      if (channel === 'email') {
        const lines = generatedContent.split('\n');
        const subjectLine = lines.find(line => line.toLowerCase().includes('betreff:'));
        const subject = subjectLine ? subjectLine.replace(/betreff:\s*/i, '').trim() : 'Vereins-Information';
        const content = generatedContent.replace(/betreff:.*\n?/i, '').trim();
        
        messages[channel] = {
          subject,
          content
        };
      } else if (channel === 'dashboard') {
        const lines = generatedContent.split('\n');
        const title = lines[0].replace(/titel:\s*/i, '').trim();
        const content = lines.slice(1).join('\n').trim();
        
        messages[channel] = {
          title,
          content
        };
      } else {
        messages[channel] = {
          content: generatedContent.trim()
        };
      }

      console.log(`✅ Generated ${channel} content successfully`);
    }

    // Log analytics
    if (clubId) {
      await supabase
        .from('communication_analytics')
        .insert({
          club_id: clubId,
          event_type: 'ai_generation',
          channel: 'ai_assistant',
          metadata: {
            prompt_length: prompt.length,
            tone,
            target_group: targetGroup,
            channels_generated: channels.length
          }
        });
    }

    return new Response(JSON.stringify({
      success: true,
      messages,
      metadata: {
        tone,
        targetGroup,
        channels: channels.length,
        timestamp: new Date().toISOString()
      }
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
}

serve(handler);