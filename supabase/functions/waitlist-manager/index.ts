import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface WaitlistEntry {
  id: string;
  club_id: string;
  user_id: string;
  player_name: string;
  partner_name?: string;
  preferred_date: string;
  start_time_range: string;
  end_time_range: string;
  preferred_courts: string[];
  auto_booking_enabled: boolean;
  position: number;
  status: 'waiting' | 'converted_to_booking' | 'cancelled' | 'expired';
  created_at: string;
  updated_at: string;
}

interface Booking {
  id: string;
  club_id: string;
  court_id: string;
  booking_date: string;
  start_time: string;
  end_time: string;
  player_name: string;
  partner_name?: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const { action, ...params } = await req.json();

    console.log('Waitlist manager action:', action, params);

    switch (action) {
      case 'process_booking_cancellation':
        return await processBookingCancellation(supabaseClient, params);
      case 'check_waiting_entries':
        return await checkWaitingEntries(supabaseClient, params);
      default:
        throw new Error(`Unknown action: ${action}`);
    }

  } catch (error: any) {
    console.error('Error in waitlist-manager:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json', ...corsHeaders },
      }
    );
  }
});

async function processBookingCancellation(supabaseClient: any, params: any) {
  const { club_id, court_id, booking_date, start_time, end_time } = params;

  console.log('Processing booking cancellation for:', { club_id, court_id, booking_date, start_time, end_time });

  try {
    // Use enhanced atomic database function for safe auto-booking
    const { data: result, error } = await supabaseClient
      .rpc('process_waitlist_auto_booking_enhanced', {
        p_club_id: club_id,
        p_court_id: court_id,
        p_booking_date: booking_date,
        p_start_time: start_time,
        p_end_time: end_time
      });

    if (error) {
      throw new Error(`Error processing waitlist: ${error.message}`);
    }

    console.log('Waitlist processing result:', result);

    if (result.success) {
      // Auto-booking was created successfully
      return new Response(
        JSON.stringify({ 
          message: result.message,
          booking_created: true,
          user_id: result.user_id,
          entry_id: result.entry_id,
          booking_id: result.booking_id
        }),
        { headers: { 'Content-Type': 'application/json', ...corsHeaders } }
      );
    } else if (result.notify_entries) {
      // Need to send notifications to waitlist entries
      const { data: waitlistEntries, error: waitlistError } = await supabaseClient
        .from('waitlist_entries')
        .select('*')
        .eq('club_id', club_id)
        .eq('preferred_date', booking_date)
        .eq('status', 'waiting')
        .lte('start_time_range', start_time)
        .gte('end_time_range', end_time)
        .order('position', { ascending: true })
        .order('created_at', { ascending: true })
        .limit(5); // Limit notifications to prevent spam

      if (waitlistError) {
        throw new Error(`Error fetching waitlist entries: ${waitlistError.message}`);
      }

      if (!waitlistEntries || waitlistEntries.length === 0) {
        return new Response(
          JSON.stringify({ message: 'No waitlist entries found for notification' }),
          { headers: { 'Content-Type': 'application/json', ...corsHeaders } }
        );
      }

      // Filter entries that match preferred courts
      const matchingEntries = waitlistEntries.filter((entry: WaitlistEntry) => 
        entry.preferred_courts.length === 0 || entry.preferred_courts.includes(court_id)
      );

      if (matchingEntries.length === 0) {
        return new Response(
          JSON.stringify({ message: 'No entries matching court preferences' }),
          { headers: { 'Content-Type': 'application/json', ...corsHeaders } }
        );
      }

      // Send notifications to matching entries
      const notificationPromises = matchingEntries.map(async (entry: WaitlistEntry) => {
        try {
          return await supabaseClient
            .functions
            .invoke('send-waitlist-notification', {
              body: {
                user_id: entry.user_id,
                entry_id: entry.id,
                club_id: club_id,
                court_id: court_id,
                booking_date: booking_date,
                start_time: start_time,
                end_time: end_time,
                player_name: entry.player_name,
                partner_name: entry.partner_name
              }
            });
        } catch (notificationError) {
          console.error('Error sending notification for entry:', entry.id, notificationError);
          return null;
        }
      });

      const results = await Promise.allSettled(notificationPromises);
      const successCount = results.filter(r => r.status === 'fulfilled').length;

      console.log(`Notifications sent to ${successCount}/${matchingEntries.length} users`);
      return new Response(
        JSON.stringify({ 
          message: `Notifications sent to ${successCount} users`,
          notifications_sent: successCount,
          total_eligible: matchingEntries.length
        }),
        { headers: { 'Content-Type': 'application/json', ...corsHeaders } }
      );
    } else {
      // No matching entries or other reason
      return new Response(
        JSON.stringify({ message: result.message }),
        { headers: { 'Content-Type': 'application/json', ...corsHeaders } }
      );
    }
  } catch (error: any) {
    console.error('Error in processBookingCancellation:', error);
    throw error;
  }
}

async function checkWaitingEntries(supabaseClient: any, params: any) {
  const { club_id, date, start_time, end_time, court_ids } = params;

  let query = supabaseClient
    .from('waitlist_entries')
    .select('*', { count: 'exact' })
    .eq('club_id', club_id)
    .eq('preferred_date', date)
    .eq('status', 'waiting')
    .lte('start_time_range', start_time)
    .gte('end_time_range', end_time);

  if (court_ids && court_ids.length > 0) {
    query = query.or(
      `preferred_courts.eq.{},preferred_courts.cs.{${court_ids.join(',')}}`
    );
  }

  const { count, error } = await query;

  if (error) {
    throw new Error(`Error checking waiting entries: ${error.message}`);
  }

  return new Response(
    JSON.stringify({ count: count || 0 }),
    { headers: { 'Content-Type': 'application/json', ...corsHeaders } }
  );
}