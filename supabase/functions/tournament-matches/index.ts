import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.39.3";

// ===========================================
// DYNAMIC TIMEZONE CONFIGURATION
// ===========================================

// Get club timezone from database
async function getClubTimezone(supabaseClient: any, clubId: string): Promise<string> {
  const { data, error } = await supabaseClient
    .rpc('get_club_timezone', { _club_id: clubId });
  
  if (error) {
    console.error('Error fetching club timezone:', error);
    return 'Europe/Berlin'; // Fallback
  }
  
  return data || 'Europe/Berlin';
}

// Timezone-Utility-Funktionen für Edge Functions
function toClubTimezone(date: Date | string, timezone: string): Date {
  const d = typeof date === 'string' ? new Date(date) : date
  return new Date(d.toLocaleString('en-US', { timeZone: timezone }))
}

function formatDateForClub(date: Date | string, timezone: string): string {
  const d = typeof date === 'string' ? new Date(date) : date
  return d.toLocaleDateString('de-DE', {
    day: '2-digit',
    month: '2-digit', 
    year: 'numeric',
    timeZone: timezone
  })
}

function formatTimeForClub(date: Date | string, timezone: string): string {
  const d = typeof date === 'string' ? new Date(date) : d
  return d.toLocaleTimeString('de-DE', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false,
    timeZone: timezone
  })
}

function debugTimezone(label: string, date: Date | string, timezone: string) {
  const d = typeof date === 'string' ? new Date(date) : date
  console.log(`🕒 ${label}:`, {
    timezone: timezone,
    originalISO: d.toISOString(),
    inClubTZ: d.toLocaleString('de-DE', { timeZone: timezone }),
    dateOnly: formatDateForClub(d, timezone),
    timeOnly: formatTimeForClub(d, timezone)
  })
}

// ===========================================

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

// Tournament Format Engine - Integrated Logic
interface Participant {
  id: string;
  tournament_id: string;
  user_id?: string;
  guest_name?: string;
  guest_email?: string;
  seeding?: number;
  status: string;
}

interface Match {
  tournament_id: string;
  club_id: string;
  participant1_id: string;
  participant2_id?: string | null;
  participant1_placeholder?: string;
  participant2_placeholder?: string;
  round_number: number;
  match_number: number;
  status: string;
  winner_id?: string | null;
  group_number?: number;
  scheduled_at?: string;
  scheduled_date?: string;
  scheduled_time?: string;
  court_id?: string;
  estimated_duration_minutes?: number;
}

// Helper function to calculate total match duration based on format configuration
const calculateMatchDuration = (formatConfig: any, formatType?: string) => {
  const timeConfig = formatConfig?.time_config || {};
  
  // Format-specific base durations (match only)
  let matchDuration: number;
  switch (formatType) {
    case 'knockout':
      matchDuration = timeConfig.match_duration_minutes || 120;
      break;
    case 'round_robin':
      matchDuration = timeConfig.match_duration_minutes || 90;
      break;
    case 'uts':
      matchDuration = timeConfig.match_duration_minutes || 90;
      break;
    case 'team_tie':
      matchDuration = timeConfig.match_duration_minutes || 180;
      break;
    case 'timeboxed':
      matchDuration = timeConfig.match_duration_minutes || 60;
      break;
    case 'social_mixer':
      matchDuration = timeConfig.match_duration_minutes || 30;
      break;
    case 'pro_set':
      matchDuration = timeConfig.match_duration_minutes || 60;
      break;
    case 'challenge_ladder':
      matchDuration = timeConfig.match_duration_minutes || 90;
      break;
    default:
      matchDuration = timeConfig.match_duration_minutes || 90;
  }
  
  // Add buffer time
  const betweenMatchBufferMinutes = timeConfig.between_match_buffer_minutes || 
    (formatType === 'knockout' ? 15 :
     formatType === 'uts' ? 15 :
     formatType === 'team_tie' ? 30 :
     formatType === 'timeboxed' ? 5 :
     formatType === 'social_mixer' ? 5 :
     formatType === 'pro_set' ? 5 :
     10);
  
  const totalDuration = matchDuration + betweenMatchBufferMinutes;
  
  console.log(`${formatType} match duration: ${matchDuration}min + ${betweenMatchBufferMinutes}min buffer = ${totalDuration}min total`);
  
  return totalDuration;
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  if (req.method !== 'POST') {
    return new Response(JSON.stringify({ 
      success: false, 
      error: `Method ${req.method} not allowed. Only POST requests are supported.` 
    }), {
      status: 405,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }

  const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
  const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
  const supabase = createClient(supabaseUrl, supabaseKey);

  try {
    console.log('Tournament matches function called with method:', req.method);
    
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      console.error('No authorization header provided');
      throw new Error('No authorization header');
    }

    // Verify JWT and get user
    const { data: { user }, error: authError } = await supabase.auth.getUser(
      authHeader.replace('Bearer ', '')
    );
    if (authError || !user) {
      console.error('Authentication failed:', authError);
      throw new Error('Unauthorized');
    }

    let requestBody;
    try {
      requestBody = await req.json();
    } catch (parseError) {
      console.error('Failed to parse request body:', parseError);
      throw new Error('Invalid JSON in request body');
    }

    const { action, tournament_id, format_config, match_id, sets, winner_id, smart_scheduling_config } = requestBody;
    console.log('Received request:', { action, tournament_id, format_config, match_id, user_id: user.id });

    if (!action) {
      throw new Error('Action is required');
    }

    if (!tournament_id) {
      throw new Error('Tournament ID is required');
    }

    if (action === 'generate') {
      console.log('Starting match generation for tournament:', tournament_id);

      // Step 1: Verify tournament exists and get details with club timezone
      const { data: tournament, error: tournamentError } = await supabase
        .from('tournaments')
        .select(`
          id, name, format_type, format_config, status, club_id, tournament_start, tournament_end,
          clubs!inner (
            id, timezone
          )
        `)
        .eq('id', tournament_id)
        .single();

      if (tournamentError || !tournament) {
        console.error('Tournament not found:', tournamentError);
        throw new Error('Tournament not found');
      }

      // Get club timezone for all time calculations
      const clubTimezone = await getClubTimezone(supabase, tournament.club_id);
      console.log('Using club timezone for tournament:', clubTimezone);

      // Step 2: Check user permissions
      const { data: membership, error: membershipError } = await supabase
        .from('club_memberships')
        .select('role, club_id')
        .eq('user_id', user.id)
        .eq('club_id', tournament.club_id)
        .eq('is_active', true)
        .in('role', ['admin', 'super_admin']);

      if (membershipError || !membership || membership.length === 0) {
        console.error('Permission check failed:', membershipError);
        throw new Error('Keine Berechtigung zum Generieren von Spielen für diesen Club');
      }

      // Step 3: Check tournament status
      if (tournament.status !== 'registration' && tournament.status !== 'active') {
        throw new Error(`Spiele können nur für Turniere im Status "Anmeldung" oder "Aktiv" generiert werden. Aktueller Status: ${tournament.status}`);
      }

      // Step 4: Get confirmed participants
      const { data: participants, error: participantsError } = await supabase
        .from('tournament_participants')
        .select('*')
        .eq('tournament_id', tournament_id)
        .eq('status', 'confirmed')
        .order('seeding', { ascending: true });

      if (participantsError) {
        console.error('Participants fetch error:', participantsError);
        throw new Error(`Fehler beim Abrufen der Teilnehmer: ${participantsError.message}`);
      }

      const participantCount = participants?.length || 0;
      console.log(`Found ${participantCount} confirmed participants`);

      if (participantCount < 2) {
        throw new Error(`Mindestens 2 bestätigte Teilnehmer erforderlich. Aktuell: ${participantCount} Teilnehmer`);
      }

      // Step 5: Check if matches already exist
      const { data: existingMatches, error: existingMatchesError } = await supabase
        .from('tournament_matches')
        .select('id')
        .eq('tournament_id', tournament_id)
        .limit(1);

      if (existingMatchesError) {
        console.error('Existing matches check error:', existingMatchesError);
        throw new Error(`Fehler beim Prüfen vorhandener Spiele: ${existingMatchesError.message}`);
      }

      if (existingMatches && existingMatches.length > 0) {
        throw new Error('Spiele für dieses Turnier wurden bereits generiert. Löschen Sie zuerst die vorhandenen Spiele.');
      }

      // Step 6: Calculate format-specific match duration
      const formatType = tournament.format_type || 'knockout';
      const finalConfig = format_config || tournament.format_config || {};
      const totalDuration = calculateMatchDuration(finalConfig, formatType);

      // Step 7: Generate first round matches
      const matches: Match[] = [];
      let matchNumber = 1;

      for (let i = 0; i < participants.length; i += 2) {
        if (i + 1 < participants.length) {
          const participant1 = participants[i];
          const participant2 = participants[i + 1];

          const match: Match = {
            tournament_id,
            club_id: tournament.club_id,
            round_number: 1,
            match_number: matchNumber++,
            participant1_id: participant1.id,
            participant2_id: participant2.id,
            status: 'scheduled',
            estimated_duration_minutes: totalDuration
          };

          matches.push(match);
        }
      }

      console.log(`Generated ${matches.length} matches for first round with ${totalDuration}min duration each`);

      // Step 8: Insert matches into tournament_matches table
      const { data: insertedMatches, error: insertError } = await supabase
        .from('tournament_matches')
        .insert(matches)
        .select();

      if (insertError) {
        console.error('Error inserting matches:', insertError);
        throw new Error(`Failed to insert matches: ${insertError.message}`);
      }

      console.log('Matches inserted successfully');

      // Step 9: Smart Scheduling (if configured)
      let schedulingResult = null;
      if (smart_scheduling_config) {
        console.log('Smart scheduling config detected:', smart_scheduling_config);
        console.log('Starting smart scheduling with config:', smart_scheduling_config);
        
        try {
          schedulingResult = await applySmartScheduling(
            supabase,
            tournament,
            insertedMatches,
            smart_scheduling_config
          );
          
          if (schedulingResult.success) {
            console.log(`Smart scheduling completed: ${schedulingResult.scheduled_count} matches scheduled`);
          } else {
            console.warn('Smart scheduling failed:', schedulingResult.error);
          }
        } catch (schedulingError) {
          console.error('Smart scheduling error:', schedulingError);
          // Continue without failing the entire operation
        }
      }

      // Step 10: Update tournament status to active
      await supabase
        .from('tournaments')
        .update({ status: 'active' })
        .eq('id', tournament_id);

      const responseMessage = schedulingResult?.success 
        ? `Successfully generated and scheduled ${matches.length} matches. ${schedulingResult.scheduled_count} matches automatically scheduled.`
        : `Successfully generated ${matches.length} matches with format-specific durations (${totalDuration}min each). You can now schedule them in the tournament calendar.`;

      return new Response(JSON.stringify({
        success: true,
        message: responseMessage,
        matches: insertedMatches,
        total_duration_minutes: totalDuration,
        format_type: formatType,
        smart_scheduling: schedulingResult
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });

    } else if (action === 'delete_all') {
      console.log('Deleting all matches for tournament:', tournament_id);

      // Step 1: Verify tournament exists and get details
      const { data: tournament, error: tournamentError } = await supabase
        .from('tournaments')
        .select('id, club_id')
        .eq('id', tournament_id)
        .single();

      if (tournamentError || !tournament) {
        console.error('Tournament not found:', tournamentError);
        throw new Error('Tournament not found');
      }

      // Step 2: Check user permissions
      const { data: membership, error: membershipError } = await supabase
        .from('club_memberships')
        .select('role, club_id')
        .eq('user_id', user.id)
        .eq('club_id', tournament.club_id)
        .eq('is_active', true)
        .in('role', ['admin', 'super_admin']);

      if (membershipError || !membership || membership.length === 0) {
        console.error('Permission check failed:', membershipError);
        throw new Error('Keine Berechtigung zum Löschen von Spielen für diesen Club');
      }

      // Step 3: Delete all matches for this tournament
      const { error: deleteError } = await supabase
        .from('tournament_matches')
        .delete()
        .eq('tournament_id', tournament_id);

      if (deleteError) {
        console.error('Error deleting matches:', deleteError);
        throw new Error(`Failed to delete matches: ${deleteError.message}`);
      }

      console.log('All matches deleted successfully');

      return new Response(JSON.stringify({
        success: true,
        message: 'Alle Spiele erfolgreich gelöscht'
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });

    } else if (action === 'update_score') {
      // Handle match score updates with sequential scheduling
      if (!match_id || !sets) {
        throw new Error('Match ID and sets data are required for score updates');
      }

      const { data: updatedMatch, error: updateError } = await supabase
        .from('tournament_matches')
        .update({
          score_json: { sets, winner: winner_id },
          winner_id,
          status: winner_id ? 'completed' : 'in_progress',
          updated_at: new Date().toISOString()
        })
        .eq('id', match_id)
        .select()
        .single();

      if (updateError) {
        console.error('Error updating match score:', updateError);
        throw new Error(`Failed to update match score: ${updateError.message}`);
      }

      // If match is completed, automatically start next match on same court
      if (winner_id && updatedMatch.court_id) {
        console.log('Match completed, triggering sequential scheduling...');
        await triggerNextMatchOnCourt(supabase, updatedMatch);
      }

      return new Response(JSON.stringify({
        success: true,
        message: 'Match score updated successfully',
        match: updatedMatch
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });

    } else {
      throw new Error(`Unknown action: ${action}`);
    }

  } catch (error) {
    console.error('Error in tournament matches function:', error);
    return new Response(JSON.stringify({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});

// Smart Scheduling Engine Integration
async function applySmartScheduling(supabase: any, tournament: any, matches: any[], config: any) {
  try {
    console.log('Fetching available courts for smart scheduling...');
    
    // Get available courts for this tournament
    const { data: courts, error: courtsError } = await supabase
      .from('courts')
      .select('*')
      .eq('club_id', tournament.club_id)
      .eq('locked', false);

    if (courtsError) {
      console.error('Court fetch error:', courtsError);
      throw new Error(`Failed to fetch courts: ${courtsError.message}`);
    }

    if (!courts || courts.length === 0) {
      throw new Error('No available courts found for scheduling');
    }

    console.log(`Found ${courts.length} available courts`);

    // CRITICAL: Initialize tournament start date CORRECTLY with club timezone
    let tournamentStartDate;
    
    // Get club timezone for proper time calculations
    const clubTimezone = await getClubTimezone(supabase, tournament.club_id);
    
    console.log('CRITICAL: Tournament start date should use club timezone:', clubTimezone);
    console.log('Tournament object tournament_start field:', tournament.tournament_start);
    
    // Debug timezone information
    debugTimezone('Tournament start from DB', tournament.tournament_start, clubTimezone);
    
    // Priority 1: Use provided config time if available (user input takes precedence)
    if (config.start_date && (config.start_time || config.tournament_start_time)) {
      tournamentStartDate = new Date(config.start_date);
      const timeStr = config.start_time || config.tournament_start_time;
      const [hours, minutes] = timeStr.split(':').map(Number);
      tournamentStartDate.setHours(hours, minutes, 0, 0);
      debugTimezone('Using config start date and time from user input', tournamentStartDate, clubTimezone);
      console.log('CRITICAL: Using user-specified time:', timeStr, '→', tournamentStartDate.toISOString());
    }
    // Priority 2: Use tournament's actual start date/time from database (only date, keep user time)
    else if (tournament.tournament_start) {
      tournamentStartDate = new Date(tournament.tournament_start);
      
      // If user provided a specific start time, override the time part
      if (config.tournament_start_time) {
        const [hours, minutes] = config.tournament_start_time.split(':').map(Number);
        tournamentStartDate.setHours(hours, minutes, 0, 0);
        console.log('CRITICAL: Overriding database time with user time:', config.tournament_start_time);
      }
      
      debugTimezone('Parsed tournament start date (potentially with user time override)', tournamentStartDate, clubTimezone);
      console.log('CRITICAL: Tournament start date passed to scheduler:', tournamentStartDate.toISOString());
    } 
    // Priority 3: Use provided config start_date as fallback
    else if (config.start_date) {
      tournamentStartDate = new Date(config.start_date);
      if (config.start_time || config.tournament_start_time) {
        const timeStr = config.start_time || config.tournament_start_time;
        const [hours, minutes] = timeStr.split(':').map(Number);
        tournamentStartDate.setHours(hours, minutes, 0, 0);
      } else {
        tournamentStartDate.setHours(9, 0, 0, 0); // Default to 9 AM
      }
      debugTimezone('Using config start date', tournamentStartDate, clubTimezone);
    }
    // Priority 3: Emergency fallback - should NEVER happen for a valid tournament
    if (!tournamentStartDate) {
      console.error('CRITICAL ERROR: No valid tournament start date found! Using current date as emergency fallback');
      tournamentStartDate = new Date();
      if (config.tournament_start_time) {
        const [hours, minutes] = config.tournament_start_time.split(':').map(Number);
        tournamentStartDate.setHours(hours, minutes, 0, 0);
      }
      console.log('EMERGENCY FALLBACK - Using current date:', tournamentStartDate.toISOString());
    }

    // Final validation to ensure we have a valid date
    if (!tournamentStartDate || isNaN(tournamentStartDate.getTime())) {
      console.error('CRITICAL: Invalid tournament start date! Creating fallback');
      tournamentStartDate = new Date();
      tournamentStartDate.setHours(9, 0, 0, 0);
    }
    
    console.log(`FINAL tournament start date for scheduling:`, tournamentStartDate.toISOString());
    console.log(`Starting smart scheduling with ${courts.length} courts`);
    
    // Ensure the config uses the tournament start date, not today's date
    const correctedConfig = {
      ...config,
      start_date: tournamentStartDate.toISOString().split('T')[0], // Format as YYYY-MM-DD
      start_time: formatTimeForClub(tournamentStartDate, clubTimezone), // Use club timezone
      club_timezone: clubTimezone
    };
    
    debugTimezone('Corrected config for scheduling', tournamentStartDate, clubTimezone);
    console.log('Corrected config for scheduling:', {
      start_date: correctedConfig.start_date,
      start_time: correctedConfig.start_time,
      originalDateTime: tournamentStartDate.toISOString()
    });
    
    const scheduledMatches = await scheduleMatchesIntelligently(
      matches,
      courts,
      correctedConfig,
      tournament.format_type,
      tournamentStartDate,
      clubTimezone
    );

    // Update matches with scheduling information
    const updatePromises = scheduledMatches.map(async (scheduledMatch) => {
      const { error } = await supabase
        .from('tournament_matches')
        .update({
          scheduled_date: scheduledMatch.scheduled_date,
          scheduled_time: scheduledMatch.scheduled_time,
          court_id: scheduledMatch.court_id,
          scheduled_at: scheduledMatch.scheduled_at?.toISOString()
        })
        .eq('id', scheduledMatch.id);

      if (error) {
        console.error(`Failed to update match ${scheduledMatch.id}:`, error);
        return false;
      }
      return true;
    });

    const updateResults = await Promise.all(updatePromises);
    const successCount = updateResults.filter(Boolean).length;

    return {
      success: true,
      scheduled_count: successCount,
      total_matches: matches.length,
      scheduling_config: config
    };

  } catch (error) {
    console.error('Smart scheduling failed:', error);
    return {
      success: false,
      error: error.message,
      scheduled_count: 0
    };
  }
}

// Intelligent Match Scheduling Algorithm
async function scheduleMatchesIntelligently(
  matches: any[],
  courts: any[],
  config: any,
  formatType: string,
  startDate: Date,
  clubTimezone: string
) {
  console.log(`Starting intelligent scheduling for ${matches.length} matches`);
  console.log(`CRITICAL: Tournament start date passed to scheduler:`, startDate.toISOString());
  console.log(`CRITICAL: Tournament start date should be September 1st, 2025`);
  
  // FORCE the start date to be the tournament date, not today
  const actualTournamentDate = new Date(startDate);
  console.log(`FORCED tournament date:`, actualTournamentDate.toISOString());

  // Sort courts by preference (main court first, then by number)
  const sortedCourts = courts.sort((a, b) => {
    if (a.is_main_court && !b.is_main_court) return -1;
    if (!a.is_main_court && b.is_main_court) return 1;
    return (a.number || 0) - (b.number || 0);
  });

  console.log(`PARALLEL SCHEDULING: Using ${sortedCourts.length} courts for round-robin allocation`);

  // Initialize court schedules
  const courtSchedules = new Map();
  sortedCourts.forEach(court => {
    courtSchedules.set(court.id, []);
  });

  // Initialize player schedules to track conflicts
  const playerSchedules = new Map();

  // Configure daily time windows
  const dailyStartTime = parseTimeString(config.daily_start_time || '09:00');
  const dailyEndTime = parseTimeString(config.daily_end_time || '21:00');
  const minBreakMinutes = config.minimum_break_between_matches || 30;
  const maxMatchesPerPlayerPerDay = config.max_matches_per_player_per_day || 2;

  const scheduledMatches = [];

  // Group matches by format-specific logic
  const matchGroups = groupMatchesByFormat(matches, formatType, config);

  for (const group of matchGroups) {
    console.log(`Scheduling group of ${group.length} matches with parallel allocation`);

    for (let matchIndex = 0; matchIndex < group.length; matchIndex++) {
      const match = group[matchIndex];
      
      // PARALLEL ALLOCATION: Round-robin court assignment
      const targetCourtIndex = matchIndex % sortedCourts.length;
      const targetCourt = sortedCourts[targetCourtIndex];
      
      console.log(`PARALLEL: Match ${matchIndex + 1} assigned to court ${targetCourt.number} (index ${targetCourtIndex})`);

      const schedulingResult = findBestTimeSlotParallel(
        match,
        targetCourt,
        sortedCourts,
        courtSchedules,
        playerSchedules,
        actualTournamentDate,
        {
          dailyStartTime,
          dailyEndTime,
          minBreakMinutes,
          maxMatchesPerPlayerPerDay,
          useMainCourtForFinals: config.use_main_court_for_finals || false
        }
      );

      if (schedulingResult) {
        console.log(`Match ${match.id} scheduled on court ${targetCourt.number}:`, {
          court_id: schedulingResult.court_id,
          scheduled_date: schedulingResult.scheduled_date,
          scheduled_time: schedulingResult.scheduled_time,
          scheduled_at: schedulingResult.scheduled_at
        });
        scheduledMatches.push(schedulingResult);
        updateSchedules(courtSchedules, playerSchedules, schedulingResult);
      } else {
        console.warn(`Could not schedule match ${match.id} on target court ${targetCourt.number}`);
      }
    }

    // Add gap between rounds for knockout tournaments
    if (formatType === 'knockout' && config.knockout_sequential) {
      const roundGapHours = config.round_gap_hours || 2;
      // Update the actual tournament date for round gaps
      actualTournamentDate.setHours(actualTournamentDate.getHours() + roundGapHours);
    }
  }

  console.log(`Successfully scheduled ${scheduledMatches.length} out of ${matches.length} matches`);
  return scheduledMatches;
}

function parseTimeString(timeString: string) {
  const [hours, minutes] = timeString.split(':').map(Number);
  return { hours, minutes };
}

function groupMatchesByFormat(matches: any[], formatType: string, config: any) {
  switch (formatType) {
    case 'knockout':
      if (config.knockout_sequential) {
        // Group by round for sequential scheduling
        const rounds = new Map();
        matches.forEach(match => {
          if (!rounds.has(match.round_number)) {
            rounds.set(match.round_number, []);
          }
          rounds.get(match.round_number).push(match);
        });
        return Array.from(rounds.values()).sort((a, b) => a[0].round_number - b[0].round_number);
      } else {
        return [matches]; // Schedule all together
      }

    case 'round_robin':
      if (config.group_stage_parallel) {
        return [matches]; // All can be parallel
      } else {
        // Create groups avoiding player conflicts
        return createNonConflictingGroups(matches);
      }

    default:
      return [matches];
  }
}

function createNonConflictingGroups(matches: any[]) {
  const groups = [];
  const unscheduled = [...matches];

  while (unscheduled.length > 0) {
    const currentGroup = [];
    const usedPlayers = new Set();

    for (let i = unscheduled.length - 1; i >= 0; i--) {
      const match = unscheduled[i];
      const players = [match.participant1_id, match.participant2_id].filter(Boolean);
      
      if (!players.some(p => usedPlayers.has(p))) {
        currentGroup.push(match);
        players.forEach(p => usedPlayers.add(p));
        unscheduled.splice(i, 1);
      }
    }

    if (currentGroup.length > 0) {
      groups.push(currentGroup);
    }
  }

  return groups;
}

function findBestTimeSlot(
  match: any,
  courts: any[],
  courtSchedules: Map<string, any[]>,
  playerSchedules: Map<string, any[]>,
  startDate: Date,
  options: any
) {
  // SEQUENTIAL SCHEDULING: Use the actual match duration without buffers
  const timeConfig = options.timeConfig || {};
  const calculateMatchDuration = (config: any) => {
    const matchMinutes = config.matchDurationMinutes || 90;
    const warmupMinutes = config.warmupMinutes || 5;
    const breakMinutes = config.betweenMatchBreakMinutes || 10;
    const prepMinutes = config.courtPreparationMinutes || 0;
    return matchMinutes + warmupMinutes + breakMinutes + prepMinutes;
  };
  
  const matchDuration = calculateMatchDuration(timeConfig);
  // NO BUFFER TIME - sequential scheduling means next match starts exactly when previous ends
  const totalDuration = matchDuration;

  // Try each court
  for (const court of courts) {
    // Skip main court for non-finals if configured
    if (options.useMainCourtForFinals && court.is_main_court && !isMatchFinal(match)) {
      continue;
    }

    const timeSlot = findEarliestSlot(
      court,
      courtSchedules.get(court.id) || [],
      playerSchedules,
      match,
      startDate,
      totalDuration,
      options
    );

    if (timeSlot) {
      return {
        ...match,
        court_id: court.id,
        scheduled_date: timeSlot.start.toISOString().split('T')[0],
        scheduled_time: timeSlot.start.toTimeString().substring(0, 5),
        scheduled_at: timeSlot.start,
        ends_at: timeSlot.end
      };
    }
  }

  return null;
}

function findBestTimeSlotParallel(
  match: any,
  targetCourt: any,
  allCourts: any[],
  courtSchedules: Map<string, any[]>,
  playerSchedules: Map<string, any[]>,
  startDate: Date,
  options: any
) {
  // PARALLEL SCHEDULING: Try target court first, fallback to others if needed
  const timeConfig = options.timeConfig || {};
  const calculateMatchDuration = (config: any) => {
    const matchMinutes = config.matchDurationMinutes || 90;
    const warmupMinutes = config.warmupMinutes || 5;
    const breakMinutes = config.betweenMatchBreakMinutes || 10;
    const prepMinutes = config.courtPreparationMinutes || 0;
    return matchMinutes + warmupMinutes + breakMinutes + prepMinutes;
  };
  
  const matchDuration = calculateMatchDuration(timeConfig);
  const totalDuration = matchDuration;

  // Skip main court for non-finals if configured
  if (options.useMainCourtForFinals && targetCourt.is_main_court && !isMatchFinal(match)) {
    console.log(`Skipping main court ${targetCourt.number} for non-final match`);
    // Find next available court from all courts
    for (const court of allCourts) {
      if (court.id === targetCourt.id || (court.is_main_court && options.useMainCourtForFinals)) continue;
      
      const timeSlot = findEarliestSlotParallel(
        court,
        courtSchedules.get(court.id) || [],
        playerSchedules,
        match,
        startDate,
        totalDuration,
        options
      );

      if (timeSlot) {
        return {
          ...match,
          court_id: court.id,
          scheduled_date: timeSlot.start.toISOString().split('T')[0],
          scheduled_time: timeSlot.start.toTimeString().substring(0, 5),
          scheduled_at: timeSlot.start,
          ends_at: timeSlot.end
        };
      }
    }
    return null;
  }

  // Try target court first
  const timeSlot = findEarliestSlotParallel(
    targetCourt,
    courtSchedules.get(targetCourt.id) || [],
    playerSchedules,
    match,
    startDate,
    totalDuration,
    options
  );

  if (timeSlot) {
    return {
      ...match,
      court_id: targetCourt.id,
      scheduled_date: timeSlot.start.toISOString().split('T')[0],
      scheduled_time: timeSlot.start.toTimeString().substring(0, 5),
      scheduled_at: timeSlot.start,
      ends_at: timeSlot.end
    };
  }

  // Fallback: try other courts if target court is not available
  console.log(`Target court ${targetCourt.number} not available, trying fallback courts`);
  for (const court of allCourts) {
    if (court.id === targetCourt.id) continue; // Skip target court, already tried
    
    if (options.useMainCourtForFinals && court.is_main_court && !isMatchFinal(match)) {
      continue;
    }

    const fallbackTimeSlot = findEarliestSlotParallel(
      court,
      courtSchedules.get(court.id) || [],
      playerSchedules,
      match,
      startDate,
      totalDuration,
      options
    );

    if (fallbackTimeSlot) {
      console.log(`Using fallback court ${court.number} instead of target court ${targetCourt.number}`);
      return {
        ...match,
        court_id: court.id,
        scheduled_date: fallbackTimeSlot.start.toISOString().split('T')[0],
        scheduled_time: fallbackTimeSlot.start.toTimeString().substring(0, 5),
        scheduled_at: fallbackTimeSlot.start,
        ends_at: fallbackTimeSlot.end
      };
    }
  }

  return null;
}

function findEarliestSlot(
  court: any,
  courtSchedule: any[],
  playerSchedules: Map<string, any[]>,
  match: any,
  startDate: Date,
  duration: number,
  options: any,
  clubTimezone: string
) {
  debugTimezone('findEarliestSlot received startDate', startDate, clubTimezone);
  console.log(`Sequential scheduling: Finding next slot after last match on court ${court.id}`);
  
  // SEQUENTIAL LOGIC: Find the end time of the last match on this court
  const lastMatchEndTime = findLastMatchEndTimeOnCourt(courtSchedule);
  
  // For the first match on a court, use tournament start time
  // For subsequent matches, start exactly when the previous match ends
  let slotStart;
  
  if (lastMatchEndTime) {
    slotStart = new Date(lastMatchEndTime);
    console.log(`Sequential: Next match starts exactly when previous ends: ${slotStart.toISOString()}`);
  } else {
    // This is the first match on this court
    const tournamentDate = new Date(startDate);
    slotStart = new Date(Math.max(tournamentDate.getTime(), startDate.getTime()));
    console.log(`Sequential: First match on court starts at tournament time: ${slotStart.toISOString()}`);
  }
  
  const slotEnd = new Date(slotStart.getTime() + duration * 60000);
  
  // Simple validation: Check if this slot fits within daily hours
  const startHour = options.dailyStartTime.hours;
  const startMinute = options.dailyStartTime.minutes;
  const endHour = options.dailyEndTime.hours;
  const endMinute = options.dailyEndTime.minutes;
  
  const dayStart = new Date(slotStart);
  dayStart.setHours(startHour, startMinute, 0, 0);
  
  const dayEnd = new Date(slotStart);
  dayEnd.setHours(endHour, endMinute, 0, 0);
  
  // If slot goes beyond daily hours, move to next day
  if (slotEnd.getTime() > dayEnd.getTime()) {
    const nextDay = new Date(slotStart);
    nextDay.setDate(nextDay.getDate() + 1);
    nextDay.setHours(startHour, startMinute, 0, 0);
    
    slotStart = nextDay;
    const newSlotEnd = new Date(slotStart.getTime() + duration * 60000);
    
    console.log(`Sequential: Moved to next day: ${slotStart.toISOString()}`);
    return { start: slotStart, end: newSlotEnd };
  }
  
  // Check player availability (simplified - just check conflicts)
  if (arePlayersAvailable(match, slotStart, slotEnd, playerSchedules, options)) {
    return { start: slotStart, end: slotEnd };
  }
  
  // If players not available, this needs to be handled at a higher level
  console.log('Sequential: Players not available for calculated slot');
  return null;
}

function findEarliestSlotParallel(
  court: any,
  courtSchedule: any[],
  playerSchedules: Map<string, any[]>,
  match: any,
  startDate: Date,
  duration: number,
  options: any,
  clubTimezone: string
) {
  debugTimezone('findEarliestSlotParallel received startDate', startDate, clubTimezone);
  console.log(`PARALLEL scheduling: Finding slot on court ${court.number || court.id}`);
  
  // PARALLEL LOGIC: Find the end time of the last match on this specific court
  const lastMatchEndTime = findLastMatchEndTimeOnCourt(courtSchedule);
  
  let slotStart;
  
  if (lastMatchEndTime) {
    // This court has previous matches - schedule after the last one
    slotStart = new Date(lastMatchEndTime);
    console.log(`PARALLEL: Court ${court.number} has previous matches, next starts at: ${slotStart.toISOString()}`);
  } else {
    // This is the first match on this court - can start at tournament time
    slotStart = new Date(startDate);
    console.log(`PARALLEL: Court ${court.number} is free, first match starts at tournament time: ${slotStart.toISOString()}`);
  }
  
  const slotEnd = new Date(slotStart.getTime() + duration * 60000);
  
  // Validate daily hours
  const startHour = options.dailyStartTime.hours;
  const startMinute = options.dailyStartTime.minutes;
  const endHour = options.dailyEndTime.hours;
  const endMinute = options.dailyEndTime.minutes;
  
  const dayStart = new Date(slotStart);
  dayStart.setHours(startHour, startMinute, 0, 0);
  
  const dayEnd = new Date(slotStart);
  dayEnd.setHours(endHour, endMinute, 0, 0);
  
  // If slot goes beyond daily hours, move to next day
  if (slotEnd.getTime() > dayEnd.getTime()) {
    const nextDay = new Date(slotStart);
    nextDay.setDate(nextDay.getDate() + 1);
    nextDay.setHours(startHour, startMinute, 0, 0);
    
    slotStart = nextDay;
    const newSlotEnd = new Date(slotStart.getTime() + duration * 60000);
    
    console.log(`PARALLEL: Moved to next day on court ${court.number}: ${slotStart.toISOString()}`);
    return { start: slotStart, end: newSlotEnd };
  }
  
  // Check player availability
  if (arePlayersAvailable(match, slotStart, slotEnd, playerSchedules, options)) {
    console.log(`PARALLEL: Slot confirmed for court ${court.number}: ${slotStart.toISOString()} - ${slotEnd.toISOString()}`);
    return { start: slotStart, end: slotEnd };
  }
  
  console.log(`PARALLEL: Players not available for slot on court ${court.number}`);
  return null;
}

function findLastMatchEndTimeOnCourt(courtSchedule: any[]) {
  if (!courtSchedule || courtSchedule.length === 0) {
    return null;
  }
  
  // Find the latest end time
  const sortedSchedule = [...courtSchedule].sort((a, b) => b.end.getTime() - a.end.getTime());
  return sortedSchedule[0]?.end || null;
}

function isTimeSlotAvailable(schedule: any[], start: Date, end: Date) {
  // CRITICAL FIX: Proper overlap detection with buffer time
  const hasOverlap = schedule.some(slot => {
    // Two time slots overlap if one starts before the other ends
    const overlap = !(end.getTime() <= slot.start.getTime() || start.getTime() >= slot.end.getTime());
    if (overlap) {
      console.log(`Overlap detected: New slot ${start.toISOString()} - ${end.toISOString()} conflicts with existing ${slot.start.toISOString()} - ${slot.end.toISOString()}`);
    }
    return overlap;
  });
  
  return !hasOverlap;
}

function arePlayersAvailable(
  match: any,
  start: Date,
  end: Date,
  playerSchedules: Map<string, any[]>,
  options: any
) {
  const players = [match.participant1_id, match.participant2_id].filter(Boolean);

  for (const playerId of players) {
    const playerSchedule = playerSchedules.get(playerId) || [];

    // Check daily match limit
    const dayMatches = playerSchedule.filter(slot => 
      slot.start.toDateString() === start.toDateString()
    );

    if (dayMatches.length >= options.maxMatchesPerPlayerPerDay) {
      return false;
    }

    // Check minimum break time
    const hasConflict = playerSchedule.some(slot => {
      const breakTime = options.minBreakMinutes * 60000;
      return (
        start.getTime() < slot.end.getTime() + breakTime &&
        end.getTime() > slot.start.getTime() - breakTime
      );
    });

    if (hasConflict) {
      return false;
    }
  }

  return true;
}

function isMatchFinal(match: any) {
  // Simple heuristic: highest round number with lowest match number
  return match.format_constraints?.is_final || false;
}

function updateSchedules(
  courtSchedules: Map<string, any[]>,
  playerSchedules: Map<string, any[]>,
  scheduledMatch: any
) {
  // Update court schedule
  const courtSchedule = courtSchedules.get(scheduledMatch.court_id) || [];
  courtSchedule.push({
    start: scheduledMatch.scheduled_at,
    end: scheduledMatch.ends_at,
    matchId: scheduledMatch.id
  });
  courtSchedule.sort((a, b) => a.start.getTime() - b.start.getTime());
  courtSchedules.set(scheduledMatch.court_id, courtSchedule);

  // Update player schedules
  const players = [scheduledMatch.participant1_id, scheduledMatch.participant2_id].filter(Boolean);
  players.forEach(playerId => {
    const playerSchedule = playerSchedules.get(playerId) || [];
    playerSchedule.push({
      start: scheduledMatch.scheduled_at,
      end: scheduledMatch.ends_at,
      matchId: scheduledMatch.id
    });
    playerSchedule.sort((a, b) => a.start.getTime() - b.start.getTime());
    playerSchedules.set(playerId, playerSchedule);
  });
}

// NEW: Event-driven sequential scheduling when match completes
async function triggerNextMatchOnCourt(supabase: any, completedMatch: any) {
  try {
    console.log('Triggering sequential scheduling for completed match:', completedMatch.id);
    
    // Find the next unscheduled match on the same court
    const { data: nextMatches, error } = await supabase
      .from('tournament_matches')
      .select('*')
      .eq('tournament_id', completedMatch.tournament_id)
      .eq('court_id', completedMatch.court_id)
      .is('scheduled_at', null)
      .eq('status', 'scheduled')
      .order('round_number', { ascending: true })
      .order('match_number', { ascending: true })
      .limit(1);
    
    if (error) {
      console.error('Error finding next match:', error);
      return;
    }
    
    if (!nextMatches || nextMatches.length === 0) {
      console.log('No more unscheduled matches on this court');
      return;
    }
    
    const nextMatch = nextMatches[0];
    
    // Calculate when this match ends (actual completion time = now)
    const completionTime = new Date();
    
    // Schedule the next match to start exactly now (or very soon)
    const nextStartTime = new Date(completionTime.getTime() + 60000); // 1 minute buffer for court change
    const nextEndTime = new Date(nextStartTime.getTime() + (nextMatch.estimated_duration_minutes || 90) * 60000);
    
    // Update the next match with sequential scheduling
    const { error: updateError } = await supabase
      .from('tournament_matches')
      .update({
        scheduled_at: nextStartTime.toISOString(),
        scheduled_date: nextStartTime.toISOString().split('T')[0],
        scheduled_time: nextStartTime.toTimeString().substring(0, 5),
        updated_at: new Date().toISOString()
      })
      .eq('id', nextMatch.id);
    
    if (updateError) {
      console.error('Error updating next match schedule:', updateError);
      return;
    }
    
    console.log(`Sequential: Next match ${nextMatch.id} scheduled to start at ${nextStartTime.toISOString()}`);
    
  } catch (error) {
    console.error('Error in triggerNextMatchOnCourt:', error);
  }
}