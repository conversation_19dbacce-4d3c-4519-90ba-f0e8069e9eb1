import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface TournamentFormat {
  type: string;
  name: string;
  description: string;
  config: Record<string, any>;
  category: string;
}

interface TournamentParticipant {
  id: string;
  tournament_id: string;
  club_id: string;
  user_id?: string;
  guest_name?: string;
  guest_email?: string;
  is_guest: boolean;
  seeding?: number;
  status: string;
}

const TOURNAMENT_FORMATS: TournamentFormat[] = [
  // Standard Formats  
  {
    type: 'knockout',
    name: 'K.o.-System (Single Elimination)',
    description: 'Klassisches Ausscheidungsturnier - jeder Spieler scheidet nach der ersten Niederlage aus. Ideal für Turniere mit vielen Teilnehmern.',
    config: { seeding: 'random', consolation_plate: false },
    category: 'Standard'
  },
  {
    type: 'round_robin',
    name: '<PERSON> Robin (Jeder gegen jeden)',
    description: '<PERSON><PERSON>nehmer spielt gegen jeden anderen genau einmal. Platzierung nach Matchbilanz. Perfekt für kleinere Gruppen.',
    config: { groups: 1, tiebreaker: ['head_to_head', 'set_ratio'] },
    category: 'Standard'
  },
  {
    type: 'uts',
    name: 'UTS (Universal Tennis Scoring)',
    description: 'Revolutionäres Format: 4 Punkte = 1 Game, bei 3:3 Sudden Death. Kurze Sätze, schnelle Matches.',
    config: { best_of: 3, seeding: 'rating', sets_to_win: 2 },
    category: 'Innovative'
  }
  // ... weitere Formate werden hier eingefügt
];

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    const { action, payload } = await req.json()
    console.log(`Tournament management action: ${action}`)

    switch (action) {
      case 'get_formats':
        return new Response(
          JSON.stringify({ 
            success: true, 
            data: TOURNAMENT_FORMATS 
          }),
          { 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        )

      case 'register_participant':
        return await registerParticipant(supabaseClient, payload)
        
      case 'withdraw_participant':
        return await withdrawParticipant(supabaseClient, payload)
        
      case 'generate_matches':
        return await generateMatches(supabaseClient, payload)
        
      case 'validate_tournament_setup':
        return await validateTournamentSetup(supabaseClient, payload)

      default:
        return new Response(
          JSON.stringify({ 
            success: false, 
            error: 'Unknown action' 
          }),
          { 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 400
          }
        )
    }
  } catch (error) {
    console.error('Tournament management error:', error)
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message 
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    )
  }
})

async function registerParticipant(supabaseClient: any, payload: any) {
  const { tournament_id, club_id, user_id, guest_name, guest_email, is_guest } = payload

  // Validate tournament exists and is accepting registrations
  const { data: tournament, error: tournamentError } = await supabaseClient
    .from('tournaments')
    .select('*')
    .eq('id', tournament_id)
    .eq('club_id', club_id)
    .single()

  if (tournamentError || !tournament) {
    throw new Error('Turnier nicht gefunden oder nicht verfügbar')
  }

  if (tournament.status !== 'registration') {
    throw new Error('Anmeldung für dieses Turnier ist nicht mehr möglich')
  }

  // Check if registration period is valid
  const now = new Date()
  if (tournament.registration_end && new Date(tournament.registration_end) < now) {
    throw new Error('Anmeldeschluss ist bereits verstrichen')
  }
  if (tournament.registration_start && new Date(tournament.registration_start) > now) {
    throw new Error('Anmeldung hat noch nicht begonnen')
  }

  // Check if already registered
  const { data: existing } = await supabaseClient
    .from('tournament_participants')
    .select('id')
    .eq('tournament_id', tournament_id)
    .eq(user_id ? 'user_id' : 'guest_email', user_id || guest_email)
    .maybeSingle()

  if (existing) {
    throw new Error('Bereits für dieses Turnier angemeldet')
  }

  // Check participant limit
  if (tournament.max_participants) {
    const { count } = await supabaseClient
      .from('tournament_participants')
      .select('*', { count: 'exact', head: true })
      .eq('tournament_id', tournament_id)
      .eq('status', 'registered')

    if (count >= tournament.max_participants) {
      throw new Error('Maximale Teilnehmerzahl erreicht')
    }
  }

  // Register participant
  const participantData: any = {
    tournament_id,
    club_id,
    is_guest: is_guest || false,
    status: 'registered'
  }

  if (is_guest) {
    if (!guest_name || !guest_email) {
      throw new Error('Name und E-Mail für Gäste erforderlich')
    }
    participantData.guest_name = guest_name
    participantData.guest_email = guest_email
  } else {
    if (!user_id) {
      throw new Error('User ID für Mitglieder erforderlich')
    }
    participantData.user_id = user_id
  }

  const { data, error } = await supabaseClient
    .from('tournament_participants')
    .insert([participantData])
    .select()
    .single()

  if (error) {
    throw new Error(`Anmeldung fehlgeschlagen: ${error.message}`)
  }

  console.log(`Participant registered: ${JSON.stringify(data)}`)

  return new Response(
    JSON.stringify({ 
      success: true, 
      data,
      message: 'Erfolgreich angemeldet' 
    }),
    { 
      headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
    }
  )
}

async function withdrawParticipant(supabaseClient: any, payload: any) {
  const { participant_id, user_id } = payload

  // Verify participant belongs to user
  const { data: participant, error: participantError } = await supabaseClient
    .from('tournament_participants')
    .select('*, tournaments(*)')
    .eq('id', participant_id)
    .single()

  if (participantError || !participant) {
    throw new Error('Anmeldung nicht gefunden')
  }

  // Check if user is authorized to withdraw this participant
  if (participant.user_id !== user_id) {
    throw new Error('Nicht berechtigt, diese Anmeldung zu stornieren')
  }

  // Check if tournament still allows withdrawal
  if (participant.tournaments?.status !== 'registration') {
    throw new Error('Abmeldung nicht mehr möglich - Turnier hat bereits begonnen')
  }

  // Update participant status
  const { error } = await supabaseClient
    .from('tournament_participants')
    .update({ status: 'withdrawn' })
    .eq('id', participant_id)

  if (error) {
    throw new Error(`Abmeldung fehlgeschlagen: ${error.message}`)
  }

  console.log(`Participant withdrawn: ${participant_id}`)

  return new Response(
    JSON.stringify({ 
      success: true, 
      message: 'Erfolgreich abgemeldet' 
    }),
    { 
      headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
    }
  )
}

async function generateMatches(supabaseClient: any, payload: any) {
  const { tournament_id, format_type } = payload

  // Get tournament and participants
  const { data: tournament, error: tournamentError } = await supabaseClient
    .from('tournaments')
    .select('*')
    .eq('id', tournament_id)
    .single()

  if (tournamentError || !tournament) {
    throw new Error('Turnier nicht gefunden')
  }

  const { data: participants, error: participantsError } = await supabaseClient
    .from('tournament_participants')
    .select('*')
    .eq('tournament_id', tournament_id)
    .eq('status', 'registered')

  if (participantsError) {
    throw new Error('Teilnehmer konnten nicht geladen werden')
  }

  if (!participants || participants.length < 2) {
    throw new Error('Mindestens 2 Teilnehmer erforderlich')
  }

  // Generate matches based on format
  const matches = await generateMatchesForFormat(format_type || 'knockout', participants)

  // Insert matches into database
  const { data: insertedMatches, error: matchError } = await supabaseClient
    .from('tournament_matches')
    .insert(matches.map(match => ({
      ...match,
      tournament_id
    })))
    .select()

  if (matchError) {
    throw new Error(`Spiele konnten nicht erstellt werden: ${matchError.message}`)
  }

  console.log(`Generated ${insertedMatches.length} matches for tournament ${tournament_id}`)

  return new Response(
    JSON.stringify({ 
      success: true, 
      data: insertedMatches,
      message: `${insertedMatches.length} Spiele erfolgreich generiert` 
    }),
    { 
      headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
    }
  )
}

async function generateMatchesForFormat(formatType: string, participants: TournamentParticipant[]) {
  switch (formatType) {
    case 'knockout':
      return generateKnockoutMatches(participants)
    case 'round_robin':
      return generateRoundRobinMatches(participants)
    case 'uts':
      return generateUTSMatches(participants)
    default:
      return generateKnockoutMatches(participants) // Fallback
  }
}

function generateKnockoutMatches(participants: TournamentParticipant[]) {
  const matches = []
  const shuffled = [...participants].sort(() => Math.random() - 0.5)
  
  // First round
  for (let i = 0; i < shuffled.length; i += 2) {
    if (i + 1 < shuffled.length) {
      matches.push({
        round_number: 1,
        match_number: Math.floor(i / 2) + 1,
        player1_id: shuffled[i].id,
        player2_id: shuffled[i + 1].id,
        player1_name: shuffled[i].guest_name || `User ${shuffled[i].user_id}`,
        player2_name: shuffled[i + 1].guest_name || `User ${shuffled[i + 1].user_id}`,
        status: 'pending'
      })
    }
  }
  
  // Generate placeholder matches for subsequent rounds
  let roundMatches = Math.floor(shuffled.length / 2)
  let round = 2
  
  while (roundMatches > 1) {
    for (let i = 0; i < Math.floor(roundMatches / 2); i++) {
      matches.push({
        round_number: round,
        match_number: i + 1,
        status: 'pending'
      })
    }
    roundMatches = Math.floor(roundMatches / 2)
    round++
  }
  
  return matches
}

function generateRoundRobinMatches(participants: TournamentParticipant[]) {
  const matches = []
  let matchNumber = 1
  
  for (let i = 0; i < participants.length; i++) {
    for (let j = i + 1; j < participants.length; j++) {
      matches.push({
        round_number: 1, // All matches in round robin are in "round 1"
        match_number: matchNumber++,
        player1_id: participants[i].id,
        player2_id: participants[j].id,
        player1_name: participants[i].guest_name || `User ${participants[i].user_id}`,
        player2_name: participants[j].guest_name || `User ${participants[j].user_id}`,
        status: 'pending'
      })
    }
  }
  
  return matches
}

function generateUTSMatches(participants: TournamentParticipant[]) {
  // UTS can use knockout format but with special scoring rules
  return generateKnockoutMatches(participants)
}

async function validateTournamentSetup(supabaseClient: any, payload: any) {
  const { tournament_id } = payload
  const issues = []

  // Check tournament exists
  const { data: tournament, error: tournamentError } = await supabaseClient
    .from('tournaments')
    .select('*')
    .eq('id', tournament_id)
    .single()

  if (tournamentError || !tournament) {
    issues.push('Turnier nicht gefunden')
    return createValidationResponse(false, issues)
  }

  // Check participants
  const { data: participants, error: participantsError } = await supabaseClient
    .from('tournament_participants')
    .select('*')
    .eq('tournament_id', tournament_id)
    .eq('status', 'registered')

  if (participantsError) {
    issues.push('Teilnehmer konnten nicht geladen werden')
  } else {
    if (!participants || participants.length < 2) {
      issues.push('Mindestens 2 Teilnehmer erforderlich')
    }

    if (tournament.max_participants && participants.length > tournament.max_participants) {
      issues.push(`Zu viele Teilnehmer (${participants.length}/${tournament.max_participants})`)
    }
  }

  // Check format configuration
  if (!tournament.format_type) {
    issues.push('Kein Turnier-Format festgelegt')
  } else {
    const format = TOURNAMENT_FORMATS.find(f => f.type === tournament.format_type)
    if (!format) {
      issues.push('Unbekanntes Turnier-Format')
    }
  }

  // Check dates
  const now = new Date()
  if (tournament.tournament_start && new Date(tournament.tournament_start) < now) {
    issues.push('Turnier-Startdatum liegt in der Vergangenheit')
  }

  if (tournament.registration_end && tournament.tournament_start) {
    if (new Date(tournament.registration_end) > new Date(tournament.tournament_start)) {
      issues.push('Anmeldeschluss nach Turnier-Start')
    }
  }

  return createValidationResponse(issues.length === 0, issues)
}

function createValidationResponse(isValid: boolean, issues: string[]) {
  return new Response(
    JSON.stringify({ 
      success: true,
      data: {
        is_valid: isValid,
        issues: issues
      }
    }),
    { 
      headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
    }
  )
}