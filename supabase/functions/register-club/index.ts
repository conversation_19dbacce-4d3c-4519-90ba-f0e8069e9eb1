import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface RegisterClubRequest {
  adminData: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    birthDate: string;
    street: string;
    houseNumber: string;
    postalCode: string;
    city: string;
    membershipCategory: string;
  };
  clubData: {
    name: string;
    description?: string;
    logoUrl?: string;
    customDomain?: string;
    subdomain?: string;
  };
  courtSetup: {
    courts: Array<{
      number: number;
      surfaceType: string;
      courtGroup?: string;
    }>;
  };
  playingTimes: {
    availability: Array<{
      courtId?: string;
      startTime: string;
      endTime: string;
      daysOfWeek: string[];
    }>;
  };
}

serve(async (req) => {
  console.log('=== Club registration request START ===');
  console.log('Method:', req.method);
  console.log('URL:', req.url);
  
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    console.log('Handling CORS preflight request');
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Check environment variables
    const supabaseUrl = Deno.env.get('SUPABASE_URL');
    const serviceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
    
    console.log('Environment check - URL exists:', !!supabaseUrl);
    console.log('Environment check - Service key exists:', !!serviceRoleKey);
    
    if (!supabaseUrl || !serviceRoleKey) {
      throw new Error('Missing required environment variables');
    }

    // Create Supabase client with service role key for admin operations
    const supabaseAdmin = createClient(
      supabaseUrl,
      serviceRoleKey,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
      }
    );

    console.log('Supabase admin client created');

    let requestBody;
    try {
      requestBody = await req.text();
      console.log('Raw request body received, length:', requestBody.length);
    } catch (err) {
      console.error('Failed to read request body:', err);
      throw new Error('Invalid request body');
    }
    
    let parsedData;
    try {
      parsedData = JSON.parse(requestBody);
      console.log('Request body parsed successfully');
    } catch (err) {
      console.error('Failed to parse JSON:', err);
      throw new Error('Invalid JSON in request body');
    }
    
    const { adminData, clubData, courtSetup, playingTimes }: RegisterClubRequest = parsedData;
    console.log('Processing club registration for:', adminData?.email || 'unknown email');
    console.log('Club name:', clubData?.name || 'unknown name');

    if (!adminData || !clubData) {
      throw new Error('Missing required adminData or clubData');
    }

    let userId: string;
    let userExists = false;

    // Try to create user first
    console.log('Attempting to create user...');
    const { data: signUpData, error: signUpError } = await supabaseAdmin.auth.admin.createUser({
      email: adminData.email,
      password: adminData.password,
      email_confirm: true,
      user_metadata: {
        first_name: adminData.firstName,
        last_name: adminData.lastName,
        birth_date: adminData.birthDate,
        street: adminData.street,
        house_number: adminData.houseNumber,
        postal_code: adminData.postalCode,
        city: adminData.city,
        membership_category: adminData.membershipCategory,
      },
    });

    if (signUpError) {
      console.log('User creation error:', signUpError.message);
      if (signUpError.message.includes('already registered') || signUpError.message.includes('User already registered')) {
        // User exists, get their ID
        console.log('User already exists, looking up user...');
        const { data: userData, error: userError } = await supabaseAdmin
          .from('profiles')
          .select('id')
          .eq('email', adminData.email)
          .single();

        if (userError || !userData) {
          console.error('Could not find existing user:', userError);
          throw new Error('Benutzer existiert bereits, konnte aber nicht gefunden werden');
        }
        userId = userData.id;
        userExists = true;
        console.log('Using existing user:', userId);
      } else {
        throw signUpError;
      }
    } else {
      if (!signUpData.user) {
        throw new Error('Benutzer konnte nicht erstellt werden');
      }
      userId = signUpData.user.id;
      console.log('Created new user:', userId);
    }

    // Generate unique club slug
    console.log('Generating unique slug for club...');
    let baseSlug = clubData.name
      .toLowerCase()
      .replace(/ä/g, 'ae')
      .replace(/ö/g, 'oe')
      .replace(/ü/g, 'ue')
      .replace(/ß/g, 'ss')
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');

    let slug = baseSlug;
    let slugCounter = 1;
    
    // Check if slug exists and generate unique one if needed
    while (true) {
      console.log('Checking slug availability:', slug);
      const { data: existingClub } = await supabaseAdmin
        .from('clubs')
        .select('id')
        .eq('slug', slug)
        .maybeSingle();
        
      if (!existingClub) {
        console.log('Slug is available:', slug);
        break; // Slug is unique
      }
      
      console.log('Slug already exists, trying next variant...');
      slug = `${baseSlug}-${slugCounter}`;
      slugCounter++;
      
      if (slugCounter > 100) {
        throw new Error('Konnte keinen eindeutigen Club-Slug generieren');
      }
    }
    
    console.log('Final unique slug:', slug);

    // Create club
    console.log('Creating club...');
    const { data: clubInsertData, error: clubError } = await supabaseAdmin
      .from('clubs')
      .insert({
        name: clubData.name,
        description: clubData.description,
        slug,
        custom_domain: clubData.customDomain,
        subdomain: slug, // Always use the generated unique slug
        logo_url: clubData.logoUrl,
        is_active: true,
        settings: {},
      })
      .select()
      .single();

    if (clubError) {
      console.error('Club creation error:', clubError);
      throw new Error(`Club konnte nicht erstellt werden: ${clubError.message}`);
    }

    console.log('Club created successfully:', clubInsertData.id);

    // Create club membership for admin
    console.log('Creating admin membership...');
    const { error: membershipError } = await supabaseAdmin
      .from('club_memberships')
      .insert({
        user_id: userId,
        club_id: clubInsertData.id,
        role: 'admin',
        is_active: true,
      });

    if (membershipError) {
      console.error('Membership creation error:', membershipError);
      throw new Error(`Admin-Mitgliedschaft konnte nicht erstellt werden: ${membershipError.message}`);
    }

    console.log('Admin membership created successfully');

    // Create courts if provided
    if (courtSetup.courts && courtSetup.courts.length > 0) {
      console.log('Creating courts, count:', courtSetup.courts.length);
      const courtsToInsert = courtSetup.courts.map(court => ({
        club_id: clubInsertData.id,
        number: court.number,
        surface_type: court.surfaceType,
        court_group: court.courtGroup,
        locked: false,
      }));

      const { data: courtsData, error: courtsError } = await supabaseAdmin
        .from('courts')
        .insert(courtsToInsert)
        .select();

      if (courtsError) {
        console.error('Courts creation error:', courtsError);
        throw new Error(`Plätze konnten nicht erstellt werden: ${courtsError.message}`);
      }

      console.log('Courts created successfully, count:', courtsData.length);

      // Create court availability if provided
      if (playingTimes.availability && playingTimes.availability.length > 0 && courtsData) {
        console.log('Creating court availability...');
        const availabilityToInsert = [];
        
        // Convert day strings to numbers (0 = Sunday, 1 = Monday, etc.)
        const dayMap: Record<string, number> = {
          'sunday': 0,
          'monday': 1,
          'tuesday': 2,
          'wednesday': 3,
          'thursday': 4,
          'friday': 5,
          'saturday': 6,
        };
        
        for (const availability of playingTimes.availability) {
          for (const court of courtsData) {
            availabilityToInsert.push({
              court_id: court.id,
              start_time: availability.startTime,
              end_time: availability.endTime,
              days_of_week: availability.daysOfWeek.map(day => dayMap[day]).filter(day => day !== undefined),
            });
          }
        }

        const { error: availabilityError } = await supabaseAdmin
          .from('court_availability')
          .insert(availabilityToInsert);

        if (availabilityError) {
          console.error('Availability creation error:', availabilityError);
          // Don't throw here, availability is not critical
          console.log('Court availability creation failed, but continuing...');
        } else {
          console.log('Court availability created successfully');
        }
      }
    }

    // Create user admin role if new user
    if (!userExists) {
      console.log('Creating admin role for new user...');
      const { error: roleError } = await supabaseAdmin
        .from('user_roles')
        .insert({
          user_id: userId,
          role: 'admin',
        });

      if (roleError) {
        console.error('Admin role creation error:', roleError);
        // Don't throw here, role creation is not critical for basic club functionality
        console.log('Admin role creation failed, but continuing...');
      } else {
        console.log('Admin role created successfully');
      }
    }

    console.log('=== Club registration completed successfully ===');

    return new Response(
      JSON.stringify({
        success: true,
        club: clubInsertData,
        message: 'Club erfolgreich registriert',
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    );

  } catch (error: any) {
    console.error('=== ERROR in club registration ===');
    console.error('Error type:', typeof error);
    console.error('Error message:', error.message);
    console.error('Error stack:', error.stack);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message || 'Unbekannter Fehler bei der Club-Registrierung',
        details: error.stack || 'No stack trace available',
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    );
  }
});