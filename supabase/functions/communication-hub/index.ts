import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface CommunicationRequest {
  type: 'send_campaign' | 'get_analytics' | 'create_smart_group' | 'send_push';
  data: any;
}

const handler = async (req: Request): Promise<Response> => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    console.log("🎯 Communication Hub called");

    // Get Authorization header to forward to other functions
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('Authentifizierung erforderlich. Bitte melden Sie sich an.');
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    const { type, data }: CommunicationRequest = await req.json();
    console.log(`📋 Processing request type: ${type}`);

    switch (type) {
      case 'send_campaign':
        return await handleSendCampaign(supabase, data, authHeader);

      case 'get_analytics':
        return await handleGetAnalytics(supabase, data);

      case 'create_smart_group':
        return await handleCreateSmartGroup(supabase, data);

      case 'send_push':
        return await handleSendPush(supabase, data);

      default:
        throw new Error(`Unknown request type: ${type}`);
    }

  } catch (error: any) {
    console.error("💥 Error in communication hub:", error);
    return new Response(
      JSON.stringify({ 
        error: error.message,
        success: false 
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json", ...corsHeaders },
      }
    );
  }
};

async function handleSendCampaign(supabase: any, data: any, authHeader: string) {
  const { campaignId, clubId } = data;
  console.log(`📧 Sending campaign ${campaignId} for club ${clubId}`);

  // Validate required parameters
  if (!campaignId || !clubId) {
    throw new Error('Campaign ID and Club ID are required');
  }

  // Get campaign details with club validation
  const { data: campaign, error: campaignError } = await supabase
    .from('communication_campaigns')
    .select('*')
    .eq('id', campaignId)
    .eq('club_id', clubId) // Ensure campaign belongs to the specified club
    .single();

  if (campaignError) {
    throw new Error(`Campaign not found or access denied: ${campaignError.message}`);
  }

  // Double-check club ownership for security
  if (campaign.club_id !== clubId) {
    throw new Error(`Access denied: Campaign ${campaignId} does not belong to club ${clubId}`);
  }

  console.log(`✅ Campaign validated for club ${clubId}: ${campaign.title}`);

  // Get recipients based on target groups (already club-scoped)
  const recipients = await getRecipientsForGroups(supabase, campaign.target_groups, clubId);

  // Process each channel
  const results = [];
  
  for (const channel of campaign.channels) {
    switch (channel) {
      case 'email':
        // Call consolidated email service for campaigns
        const emailResult = await supabase.functions.invoke('club-email-service', {
          headers: {
            'Authorization': authHeader // Forward auth header
          },
          body: {
            clubId,
            to: recipients.map((r: any) => r.email),
            subject: campaign.title,
            type: 'campaign',
            campaignId,
            batchSize: 10,
            delayBetweenBatches: 1000,
            trackAnalytics: true,
            templateData: {
              content: campaign.message_content,
              clubName: campaign.title // Will be fetched from DB in template
            }
          }
        });
        results.push({ channel: 'email', result: emailResult });
        break;

      case 'push':
        // Send push notifications directly to dashboard
        await sendPushNotifications(supabase, recipients, campaign, clubId);
        results.push({ channel: 'push', success: true });
        break;

      case 'dashboard':
        // Create dashboard message
        await createDashboardMessage(supabase, campaign, clubId);
        results.push({ channel: 'dashboard', success: true });
        break;
    }
  }

  // Update campaign status
  await supabase
    .from('communication_campaigns')
    .update({
      status: 'sent',
      sent_at: new Date().toISOString()
    })
    .eq('id', campaignId);

  return new Response(JSON.stringify({
    success: true,
    message: 'Campaign sent successfully',
    results
  }), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' },
  });
}

async function handleGetAnalytics(supabase: any, data: any) {
  const { clubId, timeframe } = data;
  console.log(`📊 Getting analytics for club ${clubId}`);

  const startDate = new Date();
  switch (timeframe) {
    case 'week':
      startDate.setDate(startDate.getDate() - 7);
      break;
    case 'month':
      startDate.setMonth(startDate.getMonth() - 1);
      break;
    case 'year':
      startDate.setFullYear(startDate.getFullYear() - 1);
      break;
    default:
      startDate.setDate(startDate.getDate() - 30);
  }

  const { data: analytics, error } = await supabase
    .from('communication_analytics')
    .select('*')
    .eq('club_id', clubId)
    .gte('created_at', startDate.toISOString());

  if (error) {
    throw new Error(`Analytics error: ${error.message}`);
  }

  // Process analytics data
  const processed = processAnalyticsData(analytics);

  return new Response(JSON.stringify({
    success: true,
    analytics: processed
  }), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' },
  });
}

async function handleCreateSmartGroup(supabase: any, data: any) {
  const { name, criteria, clubId } = data;
  console.log(`👥 Creating smart group: ${name}`);

  // Create the group
  const { data: group, error } = await supabase
    .from('communication_groups')
    .insert({
      name,
      criteria,
      club_id: clubId,
      is_dynamic: true
    })
    .select()
    .single();

  if (error) {
    throw new Error(`Group creation error: ${error.message}`);
  }

  return new Response(JSON.stringify({
    success: true,
    group
  }), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' },
  });
}

async function handleSendPush(supabase: any, data: any) {
  const { recipients, title, content, clubId } = data;
  console.log(`🔔 Sending push notifications to ${recipients.length} recipients`);

  await sendPushNotifications(supabase, recipients, { title, message_content: content }, clubId);

  return new Response(JSON.stringify({
    success: true,
    message: 'Push notifications sent'
  }), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' },
  });
}

async function getRecipientsForGroups(supabase: any, targetGroups: string[], clubId: string) {
  console.log(`🎯 Getting recipients for groups: ${targetGroups.join(', ')} in club ${clubId}`);

  // Fetch all active members for the club with additional fields needed for filtering
  const { data: allMembers, error } = await supabase
    .from('club_memberships')
    .select(`
      user_id,
      email,
      first_name,
      last_name,
      membership_type,
      birth_date,
      role,
      joined_at
    `)
    .eq('club_id', clubId)
    .eq('is_active', true)
    .not('email', 'is', null); // Only include members with email addresses

  if (error) {
    throw new Error(`Recipients error: ${error.message}`);
  }

  if (!allMembers || allMembers.length === 0) {
    console.log(`⚠️ No active members found for club ${clubId}`);
    return [];
  }

  // If "all_members" is selected, return all members
  if (targetGroups.includes('all_members')) {
    console.log(`📧 Returning all ${allMembers.length} members for club ${clubId}`);
    return allMembers;
  }

  // Filter members based on target group criteria
  const filteredMembers = allMembers.filter(member => {
    return targetGroups.some(groupId => {
      switch (groupId) {
        case 'active_players': {
          const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
          const joinedAt = new Date(member.joined_at);
          return joinedAt < thirtyDaysAgo && member.role !== 'board';
        }

        case 'tournament_players':
          return member.membership_type?.toLowerCase().includes('tournament') ||
                 member.membership_type?.toLowerCase().includes('turnier') ||
                 member.role === 'tournament_player';

        case 'junior_members': {
          if (!member.birth_date) return false;
          const birthDate = new Date(member.birth_date);
          const now = new Date();
          const age = now.getFullYear() - birthDate.getFullYear();
          const monthDiff = now.getMonth() - birthDate.getMonth();
          if (monthDiff < 0 || (monthDiff === 0 && now.getDate() < birthDate.getDate())) {
            return age - 1 < 18;
          }
          return age < 18;
        }

        case 'board_members':
          return member.role === 'board' ||
                 member.role === 'admin' ||
                 member.membership_type?.toLowerCase().includes('vorstand') ||
                 member.membership_type?.toLowerCase().includes('board');

        case 'new_members': {
          const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
          const joinedAt = new Date(member.joined_at);
          return joinedAt >= thirtyDaysAgo;
        }

        case 'trainers':
          return member.role === 'trainer' ||
                 member.membership_type?.toLowerCase().includes('trainer') ||
                 member.membership_type?.toLowerCase().includes('coach');

        default:
          console.log(`⚠️ Unknown target group: ${groupId}`);
          return false;
      }
    });
  });

  console.log(`📧 Filtered ${filteredMembers.length} recipients from ${allMembers.length} total members for groups: ${targetGroups.join(', ')}`);
  return filteredMembers;
}

async function sendPushNotifications(supabase: any, recipients: any[], campaign: any, clubId: string) {
  console.log(`🔔 Creating push notifications for ${recipients.length} recipients`);

  // Create dashboard messages for each recipient
  const messages = recipients.map(recipient => ({
    club_id: clubId,
    recipient_type: 'individual',
    recipient_ids: [recipient.user_id],
    title: campaign.title,
    content: campaign.message_content,
    channel: 'push',
    type: 'notification',
    priority: 'normal',
    expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days
  }));

  const { error } = await supabase
    .from('communication_messages')
    .insert(messages);

  if (error) {
    throw new Error(`Push notification error: ${error.message}`);
  }
}

async function createDashboardMessage(supabase: any, campaign: any, clubId: string) {
  console.log(`📰 Creating dashboard message`);

  const { error } = await supabase
    .from('communication_messages')
    .insert({
      club_id: clubId,
      recipient_type: 'all',
      title: campaign.title,
      content: campaign.message_content,
      channel: 'dashboard',
      type: 'news',
      priority: 'normal'
    });

  if (error) {
    throw new Error(`Dashboard message error: ${error.message}`);
  }
}

function processAnalyticsData(analytics: any[]) {
  const stats = {
    total_campaigns: 0,
    total_emails_sent: 0,
    total_push_sent: 0,
    avg_engagement: 0,
    by_channel: {} as any
  };

  analytics.forEach(entry => {
    if (entry.event_type === 'campaign_sent') {
      stats.total_campaigns++;
      
      if (entry.channel === 'email') {
        stats.total_emails_sent += entry.metadata?.recipients_count || 0;
      } else if (entry.channel === 'push') {
        stats.total_push_sent += entry.metadata?.recipients_count || 0;
      }
      
      if (!stats.by_channel[entry.channel]) {
        stats.by_channel[entry.channel] = { sent: 0, opened: 0, clicked: 0 };
      }
      stats.by_channel[entry.channel].sent += entry.metadata?.recipients_count || 0;
    }
  });

  return stats;
}

serve(handler);