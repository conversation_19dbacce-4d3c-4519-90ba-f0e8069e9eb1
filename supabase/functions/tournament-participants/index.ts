import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
  const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
  const supabase = createClient(supabaseUrl, supabaseKey);

  try {
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('No authorization header');
    }

    // Verify JWT and get user
    const { data: { user }, error: authError } = await supabase.auth.getUser(
      authHeader.replace('Bearer ', '')
    );
    if (authError || !user) {
      throw new Error('Unauthorized');
    }

    const url = new URL(req.url);
    const pathParts = url.pathname.split('/');
    const tournamentId = pathParts[pathParts.length - 2];

    if (req.method === 'GET') {
      // Get all participants for tournament
      const { data: participants, error } = await supabase
        .from('tournament_participants')
        .select('*')
        .eq('tournament_id', tournamentId)
        .order('seed_number', { ascending: true });

      if (error) throw error;

      return new Response(JSON.stringify(participants), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    if (req.method === 'POST') {
      // Register participant
      const { user_id, user_name, user_email, is_guest = false, seed_number } = await req.json();

      // Check if tournament allows participants
      const { data: tournament, error: tournamentError } = await supabase
        .from('tournaments')
        .select('max_participants, allow_guests, status')
        .eq('id', tournamentId)
        .single();

      if (tournamentError) throw tournamentError;

      if (tournament.status !== 'registration') {
        throw new Error('Anmeldung für dieses Turnier ist geschlossen');
      }

      if (is_guest && !tournament.allow_guests) {
        throw new Error('Gäste sind für dieses Turnier nicht erlaubt');
      }

      // Check participant limit
      if (tournament.max_participants) {
        const { count } = await supabase
          .from('tournament_participants')
          .select('*', { count: 'exact', head: true })
          .eq('tournament_id', tournamentId);

        if (count && count >= tournament.max_participants) {
          throw new Error('Maximale Teilnehmerzahl erreicht');
        }
      }

      // Auto-assign seed number if not provided
      let finalSeedNumber = seed_number;
      if (!finalSeedNumber) {
        const { data: lastParticipant } = await supabase
          .from('tournament_participants')
          .select('seed_number')
          .eq('tournament_id', tournamentId)
          .order('seed_number', { ascending: false })
          .limit(1)
          .single();

        finalSeedNumber = (lastParticipant?.seed_number || 0) + 1;
      }

      const { data: participant, error } = await supabase
        .from('tournament_participants')
        .insert({
          tournament_id: tournamentId,
          user_id: is_guest ? null : user_id,
          user_name,
          user_email,
          is_guest,
          seed_number: finalSeedNumber,
          status: 'registered',
          registered_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      return new Response(JSON.stringify(participant), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    if (req.method === 'PUT') {
      // Update participant (e.g., confirm registration, update seed)
      const participantId = pathParts[pathParts.length - 1];
      const updates = await req.json();

      const { data: participant, error } = await supabase
        .from('tournament_participants')
        .update(updates)
        .eq('id', participantId)
        .select()
        .single();

      if (error) throw error;

      return new Response(JSON.stringify(participant), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    if (req.method === 'DELETE') {
      // Remove participant
      const participantId = pathParts[pathParts.length - 1];

      const { error } = await supabase
        .from('tournament_participants')
        .delete()
        .eq('id', participantId);

      if (error) throw error;

      return new Response(JSON.stringify({ success: true }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    return new Response('Method not allowed', { status: 405, headers: corsHeaders });

  } catch (error) {
    console.error('Tournament participants error:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});