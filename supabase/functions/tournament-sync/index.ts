import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  if (req.method !== 'POST') {
    return new Response(JSON.stringify({ 
      success: false, 
      error: `Method ${req.method} not allowed. Only POST requests are supported.` 
    }), {
      status: 405,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }

  const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
  const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
  const supabase = createClient(supabaseUrl, supabaseKey);

  try {
    console.log('Tournament sync function called');
    
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      console.error('No authorization header provided');
      throw new Error('No authorization header');
    }

    // Verify JWT and get user
    const { data: { user }, error: authError } = await supabase.auth.getUser(
      authHeader.replace('Bearer ', '')
    );
    if (authError || !user) {
      console.error('Authentication failed:', authError);
      throw new Error('Unauthorized');
    }

    let requestBody;
    try {
      requestBody = await req.json();
    } catch (parseError) {
      console.error('Failed to parse request body:', parseError);
      throw new Error('Invalid JSON in request body');
    }

    const { action, match_id, tournament_id } = requestBody;
    console.log('Received request:', { action, match_id, tournament_id, user_id: user.id });

    if (!action) {
      throw new Error('Action is required');
    }

    if (action === 'sync_match') {
      if (!match_id) {
        throw new Error('Match ID is required for sync action');
      }

      // Get match details
      const { data: match, error: matchError } = await supabase
        .from('tournament_matches')
        .select(`
          *,
          tournament:tournaments(id, name, club_id),
          participant1:tournament_participants!participant1_id(id, guest_name, is_guest, user_id),
          participant2:tournament_participants!participant2_id(id, guest_name, is_guest, user_id)
        `)
        .eq('id', match_id)
        .single();

      if (matchError || !match) {
        console.error('Match not found:', matchError);
        throw new Error('Match not found');
      }

      // Check if match has required scheduling info
      if (!match.scheduled_date || !match.scheduled_time || !match.court_id) {
        throw new Error('Match must be scheduled (date, time, court) before syncing to booking system');
      }

      // Check if booking already exists
      if (match.booking_id) {
        throw new Error('Match is already synced to booking system');
      }

      // Check user permissions
      const { data: membership, error: membershipError } = await supabase
        .from('club_memberships')
        .select('role, club_id')
        .eq('user_id', user.id)
        .eq('club_id', match.tournament.club_id)
        .eq('is_active', true)
        .in('role', ['admin', 'super_admin']);

      if (membershipError || !membership || membership.length === 0) {
        console.error('Permission check failed:', membershipError);
        throw new Error('Keine Berechtigung zum Synchronisieren von Spielen für diesen Club');
      }

      // Calculate end time
      const matchDuration = match.estimated_duration_minutes || 90;
      const [hours, minutes] = match.scheduled_time.split(':').map(Number);
      const startMinutes = hours * 60 + minutes;
      const endMinutes = startMinutes + matchDuration;
      const endHour = Math.floor(endMinutes / 60);
      const endMin = endMinutes % 60;
      const endTime = `${endHour.toString().padStart(2, '0')}:${endMin.toString().padStart(2, '0')}`;

      // Generate player names
      const player1Name = match.participant1?.is_guest 
        ? (match.participant1.guest_name || 'Gast 1')
        : `Teilnehmer ${match.participant1?.id?.slice(0, 8) || '1'}`;
      
      const player2Name = match.participant2?.is_guest 
        ? (match.participant2.guest_name || 'Gast 2')
        : `Teilnehmer ${match.participant2?.id?.slice(0, 8) || '2'}`;

      // Check for booking conflicts
      const { data: existingBookings, error: conflictError } = await supabase
        .from('bookings')
        .select('id, player_name, start_time, end_time')
        .eq('court_id', match.court_id)
        .eq('booking_date', match.scheduled_date)
        .neq('start_time', endTime)
        .neq('end_time', match.scheduled_time);

      if (conflictError) {
        console.error('Conflict check error:', conflictError);
        throw new Error('Fehler beim Prüfen von Buchungskonflikten');
      }

      // Check for time overlaps
      if (existingBookings && existingBookings.length > 0) {
        for (const booking of existingBookings) {
          const bookingStart = booking.start_time.split(':').map(Number);
          const bookingEnd = booking.end_time.split(':').map(Number);
          const bookingStartMin = bookingStart[0] * 60 + bookingStart[1];
          const bookingEndMin = bookingEnd[0] * 60 + bookingEnd[1];
          
          if (startMinutes < bookingEndMin && endMinutes > bookingStartMin) {
            throw new Error(`Buchungskonflikt mit existierender Buchung: ${booking.player_name} (${booking.start_time} - ${booking.end_time})`);
          }
        }
      }

      // Create booking entry
      const { data: booking, error: bookingError } = await supabase
        .from('bookings')
        .insert({
          club_id: match.tournament.club_id,
          court_id: match.court_id,
          booking_date: match.scheduled_date,
          start_time: match.scheduled_time,
          end_time: endTime,
          player_name: `${player1Name} vs ${player2Name}`,
          partner_name: `Turnier: ${match.tournament.name}`,
          created_by: user.id,
          acting_account_id: null,
          booked_for_account_id: null
        })
        .select()
        .single();

      if (bookingError) {
        console.error('Booking creation error:', bookingError);
        throw new Error(`Fehler beim Erstellen der Buchung: ${bookingError.message}`);
      }

      console.log('Booking created successfully:', booking.id);

      // Update match with booking_id
      const { error: updateError } = await supabase
        .from('tournament_matches')
        .update({ 
          booking_id: booking.id,
          updated_at: new Date().toISOString()
        })
        .eq('id', match_id);

      if (updateError) {
        console.error('Match update error:', updateError);
        // Try to rollback booking creation
        await supabase.from('bookings').delete().eq('id', booking.id);
        throw new Error(`Fehler beim Aktualisieren des Spiels: ${updateError.message}`);
      }

      return new Response(JSON.stringify({
        success: true,
        message: 'Match erfolgreich mit Buchungssystem synchronisiert',
        booking_id: booking.id,
        booking_details: {
          court_id: match.court_id,
          date: match.scheduled_date,
          time: `${match.scheduled_time} - ${endTime}`,
          duration: matchDuration,
          players: `${player1Name} vs ${player2Name}`
        }
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });

    } else if (action === 'unsync_match') {
      if (!match_id) {
        throw new Error('Match ID is required for unsync action');
      }

      // Get match details
      const { data: match, error: matchError } = await supabase
        .from('tournament_matches')
        .select('booking_id, tournament:tournaments(club_id)')
        .eq('id', match_id)
        .single();

      if (matchError || !match) {
        console.error('Match not found:', matchError);
        throw new Error('Match not found');
      }

      if (!match.booking_id) {
        throw new Error('Match is not synced to booking system');
      }

      // Check permissions
      const { data: membership, error: membershipError } = await supabase
        .from('club_memberships')
        .select('role')
        .eq('user_id', user.id)
        .eq('club_id', match.tournament.club_id)
        .eq('is_active', true)
        .in('role', ['admin', 'super_admin']);

      if (membershipError || !membership || membership.length === 0) {
        throw new Error('Keine Berechtigung zum Aufheben der Synchronisation');
      }

      // Delete booking
      const { error: deleteError } = await supabase
        .from('bookings')
        .delete()
        .eq('id', match.booking_id);

      if (deleteError) {
        console.error('Booking deletion error:', deleteError);
        throw new Error(`Fehler beim Löschen der Buchung: ${deleteError.message}`);
      }

      // Update match to remove booking_id
      const { error: updateError } = await supabase
        .from('tournament_matches')
        .update({ 
          booking_id: null,
          updated_at: new Date().toISOString()
        })
        .eq('id', match_id);

      if (updateError) {
        console.error('Match update error:', updateError);
        throw new Error(`Fehler beim Aktualisieren des Spiels: ${updateError.message}`);
      }

      return new Response(JSON.stringify({
        success: true,
        message: 'Synchronisation mit Buchungssystem aufgehoben'
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });

    } else {
      throw new Error(`Unknown action: ${action}`);
    }

  } catch (error) {
    console.error('Error in tournament sync function:', error);
    return new Response(JSON.stringify({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});