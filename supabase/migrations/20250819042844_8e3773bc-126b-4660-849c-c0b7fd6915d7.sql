-- Create a test SuperAdmin user (temporary for testing)
-- This is just for demonstration - in production this would be done through proper admin interface

-- First, let's check if we can create a function to grant SuperAdmin permissions
CREATE OR REPLACE FUNCTION public.grant_super_admin(_user_email text)
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
AS $$
  INSERT INTO public.meta_admin_permissions (user_id, role, granted_by)
  SELECT 
    u.id,
    'SUPER_ADMIN'::meta_admin_role,
    u.id  -- Self-granted for initial setup
  FROM auth.users u
  WHERE u.email = _user_email
  AND NOT EXISTS (
    SELECT 1 FROM public.meta_admin_permissions 
    WHERE user_id = u.id AND role = 'SUPER_ADMIN'::meta_admin_role
  )
  RETURNING true;
$$;