
-- Tabelle für Buchungsregeln erstellen
CREATE TABLE IF NOT EXISTS public.booking_rules (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  club_id UUID NOT NULL REFERENCES public.clubs(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  rule_type TEXT NOT NULL,
  conditions JSONB NOT NULL DEFAULT '{}',
  actions JSONB NOT NULL DEFAULT '{}',
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- RLS Policies für booking_rules
ALTER TABLE public.booking_rules ENABLE ROW LEVEL SECURITY;

-- Club-Admins können ihre Club-Regeln verwalten
CREATE POLICY "Club admins can manage their club booking rules"
ON public.booking_rules
FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid()
      AND cm.club_id = booking_rules.club_id
      AND cm.role IN ('admin', 'super_admin')
      AND cm.is_active = true
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid()
      AND cm.club_id = booking_rules.club_id
      AND cm.role IN ('admin', 'super_admin')
      AND cm.is_active = true
  )
);

-- Club-Mitglieder können aktive Regeln ihres Clubs einsehen
CREATE POLICY "Club members can view active booking rules"
ON public.booking_rules
FOR SELECT
USING (
  is_active = true
  AND EXISTS (
    SELECT 1 FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid()
      AND cm.club_id = booking_rules.club_id
      AND cm.is_active = true
  )
);

-- Meta-Admins können alle Regeln verwalten
CREATE POLICY "Meta admins can manage all booking rules"
ON public.booking_rules
FOR ALL
USING (is_meta_admin(auth.uid()))
WITH CHECK (is_meta_admin(auth.uid()));

-- Trigger für updated_at
CREATE TRIGGER update_booking_rules_updated_at
  BEFORE UPDATE ON public.booking_rules
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

-- Index für bessere Performance
CREATE INDEX idx_booking_rules_club_id ON public.booking_rules(club_id);
CREATE INDEX idx_booking_rules_active ON public.booking_rules(is_active);
