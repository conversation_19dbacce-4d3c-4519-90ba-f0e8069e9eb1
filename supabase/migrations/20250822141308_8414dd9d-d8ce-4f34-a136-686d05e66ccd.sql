-- Create tournament_format_definitions table for pre-defined format templates
CREATE TABLE IF NOT EXISTS public.tournament_format_definitions (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  format_type TEXT NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  default_config JSONB NOT NULL DEFAULT '{}',
  is_active BOOLEAN NOT NULL DEFAULT true,
  category TEXT NOT NULL DEFAULT 'standard',
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Add RLS Policies
ALTER TABLE public.tournament_format_definitions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Everyone can view active format definitions" 
ON public.tournament_format_definitions 
FOR SELECT 
USING (is_active = true);

CREATE POLICY "Meta admins can manage format definitions" 
ON public.tournament_format_definitions 
FOR ALL 
USING (is_meta_admin(auth.uid()));

-- Insert standard formats only if they don't exist
INSERT INTO public.tournament_format_definitions (format_type, name, description, default_config, category) 
SELECT * FROM (VALUES
('knockout', 'K.o.-System (Single Elimination)', 'Klassisches Ausscheidungsturnier - wer verliert, ist raus', '{"seeding": "random", "consolation_plate": false, "place_playoffs": false}', 'standard'),
('round_robin', 'Round Robin (Jeder gegen jeden)', 'Jeder spielt gegen jeden - Tabellenmodus', '{"groups": 1, "group_size": null, "tiebreaker": ["head_to_head", "set_ratio", "game_ratio"]}', 'standard'),
('groups_to_ko', 'Gruppen → K.o.', 'Vorrunden in Gruppen, dann K.o.-Phase', '{"groups": 4, "group_size": 4, "advance_per_group": 2, "ko_seeding": "group_position"}', 'standard'),
('double_elimination', 'Doppel-K.o. (Double Elimination)', 'Zwei Niederlagen nötig für Ausscheiden', '{"bracket_reset": true, "consolation": false}', 'standard'),
('swiss', 'Schweizer System', 'Paarung nach aktueller Bilanz', '{"rounds": 5, "cut_to_ko": false, "cut_after_round": null}', 'standard')
) AS t(format_type, name, description, default_config, category) 
WHERE NOT EXISTS (SELECT 1 FROM public.tournament_format_definitions WHERE format_type = t.format_type);

-- Update trigger für tournament_format_definitions
CREATE TRIGGER update_tournament_format_definitions_updated_at
BEFORE UPDATE ON public.tournament_format_definitions
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();