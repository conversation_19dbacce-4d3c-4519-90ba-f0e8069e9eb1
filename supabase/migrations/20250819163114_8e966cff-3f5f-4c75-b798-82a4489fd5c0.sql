-- Create team_members table with proper structure
CREATE TABLE IF NOT EXISTS team_members (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  team_id uuid NOT NULL REFERENCES teams(id) ON DELETE CASCADE,
  user_id uuid NOT NULL, -- This references club_memberships.user_id
  created_at timestamp with time zone DEFAULT now(),
  UNIQUE(team_id, user_id) -- Prevent duplicate assignments
);

-- Enable RLS on team_members
ALTER TABLE team_members ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for team_members
DROP POLICY IF EXISTS "Club members can view team members" ON team_members;
CREATE POLICY "Club members can view team members" 
ON team_members FOR SELECT 
USING (
  EXISTS (
    SELECT 1 FROM teams t
    JOIN club_memberships cm ON cm.club_id IN (
      SELECT club_id FROM club_memberships WHERE user_id = auth.uid() AND is_active = true
    )
    WHERE t.id = team_members.team_id
  )
);

DROP POLICY IF EXISTS "Club admins can manage team members" ON team_members;
CREATE POLICY "Club admins can manage team members" 
ON team_members FOR ALL 
USING (
  EXISTS (
    SELECT 1 FROM teams t
    JOIN club_memberships cm ON cm.club_id = t.club_id
    WHERE t.id = team_members.team_id 
    AND cm.user_id = auth.uid() 
    AND cm.role IN ('admin', 'trainer') 
    AND cm.is_active = true
  )
);