-- Fix critical security vulnerability in accounts table
-- Remove the dangerous "Allow all operations on accounts" policy that makes the table public

-- First, drop the overly permissive policy
DROP POLICY IF EXISTS "Allow all operations on accounts" ON public.accounts;

-- The remaining policies provide proper access control:
-- 1. "Account owners select" - users can view their own accounts
-- 2. "Account owners update" - users can update their own accounts  
-- 3. "Guardians select dependents" - guardians can view dependent accounts
-- 4. "Guardians update dependents" - guardians can update dependent accounts

-- Add a policy for club admins to manage accounts within their clubs
CREATE POLICY "Club admins can view club accounts"
ON public.accounts
FOR SELECT
USING (
  EXISTS (
    SELECT 1 
    FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid()
      AND cm.club_id = accounts.club_id
      AND cm.role IN ('admin', 'super_admin')
      AND cm.is_active = true
  )
);

-- Add a policy for club admins to update accounts within their clubs
CREATE POLICY "Club admins can update club accounts"
ON public.accounts
FOR UPDATE
USING (
  EXISTS (
    SELECT 1 
    FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid()
      AND cm.club_id = accounts.club_id
      AND cm.role IN ('admin', 'super_admin')
      AND cm.is_active = true
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 
    FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid()
      AND cm.club_id = accounts.club_id
      AND cm.role IN ('admin', 'super_admin')
      AND cm.is_active = true
  )
);

-- Add a policy for club admins to create accounts within their clubs
CREATE POLICY "Club admins can create club accounts"
ON public.accounts
FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 
    FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid()
      AND cm.club_id = accounts.club_id
      AND cm.role IN ('admin', 'super_admin')
      AND cm.is_active = true
  )
);

-- Add meta admin policy for system administration
CREATE POLICY "Meta admins can manage all accounts"
ON public.accounts
FOR ALL
USING (is_meta_admin(auth.uid()))
WITH CHECK (is_meta_admin(auth.uid()));