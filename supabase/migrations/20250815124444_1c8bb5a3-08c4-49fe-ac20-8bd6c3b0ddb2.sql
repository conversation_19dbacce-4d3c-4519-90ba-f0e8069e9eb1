-- Phase 1: Unify Role Systems Migration (Fixed)
-- This migration consolidates the parallel role systems into a single meta_admin_permissions system

-- Step 1: Migrate existing super_admin users to meta_admin_permissions
-- Insert super_admin users into meta_admin_permissions if they don't already exist
INSERT INTO public.meta_admin_permissions (user_id, role, granted_by, granted_at)
SELECT DISTINCT 
  ur.user_id,
  'SUPER_ADMIN'::meta_admin_role,
  ur.user_id, -- Self-granted for migration
  COALESCE(ur.assigned_at, now())
FROM public.user_roles ur
WHERE ur.role = 'super_admin'
  AND NOT EXISTS (
    SELECT 1 
    FROM public.meta_admin_permissions map 
    WHERE map.user_id = ur.user_id 
      AND map.role = 'SUPER_ADMIN'
  );

-- Step 2: Remove super_admin from user_roles table (will be handled by meta_admin_permissions)
DELETE FROM public.user_roles 
WHERE role = 'super_admin';

-- Step 3: Remove super_admin from role_permissions table if it exists
DELETE FROM public.role_permissions 
WHERE role = 'super_admin';

-- Step 4: Create new app_role enum without super_admin
CREATE TYPE public.app_role_new AS ENUM ('admin', 'trainer', 'mitglied', 'guest');

-- Step 5: Update all tables to use the new enum (with CASCADE to handle dependencies)
-- First update the function to use new signature temporarily
CREATE OR REPLACE FUNCTION public.has_role(_user_id uuid, _role text)
RETURNS boolean
LANGUAGE sql
STABLE SECURITY DEFINER
SET search_path TO 'public'
AS $$
  SELECT EXISTS (
    SELECT 1
    FROM public.user_roles
    WHERE user_id = _user_id
      AND role::text = _role
  );
$$;

-- Update tables to use new enum
ALTER TABLE public.user_roles 
  ALTER COLUMN role TYPE public.app_role_new 
  USING role::text::public.app_role_new;

ALTER TABLE public.role_permissions 
  ALTER COLUMN role TYPE public.app_role_new 
  USING role::text::public.app_role_new;

-- Step 6: Update function to use proper enum again
DROP FUNCTION public.has_role(uuid, text);
CREATE OR REPLACE FUNCTION public.has_role(_user_id uuid, _role app_role_new)
RETURNS boolean
LANGUAGE sql
STABLE SECURITY DEFINER
SET search_path TO 'public'
AS $$
  SELECT EXISTS (
    SELECT 1
    FROM public.user_roles
    WHERE user_id = _user_id
      AND role = _role
  );
$$;

-- Update other functions
DROP FUNCTION IF EXISTS public.get_user_roles(uuid);
CREATE OR REPLACE FUNCTION public.get_user_roles(_user_id uuid)
RETURNS TABLE(role app_role_new)
LANGUAGE sql
STABLE SECURITY DEFINER
SET search_path TO 'public'
AS $$
  SELECT ur.role
  FROM public.user_roles ur
  WHERE ur.user_id = _user_id;
$$;

-- Step 7: Drop old enum and rename new one
DROP TYPE public.app_role CASCADE;
ALTER TYPE public.app_role_new RENAME TO app_role;

-- Step 8: Recreate has_role function with correct signature
CREATE OR REPLACE FUNCTION public.has_role(_user_id uuid, _role app_role)
RETURNS boolean
LANGUAGE sql
STABLE SECURITY DEFINER
SET search_path TO 'public'
AS $$
  SELECT EXISTS (
    SELECT 1
    FROM public.user_roles
    WHERE user_id = _user_id
      AND role = _role
  );
$$;

-- Step 9: Add helpful function to check if user has any admin role (meta or regular)
CREATE OR REPLACE FUNCTION public.has_any_admin_role(_user_id uuid)
RETURNS boolean
LANGUAGE sql
STABLE SECURITY DEFINER
SET search_path TO 'public'
AS $$
  -- Check for regular admin role OR any meta-admin role
  SELECT (
    EXISTS (
      SELECT 1 FROM public.user_roles 
      WHERE user_id = _user_id AND role = 'admin'
    )
    OR
    EXISTS (
      SELECT 1 FROM public.meta_admin_permissions 
      WHERE user_id = _user_id 
        AND (expires_at IS NULL OR expires_at > now())
    )
  );
$$;