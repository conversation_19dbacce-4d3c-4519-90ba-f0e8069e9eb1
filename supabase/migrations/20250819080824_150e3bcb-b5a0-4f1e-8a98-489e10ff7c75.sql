-- Erweitere club_memberships um detaillierte Mitgliederdaten
ALTER TABLE public.club_memberships 
ADD COLUMN IF NOT EXISTS first_name text,
ADD COLUMN IF NOT EXISTS last_name text,
ADD COLUMN IF NOT EXISTS email text,
ADD COLUMN IF NOT EXISTS phone text,
ADD COLUMN IF NOT EXISTS address_street text,
ADD COLUMN IF NOT EXISTS address_postal_code text,
ADD COLUMN IF NOT EXISTS address_city text,
ADD COLUMN IF NOT EXISTS birth_date date,
ADD COLUMN IF NOT EXISTS membership_type text,
ADD COLUMN IF NOT EXISTS account_type text DEFAULT 'member',
ADD COLUMN IF NOT EXISTS updated_at timestamp with time zone DEFAULT now();

-- Mi<PERSON><PERSON>e Daten aus accounts in club_memberships (wo User-ID übereinstimmt)
UPDATE public.club_memberships 
SET 
  first_name = a.first_name,
  last_name = a.last_name,
  email = a.email,
  phone = a.phone,
  address_street = a.address_street,
  address_postal_code = a.address_postal_code,
  address_city = a.address_city,
  birth_date = a.birth_date,
  membership_type = a.membership_type::text,
  account_type = CASE 
    WHEN club_memberships.role = 'admin' THEN 'Admin'
    WHEN club_memberships.role = 'member' THEN 'Member'
    ELSE 'Member'
  END
FROM public.accounts a
WHERE a.user_id = club_memberships.user_id 
  AND a.club_id = club_memberships.club_id;

-- Erstelle einen automatischen Update-Trigger für updated_at
CREATE OR REPLACE FUNCTION public.update_club_memberships_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_club_memberships_updated_at
  BEFORE UPDATE ON public.club_memberships
  FOR EACH ROW
  EXECUTE FUNCTION public.update_club_memberships_updated_at();

-- Erstelle eine View für bessere Abwärtskompatibilität mit bestehenden Queries
CREATE OR REPLACE VIEW public.unified_members AS
SELECT 
  cm.id,
  cm.user_id,
  cm.club_id,
  cm.role,
  cm.is_active,
  cm.joined_at,
  cm.first_name,
  cm.last_name,
  cm.email,
  cm.phone,
  cm.address_street,
  cm.address_postal_code,
  cm.address_city,
  cm.birth_date,
  cm.membership_type,
  cm.account_type,
  cm.updated_at
FROM public.club_memberships cm
WHERE cm.first_name IS NOT NULL;

-- RLS Policy für die neue erweiterte Struktur
DROP POLICY IF EXISTS "Users can view their own memberships" ON public.club_memberships;
DROP POLICY IF EXISTS "Club admins can manage club members" ON public.club_memberships;
DROP POLICY IF EXISTS "Meta admins can view all memberships" ON public.club_memberships;

CREATE POLICY "Users can view their own memberships" 
ON public.club_memberships 
FOR SELECT 
USING (user_id = auth.uid());

CREATE POLICY "Club admins can manage club members" 
ON public.club_memberships 
FOR ALL 
USING (
  EXISTS (
    SELECT 1 FROM public.club_memberships admin_cm 
    WHERE admin_cm.club_id = club_memberships.club_id 
      AND admin_cm.user_id = auth.uid() 
      AND admin_cm.role = 'admin'
      AND admin_cm.is_active = true
  )
);

CREATE POLICY "Meta admins can manage all memberships" 
ON public.club_memberships 
FOR ALL 
USING (is_meta_admin(auth.uid()));

CREATE POLICY "Users can update their own membership data" 
ON public.club_memberships 
FOR UPDATE 
USING (user_id = auth.uid())
WITH CHECK (user_id = auth.uid());