-- First, let's check if team_members table exists and what columns it has
-- If it doesn't exist, create it with proper structure

-- Create team_members table if it doesn't exist
CREATE TABLE IF NOT EXISTS team_members (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  team_id uuid NOT NULL,
  user_id uuid NOT NULL, -- This should reference club_memberships.user_id
  created_at timestamp with time zone DEFAULT now(),
  UNIQUE(team_id, user_id) -- Prevent duplicate assignments
);

-- Enable RLS on team_members
ALTER TABLE team_members ENABLE ROW LEVEL SECURITY;

-- Add foreign key constraints
ALTER TABLE team_members 
ADD CONSTRAINT IF NOT EXISTS fk_team_members_teams 
FOREIGN KEY (team_id) REFERENCES teams(id) ON DELETE CASCADE;

-- Create RLS policies for team_members
CREATE POLICY IF NOT EXISTS "Club members can view team members" 
ON team_members FOR SELECT 
USING (
  EXISTS (
    SELECT 1 FROM teams t
    JOIN club_memberships cm ON cm.club_id IN (
      SELECT club_id FROM club_memberships WHERE user_id = auth.uid() AND is_active = true
    )
    WHERE t.id = team_members.team_id
  )
);

CREATE POLICY IF NOT EXISTS "Club admins can manage team members" 
ON team_members FOR ALL 
USING (
  EXISTS (
    SELECT 1 FROM teams t
    JOIN club_memberships cm ON cm.club_id = t.club_id
    WHERE t.id = team_members.team_id 
    AND cm.user_id = auth.uid() 
    AND cm.role IN ('admin', 'trainer') 
    AND cm.is_active = true
  )
);