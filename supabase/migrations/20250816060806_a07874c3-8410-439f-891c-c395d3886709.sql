-- Lösche alle Dennis Club 1 Einträge und zugehörige Daten (korrigiert)
DO $$
DECLARE
    club_ids uuid[] := ARRAY(
        SELECT c.id FROM clubs c
        WHERE c.name = 'Dennis Club 1'
    );
    target_club_id uuid;
BEGIN
    -- <PERSON><PERSON><PERSON> jeden Dennis Club 1
    FOREACH target_club_id IN ARRAY club_ids
    LOOP
        -- Lösche zugehörige Daten in der richtigen Reihenfolge (abhängige zuerst)
        
        -- Lösche Buchungen
        DELETE FROM bookings b WHERE b.club_id = target_club_id;
        
        -- Lösche Court-bezogene Daten
        DELETE FROM court_availability ca WHERE ca.court_id IN (SELECT co.id FROM courts co WHERE co.club_id = target_club_id);
        DELETE FROM court_blocks cb WHERE cb.court_id IN (SELECT co.id FROM courts co WHERE co.club_id = target_club_id);
        DELETE FROM courts co WHERE co.club_id = target_club_id;
        
        -- Lösche Aktivitäten und zugehörige Daten
        DELETE FROM activity_registrations ar WHERE ar.activity_id IN (SELECT a.id FROM activities a WHERE a.club_id = target_club_id);
        DELETE FROM work_log_assignments wla WHERE wla.work_log_id IN (
            SELECT wl.id FROM work_logs wl 
            JOIN activities a ON wl.activity_id = a.id 
            WHERE a.club_id = target_club_id
        );
        DELETE FROM work_logs wl WHERE wl.activity_id IN (SELECT a.id FROM activities a WHERE a.club_id = target_club_id);
        DELETE FROM activities a WHERE a.club_id = target_club_id;
        
        -- Lösche Members und zugehörige Daten
        DELETE FROM members m WHERE m.club_id = target_club_id;
        
        -- Lösche Accounts und zugehörige Daten für diesen Club
        DELETE FROM login_identities li WHERE li.account_id IN (SELECT acc.id FROM accounts acc WHERE acc.club_id = target_club_id);
        DELETE FROM invites i WHERE i.account_id IN (SELECT acc.id FROM accounts acc WHERE acc.club_id = target_club_id);
        DELETE FROM fee_assignments fa WHERE fa.account_id IN (SELECT acc.id FROM accounts acc WHERE acc.club_id = target_club_id);
        DELETE FROM guardianships g WHERE g.guardian_account_id IN (SELECT acc.id FROM accounts acc WHERE acc.club_id = target_club_id) 
                                   OR g.dependent_account_id IN (SELECT acc.id FROM accounts acc WHERE acc.club_id = target_club_id);
        DELETE FROM household_members hm WHERE hm.account_id IN (SELECT acc.id FROM accounts acc WHERE acc.club_id = target_club_id);
        DELETE FROM group_members gm WHERE gm.account_id IN (SELECT acc.id FROM accounts acc WHERE acc.club_id = target_club_id);
        DELETE FROM billing_agreements ba WHERE ba.payer_account_id IN (SELECT acc.id FROM accounts acc WHERE acc.club_id = target_club_id)
                                        OR ba.beneficiary_account_id IN (SELECT acc.id FROM accounts acc WHERE acc.club_id = target_club_id);
        DELETE FROM accounts acc WHERE acc.club_id = target_club_id;
        
        -- Lösche Club-Mitgliedschaften
        DELETE FROM club_memberships cm WHERE cm.club_id = target_club_id;
        
        -- Lösche Club-Subscriptions
        DELETE FROM club_subscriptions cs WHERE cs.club_id = target_club_id;
        
        -- Lösche Domains
        DELETE FROM domains d WHERE d.club_id = target_club_id;
        
        -- Lösche Audit Logs für diesen Club
        DELETE FROM audit_logs al WHERE al.club_id = target_club_id;
        
        -- Lösche Support Tickets für diesen Club
        DELETE FROM support_tickets st WHERE st.club_id = target_club_id;
        
        RAISE NOTICE 'Deleted all data for club_id: %', target_club_id;
    END LOOP;
    
    -- Lösche schließlich die Club-Einträge selbst
    DELETE FROM clubs c WHERE c.name = 'Dennis Club 1';
    
    RAISE NOTICE 'Successfully deleted all Dennis Club 1 entries and related data';
END $$;