-- 1) Create fee_categories table
CREATE TABLE IF NOT EXISTS public.fee_categories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  value TEXT NOT NULL UNIQUE,            -- slug/technical key (e.g. "membership")
  display_name TEXT NOT NULL,            -- human readable (e.g. "Mitgliedschaft")
  color TEXT,                            -- optional color token / class name
  sort_order INTEGER NOT NULL DEFAULT 0, -- for ordering in UI
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Enable RLS and policies
ALTER TABLE public.fee_categories ENABLE ROW LEVEL SECURITY;

-- <PERSON><PERSON> can manage all categories
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname = 'public' AND tablename = 'fee_categories' AND policyname = 'Admins can manage fee categories'
  ) THEN
    CREATE POLICY "Admins can manage fee categories"
    ON public.fee_categories
    FOR ALL
    USING (has_role(auth.uid(), 'admin'::app_role))
    WITH CHECK (has_role(auth.uid(), 'admin'::app_role));
  END IF;
END $$;

-- Everyone can view active categories
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname = 'public' AND tablename = 'fee_categories' AND policyname = 'Everyone can view active categories'
  ) THEN
    CREATE POLICY "Everyone can view active categories"
    ON public.fee_categories
    FOR SELECT
    USING (is_active = TRUE);
  END IF;
END $$;

-- Trigger to auto-update updated_at
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_trigger WHERE tgname = 'update_fee_categories_updated_at'
  ) THEN
    CREATE TRIGGER update_fee_categories_updated_at
    BEFORE UPDATE ON public.fee_categories
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();
  END IF;
END $$;

-- 2) Seed defaults and/or migrate existing text categories into fee_categories
-- Insert known defaults idempotently
INSERT INTO public.fee_categories (value, display_name, sort_order)
VALUES 
  ('membership', 'Mitgliedschaft', 1),
  ('course', 'Kurs/Training', 2),
  ('event', 'Event/Turnier', 3),
  ('other', 'Sonstiges', 99)
ON CONFLICT (value) DO NOTHING;

-- Also insert any distinct text categories from fee_types that aren't present yet
INSERT INTO public.fee_categories (value, display_name)
SELECT DISTINCT ft.category AS value,
       INITCAP(REPLACE(ft.category, '_', ' ')) AS display_name
FROM public.fee_types ft
LEFT JOIN public.fee_categories fc ON fc.value = ft.category
WHERE ft.category IS NOT NULL AND fc.id IS NULL;

-- 3) Add category_id to fee_types and backfill
ALTER TABLE public.fee_types ADD COLUMN IF NOT EXISTS category_id uuid;

-- Backfill category_id from the existing text value
UPDATE public.fee_types ft
SET category_id = fc.id
FROM public.fee_categories fc
WHERE ft.category IS NOT NULL AND fc.value = ft.category AND (ft.category_id IS NULL OR ft.category_id <> fc.id);

-- Add FK and index
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_constraint WHERE conname = 'fee_types_category_id_fkey'
  ) THEN
    ALTER TABLE public.fee_types
    ADD CONSTRAINT fee_types_category_id_fkey
    FOREIGN KEY (category_id) REFERENCES public.fee_categories(id)
    ON DELETE SET NULL;
  END IF;
END $$;

CREATE INDEX IF NOT EXISTS idx_fee_types_category_id ON public.fee_types (category_id);
