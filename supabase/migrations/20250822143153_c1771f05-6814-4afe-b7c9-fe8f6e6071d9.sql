-- Create tournaments table with all necessary columns
CREATE TABLE IF NOT EXISTS public.tournaments (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  status TEXT NOT NULL DEFAULT 'registration' CHECK (status IN ('registration', 'active', 'completed', 'cancelled')),
  format_type TEXT DEFAULT 'knockout',
  format_config JSONB DEFAULT '{}',
  registration_start TIMESTAMP WITH TIME ZONE,
  registration_end TIMESTAMP WITH TIME ZONE,
  tournament_start TIMESTAMP WITH TIME ZONE,
  tournament_end TIMESTAMP WITH TIME ZONE,
  max_participants INTEGER,
  allow_guests BOOLEAN NOT NULL DEFAULT true,
  club_id UUID NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.tournaments ENABLE ROW LEVEL SECURITY;

-- Create policies for tournament access
CREATE POLICY "Club members can view tournaments in their club"
ON public.tournaments
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid()
      AND cm.club_id = tournaments.club_id
      AND cm.is_active = true
  )
);

CREATE POLICY "Club admins can create tournaments"
ON public.tournaments
FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid()
      AND cm.club_id = tournaments.club_id
      AND cm.role IN ('admin', 'super_admin')
      AND cm.is_active = true
  )
);

CREATE POLICY "Club admins can update tournaments"
ON public.tournaments
FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid()
      AND cm.club_id = tournaments.club_id
      AND cm.role IN ('admin', 'super_admin')
      AND cm.is_active = true
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid()
      AND cm.club_id = tournaments.club_id
      AND cm.role IN ('admin', 'super_admin')
      AND cm.is_active = true
  )
);

CREATE POLICY "Club admins can delete tournaments"
ON public.tournaments
FOR DELETE
USING (
  EXISTS (
    SELECT 1 FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid()
      AND cm.club_id = tournaments.club_id
      AND cm.role IN ('admin', 'super_admin')
      AND cm.is_active = true
  )
);

CREATE POLICY "Meta admins can manage all tournaments"
ON public.tournaments
FOR ALL
USING (is_meta_admin(auth.uid()))
WITH CHECK (is_meta_admin(auth.uid()));

-- Create trigger for updated_at
CREATE TRIGGER update_tournaments_updated_at
BEFORE UPDATE ON public.tournaments
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- Create tournament participants table
CREATE TABLE IF NOT EXISTS public.tournament_participants (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  tournament_id UUID NOT NULL REFERENCES public.tournaments(id) ON DELETE CASCADE,
  user_id UUID,
  player_name TEXT NOT NULL,
  email TEXT,
  phone TEXT,
  is_guest BOOLEAN NOT NULL DEFAULT false,
  seeding INTEGER,
  status TEXT NOT NULL DEFAULT 'registered' CHECK (status IN ('registered', 'confirmed', 'withdrawn', 'disqualified')),
  registered_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS for tournament participants
ALTER TABLE public.tournament_participants ENABLE ROW LEVEL SECURITY;

-- Create policies for tournament participants
CREATE POLICY "Club members can view participants in club tournaments"
ON public.tournament_participants
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM public.tournaments t
    JOIN public.club_memberships cm ON cm.club_id = t.club_id
    WHERE t.id = tournament_participants.tournament_id
      AND cm.user_id = auth.uid()
      AND cm.is_active = true
  )
);

CREATE POLICY "Users can register themselves"
ON public.tournament_participants
FOR INSERT
WITH CHECK (
  user_id = auth.uid() OR
  EXISTS (
    SELECT 1 FROM public.tournaments t
    JOIN public.club_memberships cm ON cm.club_id = t.club_id
    WHERE t.id = tournament_participants.tournament_id
      AND cm.user_id = auth.uid()
      AND cm.role IN ('admin', 'super_admin')
      AND cm.is_active = true
  )
);

CREATE POLICY "Club admins can manage participants"
ON public.tournament_participants
FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM public.tournaments t
    JOIN public.club_memberships cm ON cm.club_id = t.club_id
    WHERE t.id = tournament_participants.tournament_id
      AND cm.user_id = auth.uid()
      AND cm.role IN ('admin', 'super_admin')
      AND cm.is_active = true
  )
);

-- Create tournament matches table
CREATE TABLE IF NOT EXISTS public.tournament_matches (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  tournament_id UUID NOT NULL REFERENCES public.tournaments(id) ON DELETE CASCADE,
  round_number INTEGER NOT NULL,
  match_number INTEGER NOT NULL,
  player1_id UUID REFERENCES public.tournament_participants(id),
  player2_id UUID REFERENCES public.tournament_participants(id),
  player1_name TEXT,
  player2_name TEXT,
  court_id UUID REFERENCES public.courts(id),
  scheduled_at TIMESTAMP WITH TIME ZONE,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'cancelled', 'walkover')),
  winner_id UUID REFERENCES public.tournament_participants(id),
  score TEXT,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS for tournament matches
ALTER TABLE public.tournament_matches ENABLE ROW LEVEL SECURITY;

-- Create policies for tournament matches
CREATE POLICY "Club members can view matches in club tournaments"
ON public.tournament_matches
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM public.tournaments t
    JOIN public.club_memberships cm ON cm.club_id = t.club_id
    WHERE t.id = tournament_matches.tournament_id
      AND cm.user_id = auth.uid()
      AND cm.is_active = true
  )
);

CREATE POLICY "Club admins can manage matches"
ON public.tournament_matches
FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM public.tournaments t
    JOIN public.club_memberships cm ON cm.club_id = t.club_id
    WHERE t.id = tournament_matches.tournament_id
      AND cm.user_id = auth.uid()
      AND cm.role IN ('admin', 'super_admin')
      AND cm.is_active = true
  )
);

-- Create triggers for updated_at
CREATE TRIGGER update_tournament_participants_updated_at
BEFORE UPDATE ON public.tournament_participants
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_tournament_matches_updated_at
BEFORE UPDATE ON public.tournament_matches
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();