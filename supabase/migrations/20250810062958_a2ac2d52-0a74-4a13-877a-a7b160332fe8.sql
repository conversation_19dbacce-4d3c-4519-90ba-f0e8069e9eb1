
-- 1) Enum für Zusagen/Absagen
CREATE TYPE public.rsvp_status AS ENUM ('yes', 'no', 'maybe');

-- 2) Helper-Funktion: prüft, ob ein User Mitglied eines Teams ist
CREATE OR REPLACE FUNCTION public.is_member_of_team(_team_id uuid, _user_id uuid)
RETURNS boolean
LANGUAGE sql
STABLE
SECURITY DEFINER
SET search_path TO 'public'
AS $$
  SELECT EXISTS (
    SELECT 1
    FROM public.team_members tm
    JOIN public.accounts a ON a.id = tm.account_id
    WHERE tm.team_id = _team_id
      AND a.user_id = _user_id
  );
$$;

-- 3) Tabellen

-- teams: Mannschaften
CREATE TABLE public.teams (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  description text,
  league text,
  captain_account_id uuid NULL REFERENCES public.accounts(id) ON DELETE SET NULL,
  created_at timestamptz NOT NULL DEFAULT now(),
  updated_at timestamptz NOT NULL DEFAULT now()
);

-- team_members: Zuordnung Accounts <-> Teams
CREATE TABLE public.team_members (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  team_id uuid NOT NULL REFERENCES public.teams(id) ON DELETE CASCADE,
  account_id uuid NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
  is_captain boolean NOT NULL DEFAULT false,
  added_at timestamptz NOT NULL DEFAULT now(),
  UNIQUE (team_id, account_id)
);

-- team_messages: Team-Chat
CREATE TABLE public.team_messages (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  team_id uuid NOT NULL REFERENCES public.teams(id) ON DELETE CASCADE,
  author_account_id uuid NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
  content text NOT NULL,
  created_at timestamptz NOT NULL DEFAULT now(),
  updated_at timestamptz NOT NULL DEFAULT now()
);

-- team_games: Spiele
CREATE TABLE public.team_games (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  team_id uuid NOT NULL REFERENCES public.teams(id) ON DELETE CASCADE,
  opponent text,
  game_date date NOT NULL,
  start_time time NOT NULL,
  location text,
  notes text,
  result text,
  created_by_account_id uuid NULL REFERENCES public.accounts(id) ON DELETE SET NULL,
  created_at timestamptz NOT NULL DEFAULT now(),
  updated_at timestamptz NOT NULL DEFAULT now()
);

-- team_trainings: Trainings
CREATE TABLE public.team_trainings (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  team_id uuid NOT NULL REFERENCES public.teams(id) ON DELETE CASCADE,
  training_date date NOT NULL,
  start_time time NOT NULL,
  end_time time,
  location text,
  notes text,
  created_by_account_id uuid NULL REFERENCES public.accounts(id) ON DELETE SET NULL,
  created_at timestamptz NOT NULL DEFAULT now(),
  updated_at timestamptz NOT NULL DEFAULT now()
);

-- team_game_rsvps: Zusagen/Absagen zu Spielen
CREATE TABLE public.team_game_rsvps (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  game_id uuid NOT NULL REFERENCES public.team_games(id) ON DELETE CASCADE,
  account_id uuid NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
  status public.rsvp_status NOT NULL DEFAULT 'maybe',
  created_at timestamptz NOT NULL DEFAULT now(),
  updated_at timestamptz NOT NULL DEFAULT now(),
  UNIQUE (game_id, account_id)
);

-- team_training_rsvps: Zusagen/Absagen zu Trainings
CREATE TABLE public.team_training_rsvps (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  training_id uuid NOT NULL REFERENCES public.team_trainings(id) ON DELETE CASCADE,
  account_id uuid NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
  status public.rsvp_status NOT NULL DEFAULT 'maybe',
  created_at timestamptz NOT NULL DEFAULT now(),
  updated_at timestamptz NOT NULL DEFAULT now(),
  UNIQUE (training_id, account_id)
);

-- 4) RLS aktivieren
ALTER TABLE public.teams ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.team_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.team_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.team_games ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.team_trainings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.team_game_rsvps ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.team_training_rsvps ENABLE ROW LEVEL SECURITY;

-- 5) RLS-Policies

-- teams
CREATE POLICY "Admins manage teams"
  ON public.teams
  FOR ALL
  USING (has_role(auth.uid(), 'admin'::app_role))
  WITH CHECK (has_role(auth.uid(), 'admin'::app_role));

CREATE POLICY "Members can view own teams"
  ON public.teams
  FOR SELECT
  USING (EXISTS (
    SELECT 1
    FROM public.team_members tm
    JOIN public.accounts a ON a.id = tm.account_id
    WHERE tm.team_id = teams.id
      AND a.user_id = auth.uid()
  ));

CREATE POLICY "Members can update own teams"
  ON public.teams
  FOR UPDATE
  USING (EXISTS (
    SELECT 1
    FROM public.team_members tm
    JOIN public.accounts a ON a.id = tm.account_id
    WHERE tm.team_id = teams.id
      AND a.user_id = auth.uid()
  ))
  WITH CHECK (EXISTS (
    SELECT 1
    FROM public.team_members tm
    JOIN public.accounts a ON a.id = tm.account_id
    WHERE tm.team_id = teams.id
      AND a.user_id = auth.uid()
  ));

-- team_members
CREATE POLICY "Admins manage team_members"
  ON public.team_members
  FOR ALL
  USING (has_role(auth.uid(), 'admin'::app_role))
  WITH CHECK (has_role(auth.uid(), 'admin'::app_role));

CREATE POLICY "Members can view members of own team"
  ON public.team_members
  FOR SELECT
  USING (is_member_of_team(team_id, auth.uid()));

CREATE POLICY "Members manage roster of own team"
  ON public.team_members
  FOR INSERT
  WITH CHECK (is_member_of_team(team_id, auth.uid()));

CREATE POLICY "Members update roster of own team"
  ON public.team_members
  FOR UPDATE
  USING (is_member_of_team(team_id, auth.uid()))
  WITH CHECK (is_member_of_team(team_id, auth.uid()));

CREATE POLICY "Members remove roster of own team"
  ON public.team_members
  FOR DELETE
  USING (is_member_of_team(team_id, auth.uid()));

-- team_messages
CREATE POLICY "Admins manage team_messages"
  ON public.team_messages
  FOR ALL
  USING (has_role(auth.uid(), 'admin'::app_role))
  WITH CHECK (has_role(auth.uid(), 'admin'::app_role));

CREATE POLICY "Members view messages of own team"
  ON public.team_messages
  FOR SELECT
  USING (is_member_of_team(team_id, auth.uid()));

CREATE POLICY "Members can create own messages"
  ON public.team_messages
  FOR INSERT
  WITH CHECK (
    is_member_of_team(team_id, auth.uid()) AND
    account_belongs_to_user(author_account_id, auth.uid())
  );

CREATE POLICY "Authors can update own messages"
  ON public.team_messages
  FOR UPDATE
  USING (account_belongs_to_user(author_account_id, auth.uid()))
  WITH CHECK (
    is_member_of_team(team_id, auth.uid()) AND
    account_belongs_to_user(author_account_id, auth.uid())
  );

CREATE POLICY "Authors can delete own messages"
  ON public.team_messages
  FOR DELETE
  USING (account_belongs_to_user(author_account_id, auth.uid()));

-- team_games
CREATE POLICY "Admins manage team_games"
  ON public.team_games
  FOR ALL
  USING (has_role(auth.uid(), 'admin'::app_role))
  WITH CHECK (has_role(auth.uid(), 'admin'::app_role));

CREATE POLICY "Members view games of own team"
  ON public.team_games
  FOR SELECT
  USING (is_member_of_team(team_id, auth.uid()));

CREATE POLICY "Members manage games of own team - insert"
  ON public.team_games
  FOR INSERT
  WITH CHECK (is_member_of_team(team_id, auth.uid()));

CREATE POLICY "Members manage games of own team - update"
  ON public.team_games
  FOR UPDATE
  USING (is_member_of_team(team_id, auth.uid()))
  WITH CHECK (is_member_of_team(team_id, auth.uid()));

CREATE POLICY "Members manage games of own team - delete"
  ON public.team_games
  FOR DELETE
  USING (is_member_of_team(team_id, auth.uid()));

-- team_trainings
CREATE POLICY "Admins manage team_trainings"
  ON public.team_trainings
  FOR ALL
  USING (has_role(auth.uid(), 'admin'::app_role))
  WITH CHECK (has_role(auth.uid(), 'admin'::app_role));

CREATE POLICY "Members view trainings of own team"
  ON public.team_trainings
  FOR SELECT
  USING (is_member_of_team(team_id, auth.uid()));

CREATE POLICY "Members manage trainings of own team - insert"
  ON public.team_trainings
  FOR INSERT
  WITH CHECK (is_member_of_team(team_id, auth.uid()));

CREATE POLICY "Members manage trainings of own team - update"
  ON public.team_trainings
  FOR UPDATE
  USING (is_member_of_team(team_id, auth.uid()))
  WITH CHECK (is_member_of_team(team_id, auth.uid()));

CREATE POLICY "Members manage trainings of own team - delete"
  ON public.team_trainings
  FOR DELETE
  USING (is_member_of_team(team_id, auth.uid()));

-- team_game_rsvps
CREATE POLICY "Admins manage team_game_rsvps"
  ON public.team_game_rsvps
  FOR ALL
  USING (has_role(auth.uid(), 'admin'::app_role))
  WITH CHECK (has_role(auth.uid(), 'admin'::app_role));

CREATE POLICY "Members view RSVPs of games in own teams"
  ON public.team_game_rsvps
  FOR SELECT
  USING (EXISTS (
    SELECT 1
    FROM public.team_games g
    WHERE g.id = team_game_rsvps.game_id
      AND is_member_of_team(g.team_id, auth.uid())
  ));

CREATE POLICY "Members create/update own game RSVP"
  ON public.team_game_rsvps
  FOR INSERT
  WITH CHECK (
    account_belongs_to_user(account_id, auth.uid()) AND
    EXISTS (
      SELECT 1
      FROM public.team_games g
      WHERE g.id = team_game_rsvps.game_id
        AND is_member_of_team(g.team_id, auth.uid())
    )
  );

CREATE POLICY "Members update own game RSVP"
  ON public.team_game_rsvps
  FOR UPDATE
  USING (account_belongs_to_user(account_id, auth.uid()))
  WITH CHECK (
    account_belongs_to_user(account_id, auth.uid()) AND
    EXISTS (
      SELECT 1
      FROM public.team_games g
      WHERE g.id = team_game_rsvps.game_id
        AND is_member_of_team(g.team_id, auth.uid())
    )
  );

CREATE POLICY "Members delete own game RSVP"
  ON public.team_game_rsvps
  FOR DELETE
  USING (account_belongs_to_user(account_id, auth.uid()));

-- team_training_rsvps
CREATE POLICY "Admins manage team_training_rsvps"
  ON public.team_training_rsvps
  FOR ALL
  USING (has_role(auth.uid(), 'admin'::app_role))
  WITH CHECK (has_role(auth.uid(), 'admin'::app_role));

CREATE POLICY "Members view RSVPs of trainings in own teams"
  ON public.team_training_rsvps
  FOR SELECT
  USING (EXISTS (
    SELECT 1
    FROM public.team_trainings t
    WHERE t.id = team_training_rsvps.training_id
      AND is_member_of_team(t.team_id, auth.uid())
  ));

CREATE POLICY "Members create training RSVP"
  ON public.team_training_rsvps
  FOR INSERT
  WITH CHECK (
    account_belongs_to_user(account_id, auth.uid()) AND
    EXISTS (
      SELECT 1
      FROM public.team_trainings t
      WHERE t.id = team_training_rsvps.training_id
        AND is_member_of_team(t.team_id, auth.uid())
    )
  );

CREATE POLICY "Members update own training RSVP"
  ON public.team_training_rsvps
  FOR UPDATE
  USING (account_belongs_to_user(account_id, auth.uid()))
  WITH CHECK (
    account_belongs_to_user(account_id, auth.uid()) AND
    EXISTS (
      SELECT 1
      FROM public.team_trainings t
      WHERE t.id = team_training_rsvps.training_id
        AND is_member_of_team(t.team_id, auth.uid())
    )
  );

CREATE POLICY "Members delete own training RSVP"
  ON public.team_training_rsvps
  FOR DELETE
  USING (account_belongs_to_user(account_id, auth.uid()));

-- 6) updated_at Trigger anlegen
CREATE TRIGGER set_updated_at_teams
BEFORE UPDATE ON public.teams
FOR EACH ROW EXECUTE PROCEDURE public.update_updated_at_column();

CREATE TRIGGER set_updated_at_team_messages
BEFORE UPDATE ON public.team_messages
FOR EACH ROW EXECUTE PROCEDURE public.update_updated_at_column();

CREATE TRIGGER set_updated_at_team_games
BEFORE UPDATE ON public.team_games
FOR EACH ROW EXECUTE PROCEDURE public.update_updated_at_column();

CREATE TRIGGER set_updated_at_team_trainings
BEFORE UPDATE ON public.team_trainings
FOR EACH ROW EXECUTE PROCEDURE public.update_updated_at_column();

CREATE TRIGGER set_updated_at_team_game_rsvps
BEFORE UPDATE ON public.team_game_rsvps
FOR EACH ROW EXECUTE PROCEDURE public.update_updated_at_column();

CREATE TRIGGER set_updated_at_team_training_rsvps
BEFORE UPDATE ON public.team_training_rsvps
FOR EACH ROW EXECUTE PROCEDURE public.update_updated_at_column();

-- 7) Indizes für Performance
CREATE INDEX team_members_team_id_idx ON public.team_members (team_id);
CREATE INDEX team_members_account_id_idx ON public.team_members (account_id);
CREATE INDEX team_messages_team_created_idx ON public.team_messages (team_id, created_at DESC);
CREATE INDEX team_games_team_date_idx ON public.team_games (team_id, game_date, start_time);
CREATE INDEX team_trainings_team_date_idx ON public.team_trainings (team_id, training_date, start_time);
CREATE INDEX team_game_rsvps_game_idx ON public.team_game_rsvps (game_id);
CREATE INDEX team_training_rsvps_training_idx ON public.team_training_rsvps (training_id);
