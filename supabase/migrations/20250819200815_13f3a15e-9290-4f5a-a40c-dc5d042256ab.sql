-- Fix courts policy for guests - they should see ALL courts, not filtered by club
-- The current policy still tries to match club_id but guests have no club membership

-- Update the SELECT policy for courts to allow guests to see all courts
DROP POLICY IF EXISTS "courts_select_policy" ON public.courts;
CREATE POLICY "courts_select_policy"
ON public.courts
FOR SELECT
TO authenticated
USING (
  -- <PERSON><PERSON> admins can see all
  is_meta_admin(auth.uid()) 
  OR 
  -- Club members see courts of their club
  (EXISTS (
    SELECT 1
    FROM public.club_memberships m
    WHERE m.user_id = auth.uid() 
      AND m.club_id = courts.club_id 
      AND COALESCE(m.is_active, true) = true
  ))
  OR
  -- Guests can see ALL courts (no club restriction)
  (EXISTS (
    SELECT 1
    FROM public.user_roles ur
    WHERE ur.user_id = auth.uid() 
      AND ur.role = 'guest'
  ))
);

-- Also fix court_availability - guests should see all court availability
DROP POLICY IF EXISTS "court_availability_select" ON public.court_availability;
CREATE POLICY "court_availability_select"
ON public.court_availability
FOR SELECT
TO authenticated
USING (
  -- Club members see availability for their club's courts
  EXISTS (
    SELECT 1
    FROM public.club_memberships m
    JOIN public.courts c ON c.id = court_availability.court_id
    WHERE m.user_id = auth.uid() 
      AND m.club_id = c.club_id 
      AND COALESCE(m.is_active, true) = true
  )
  OR
  -- Guests can see ALL court availability
  EXISTS (
    SELECT 1
    FROM public.user_roles ur
    WHERE ur.user_id = auth.uid() 
      AND ur.role = 'guest'
  )
);

-- Also fix court_blocks - guests should see all court blocks
DROP POLICY IF EXISTS "court_blocks_select" ON public.court_blocks;
CREATE POLICY "court_blocks_select"
ON public.court_blocks
FOR SELECT
TO authenticated
USING (
  -- Club members see blocks for their club's courts
  EXISTS (
    SELECT 1
    FROM public.club_memberships m
    JOIN public.courts c ON c.id = court_blocks.court_id
    WHERE m.user_id = auth.uid() 
      AND m.club_id = c.club_id 
      AND COALESCE(m.is_active, true) = true
  )
  OR
  -- Guests can see ALL court blocks
  EXISTS (
    SELECT 1
    FROM public.user_roles ur
    WHERE ur.user_id = auth.uid() 
      AND ur.role = 'guest'
  )
);