-- Add missing columns to tournaments table if they don't exist
DO $$ 
BEGIN
  -- Add format_type column if it doesn't exist
  BEGIN
    ALTER TABLE public.tournaments ADD COLUMN format_type TEXT DEFAULT 'knockout';
  EXCEPTION
    WHEN duplicate_column THEN
      -- Column already exists, skip
      NULL;
  END;

  -- Add format_config column if it doesn't exist
  BEGIN
    ALTER TABLE public.tournaments ADD COLUMN format_config JSONB DEFAULT '{}';
  EXCEPTION
    WHEN duplicate_column THEN
      -- Column already exists, skip
      NULL;
  END;

  -- Add registration_start column if it doesn't exist
  BEGIN
    ALTER TABLE public.tournaments ADD COLUMN registration_start TIMESTAMP WITH TIME ZONE;
  EXCEPTION
    WHEN duplicate_column THEN
      -- Column already exists, skip
      NULL;
  END;

  -- Add registration_end column if it doesn't exist
  BEGIN
    ALTER TABLE public.tournaments ADD COLUMN registration_end TIMESTAMP WITH TIME ZONE;
  EXCEPTION
    WHEN duplicate_column THEN
      -- Column already exists, skip
      NULL;
  END;

  -- Add tournament_start column if it doesn't exist
  BEGIN
    ALTER TABLE public.tournaments ADD COLUMN tournament_start TIMESTAMP WITH TIME ZONE;
  EXCEPTION
    WHEN duplicate_column THEN
      -- Column already exists, skip
      NULL;
  END;

  -- Add tournament_end column if it doesn't exist
  BEGIN
    ALTER TABLE public.tournaments ADD COLUMN tournament_end TIMESTAMP WITH TIME ZONE;
  EXCEPTION
    WHEN duplicate_column THEN
      -- Column already exists, skip
      NULL;
  END;

  -- Add max_participants column if it doesn't exist
  BEGIN
    ALTER TABLE public.tournaments ADD COLUMN max_participants INTEGER;
  EXCEPTION
    WHEN duplicate_column THEN
      -- Column already exists, skip
      NULL;
  END;

  -- Add allow_guests column if it doesn't exist
  BEGIN
    ALTER TABLE public.tournaments ADD COLUMN allow_guests BOOLEAN NOT NULL DEFAULT true;
  EXCEPTION
    WHEN duplicate_column THEN
      -- Column already exists, skip
      NULL;
  END;

  -- Add club_id column if it doesn't exist
  BEGIN
    ALTER TABLE public.tournaments ADD COLUMN club_id UUID;
  EXCEPTION
    WHEN duplicate_column THEN
      -- Column already exists, skip
      NULL;
  END;
END $$;