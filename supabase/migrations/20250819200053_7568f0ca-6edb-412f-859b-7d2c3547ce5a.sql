-- Fix <PERSON>'s incorrect roles and memberships
-- First, get <PERSON>'s user ID
DO $$
DECLARE
    gustav_user_id UUID;
BEGIN
    -- Get <PERSON>'s user ID
    SELECT id INTO gustav_user_id 
    FROM auth.users 
    WHERE email = '<EMAIL>';
    
    IF gustav_user_id IS NOT NULL THEN
        -- Update his user role from 'mitglied' to 'guest'
        UPDATE public.user_roles 
        SET role = 'guest' 
        WHERE user_id = gustav_user_id AND role = 'mitglied';
        
        -- Remove his club membership (guests should not have club memberships)
        DELETE FROM public.club_memberships 
        WHERE user_id = gustav_user_id;
        
        RAISE LOG 'Fixed <PERSON> roles - changed to guest and removed club membership for user: %', gustav_user_id;
    ELSE
        RAISE LOG 'Gustav <PERSON> user not found';
    END IF;
END $$;