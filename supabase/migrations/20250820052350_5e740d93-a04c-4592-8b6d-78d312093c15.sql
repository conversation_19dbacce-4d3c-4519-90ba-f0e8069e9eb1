-- Fix search path for security functions
CREATE OR REPLACE FUNCTION public.update_waitlist_position()
RETURNS TRIGGER 
LANGUAGE plpgsql 
SECURITY DEFINER
SET search_path = 'public'
AS $$
BEGIN
  -- Set position as next available position for the same criteria
  IF TG_OP = 'INSERT' THEN
    NEW.position := COALESCE(
      (SELECT MAX(position) + 1 
       FROM public.waitlist_entries 
       WHERE club_id = NEW.club_id 
         AND preferred_date = NEW.preferred_date
         AND status = 'waiting'), 
      1
    );
  END IF;
  
  RETURN NEW;
END;
$$;

CREATE OR REPLACE FUNCTION public.validate_auto_booking_limit()
RETURNS TRIGGER 
LANGUAGE plpgsql 
SECURITY DEFINER
SET search_path = 'public'
AS $$
BEGIN
  IF NEW.auto_booking_enabled = true THEN
    -- Check if user already has an active auto-booking entry
    IF EXISTS (
      SELECT 1 FROM public.waitlist_entries 
      WHERE user_id = NEW.user_id 
        AND auto_booking_enabled = true 
        AND status = 'waiting'
        AND (TG_OP = 'INSERT' OR id != NEW.id)
    ) THEN
      RAISE EXCEPTION 'Benutzer kann nur eine Auto-Zusage Warteliste zur Zeit haben';
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$;