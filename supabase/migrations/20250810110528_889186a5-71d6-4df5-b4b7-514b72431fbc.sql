-- Secure newly created tables with RLS and minimal policies

-- login_identities
ALTER TABLE public.login_identities ENABLE ROW LEVEL SECURITY;
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='login_identities' AND policyname='Admins manage login_identities'
  ) THEN
    CREATE POLICY "Admins manage login_identities"
      ON public.login_identities
      FOR ALL
      TO authenticated
      USING (public.has_role(auth.uid(), 'admin'::public.app_role))
      WITH CHECK (public.has_role(auth.uid(), 'admin'::public.app_role));
  END IF;
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='login_identities' AND policyname='Owners view/manage their login identities'
  ) THEN
    CREATE POLICY "Owners view/manage their login identities"
      ON public.login_identities
      FOR ALL
      TO authenticated
      USING (EXISTS (
        SELECT 1 FROM public.accounts a
        WHERE a.id = login_identities.account_id AND a.user_id = auth.uid()
      ))
      WITH CHECK (EXISTS (
        SELECT 1 FROM public.accounts a
        WHERE a.id = login_identities.account_id AND a.user_id = auth.uid()
      ));
  END IF;
END $$;

-- households
ALTER TABLE public.households ENABLE ROW LEVEL SECURITY;
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='households' AND policyname='Admins manage households'
  ) THEN
    CREATE POLICY "Admins manage households"
      ON public.households
      FOR ALL
      TO authenticated
      USING (public.has_role(auth.uid(), 'admin'::public.app_role))
      WITH CHECK (public.has_role(auth.uid(), 'admin'::public.app_role));
  END IF;
END $$;

-- household_members
ALTER TABLE public.household_members ENABLE ROW LEVEL SECURITY;
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='household_members' AND policyname='Admins manage household_members'
  ) THEN
    CREATE POLICY "Admins manage household_members"
      ON public.household_members
      FOR ALL
      TO authenticated
      USING (public.has_role(auth.uid(), 'admin'::public.app_role))
      WITH CHECK (public.has_role(auth.uid(), 'admin'::public.app_role));
  END IF;
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='household_members' AND policyname='Users view their household membership'
  ) THEN
    CREATE POLICY "Users view their household membership"
      ON public.household_members
      FOR SELECT
      TO authenticated
      USING (account_id IN (SELECT account_id FROM public.effective_rights(auth.uid())));
  END IF;
END $$;

-- guardianships
ALTER TABLE public.guardianships ENABLE ROW LEVEL SECURITY;
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='guardianships' AND policyname='Admins manage guardianships'
  ) THEN
    CREATE POLICY "Admins manage guardianships"
      ON public.guardianships
      FOR ALL
      TO authenticated
      USING (public.has_role(auth.uid(), 'admin'::public.app_role))
      WITH CHECK (public.has_role(auth.uid(), 'admin'::public.app_role));
  END IF;
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='guardianships' AND policyname='Guardians view active guardianships'
  ) THEN
    CREATE POLICY "Guardians view active guardianships"
      ON public.guardianships
      FOR SELECT
      TO authenticated
      USING (
        guardian_account_id IN (SELECT account_id FROM public.effective_rights(auth.uid()))
        OR dependent_account_id IN (SELECT account_id FROM public.effective_rights(auth.uid()))
      );
  END IF;
END $$;

-- billing_agreements
ALTER TABLE public.billing_agreements ENABLE ROW LEVEL SECURITY;
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='billing_agreements' AND policyname='Admins manage billing_agreements'
  ) THEN
    CREATE POLICY "Admins manage billing_agreements"
      ON public.billing_agreements
      FOR ALL
      TO authenticated
      USING (public.has_role(auth.uid(), 'admin'::public.app_role))
      WITH CHECK (public.has_role(auth.uid(), 'admin'::public.app_role));
  END IF;
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='billing_agreements' AND policyname='Users view their billing agreements'
  ) THEN
    CREATE POLICY "Users view their billing agreements"
      ON public.billing_agreements
      FOR SELECT
      TO authenticated
      USING (
        payer_account_id IN (SELECT account_id FROM public.effective_rights(auth.uid()))
        OR beneficiary_account_id IN (SELECT account_id FROM public.effective_rights(auth.uid()))
      );
  END IF;
END $$;

-- invites
ALTER TABLE public.invites ENABLE ROW LEVEL SECURITY;
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='invites' AND policyname='Admins manage invites'
  ) THEN
    CREATE POLICY "Admins manage invites"
      ON public.invites
      FOR ALL
      TO authenticated
      USING (public.has_role(auth.uid(), 'admin'::public.app_role))
      WITH CHECK (public.has_role(auth.uid(), 'admin'::public.app_role));
  END IF;
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='invites' AND policyname='Owners manage their invites'
  ) THEN
    CREATE POLICY "Owners manage their invites"
      ON public.invites
      FOR ALL
      TO authenticated
      USING (account_id IN (SELECT account_id FROM public.effective_rights(auth.uid())))
      WITH CHECK (account_id IN (SELECT account_id FROM public.effective_rights(auth.uid())));
  END IF;
END $$;