-- Create table for customizable tournament format configurations
CREATE TABLE public.tournament_format_configs (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  club_id UUID NOT NULL REFERENCES public.clubs(id) ON DELETE CASCADE,
  tournament_id UUID REFERENCES public.tournaments(id) ON DELETE CASCADE,
  format_type TEXT NOT NULL,
  custom_rules JSONB DEFAULT '{}'::jsonb,
  custom_description TEXT,
  custom_name TEXT,
  time_config JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS
ALTER TABLE public.tournament_format_configs ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Club admins can manage format configs" 
ON public.tournament_format_configs 
FOR ALL 
USING (EXISTS (
  SELECT 1 FROM public.club_memberships cm
  WHERE cm.user_id = auth.uid() 
    AND cm.club_id = tournament_format_configs.club_id 
    AND cm.role = ANY(ARRAY['admin', 'super_admin']) 
    AND cm.is_active = true
));

CREATE POLICY "Club members can view format configs" 
ON public.tournament_format_configs 
FOR SELECT 
USING (EXISTS (
  SELECT 1 FROM public.club_memberships cm
  WHERE cm.user_id = auth.uid() 
    AND cm.club_id = tournament_format_configs.club_id 
    AND cm.is_active = true
));

-- Add trigger for updated_at
CREATE TRIGGER update_tournament_format_configs_updated_at
BEFORE UPDATE ON public.tournament_format_configs
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- Add index for performance
CREATE INDEX idx_tournament_format_configs_club_format ON public.tournament_format_configs(club_id, format_type);
CREATE INDEX idx_tournament_format_configs_tournament ON public.tournament_format_configs(tournament_id);