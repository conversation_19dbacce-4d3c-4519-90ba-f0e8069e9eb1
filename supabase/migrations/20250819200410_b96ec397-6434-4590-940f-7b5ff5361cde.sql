-- Update booking policies to allow guests with 'guest' role to book slots
-- This allows both club members AND guests to make bookings

-- Update the INSERT policy for bookings
DROP POLICY IF EXISTS "bookings_insert" ON public.bookings;
CREATE POLICY "bookings_insert"
ON public.bookings
FOR INSERT
TO authenticated
WITH CHECK (
  -- Either user has active club membership OR user has guest role
  EXISTS (
    SELECT 1
    FROM public.club_memberships m
    WHERE m.user_id = auth.uid() 
      AND m.club_id = bookings.club_id 
      AND COALESCE(m.is_active, true) = true
  )
  OR
  EXISTS (
    SELECT 1
    FROM public.user_roles ur
    WHERE ur.user_id = auth.uid() 
      AND ur.role = 'guest'
  )
);

-- Update the SELECT policy for bookings  
DROP POLICY IF EXISTS "bookings_select" ON public.bookings;
CREATE POLICY "bookings_select"
ON public.bookings
FOR SELECT
TO authenticated
USING (
  -- Either user has active club membership OR user has guest role
  EXISTS (
    SELECT 1
    FROM public.club_memberships m
    WHERE m.user_id = auth.uid() 
      AND m.club_id = bookings.club_id 
      AND COALESCE(m.is_active, true) = true
  )
  OR
  EXISTS (
    SELECT 1
    FROM public.user_roles ur
    WHERE ur.user_id = auth.uid() 
      AND ur.role = 'guest'
  )
);

-- Update the UPDATE policy for bookings
DROP POLICY IF EXISTS "bookings_update" ON public.bookings;
CREATE POLICY "bookings_update"
ON public.bookings
FOR UPDATE
TO authenticated
USING (
  -- Either user has active club membership OR user has guest role (for viewing)
  EXISTS (
    SELECT 1
    FROM public.club_memberships m
    WHERE m.user_id = auth.uid() 
      AND m.club_id = bookings.club_id 
      AND COALESCE(m.is_active, true) = true
  )
  OR
  EXISTS (
    SELECT 1
    FROM public.user_roles ur
    WHERE ur.user_id = auth.uid() 
      AND ur.role = 'guest'
  )
)
WITH CHECK (
  -- Only allow updates if user created the booking, is admin/trainer, or owns the booking
  (auth.uid() = created_by) 
  OR 
  (EXISTS (
    SELECT 1
    FROM public.club_memberships m
    WHERE m.user_id = auth.uid() 
      AND m.club_id = bookings.club_id 
      AND m.role = ANY (ARRAY['admin'::text, 'trainer'::text]) 
      AND COALESCE(m.is_active, true) = true
  )) 
  OR 
  ((get_current_user_display_name() = player_name) OR (get_current_user_display_name() = partner_name))
);

-- Update the DELETE policy for bookings
DROP POLICY IF EXISTS "bookings_delete" ON public.bookings;
CREATE POLICY "bookings_delete"
ON public.bookings
FOR DELETE
TO authenticated
USING (
  -- Allow delete if user created it, is admin/trainer, or is part of the booking
  (auth.uid() = created_by) 
  OR 
  (EXISTS (
    SELECT 1
    FROM public.club_memberships m
    WHERE m.user_id = auth.uid() 
      AND m.club_id = bookings.club_id 
      AND m.role = ANY (ARRAY['admin'::text, 'trainer'::text]) 
      AND COALESCE(m.is_active, true) = true
  )) 
  OR 
  ((get_current_user_display_name() = player_name) OR (get_current_user_display_name() = partner_name))
);