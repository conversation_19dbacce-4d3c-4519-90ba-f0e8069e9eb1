-- Fix critical security vulnerability in system_settings table
-- Remove the dangerous policy that makes system configuration data public

-- Drop the overly permissive policy
DROP POLICY IF EXISTS "Allow all operations on system_settings" ON public.system_settings;

-- Create secure policies that restrict access to administrators only

-- Meta admins can view system settings
CREATE POLICY "Meta admins can view system settings"
ON public.system_settings
FOR SELECT
USING (is_meta_admin(auth.uid()));

-- Meta admins can update system settings
CREATE POLICY "Meta admins can update system settings"
ON public.system_settings
FOR UPDATE
USING (is_meta_admin(auth.uid()))
WITH CHECK (is_meta_admin(auth.uid()));

-- Meta admins can insert system settings
CREATE POLICY "Meta admins can insert system settings"
ON public.system_settings
FOR INSERT
WITH CHECK (is_meta_admin(auth.uid()));

-- Meta admins can delete system settings
CREATE POLICY "Meta admins can delete system settings"
ON public.system_settings
FOR DELETE
USING (is_meta_admin(auth.uid()));

-- Super admins get full access for system administration
CREATE POLICY "Super admins can manage all system settings"
ON public.system_settings
FOR ALL
USING (has_meta_admin_role(auth.uid(), 'SUPER_ADMIN'::meta_admin_role))
WITH CHECK (has_meta_admin_role(auth.uid(), 'SUPER_ADMIN'::meta_admin_role));