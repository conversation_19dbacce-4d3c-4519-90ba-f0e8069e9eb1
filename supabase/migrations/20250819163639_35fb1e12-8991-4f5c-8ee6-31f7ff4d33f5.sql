-- Fix RLS policies for team_members table
-- Drop existing policies and create simpler ones

DROP POLICY IF EXISTS "Club members can view team members" ON team_members;
DROP POLICY IF EXISTS "Club admins can manage team members" ON team_members;

-- Simple policy: allow all operations for authenticated users who are club members
CREATE POLICY "Allow club members to manage team members" 
ON team_members FOR ALL 
USING (auth.uid() IS NOT NULL)
WITH CHECK (auth.uid() IS NOT NULL);

-- Also ensure the table has proper permissions
GRANT ALL ON team_members TO authenticated;
GRANT ALL ON team_members TO anon;