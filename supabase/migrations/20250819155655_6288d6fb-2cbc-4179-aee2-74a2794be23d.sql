-- Add foreign key constraint to team_members table
ALTER TABLE public.team_members 
ADD CONSTRAINT team_members_account_id_fkey 
FOREIGN KEY (account_id) REFERENCES public.accounts(id) ON DELETE CASCADE;

-- Enable RLS on teams table
ALTER TABLE public.teams ENABLE ROW LEVEL SECURITY;

-- Add RLS policies for teams table
CREATE POLICY "Club admins can manage teams" ON public.teams
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.club_memberships cm
      WHERE cm.user_id = auth.uid()
        AND cm.role IN ('admin', 'super_admin')
        AND cm.is_active = true
    )
  );

CREATE POLICY "Club members can view teams" ON public.teams
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.club_memberships cm
      WHERE cm.user_id = auth.uid()
        AND cm.is_active = true
    )
  );

-- Enable RLS on team_members table  
ALTER TABLE public.team_members ENABLE ROW LEVEL SECURITY;

-- Add RLS policies for team_members table
CREATE POLICY "Club admins can manage team members" ON public.team_members
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.club_memberships cm
      WHERE cm.user_id = auth.uid()
        AND cm.role IN ('admin', 'super_admin')
        AND cm.is_active = true
    )
  );

CREATE POLICY "Club members can view team members" ON public.team_members
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.club_memberships cm
      WHERE cm.user_id = auth.uid()
        AND cm.is_active = true
    )
  );