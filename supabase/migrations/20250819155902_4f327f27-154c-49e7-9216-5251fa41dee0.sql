-- Drop existing policies for teams table
DROP POLICY IF EXISTS "Club admins can manage teams" ON public.teams;
DROP POLICY IF EXISTS "Club members can view teams" ON public.teams;

-- Drop existing policies for team_members table  
DROP POLICY IF EXISTS "Club admins can manage team members" ON public.team_members;
DROP POLICY IF EXISTS "Club members can view team members" ON public.team_members;

-- Create club-isolated policies for teams table
CREATE POLICY "Club admins can manage teams in their club" ON public.teams
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.club_memberships cm
      WHERE cm.user_id = auth.uid()
        AND cm.club_id = teams.club_id
        AND cm.role IN ('admin', 'super_admin')
        AND cm.is_active = true
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.club_memberships cm
      WHERE cm.user_id = auth.uid()
        AND cm.club_id = teams.club_id
        AND cm.role IN ('admin', 'super_admin')
        AND cm.is_active = true
    )
  );

CREATE POLICY "Club members can view teams in their club" ON public.teams
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.club_memberships cm
      WHERE cm.user_id = auth.uid()
        AND cm.club_id = teams.club_id
        AND cm.is_active = true
    )
  );

-- Create club-isolated policies for team_members table
CREATE POLICY "Club admins can manage team members in their club" ON public.team_members
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.club_memberships cm
      JOIN public.teams t ON t.id = team_members.team_id
      WHERE cm.user_id = auth.uid()
        AND cm.club_id = t.club_id
        AND cm.role IN ('admin', 'super_admin')
        AND cm.is_active = true
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.club_memberships cm
      JOIN public.teams t ON t.id = team_members.team_id
      WHERE cm.user_id = auth.uid()
        AND cm.club_id = t.club_id
        AND cm.role IN ('admin', 'super_admin')
        AND cm.is_active = true
    )
  );

CREATE POLICY "Club members can view team members in their club" ON public.team_members
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.club_memberships cm
      JOIN public.teams t ON t.id = team_members.team_id
      WHERE cm.user_id = auth.uid()
        AND cm.club_id = t.club_id
        AND cm.is_active = true
    )
  );