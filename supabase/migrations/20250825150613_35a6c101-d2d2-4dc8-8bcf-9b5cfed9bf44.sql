-- Add gender field to all relevant tables and make it required

-- Create gender enum
CREATE TYPE public.gender_type AS ENUM ('male', 'female', 'diverse');

-- Add gender column to profiles table
ALTER TABLE public.profiles 
ADD COLUMN gender public.gender_type;

-- Add gender column to club_memberships table  
ALTER TABLE public.club_memberships 
ADD COLUMN gender public.gender_type;

-- Add gender column to accounts table
ALTER TABLE public.accounts 
ADD COLUMN gender public.gender_type;

-- Update the sync_master_data function to include gender synchronization
CREATE OR REPLACE FUNCTION public.sync_master_data()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $function$
BEGIN
  -- <PERSON><PERSON> profiles aktualisiert wird, synchronisiere zu club_memberships, accounts und waitlist_entries
  IF TG_TABLE_NAME = 'profiles' THEN
    -- Update club_memberships
    UPDATE public.club_memberships 
    SET 
      title = NEW.title,
      first_name = NEW.first_name,
      last_name = NEW.last_name,
      email = NEW.email,
      gender = NEW.gender,
      address_street = NEW.street,
      address_postal_code = NEW.postal_code,
      address_city = NEW.city
    WHERE user_id = NEW.id;
    
    -- Update accounts
    UPDATE public.accounts 
    SET 
      title = NEW.title,
      first_name = NEW.first_name,
      last_name = NEW.last_name,
      email = NEW.email,
      gender = NEW.gender,
      address_street = NEW.street,
      address_house_number = NEW.house_number,
      address_postal_code = NEW.postal_code,
      address_city = NEW.city,
      address_full = CONCAT_WS(' ', NEW.street, NEW.house_number, NEW.postal_code, NEW.city),
      birth_date = NEW.birth_date
    WHERE user_id = NEW.id;
    
    -- Update booking names with title
    UPDATE public.bookings 
    SET 
      player_name = CONCAT_WS(' ', NEW.title, NEW.first_name, NEW.last_name)
    WHERE player_name = CONCAT_WS(' ', OLD.title, OLD.first_name, OLD.last_name) 
      OR created_by = NEW.id;
      
    UPDATE public.bookings 
    SET 
      partner_name = CONCAT_WS(' ', NEW.title, NEW.first_name, NEW.last_name)
    WHERE partner_name = CONCAT_WS(' ', OLD.title, OLD.first_name, OLD.last_name);

    -- Update waitlist_entries names with title
    UPDATE public.waitlist_entries 
    SET 
      player_name = CONCAT_WS(' ', NEW.title, NEW.first_name, NEW.last_name)
    WHERE player_name = CONCAT_WS(' ', OLD.title, OLD.first_name, OLD.last_name) 
      OR user_id = NEW.id;
      
    UPDATE public.waitlist_entries 
    SET 
      partner_name = CONCAT_WS(' ', NEW.title, NEW.first_name, NEW.last_name)
    WHERE partner_name = CONCAT_WS(' ', OLD.title, OLD.first_name, OLD.last_name);
    
  END IF;
  
  -- Wenn club_memberships aktualisiert wird, synchronisiere zurück zu profiles, accounts und waitlist_entries
  IF TG_TABLE_NAME = 'club_memberships' THEN
    -- Update profiles (falls Daten in club_memberships aktueller sind)
    UPDATE public.profiles 
    SET 
      title = NEW.title,
      first_name = NEW.first_name,
      last_name = NEW.last_name,
      email = NEW.email,
      gender = NEW.gender,
      street = NEW.address_street,
      postal_code = NEW.address_postal_code,
      city = NEW.address_city,
      birth_date = NEW.birth_date
    WHERE id = NEW.user_id
      AND (title != NEW.title OR first_name != NEW.first_name OR last_name != NEW.last_name OR email != NEW.email OR gender != NEW.gender);
    
    -- Update accounts
    UPDATE public.accounts 
    SET 
      title = NEW.title,
      first_name = NEW.first_name,
      last_name = NEW.last_name,
      email = NEW.email,
      gender = NEW.gender,
      phone = NEW.phone,
      address_street = NEW.address_street,
      address_postal_code = NEW.address_postal_code,
      address_city = NEW.address_city,
      address_full = CONCAT_WS(' ', NEW.address_street, NEW.address_postal_code, NEW.address_city),
      birth_date = NEW.birth_date
    WHERE user_id = NEW.user_id;
    
    -- Update booking names with title
    UPDATE public.bookings 
    SET 
      player_name = CONCAT_WS(' ', NEW.title, NEW.first_name, NEW.last_name)
    WHERE player_name = CONCAT_WS(' ', OLD.title, OLD.first_name, OLD.last_name) 
      OR created_by = NEW.user_id;
      
    UPDATE public.bookings 
    SET 
      partner_name = CONCAT_WS(' ', NEW.title, NEW.first_name, NEW.last_name)
    WHERE partner_name = CONCAT_WS(' ', OLD.title, OLD.first_name, OLD.last_name);

    -- Update waitlist_entries names with title
    UPDATE public.waitlist_entries 
    SET 
      player_name = CONCAT_WS(' ', NEW.title, NEW.first_name, NEW.last_name)
    WHERE player_name = CONCAT_WS(' ', OLD.title, OLD.first_name, OLD.last_name) 
      OR user_id = NEW.user_id;
      
    UPDATE public.waitlist_entries 
    SET 
      partner_name = CONCAT_WS(' ', NEW.title, NEW.first_name, NEW.last_name)
    WHERE partner_name = CONCAT_WS(' ', OLD.title, OLD.first_name, OLD.last_name);
  END IF;
  
  -- Wenn accounts aktualisiert wird, synchronisiere zu anderen Tabellen
  IF TG_TABLE_NAME = 'accounts' THEN
    UPDATE public.profiles 
    SET 
      title = NEW.title,
      first_name = NEW.first_name,
      last_name = NEW.last_name,
      email = NEW.email,
      gender = NEW.gender,
      street = NEW.address_street,
      house_number = NEW.address_house_number,
      postal_code = NEW.address_postal_code,
      city = NEW.address_city,
      birth_date = NEW.birth_date
    WHERE id = NEW.user_id;
    
    UPDATE public.club_memberships 
    SET 
      title = NEW.title,
      first_name = NEW.first_name,
      last_name = NEW.last_name,
      email = NEW.email,
      gender = NEW.gender,
      phone = NEW.phone,
      address_street = NEW.address_street,
      address_postal_code = NEW.address_postal_code,
      address_city = NEW.address_city,
      birth_date = NEW.birth_date
    WHERE user_id = NEW.user_id;

    -- Update waitlist_entries names with title
    UPDATE public.waitlist_entries 
    SET 
      player_name = CONCAT_WS(' ', NEW.title, NEW.first_name, NEW.last_name)
    WHERE user_id = NEW.user_id;
  END IF;
  
  RETURN NEW;
END;
$function$;

-- Update handle_new_user function to include gender from registration
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $function$
DECLARE
  target_club_id uuid;
  club_slug_from_meta text;
  club_id_from_meta text;
  account_type_from_meta text;
  gender_from_meta text;
BEGIN
  -- Add some logging to help debug
  RAISE LOG 'Creating profile for user: %', NEW.id;
  
  -- Extract account type, club information and gender from metadata
  account_type_from_meta := NEW.raw_user_meta_data ->> 'account_type';
  club_slug_from_meta := NEW.raw_user_meta_data ->> 'club_slug';
  club_id_from_meta := NEW.raw_user_meta_data ->> 'club_id';
  gender_from_meta := NEW.raw_user_meta_data ->> 'gender';
  
  RAISE LOG 'Account type: %, Club slug: %, Club ID: %, Gender: %', account_type_from_meta, club_slug_from_meta, club_id_from_meta, gender_from_meta;
  
  -- Insert profile first with all required fields including gender
  INSERT INTO public.profiles (
    id, 
    first_name, 
    last_name, 
    birth_date,
    street,
    house_number,
    postal_code,
    city,
    email, 
    gender,
    membership_category
  )
  VALUES (
    NEW.id, 
    COALESCE(NEW.raw_user_meta_data ->> 'first_name', 'Unknown'),
    COALESCE(NEW.raw_user_meta_data ->> 'last_name', 'Unknown'),
    CASE 
      WHEN NEW.raw_user_meta_data ->> 'birth_date' IS NOT NULL 
      THEN (NEW.raw_user_meta_data ->> 'birth_date')::date
      ELSE CURRENT_DATE
    END,
    COALESCE(NEW.raw_user_meta_data ->> 'street', ''),
    COALESCE(NEW.raw_user_meta_data ->> 'house_number', ''),
    COALESCE(NEW.raw_user_meta_data ->> 'postal_code', ''),
    COALESCE(NEW.raw_user_meta_data ->> 'city', ''),
    NEW.email,
    CASE 
      WHEN gender_from_meta = 'male' THEN 'male'::public.gender_type
      WHEN gender_from_meta = 'female' THEN 'female'::public.gender_type  
      WHEN gender_from_meta = 'diverse' THEN 'diverse'::public.gender_type
      ELSE 'diverse'::public.gender_type  -- Default fallback
    END,
    COALESCE((NEW.raw_user_meta_data ->> 'membership_category')::public.membership_category, 'Erwachsener'::public.membership_category)
  );
  
  -- Assign appropriate role based on account type
  IF account_type_from_meta = 'guest' THEN
    INSERT INTO public.user_roles (user_id, role)
    VALUES (NEW.id, 'guest');
    RAISE LOG 'Assigned guest role to user: %', NEW.id;
  ELSE
    INSERT INTO public.user_roles (user_id, role)
    VALUES (NEW.id, 'mitglied');
    RAISE LOG 'Assigned mitglied role to user: %', NEW.id;
  END IF;
  
  -- Create club membership for BOTH guests and members if club info is provided
  IF club_id_from_meta IS NOT NULL OR club_slug_from_meta IS NOT NULL THEN
    -- Try to find the club and create membership
    IF club_id_from_meta IS NOT NULL THEN
      target_club_id := club_id_from_meta::uuid;
      RAISE LOG 'Found club ID from metadata: %', target_club_id;
    ELSIF club_slug_from_meta IS NOT NULL THEN
      SELECT id INTO target_club_id
      FROM public.clubs 
      WHERE slug = club_slug_from_meta AND is_active = true
      LIMIT 1;
      RAISE LOG 'Found club by slug %: %', club_slug_from_meta, target_club_id;
    END IF;
    
    -- Create club membership if we found a valid club (for both guests AND members)
    IF target_club_id IS NOT NULL THEN
      RAISE LOG 'Creating club membership for user % in club %', NEW.id, target_club_id;
      
      INSERT INTO public.club_memberships (
        user_id,
        club_id,
        first_name,
        last_name,
        email,
        phone,
        birth_date,
        gender,
        address_street,
        address_postal_code,
        address_city,
        membership_type,
        account_type,
        role
      )
      VALUES (
        NEW.id,
        target_club_id,
        COALESCE(NEW.raw_user_meta_data ->> 'first_name', 'Unknown'),
        COALESCE(NEW.raw_user_meta_data ->> 'last_name', 'Unknown'),
        NEW.email,
        COALESCE(NEW.raw_user_meta_data ->> 'phone', ''),
        CASE 
          WHEN NEW.raw_user_meta_data ->> 'birth_date' IS NOT NULL 
          THEN (NEW.raw_user_meta_data ->> 'birth_date')::date
          ELSE NULL
        END,
        CASE 
          WHEN gender_from_meta = 'male' THEN 'male'::public.gender_type
          WHEN gender_from_meta = 'female' THEN 'female'::public.gender_type  
          WHEN gender_from_meta = 'diverse' THEN 'diverse'::public.gender_type
          ELSE 'diverse'::public.gender_type  -- Default fallback
        END,
        COALESCE(NEW.raw_user_meta_data ->> 'street', ''),
        COALESCE(NEW.raw_user_meta_data ->> 'postal_code', ''),
        COALESCE(NEW.raw_user_meta_data ->> 'city', ''),
        COALESCE(NEW.raw_user_meta_data ->> 'membership_category', 'Erwachsener'),
        COALESCE(NEW.raw_user_meta_data ->> 'account_type', 'member'),
        CASE 
          WHEN account_type_from_meta = 'guest' THEN 'guest'
          ELSE 'member'
        END
      );
      
      RAISE LOG 'Club membership created successfully for user: % in club: %', NEW.id, target_club_id;
    ELSE
      RAISE LOG 'No valid club found for user: %', NEW.id;
    END IF;
  ELSE
    RAISE LOG 'No club info provided for user: %', NEW.id;
  END IF;
  
  RAISE LOG 'Profile and memberships created successfully for user: %', NEW.id;
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    RAISE LOG 'Error creating profile/membership for user %: %', NEW.id, SQLERRM;
    RAISE;
END;
$function$;