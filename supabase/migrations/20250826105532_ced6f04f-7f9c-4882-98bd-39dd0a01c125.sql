-- Create club_roles table for club-specific roles
CREATE TABLE public.club_roles (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  club_id UUID NOT NULL REFERENCES public.clubs(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  display_name TEXT NOT NULL,
  description TEXT,
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  created_by UUID REFERENCES auth.users(id),
  UNIQUE(club_id, name)
);

-- Create club_role_assignments table for assigning club roles to users
CREATE TABLE public.club_role_assignments (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  club_id UUID NOT NULL REFERENCES public.clubs(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  club_role_id UUID NOT NULL REFERENCES public.club_roles(id) ON DELETE CASCADE,
  assigned_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  assigned_by UUID REFERENCES auth.users(id),
  is_active BOOLEAN NOT NULL DEFAULT true,
  UNIQUE(club_id, user_id, club_role_id)
);

-- Enable RLS on both tables
ALTER TABLE public.club_roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.club_role_assignments ENABLE ROW LEVEL SECURITY;

-- RLS Policies for club_roles
CREATE POLICY "Club admins can manage their club roles"
ON public.club_roles
FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid()
      AND cm.club_id = club_roles.club_id
      AND cm.role = ANY(ARRAY['admin', 'super_admin'])
      AND cm.is_active = true
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid()
      AND cm.club_id = club_roles.club_id
      AND cm.role = ANY(ARRAY['admin', 'super_admin'])
      AND cm.is_active = true
  )
);

CREATE POLICY "Club members can view active club roles"
ON public.club_roles
FOR SELECT
USING (
  is_active = true
  AND EXISTS (
    SELECT 1 FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid()
      AND cm.club_id = club_roles.club_id
      AND cm.is_active = true
  )
);

CREATE POLICY "Meta admins can manage all club roles"
ON public.club_roles
FOR ALL
USING (is_meta_admin(auth.uid()))
WITH CHECK (is_meta_admin(auth.uid()));

-- RLS Policies for club_role_assignments
CREATE POLICY "Club admins can manage their club role assignments"
ON public.club_role_assignments
FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid()
      AND cm.club_id = club_role_assignments.club_id
      AND cm.role = ANY(ARRAY['admin', 'super_admin'])
      AND cm.is_active = true
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid()
      AND cm.club_id = club_role_assignments.club_id
      AND cm.role = ANY(ARRAY['admin', 'super_admin'])
      AND cm.is_active = true
  )
);

CREATE POLICY "Users can view their own club role assignments"
ON public.club_role_assignments
FOR SELECT
USING (user_id = auth.uid());

CREATE POLICY "Club members can view role assignments in their club"
ON public.club_role_assignments
FOR SELECT
USING (
  is_active = true
  AND EXISTS (
    SELECT 1 FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid()
      AND cm.club_id = club_role_assignments.club_id
      AND cm.is_active = true
  )
);

CREATE POLICY "Meta admins can manage all club role assignments"
ON public.club_role_assignments
FOR ALL
USING (is_meta_admin(auth.uid()))
WITH CHECK (is_meta_admin(auth.uid()));

-- Create trigger for updating updated_at timestamp
CREATE TRIGGER update_club_roles_updated_at
  BEFORE UPDATE ON public.club_roles
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

-- Create indexes for better performance
CREATE INDEX idx_club_roles_club_id ON public.club_roles(club_id);
CREATE INDEX idx_club_roles_active ON public.club_roles(club_id, is_active) WHERE is_active = true;
CREATE INDEX idx_club_role_assignments_club_id ON public.club_role_assignments(club_id);
CREATE INDEX idx_club_role_assignments_user_id ON public.club_role_assignments(user_id);
CREATE INDEX idx_club_role_assignments_active ON public.club_role_assignments(club_id, is_active) WHERE is_active = true;