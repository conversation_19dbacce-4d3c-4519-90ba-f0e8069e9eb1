-- Korrigiere die Arbeitszeit-Berechnung durch Update der completed_hours basierend auf tatsächlichen Assignments
UPDATE public.member_work_service_targets 
SET completed_hours = (
  SELECT COALESCE(SUM(aa.hours_credited), 0)
  FROM public.activity_assignments aa
  WHERE aa.member_id = member_work_service_targets.user_id
    AND aa.club_id = member_work_service_targets.club_id
    AND aa.status = 'completed'
    AND EXTRACT(YEAR FROM aa.assigned_at) = member_work_service_targets.year
),
updated_at = now()
WHERE club_id = '4f89d335-24a5-435d-b8ad-0d4abd723a72';