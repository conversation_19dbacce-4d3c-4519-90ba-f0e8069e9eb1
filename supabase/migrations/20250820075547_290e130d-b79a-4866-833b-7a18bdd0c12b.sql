-- Erweitere die Name-Synchronisation um alle Stammdaten
-- <PERSON><PERSON><PERSON> umfassende Synchronisationsfunktion für alle Stammdaten

-- Funktion zur Synchronisation aller Stammdaten zwischen profiles, club_memberships und accounts
CREATE OR REPLACE FUNCTION public.sync_master_data()
RETURNS TRIGGER AS $$
BEGIN
  -- <PERSON><PERSON> profiles aktualisiert wird, synchronisiere zu club_memberships und accounts
  IF TG_TABLE_NAME = 'profiles' THEN
    -- Update club_memberships
    UPDATE public.club_memberships 
    SET 
      first_name = NEW.first_name,
      last_name = NEW.last_name,
      email = NEW.email,
      address_street = NEW.street,
      address_postal_code = NEW.postal_code,
      address_city = NEW.city
    WHERE user_id = NEW.id;
    
    -- Update accounts
    UPDATE public.accounts 
    SET 
      first_name = NEW.first_name,
      last_name = NEW.last_name,
      email = NEW.email,
      address_street = NEW.street,
      address_house_number = NEW.house_number,
      address_postal_code = NEW.postal_code,
      address_city = NEW.city,
      address_full = CONCAT_WS(' ', NEW.street, NEW.house_number, NEW.postal_code, NEW.city),
      birth_date = NEW.birth_date
    WHERE user_id = NEW.id;
    
    -- Update booking names (erweitert um vollständige Namen)
    UPDATE public.bookings 
    SET 
      player_name = CONCAT(NEW.first_name, ' ', NEW.last_name)
    WHERE player_name = CONCAT(OLD.first_name, ' ', OLD.last_name) 
      OR created_by = NEW.id;
      
    UPDATE public.bookings 
    SET 
      partner_name = CONCAT(NEW.first_name, ' ', NEW.last_name)
    WHERE partner_name = CONCAT(OLD.first_name, ' ', OLD.last_name);
    
  END IF;
  
  -- Wenn club_memberships aktualisiert wird, synchronisiere zurück zu profiles und accounts
  IF TG_TABLE_NAME = 'club_memberships' THEN
    -- Update profiles (falls Daten in club_memberships aktueller sind)
    UPDATE public.profiles 
    SET 
      first_name = NEW.first_name,
      last_name = NEW.last_name,
      email = NEW.email,
      street = NEW.address_street,
      postal_code = NEW.address_postal_code,
      city = NEW.address_city,
      birth_date = NEW.birth_date
    WHERE id = NEW.user_id
      AND (first_name != NEW.first_name OR last_name != NEW.last_name OR email != NEW.email);
    
    -- Update accounts
    UPDATE public.accounts 
    SET 
      first_name = NEW.first_name,
      last_name = NEW.last_name,
      email = NEW.email,
      phone = NEW.phone,
      address_street = NEW.address_street,
      address_postal_code = NEW.address_postal_code,
      address_city = NEW.address_city,
      address_full = CONCAT_WS(' ', NEW.address_street, NEW.address_postal_code, NEW.address_city),
      birth_date = NEW.birth_date
    WHERE user_id = NEW.user_id;
    
    -- Update booking names
    UPDATE public.bookings 
    SET 
      player_name = CONCAT(NEW.first_name, ' ', NEW.last_name)
    WHERE player_name = CONCAT(OLD.first_name, ' ', OLD.last_name) 
      OR created_by = NEW.user_id;
      
    UPDATE public.bookings 
    SET 
      partner_name = CONCAT(NEW.first_name, ' ', NEW.last_name)
    WHERE partner_name = CONCAT(OLD.first_name, ' ', OLD.last_name);
  END IF;
  
  -- Wenn accounts aktualisiert wird, synchronisiere zu anderen Tabellen
  IF TG_TABLE_NAME = 'accounts' THEN
    UPDATE public.profiles 
    SET 
      first_name = NEW.first_name,
      last_name = NEW.last_name,
      email = NEW.email,
      street = NEW.address_street,
      house_number = NEW.address_house_number,
      postal_code = NEW.address_postal_code,
      city = NEW.address_city,
      birth_date = NEW.birth_date
    WHERE id = NEW.user_id;
    
    UPDATE public.club_memberships 
    SET 
      first_name = NEW.first_name,
      last_name = NEW.last_name,
      email = NEW.email,
      phone = NEW.phone,
      address_street = NEW.address_street,
      address_postal_code = NEW.address_postal_code,
      address_city = NEW.address_city,
      birth_date = NEW.birth_date
    WHERE user_id = NEW.user_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Lösche alte Trigger falls vorhanden
DROP TRIGGER IF EXISTS sync_profile_updates ON public.profiles;
DROP TRIGGER IF EXISTS sync_club_membership_updates ON public.club_memberships;
DROP TRIGGER IF EXISTS sync_account_updates ON public.accounts;

-- Erstelle neue umfassende Trigger für alle Stammdaten
CREATE TRIGGER sync_profile_updates
  AFTER UPDATE ON public.profiles
  FOR EACH ROW EXECUTE FUNCTION public.sync_master_data();

CREATE TRIGGER sync_club_membership_updates  
  AFTER UPDATE ON public.club_memberships
  FOR EACH ROW EXECUTE FUNCTION public.sync_master_data();

CREATE TRIGGER sync_account_updates
  AFTER UPDATE ON public.accounts  
  FOR EACH ROW EXECUTE FUNCTION public.sync_master_data();

-- Einmalige Synchronisation aller bestehenden Daten
-- Synchronisiere von profiles zu anderen Tabellen
UPDATE public.club_memberships cm
SET 
  first_name = p.first_name,
  last_name = p.last_name,
  email = p.email,
  address_street = p.street,
  address_postal_code = p.postal_code,
  address_city = p.city,
  birth_date = p.birth_date
FROM public.profiles p
WHERE cm.user_id = p.id
  AND (cm.first_name != p.first_name OR cm.last_name != p.last_name OR cm.email != p.email);

UPDATE public.accounts a
SET 
  first_name = p.first_name,
  last_name = p.last_name,
  email = p.email,
  address_street = p.street,
  address_house_number = p.house_number,
  address_postal_code = p.postal_code,
  address_city = p.city,
  address_full = CONCAT_WS(' ', p.street, p.house_number, p.postal_code, p.city),
  birth_date = p.birth_date
FROM public.profiles p
WHERE a.user_id = p.id;

-- Log für Überwachung
CREATE TABLE IF NOT EXISTS public.master_data_sync_log (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  table_name text NOT NULL,
  record_id uuid NOT NULL,
  sync_type text NOT NULL,
  synced_fields jsonb,
  created_at timestamp with time zone DEFAULT now()
);

-- RLS für Sync Log
ALTER TABLE public.master_data_sync_log ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Meta admins can view sync log" ON public.master_data_sync_log
  FOR SELECT USING (is_meta_admin(auth.uid()));

CREATE POLICY "System can insert sync log" ON public.master_data_sync_log  
  FOR INSERT WITH CHECK (true);