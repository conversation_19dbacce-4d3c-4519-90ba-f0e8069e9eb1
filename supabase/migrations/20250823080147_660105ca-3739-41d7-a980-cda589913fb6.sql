-- Update tournament_matches table to be consistent with naming
-- The table already has estimated_duration_minutes, but we need to ensure it's properly used

-- Add an index for better performance on tournament queries
CREATE INDEX IF NOT EXISTS idx_tournament_matches_tournament_id_round 
ON tournament_matches(tournament_id, round_number, match_number);

-- Add a trigger to auto-update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_tournament_matches_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_update_tournament_matches_updated_at ON tournament_matches;
CREATE TRIGGER trigger_update_tournament_matches_updated_at
  BEFORE UPDATE ON tournament_matches
  FOR EACH ROW
  EXECUTE FUNCTION update_tournament_matches_updated_at();