-- Extend clubs table with work service configuration and exemption settings
ALTER TABLE public.clubs ADD COLUMN IF NOT EXISTS work_service_enabled boolean DEFAULT false;
ALTER TABLE public.clubs ADD COLUMN IF NOT EXISTS work_service_minimum_hours_enabled boolean DEFAULT false;
ALTER TABLE public.clubs ADD COLUMN IF NOT EXISTS default_annual_work_hours integer DEFAULT 20;
ALTER TABLE public.clubs ADD COLUMN IF NOT EXISTS work_service_min_age integer;
ALTER TABLE public.clubs ADD COLUMN IF NOT EXISTS work_service_max_age integer;
ALTER TABLE public.clubs ADD COLUMN IF NOT EXISTS work_service_exempt_membership_types text[];
ALTER TABLE public.clubs ADD COLUMN IF NOT EXISTS work_service_exempt_roles text[];

-- Create member_work_service_targets table for tracking work hour targets and exemptions
CREATE TABLE IF NOT EXISTS public.member_work_service_targets (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  club_id uuid NOT NULL,
  user_id uuid NOT NULL,
  year integer NOT NULL,
  target_hours integer NOT NULL DEFAULT 0,
  completed_hours numeric(5,2) NOT NULL DEFAULT 0,
  is_manually_exempt boolean NOT NULL DEFAULT false,
  exemption_reason text,
  is_auto_exempt boolean NOT NULL DEFAULT false,
  auto_exemption_reason text,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  UNIQUE(club_id, user_id, year)
);

-- Enable RLS on member_work_service_targets
ALTER TABLE public.member_work_service_targets ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for member_work_service_targets
CREATE POLICY "Club members can view work targets in their club"
ON public.member_work_service_targets
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid()
      AND cm.club_id = member_work_service_targets.club_id
      AND cm.is_active = true
  )
);

CREATE POLICY "Club admins can manage work targets"
ON public.member_work_service_targets
FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid()
      AND cm.club_id = member_work_service_targets.club_id
      AND cm.role IN ('admin', 'super_admin')
      AND cm.is_active = true
  )
);

CREATE POLICY "Users can view their own work targets"
ON public.member_work_service_targets
FOR SELECT
USING (user_id = auth.uid());

-- Create trigger function to update member work targets when work logs change
CREATE OR REPLACE FUNCTION public.update_member_work_targets()
RETURNS TRIGGER AS $$
DECLARE
  current_year integer;
  club_id_var uuid;
  total_hours numeric;
BEGIN
  -- Get the current year and club_id from the work log
  current_year := EXTRACT(YEAR FROM 
    CASE 
      WHEN TG_OP = 'DELETE' THEN OLD.date
      ELSE NEW.date
    END
  );
  
  -- Get club_id from related court
  SELECT c.club_id INTO club_id_var
  FROM public.work_logs wl
  JOIN public.courts c ON true  -- We'll need to establish this relationship
  WHERE wl.id = CASE WHEN TG_OP = 'DELETE' THEN OLD.work_log_id ELSE NEW.work_log_id END
  LIMIT 1;
  
  -- If we can't find club_id, try a different approach
  IF club_id_var IS NULL THEN
    -- Get the first active club for this user (fallback)
    SELECT cm.club_id INTO club_id_var
    FROM public.club_memberships cm
    WHERE cm.user_id = CASE WHEN TG_OP = 'DELETE' THEN OLD.member_id ELSE NEW.member_id END
      AND cm.is_active = true
    LIMIT 1;
  END IF;
  
  IF club_id_var IS NOT NULL THEN
    -- Calculate total hours for this member in the current year
    SELECT COALESCE(SUM(wl.duration_hours), 0) INTO total_hours
    FROM public.work_log_assignments wla
    JOIN public.work_logs wl ON wl.id = wla.work_log_id
    WHERE wla.member_id = CASE WHEN TG_OP = 'DELETE' THEN OLD.member_id ELSE NEW.member_id END
      AND EXTRACT(YEAR FROM wl.date) = current_year;
    
    -- Insert or update the member's work target record
    INSERT INTO public.member_work_service_targets (
      club_id, user_id, year, completed_hours
    ) VALUES (
      club_id_var,
      CASE WHEN TG_OP = 'DELETE' THEN OLD.member_id ELSE NEW.member_id END,
      current_year,
      total_hours
    )
    ON CONFLICT (club_id, user_id, year)
    DO UPDATE SET
      completed_hours = total_hours,
      updated_at = now();
  END IF;
  
  RETURN CASE WHEN TG_OP = 'DELETE' THEN OLD ELSE NEW END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for work log assignments
DROP TRIGGER IF EXISTS update_work_targets_trigger ON public.work_log_assignments;
CREATE TRIGGER update_work_targets_trigger
  AFTER INSERT OR UPDATE OR DELETE ON public.work_log_assignments
  FOR EACH ROW
  EXECUTE FUNCTION public.update_member_work_targets();

-- Add updated_at trigger for member_work_service_targets
CREATE TRIGGER update_member_work_service_targets_updated_at
  BEFORE UPDATE ON public.member_work_service_targets
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();