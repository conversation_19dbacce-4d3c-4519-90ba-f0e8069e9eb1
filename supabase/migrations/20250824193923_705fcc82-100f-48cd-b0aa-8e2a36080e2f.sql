-- Fix the waitlist position trigger to handle all scenarios
DROP TRIGGER IF EXISTS update_waitlist_positions ON public.waitlist_entries;

CREATE OR REPLACE FUNCTION public.update_waitlist_position()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = 'public'
AS $$
BEGIN
  -- Handle INSERT operations
  IF TG_OP = 'INSERT' THEN
    NEW.position := COALESCE(
      (SELECT MAX(position) + 1 
       FROM public.waitlist_entries 
       WHERE club_id = NEW.club_id 
         AND preferred_date = NEW.preferred_date
         AND status = 'waiting'), 
      1
    );
    RETURN NEW;
  END IF;
  
  -- Handle UPDATE operations (status changes)
  IF TG_OP = 'UPDATE' THEN
    -- If status changed from 'waiting' to something else, resequence positions
    IF OLD.status = 'waiting' AND NEW.status != 'waiting' THEN
      -- Update positions for remaining entries in the same club/date
      WITH ranked_entries AS (
        SELECT id, ROW_NUMBER() OVER (ORDER BY created_at) as new_position
        FROM public.waitlist_entries
        WHERE club_id = OLD.club_id
          AND preferred_date = OLD.preferred_date
          AND status = 'waiting'
          AND id != OLD.id  -- Exclude the entry being updated
      )
      UPDATE public.waitlist_entries
      SET position = ranked_entries.new_position
      FROM ranked_entries
      WHERE waitlist_entries.id = ranked_entries.id;
    END IF;
    
    RETURN NEW;
  END IF;
  
  -- Handle DELETE operations
  IF TG_OP = 'DELETE' THEN
    -- If deleting a waiting entry, resequence positions
    IF OLD.status = 'waiting' THEN
      WITH ranked_entries AS (
        SELECT id, ROW_NUMBER() OVER (ORDER BY created_at) as new_position
        FROM public.waitlist_entries
        WHERE club_id = OLD.club_id
          AND preferred_date = OLD.preferred_date
          AND status = 'waiting'
      )
      UPDATE public.waitlist_entries
      SET position = ranked_entries.new_position
      FROM ranked_entries
      WHERE waitlist_entries.id = ranked_entries.id;
    END IF;
    
    RETURN OLD;
  END IF;
  
  RETURN NULL;
END;
$$;

-- Create comprehensive trigger for all operations
CREATE TRIGGER update_waitlist_positions
  AFTER INSERT OR UPDATE OR DELETE ON public.waitlist_entries
  FOR EACH ROW EXECUTE FUNCTION public.update_waitlist_position();