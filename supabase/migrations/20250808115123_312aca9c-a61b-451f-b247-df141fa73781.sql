-- Update existing accounts with old membership types to new German types
UPDATE accounts 
SET membership_type = CASE 
  WHEN membership_type = 'Child' THEN 'Kinder'
  WHEN membership_type = 'Youth' THEN 'Jugendliche'
  WHEN membership_type = 'Student' THEN 'Studenten'
  WHEN membership_type = 'Adult' THEN 'Erwachsene'
  WHEN membership_type = 'Couple' THEN 'Paare'
  WHEN membership_type = 'Family' THEN 'Familien'
  ELSE membership_type
END
WHERE membership_type IN ('Child', 'Youth', 'Student', 'Adult', 'Couple', 'Family');

-- Also update the members table with the same mapping
UPDATE members 
SET membership_type = CASE 
  WHEN membership_type = 'Child' THEN 'Kinder'
  WHEN membership_type = 'Youth' THEN 'Jugendliche'  
  WHEN membership_type = 'Student' THEN 'Studenten'
  WHEN membership_type = 'Adult' THEN 'Erwachsene'
  WHEN membership_type = 'Couple' THEN 'Paare'
  WHEN membership_type = 'Family' THEN 'Familien'
  ELSE membership_type
END
WHERE membership_type IN ('Child', 'Youth', 'Student', 'Adult', 'Couple', 'Family');