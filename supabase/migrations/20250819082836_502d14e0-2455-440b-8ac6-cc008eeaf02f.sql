-- Setze search_path für bekannte Security Definer Funktionen
-- Behebe die search_path Warnungen für bestehende Funktionen

-- Funktion is_club_admin_for_club wurde bereits behoben

-- Überprüfe und behebe andere bekannte Funktionen
ALTER FUNCTION public.account_belongs_to_user(uuid, uuid) SET search_path = 'public';
ALTER FUNCTION public.is_billing_member_for_group(uuid, uuid) SET search_path = 'public';
ALTER FUNCTION public.get_current_club_id() SET search_path = 'public';
ALTER FUNCTION public.has_meta_admin_role(uuid, meta_admin_role) SET search_path = 'public';
ALTER FUNCTION public.is_meta_admin(uuid) SET search_path = 'public';
ALTER FUNCTION public.grant_super_admin(text) SET search_path = 'public';
ALTER FUNCTION public.get_user_default_landing_page(uuid) SET search_path = 'public';
ALTER FUNCTION public.has_permission(uuid, app_permission) SET search_path = 'public';
ALTER FUNCTION public.get_user_permissions(uuid) SET search_path = 'public';
ALTER FUNCTION public.is_club_admin(uuid, uuid) SET search_path = 'public';
ALTER FUNCTION public.get_user_roles(uuid) SET search_path = 'public';
ALTER FUNCTION public.has_role(uuid, app_role) SET search_path = 'public';
ALTER FUNCTION public.has_any_admin_role(uuid) SET search_path = 'public';
ALTER FUNCTION public.effective_rights(uuid) SET search_path = 'public';