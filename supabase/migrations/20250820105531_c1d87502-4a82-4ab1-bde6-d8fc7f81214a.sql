-- Add RLS policy to allow club admins to update their own club settings
CREATE POLICY "Club admins can update their club settings"
ON public.clubs
FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid()
      AND cm.club_id = clubs.id
      AND cm.role = ANY (ARRAY['admin'::text, 'super_admin'::text])
      AND cm.is_active = true
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid()
      AND cm.club_id = clubs.id
      AND cm.role = ANY (ARRAY['admin'::text, 'super_admin'::text])
      AND cm.is_active = true
  )
);