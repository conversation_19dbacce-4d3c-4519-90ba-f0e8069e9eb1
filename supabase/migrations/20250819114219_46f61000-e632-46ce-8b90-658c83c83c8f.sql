-- Multi-Tenancy & Club Isolation Migration
-- Ensure strict club-based RLS for courts and bookings

-- Drop all existing permissive policies for courts and bookings
DO $$ 
DECLARE 
    p record;
BEGIN 
    FOR p IN SELECT policyname, schemaname, tablename 
             FROM pg_policies 
             WHERE schemaname='public' AND tablename IN ('courts','bookings') 
    LOOP 
        EXECUTE format('DROP POLICY IF EXISTS %I ON public.%I;', p.policyname, p.tablename);
    END LOOP;
END$$;

-- Enable and force RLS on courts and bookings tables
ALTER TABLE public.courts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.courts FORCE ROW LEVEL SECURITY;
ALTER TABLE public.bookings ENABLE ROW LEVEL SECURITY;  
ALTER TABLE public.bookings FORCE ROW LEVEL SECURITY;

-- COURTS POLICIES: Only club members can see their club's courts
CREATE POLICY courts_select ON public.courts 
FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM public.club_memberships m 
        WHERE m.user_id = auth.uid() 
        AND m.club_id = public.courts.club_id 
        AND COALESCE(m.is_active, true) = true
    )
);

-- Only club admins/trainers can modify courts  
CREATE POLICY courts_write ON public.courts 
FOR ALL USING (
    EXISTS (
        SELECT 1 FROM public.club_memberships m 
        WHERE m.user_id = auth.uid() 
        AND m.club_id = public.courts.club_id 
        AND m.role IN ('admin', 'trainer')
        AND COALESCE(m.is_active, true) = true
    )
) WITH CHECK (
    EXISTS (
        SELECT 1 FROM public.club_memberships m 
        WHERE m.user_id = auth.uid() 
        AND m.club_id = public.courts.club_id 
        AND m.role IN ('admin', 'trainer') 
        AND COALESCE(m.is_active, true) = true
    )
);

-- BOOKINGS POLICIES: Only club members can see their club's bookings
CREATE POLICY bookings_select ON public.bookings 
FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM public.club_memberships m 
        WHERE m.user_id = auth.uid() 
        AND m.club_id = public.bookings.club_id 
        AND COALESCE(m.is_active, true) = true
    )
);

-- Club members can create bookings for their club
CREATE POLICY bookings_insert ON public.bookings 
FOR INSERT WITH CHECK (
    EXISTS (
        SELECT 1 FROM public.club_memberships m 
        WHERE m.user_id = auth.uid() 
        AND m.club_id = public.bookings.club_id 
        AND COALESCE(m.is_active, true) = true
    )
);

-- Club members can update bookings, admins/trainers can modify all
CREATE POLICY bookings_update ON public.bookings 
FOR UPDATE USING (
    EXISTS (
        SELECT 1 FROM public.club_memberships m 
        WHERE m.user_id = auth.uid() 
        AND m.club_id = public.bookings.club_id 
        AND COALESCE(m.is_active, true) = true
    )
) WITH CHECK (
    -- Either booking creator or admin/trainer
    (auth.uid() = public.bookings.created_by) OR 
    EXISTS (
        SELECT 1 FROM public.club_memberships m 
        WHERE m.user_id = auth.uid() 
        AND m.club_id = public.bookings.club_id 
        AND m.role IN ('admin', 'trainer')
        AND COALESCE(m.is_active, true) = true
    )
);

-- Only booking creator or admin/trainer can delete bookings
CREATE POLICY bookings_delete ON public.bookings 
FOR DELETE USING (
    (auth.uid() = public.bookings.created_by) OR 
    EXISTS (
        SELECT 1 FROM public.club_memberships m 
        WHERE m.user_id = auth.uid() 
        AND m.club_id = public.bookings.club_id 
        AND m.role IN ('admin', 'trainer')
        AND COALESCE(m.is_active, true) = true
    )
);

-- Performance indexes for club-based queries
CREATE INDEX IF NOT EXISTS idx_courts_club ON public.courts (club_id);
CREATE INDEX IF NOT EXISTS idx_bookings_club_start ON public.bookings(club_id, start_time);
CREATE INDEX IF NOT EXISTS idx_bookings_club_date ON public.bookings(club_id, booking_date);
CREATE INDEX IF NOT EXISTS idx_club_memberships_user_club ON public.club_memberships(user_id, club_id, is_active);

-- Ensure club_id is not null (data integrity)
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM public.courts WHERE club_id IS NULL) THEN
        ALTER TABLE public.courts ALTER COLUMN club_id SET NOT NULL;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM public.bookings WHERE club_id IS NULL) THEN  
        ALTER TABLE public.bookings ALTER COLUMN club_id SET NOT NULL;
    END IF;
END$$;