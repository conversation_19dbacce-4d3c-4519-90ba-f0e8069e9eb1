-- Update club_memberships with missing data from auth.users and profiles
UPDATE club_memberships cm
SET 
  email = COALESCE(cm.email, u.email, p.email),
  first_name = COALESCE(cm.first_name, p.first_name, u.raw_user_meta_data ->> 'first_name'),
  last_name = COALESCE(cm.last_name, p.last_name, u.raw_user_meta_data ->> 'last_name'),
  phone = COALESCE(cm.phone, u.raw_user_meta_data ->> 'phone'),
  address_street = COALESCE(cm.address_street, p.street, u.raw_user_meta_data ->> 'street'),
  address_postal_code = COALESCE(cm.address_postal_code, p.postal_code, u.raw_user_meta_data ->> 'postal_code'),
  address_city = COALESCE(cm.address_city, p.city, u.raw_user_meta_data ->> 'city'),
  birth_date = COALESCE(cm.birth_date, p.birth_date, (u.raw_user_meta_data ->> 'birth_date')::date)
FROM auth.users u
LEFT JOIN profiles p ON u.id = p.id
WHERE cm.user_id = u.id
  AND (
    cm.email IS NULL OR 
    cm.first_name IS NULL OR 
    cm.last_name IS NULL
  );