-- Update the handle_new_user function to NOT create club memberships for guests
CREATE OR REPLACE FUNCTION public.handle_new_user()
<PERSON><PERSON><PERSON><PERSON> trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $function$
DECLARE
  target_club_id uuid;
  club_slug_from_meta text;
  club_id_from_meta text;
  account_type_from_meta text;
BEGIN
  -- Add some logging to help debug
  RAISE LOG 'Creating profile for user: %', NEW.id;
  
  -- Extract account type and club information from metadata
  account_type_from_meta := NEW.raw_user_meta_data ->> 'account_type';
  club_slug_from_meta := NEW.raw_user_meta_data ->> 'club_slug';
  club_id_from_meta := NEW.raw_user_meta_data ->> 'club_id';
  
  RAISE LOG 'Account type: %, Club slug: %, Club ID: %', account_type_from_meta, club_slug_from_meta, club_id_from_meta;
  
  -- Insert profile first with all required fields
  INSERT INTO public.profiles (
    id, 
    first_name, 
    last_name, 
    birth_date,
    street,
    house_number,
    postal_code,
    city,
    email, 
    membership_category
  )
  VALUES (
    NEW.id, 
    COALESCE(NEW.raw_user_meta_data ->> 'first_name', 'Unknown'),
    COALESCE(NEW.raw_user_meta_data ->> 'last_name', 'Unknown'),
    CASE 
      WHEN NEW.raw_user_meta_data ->> 'birth_date' IS NOT NULL 
      THEN (NEW.raw_user_meta_data ->> 'birth_date')::date
      ELSE CURRENT_DATE
    END,
    COALESCE(NEW.raw_user_meta_data ->> 'street', ''),
    COALESCE(NEW.raw_user_meta_data ->> 'house_number', ''),
    COALESCE(NEW.raw_user_meta_data ->> 'postal_code', ''),
    COALESCE(NEW.raw_user_meta_data ->> 'city', ''),
    NEW.email,
    COALESCE((NEW.raw_user_meta_data ->> 'membership_category')::public.membership_category, 'Erwachsener'::public.membership_category)
  );
  
  -- Assign appropriate role based on account type
  IF account_type_from_meta = 'guest' THEN
    -- Guests get 'guest' role
    INSERT INTO public.user_roles (user_id, role)
    VALUES (NEW.id, 'guest');
    RAISE LOG 'Assigned guest role to user: %', NEW.id;
  ELSE
    -- Members get 'mitglied' role
    INSERT INTO public.user_roles (user_id, role)
    VALUES (NEW.id, 'mitglied');
    RAISE LOG 'Assigned mitglied role to user: %', NEW.id;
  END IF;
  
  -- Only create club membership if NOT a guest AND club info is provided
  IF account_type_from_meta != 'guest' AND (club_id_from_meta IS NOT NULL OR club_slug_from_meta IS NOT NULL) THEN
    -- Try to find the club and create membership
    IF club_id_from_meta IS NOT NULL THEN
      -- Direct club ID provided
      target_club_id := club_id_from_meta::uuid;
      RAISE LOG 'Found club ID from metadata: %', target_club_id;
    ELSIF club_slug_from_meta IS NOT NULL THEN
      -- Look up club by slug
      SELECT id INTO target_club_id
      FROM public.clubs 
      WHERE slug = club_slug_from_meta AND is_active = true
      LIMIT 1;
      RAISE LOG 'Found club by slug %: %', club_slug_from_meta, target_club_id;
    END IF;
    
    -- Create club membership if we found a valid club
    IF target_club_id IS NOT NULL THEN
      RAISE LOG 'Creating club membership for user % in club %', NEW.id, target_club_id;
      
      INSERT INTO public.club_memberships (
        user_id,
        club_id,
        first_name,
        last_name,
        email,
        phone,
        birth_date,
        address_street,
        address_postal_code,
        address_city,
        membership_type,
        account_type,
        role
      )
      VALUES (
        NEW.id,
        target_club_id,
        COALESCE(NEW.raw_user_meta_data ->> 'first_name', 'Unknown'),
        COALESCE(NEW.raw_user_meta_data ->> 'last_name', 'Unknown'),
        NEW.email,
        COALESCE(NEW.raw_user_meta_data ->> 'phone', ''),
        CASE 
          WHEN NEW.raw_user_meta_data ->> 'birth_date' IS NOT NULL 
          THEN (NEW.raw_user_meta_data ->> 'birth_date')::date
          ELSE NULL
        END,
        COALESCE(NEW.raw_user_meta_data ->> 'street', ''),
        COALESCE(NEW.raw_user_meta_data ->> 'postal_code', ''),
        COALESCE(NEW.raw_user_meta_data ->> 'city', ''),
        COALESCE(NEW.raw_user_meta_data ->> 'membership_category', 'Erwachsener'),
        COALESCE(NEW.raw_user_meta_data ->> 'account_type', 'member'),
        'member' -- Default role for new club members
      );
      
      RAISE LOG 'Club membership created successfully for user: % in club: %', NEW.id, target_club_id;
    ELSE
      RAISE LOG 'No valid club found for user: %', NEW.id;
    END IF;
  ELSE
    RAISE LOG 'Skipping club membership creation - Account type: %, Club info present: %', 
      account_type_from_meta, 
      (club_id_from_meta IS NOT NULL OR club_slug_from_meta IS NOT NULL);
  END IF;
  
  RAISE LOG 'Profile and memberships created successfully for user: %', NEW.id;
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    RAISE LOG 'Error creating profile/membership for user %: %', NEW.id, SQLERRM;
    RAISE;
END;
$function$;