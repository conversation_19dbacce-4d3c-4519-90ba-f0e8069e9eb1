-- First, drop all existing policies (using different policy names that might exist)
DROP POLICY IF EXISTS "Club admins can manage teams" ON public.teams;
DROP POLICY IF EXISTS "Club members can view teams" ON public.teams;
DROP POLICY IF EXISTS "Club admins can manage teams in their club" ON public.teams;
DROP POLICY IF EXISTS "Club members can view teams in their club" ON public.teams;

DROP POLICY IF EXISTS "Club admins can manage team members" ON public.team_members;
DROP POLICY IF EXISTS "Club members can view team members" ON public.team_members;
DROP POLICY IF EXISTS "Club admins can manage team members in their club" ON public.team_members;
DROP POLICY IF EXISTS "Club members can view team members in their club" ON public.team_members;

-- Create proper club-isolated policies for teams table
CREATE POLICY "teams_club_admin_policy" ON public.teams
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.club_memberships cm
      WHERE cm.user_id = auth.uid()
        AND cm.club_id = teams.club_id
        AND cm.role IN ('admin', 'super_admin')
        AND cm.is_active = true
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.club_memberships cm
      WHERE cm.user_id = auth.uid()
        AND cm.club_id = teams.club_id
        AND cm.role IN ('admin', 'super_admin')
        AND cm.is_active = true
    )
  );

CREATE POLICY "teams_club_member_view_policy" ON public.teams
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.club_memberships cm
      WHERE cm.user_id = auth.uid()
        AND cm.club_id = teams.club_id
        AND cm.is_active = true
    )
  );

-- Create proper club-isolated policies for team_members table
CREATE POLICY "team_members_club_admin_policy" ON public.team_members
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.club_memberships cm
      JOIN public.teams t ON t.id = team_members.team_id
      WHERE cm.user_id = auth.uid()
        AND cm.club_id = t.club_id
        AND cm.role IN ('admin', 'super_admin')
        AND cm.is_active = true
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.club_memberships cm
      JOIN public.teams t ON t.id = team_members.team_id
      WHERE cm.user_id = auth.uid()
        AND cm.club_id = t.club_id
        AND cm.role IN ('admin', 'super_admin')
        AND cm.is_active = true
    )
  );

CREATE POLICY "team_members_club_member_view_policy" ON public.team_members
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.club_memberships cm
      JOIN public.teams t ON t.id = team_members.team_id
      WHERE cm.user_id = auth.uid()
        AND cm.club_id = t.club_id
        AND cm.is_active = true
    )
  );