-- Create function for atomic waitlist processing
CREATE OR REPLACE FUNCTION public.process_waitlist_auto_booking(
  p_club_id uuid,
  p_court_id uuid,
  p_booking_date date,
  p_start_time time,
  p_end_time time
) RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_first_entry record;
  v_booking_id uuid;
  v_result jsonb := '{}';
BEGIN
  -- Find the highest priority waitlist entry (auto-booking enabled, lowest position, earliest created)
  SELECT * INTO v_first_entry
  FROM public.waitlist_entries
  WHERE club_id = p_club_id
    AND preferred_date = p_booking_date
    AND status = 'waiting'
    AND start_time_range <= p_start_time::text
    AND end_time_range >= p_end_time::text
    AND (
      array_length(preferred_courts, 1) IS NULL 
      OR preferred_courts = '{}' 
      OR p_court_id = ANY(preferred_courts)
    )
  ORDER BY 
    auto_booking_enabled DESC,
    position ASC,
    created_at ASC
  LIMIT 1;

  -- If no matching entry found
  IF NOT FOUND THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'No matching waitlist entries found'
    );
  END IF;

  -- If auto-booking is not enabled for the first entry
  IF NOT v_first_entry.auto_booking_enabled THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'First entry does not have auto-booking enabled',
      'notify_entries', true,
      'first_entry_id', v_first_entry.id
    );
  END IF;

  -- Check if booking slot is still available
  IF EXISTS (
    SELECT 1 FROM public.bookings
    WHERE court_id = p_court_id
      AND booking_date = p_booking_date
      AND start_time = p_start_time
      AND end_time = p_end_time
  ) THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'Booking slot already taken'
    );
  END IF;

  -- Create the auto-booking atomically
  INSERT INTO public.bookings (
    club_id,
    court_id,
    booking_date,
    start_time,
    end_time,
    player_name,
    partner_name,
    created_by
  ) VALUES (
    p_club_id,
    p_court_id,
    p_booking_date,
    p_start_time,
    p_end_time,
    v_first_entry.player_name,
    v_first_entry.partner_name,
    v_first_entry.user_id
  ) RETURNING id INTO v_booking_id;

  -- Mark waitlist entry as converted
  UPDATE public.waitlist_entries
  SET status = 'converted_to_booking',
      updated_at = now()
  WHERE id = v_first_entry.id;

  -- Update positions for remaining entries
  WITH ranked_entries AS (
    SELECT id, ROW_NUMBER() OVER (ORDER BY created_at) as new_position
    FROM public.waitlist_entries
    WHERE club_id = p_club_id
      AND preferred_date = p_booking_date
      AND status = 'waiting'
  )
  UPDATE public.waitlist_entries
  SET position = ranked_entries.new_position
  FROM ranked_entries
  WHERE waitlist_entries.id = ranked_entries.id;

  RETURN jsonb_build_object(
    'success', true,
    'message', 'Auto-booking created successfully',
    'booking_id', v_booking_id,
    'user_id', v_first_entry.user_id,
    'entry_id', v_first_entry.id,
    'player_name', v_first_entry.player_name,
    'partner_name', v_first_entry.partner_name
  );
END;
$$;