-- Add RLS policies for tournament_matches so members can see their own matches

-- Allow club members to view matches in their club's tournaments
CREATE POLICY "Club members can view tournament matches in their club" 
ON public.tournament_matches 
FOR SELECT 
USING (
  EXISTS (
    SELECT 1 FROM public.club_memberships cm 
    WHERE cm.user_id = auth.uid() 
      AND cm.club_id = tournament_matches.club_id 
      AND cm.is_active = true
  )
);

-- Allow participants to view their own tournament matches
CREATE POLICY "Tournament participants can view their own matches" 
ON public.tournament_matches 
FOR SELECT 
USING (
  participant1_id IN (
    SELECT tp.id FROM public.tournament_participants tp 
    WHERE tp.user_id = auth.uid()
  ) 
  OR 
  participant2_id IN (
    SELECT tp.id FROM public.tournament_participants tp 
    WHERE tp.user_id = auth.uid()
  )
);

-- Allow meta admins to manage all tournament matches
CREATE POLICY "Meta admins can manage all tournament matches" 
ON public.tournament_matches 
FOR ALL 
USING (is_meta_admin(auth.uid()));