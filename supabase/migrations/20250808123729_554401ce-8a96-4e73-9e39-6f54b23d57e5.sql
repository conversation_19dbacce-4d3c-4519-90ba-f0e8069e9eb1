-- Add RLS policies for members table to allow admins to access data
CREATE POLICY "Ad<PERSON> can view all members" 
ON public.members 
FOR SELECT 
USING (has_role(auth.uid(), 'admin'::app_role));

CREATE POLICY "Admins can insert members" 
ON public.members 
FOR INSERT 
WITH CHECK (has_role(auth.uid(), 'admin'::app_role));

CREATE POLICY "Admins can update members" 
ON public.members 
FOR UPDATE 
USING (has_role(auth.uid(), 'admin'::app_role));

CREATE POLICY "Admins can delete members" 
ON public.members 
FOR DELETE 
USING (has_role(auth.uid(), 'admin'::app_role));