-- Setze sichere search_path für Funktionen
ALTER FUNCTION public.update_club_memberships_updated_at() SET search_path = 'public';

-- Lösche die problematische View und erstelle sie neu ohne SECURITY DEFINER
DROP VIEW IF EXISTS public.unified_members;

-- <PERSON><PERSON><PERSON> eine sicherere Funktion für Club-Admin-Zugriff
CREATE OR REPLACE FUNCTION public.is_club_admin_for_club(_club_id uuid)
RETURNS boolean
LANGUAGE sql
STABLE SECURITY DEFINER
SET search_path = 'public'
AS $$
  SELECT EXISTS (
    SELECT 1 FROM public.club_memberships 
    WHERE club_id = _club_id 
      AND user_id = auth.uid() 
      AND role = 'admin'
      AND is_active = true
  );
$$;

-- Aktualisiere die RLS Policy für bessere Performance
DROP POLICY IF EXISTS "Club admins can manage club members" ON public.club_memberships;

CREATE POLICY "Club admins can manage club members" 
ON public.club_memberships 
FOR ALL 
USING (is_club_admin_for_club(club_id));

-- Füge Policies für Tables hinzu, die RLS aktiviert haben aber keine Policies
-- Überprüfe und füge fehlende RLS Policies hinzu
DO $$
BEGIN
  -- Für households table (falls RLS aktiviert)
  IF EXISTS (SELECT 1 FROM pg_class c JOIN pg_namespace n ON n.oid = c.relnamespace 
             WHERE n.nspname = 'public' AND c.relname = 'households' AND c.relrowsecurity) THEN
    
    -- Nur wenn noch keine Policies existieren
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE schemaname = 'public' AND tablename = 'households') THEN
      EXECUTE 'CREATE POLICY "Users can view households they belong to" ON public.households FOR SELECT USING (
        id IN (
          SELECT household_id FROM public.household_members hm 
          JOIN public.accounts a ON a.id = hm.account_id 
          WHERE a.user_id = auth.uid()
        )
      )';
    END IF;
  END IF;
END $$;