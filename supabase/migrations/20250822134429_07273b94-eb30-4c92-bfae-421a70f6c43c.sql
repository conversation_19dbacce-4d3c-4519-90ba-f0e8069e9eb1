-- Global Tournament Engine Schema

-- Global tournament templates (Meta-Admin managed)
CREATE TABLE public.tournament_templates (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  format_config J<PERSON><PERSON><PERSON> NOT NULL DEFAULT '{"type": "single_elimination", "best_of": 3}'::jsonb,
  scoring_config JSONB NOT NULL DEFAULT '{"scoring_type": "standard", "tiebreak": true}'::jsonb,
  is_global BOOLEAN NOT NULL DEFAULT true,
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  created_by UUID REFERENCES auth.users(id)
);

-- Club-specific tournaments
CREATE TABLE public.tournaments (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  club_id UUID NOT NULL,
  template_id UUID REFERENCES public.tournament_templates(id),
  name TEXT NOT NULL,
  description TEXT,
  status TEXT NOT NULL DEFAULT 'registration' CHECK (status IN ('registration', 'active', 'completed', 'cancelled')),
  registration_start TIMESTAMP WITH TIME ZONE,
  registration_end TIMESTAMP WITH TIME ZONE,
  tournament_start TIMESTAMP WITH TIME ZONE,
  tournament_end TIMESTAMP WITH TIME ZONE,
  max_participants INTEGER,
  entry_fee_id UUID,
  guest_entry_fee_id UUID,
  allow_guests BOOLEAN NOT NULL DEFAULT true,
  format_config JSONB NOT NULL DEFAULT '{"type": "single_elimination", "best_of": 3}'::jsonb,
  scoring_config JSONB NOT NULL DEFAULT '{"scoring_type": "standard", "tiebreak": true}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  created_by UUID REFERENCES auth.users(id)
);

-- Tournament participants
CREATE TABLE public.tournament_participants (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  tournament_id UUID NOT NULL REFERENCES public.tournaments(id) ON DELETE CASCADE,
  club_id UUID NOT NULL,
  user_id UUID REFERENCES auth.users(id), -- For club members
  guest_name TEXT, -- For guests
  guest_email TEXT, -- For guests
  registration_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  status TEXT NOT NULL DEFAULT 'registered' CHECK (status IN ('registered', 'confirmed', 'withdrawn', 'disqualified')),
  seed_number INTEGER,
  notes TEXT,
  CONSTRAINT participant_type_check CHECK (
    (user_id IS NOT NULL AND guest_name IS NULL AND guest_email IS NULL) OR
    (user_id IS NULL AND guest_name IS NOT NULL)
  )
);

-- Tournament matches
CREATE TABLE public.tournament_matches (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  tournament_id UUID NOT NULL REFERENCES public.tournaments(id) ON DELETE CASCADE,
  club_id UUID NOT NULL,
  round_number INTEGER NOT NULL,
  match_number INTEGER NOT NULL,
  participant1_id UUID REFERENCES public.tournament_participants(id),
  participant2_id UUID REFERENCES public.tournament_participants(id),
  court_id UUID,
  scheduled_at TIMESTAMP WITH TIME ZONE,
  actual_start TIMESTAMP WITH TIME ZONE,
  actual_end TIMESTAMP WITH TIME ZONE,
  score_json JSONB DEFAULT '{"sets": [], "winner": null}'::jsonb,
  status TEXT NOT NULL DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'in_progress', 'completed', 'walkover', 'cancelled')),
  winner_id UUID REFERENCES public.tournament_participants(id),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Tournament fee assignments (extends existing fee system)
CREATE TABLE public.tournament_fee_assignments (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  tournament_id UUID NOT NULL REFERENCES public.tournaments(id) ON DELETE CASCADE,
  participant_id UUID NOT NULL REFERENCES public.tournament_participants(id) ON DELETE CASCADE,
  fee_assignment_id UUID REFERENCES public.fee_assignments(id),
  amount NUMERIC(10,2) NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'paid', 'waived', 'refunded')),
  due_date DATE,
  paid_at TIMESTAMP WITH TIME ZONE,
  payment_method TEXT,
  payment_reference TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS on all tables
ALTER TABLE public.tournament_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tournaments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tournament_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tournament_matches ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tournament_fee_assignments ENABLE ROW LEVEL SECURITY;

-- RLS Policies for tournament_templates (Global Meta-Admin managed)
CREATE POLICY "Meta admins can manage tournament templates" ON public.tournament_templates
FOR ALL USING (is_meta_admin(auth.uid()));

CREATE POLICY "Club admins can view active tournament templates" ON public.tournament_templates
FOR SELECT USING (is_active = true AND is_global = true);

-- RLS Policies for tournaments (Club-scoped like bookings)
CREATE POLICY "Club admins can manage their club tournaments" ON public.tournaments
FOR ALL USING (
  EXISTS (
    SELECT 1 FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid() 
      AND cm.club_id = tournaments.club_id 
      AND cm.role = ANY(ARRAY['admin', 'super_admin'])
      AND cm.is_active = true
  )
);

CREATE POLICY "Club members can view their club tournaments" ON public.tournaments
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid() 
      AND cm.club_id = tournaments.club_id 
      AND cm.is_active = true
  )
);

CREATE POLICY "Guests can view tournaments that allow guests" ON public.tournaments
FOR SELECT USING (
  allow_guests = true AND 
  EXISTS (
    SELECT 1 FROM public.user_roles ur
    WHERE ur.user_id = auth.uid() AND ur.role = 'guest'
  )
);

CREATE POLICY "Meta admins can view all tournaments" ON public.tournaments
FOR SELECT USING (is_meta_admin(auth.uid()));

-- RLS Policies for tournament_participants
CREATE POLICY "Club admins can manage participants in their club" ON public.tournament_participants
FOR ALL USING (
  EXISTS (
    SELECT 1 FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid() 
      AND cm.club_id = tournament_participants.club_id 
      AND cm.role = ANY(ARRAY['admin', 'super_admin'])
      AND cm.is_active = true
  )
);

CREATE POLICY "Users can view participants in tournaments they're in" ON public.tournament_participants
FOR SELECT USING (
  user_id = auth.uid() OR
  EXISTS (
    SELECT 1 FROM public.tournament_participants tp2
    WHERE tp2.tournament_id = tournament_participants.tournament_id
      AND tp2.user_id = auth.uid()
  ) OR
  EXISTS (
    SELECT 1 FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid() 
      AND cm.club_id = tournament_participants.club_id 
      AND cm.is_active = true
  )
);

CREATE POLICY "Users can register themselves for tournaments" ON public.tournament_participants
FOR INSERT WITH CHECK (
  user_id = auth.uid() AND
  EXISTS (
    SELECT 1 FROM public.tournaments t
    WHERE t.id = tournament_participants.tournament_id
      AND t.status = 'registration'
      AND (
        EXISTS (
          SELECT 1 FROM public.club_memberships cm
          WHERE cm.user_id = auth.uid() 
            AND cm.club_id = t.club_id 
            AND cm.is_active = true
        ) OR
        (t.allow_guests = true AND EXISTS (
          SELECT 1 FROM public.user_roles ur
          WHERE ur.user_id = auth.uid() AND ur.role = 'guest'
        ))
      )
  )
);

-- RLS Policies for tournament_matches
CREATE POLICY "Club admins can manage matches in their club" ON public.tournament_matches
FOR ALL USING (
  EXISTS (
    SELECT 1 FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid() 
      AND cm.club_id = tournament_matches.club_id 
      AND cm.role = ANY(ARRAY['admin', 'super_admin'])
      AND cm.is_active = true
  )
);

CREATE POLICY "Tournament participants can view their matches" ON public.tournament_matches
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM public.tournament_participants tp
    WHERE (tp.id = tournament_matches.participant1_id OR tp.id = tournament_matches.participant2_id)
      AND tp.user_id = auth.uid()
  ) OR
  EXISTS (
    SELECT 1 FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid() 
      AND cm.club_id = tournament_matches.club_id 
      AND cm.is_active = true
  )
);

-- RLS Policies for tournament_fee_assignments
CREATE POLICY "Users can view their own tournament fees" ON public.tournament_fee_assignments
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM public.tournament_participants tp
    WHERE tp.id = tournament_fee_assignments.participant_id
      AND tp.user_id = auth.uid()
  )
);

CREATE POLICY "Club admins can manage tournament fees" ON public.tournament_fee_assignments
FOR ALL USING (
  EXISTS (
    SELECT 1 FROM public.tournament_participants tp
    JOIN public.club_memberships cm ON cm.club_id = tp.club_id
    WHERE tp.id = tournament_fee_assignments.participant_id
      AND cm.user_id = auth.uid() 
      AND cm.role = ANY(ARRAY['admin', 'super_admin'])
      AND cm.is_active = true
  )
);

-- Indexes for performance
CREATE INDEX idx_tournaments_club_id ON public.tournaments(club_id);
CREATE INDEX idx_tournaments_status ON public.tournaments(status);
CREATE INDEX idx_tournament_participants_tournament_id ON public.tournament_participants(tournament_id);
CREATE INDEX idx_tournament_participants_user_id ON public.tournament_participants(user_id);
CREATE INDEX idx_tournament_matches_tournament_id ON public.tournament_matches(tournament_id);
CREATE INDEX idx_tournament_matches_scheduled_at ON public.tournament_matches(scheduled_at);
CREATE INDEX idx_tournament_fee_assignments_participant_id ON public.tournament_fee_assignments(participant_id);

-- Triggers for updated_at
CREATE TRIGGER update_tournament_templates_updated_at
BEFORE UPDATE ON public.tournament_templates
FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_tournaments_updated_at
BEFORE UPDATE ON public.tournaments
FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_tournament_matches_updated_at
BEFORE UPDATE ON public.tournament_matches
FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_tournament_fee_assignments_updated_at
BEFORE UPDATE ON public.tournament_fee_assignments
FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();