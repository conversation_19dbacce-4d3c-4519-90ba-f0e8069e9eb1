-- Insert 12 Member demo accounts
INSERT INTO public.accounts (account_type, first_name, last_name, email, phone, address_street, address_postal_code, address_city, birth_date, membership_type) VALUES
('Member', '<PERSON>', '<PERSON>', '<EMAIL>', '+49 123 456789', 'Hauptstraße 1', '10115', 'Berlin', '1985-03-15', 'Adult'),
('Member', '<PERSON>', '<PERSON>', 'anna.schmi<PERSON>@example.com', '+49 123 456790', 'Musterstraße 12', '80331', 'München', '1990-07-22', 'Adult'),
('Member', '<PERSON>', '<PERSON>', '<EMAIL>', '+49 123 456791', 'Parkweg 8', '20095', 'Hamburg', '2010-11-08', 'Child'),
('Member', '<PERSON>', '<PERSON>', '<EMAIL>', '+49 123 456792', 'Rosenstraße 5', '50667', '<PERSON><PERSON><PERSON>', '2005-02-14', 'Youth'),
('Member', '<PERSON>', '<PERSON>', 'paul.wa<PERSON><PERSON>@example.com', '+49 123 456793', 'Bergstraße 23', '70173', 'Stuttgart', '1995-09-30', '<PERSON>'),
('Member', '<PERSON>', '<PERSON>', '<EMAIL>', '+49 123 456794', 'Waldweg 15', '40210', '<PERSON>', '1982-12-05', '<PERSON>'),
('Member', '<PERSON>', '<PERSON>hulz', '<EMAIL>', '+49 123 456795', '<PERSON>nenallee 42', '60311', 'Frankfurt', '2008-04-18', 'Child'),
('Member', 'Sophie', 'Richter', '<EMAIL>', '+49 123 456796', 'Lindenstraße 7', '04109', 'Leipzig', '2002-08-12', 'Youth'),
('Member', 'Tim', 'Klein', '<EMAIL>', '+49 123 456797', 'Kirchstraße 33', '30159', 'Hannover', '1988-01-25', 'Adult'),
('Member', 'Emma', 'Wolf', '<EMAIL>', '+49 123 456798', 'Gartenstraße 19', '01067', 'Dresden', '1992-06-03', 'Adult'),
('Member', 'Lukas', 'Neumann', '<EMAIL>', '+49 123 456799', 'Bahnhofstraße 11', '90402', 'Nürnberg', '2012-10-27', 'Child'),
('Member', 'Clara', 'Zimmermann', '<EMAIL>', '+49 123 456800', 'Schlossstraße 6', '76131', 'Karlsruhe', '1999-05-16', 'Student');

-- Insert 2 Admin demo accounts
INSERT INTO public.accounts (account_type, first_name, last_name, email, phone, address_street, address_postal_code, address_city, birth_date) VALUES
('Admin', 'Thomas', 'Admin', '<EMAIL>', '+49 123 456801', 'Verwaltungsstraße 1', '10115', 'Berlin', '1975-08-20'),
('Admin', 'Petra', 'Verwaltung', '<EMAIL>', '+49 123 456802', 'Bürostraße 25', '80331', 'München', '1980-11-12');

-- Insert 1 Trainer demo account
INSERT INTO public.accounts (account_type, first_name, last_name, email, phone, address_street, address_postal_code, address_city, birth_date) VALUES
('Trainer', 'Michael', 'Coach', '<EMAIL>', '+49 123 456803', 'Sportstraße 10', '20095', 'Hamburg', '1983-04-07');