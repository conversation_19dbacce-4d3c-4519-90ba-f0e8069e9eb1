-- Problematische Policy entfernen die Rekursion verursacht
DROP POLICY "Club members can view other club members" ON public.club_memberships;

-- Security Definer Funktion erstellen um Rekursion zu vermeiden
CREATE OR REPLACE FUNCTION public.get_user_club_ids(_user_id uuid)
RETURNS TABLE(club_id uuid)
LANGUAGE SQL
STABLE SECURITY DEFINER
SET search_path TO 'public'
AS $$
  SELECT cm.club_id
  FROM public.club_memberships cm
  WHERE cm.user_id = _user_id
    AND cm.is_active = true;
$$;

-- Neue Policy mit Security Definer Funktion
CREATE POLICY "Club members can view other members in same clubs"
ON public.club_memberships
FOR SELECT
USING (
  club_id IN (
    SELECT get_user_club_ids.club_id
    FROM public.get_user_club_ids(auth.uid())
  )
  AND is_active = true
);