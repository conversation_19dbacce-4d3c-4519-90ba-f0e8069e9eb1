-- Create teams table for team management
CREATE TABLE public.teams (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name text NOT NULL,
  description text,
  league text,
  captain_account_id uuid,
  club_id uuid NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now()
);

-- Enable RLS
ALTER TABLE public.teams ENABLE ROW LEVEL SECURITY;

-- Create team_members table for team roster
CREATE TABLE public.team_members (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  team_id uuid NOT NULL REFERENCES public.teams(id) ON DELETE CASCADE,
  account_id uuid NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  UNIQUE(team_id, account_id)
);

-- Enable RLS
ALTER TABLE public.team_members ENABLE ROW LEVEL SECURITY;

-- <PERSON><PERSON> Policies for teams
CREATE POLICY "Club members can view teams in their club" 
ON public.teams 
FOR SELECT 
USING (
  club_id IN (
    SELECT cm.club_id 
    FROM club_memberships cm 
    WHERE cm.user_id = auth.uid() AND cm.is_active = true
  )
);

CREATE POLICY "Club admins can manage teams" 
ON public.teams 
FOR ALL 
USING (
  EXISTS (
    SELECT 1 FROM club_memberships cm 
    WHERE cm.user_id = auth.uid() 
      AND cm.club_id = teams.club_id 
      AND cm.role IN ('admin', 'super_admin') 
      AND cm.is_active = true
  )
);

-- RLS Policies for team_members
CREATE POLICY "Team members visible to club members" 
ON public.team_members 
FOR SELECT 
USING (
  team_id IN (
    SELECT t.id FROM teams t 
    JOIN club_memberships cm ON cm.club_id = t.club_id 
    WHERE cm.user_id = auth.uid() AND cm.is_active = true
  )
);

CREATE POLICY "Club admins can manage team members" 
ON public.team_members 
FOR ALL 
USING (
  team_id IN (
    SELECT t.id FROM teams t 
    JOIN club_memberships cm ON cm.club_id = t.club_id 
    WHERE cm.user_id = auth.uid() 
      AND cm.role IN ('admin', 'super_admin') 
      AND cm.is_active = true
  )
);

-- Add trigger for updated_at
CREATE TRIGGER update_teams_updated_at
  BEFORE UPDATE ON public.teams
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();