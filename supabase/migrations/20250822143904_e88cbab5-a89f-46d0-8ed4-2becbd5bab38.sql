-- Drop problematic policies first
DROP POLICY IF EXISTS "Club members can view participants in club tournaments" ON public.tournament_participants;
DROP POLICY IF EXISTS "Users can register themselves" ON public.tournament_participants;
DROP POLICY IF EXISTS "Club admins can manage participants" ON public.tournament_participants;

-- Recreate tournament_participants table from scratch to ensure correct schema
DROP TABLE IF EXISTS public.tournament_participants CASCADE;
CREATE TABLE public.tournament_participants (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  tournament_id UUID NOT NULL REFERENCES public.tournaments(id) ON DELETE CASCADE,
  club_id UUID NOT NULL,
  user_id UUID,
  guest_name TEXT,
  guest_email TEXT,
  phone TEXT,
  is_guest BOOLEAN NOT NULL DEFAULT false,
  seeding INTEGER,
  status TEXT NOT NULL DEFAULT 'registered' CHECK (status IN ('registered', 'confirmed', 'withdrawn', 'disqualified')),
  registration_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS
ALTER TABLE public.tournament_participants ENABLE ROW LEVEL SECURITY;

-- Create simple, non-recursive RLS policies
CREATE POLICY "Users can view participants in tournaments they can see"
ON public.tournament_participants
FOR SELECT
USING (
  tournament_id IN (
    SELECT t.id FROM public.tournaments t
    WHERE EXISTS (
      SELECT 1 FROM public.club_memberships cm
      WHERE cm.user_id = auth.uid()
        AND cm.club_id = t.club_id
        AND cm.is_active = true
    )
  )
);

CREATE POLICY "Users can register for tournaments"
ON public.tournament_participants
FOR INSERT
WITH CHECK (
  -- User can register themselves
  (user_id = auth.uid() AND guest_name IS NULL)
  OR
  -- Or club admin can register anyone
  EXISTS (
    SELECT 1 FROM public.tournaments t
    JOIN public.club_memberships cm ON cm.club_id = t.club_id
    WHERE t.id = tournament_participants.tournament_id
      AND cm.user_id = auth.uid()
      AND cm.role IN ('admin', 'super_admin')
      AND cm.is_active = true
  )
);

CREATE POLICY "Users can update their own registrations"
ON public.tournament_participants
FOR UPDATE
USING (
  user_id = auth.uid()
  OR
  EXISTS (
    SELECT 1 FROM public.tournaments t
    JOIN public.club_memberships cm ON cm.club_id = t.club_id
    WHERE t.id = tournament_participants.tournament_id
      AND cm.user_id = auth.uid()
      AND cm.role IN ('admin', 'super_admin')
      AND cm.is_active = true
  )
)
WITH CHECK (
  user_id = auth.uid()
  OR
  EXISTS (
    SELECT 1 FROM public.tournaments t
    JOIN public.club_memberships cm ON cm.club_id = t.club_id
    WHERE t.id = tournament_participants.tournament_id
      AND cm.user_id = auth.uid()
      AND cm.role IN ('admin', 'super_admin')
      AND cm.is_active = true
  )
);

CREATE POLICY "Club admins can delete registrations"
ON public.tournament_participants
FOR DELETE
USING (
  user_id = auth.uid()
  OR
  EXISTS (
    SELECT 1 FROM public.tournaments t
    JOIN public.club_memberships cm ON cm.club_id = t.club_id
    WHERE t.id = tournament_participants.tournament_id
      AND cm.user_id = auth.uid()
      AND cm.role IN ('admin', 'super_admin')
      AND cm.is_active = true
  )
);

CREATE POLICY "Meta admins can manage all participants"
ON public.tournament_participants
FOR ALL
USING (is_meta_admin(auth.uid()))
WITH CHECK (is_meta_admin(auth.uid()));

-- Add update trigger
CREATE TRIGGER update_tournament_participants_updated_at
BEFORE UPDATE ON public.tournament_participants
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();