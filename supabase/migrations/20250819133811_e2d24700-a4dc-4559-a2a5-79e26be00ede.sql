-- Fix security warning: Set search_path for the function
CREATE OR REPLACE FUNCTION public.get_current_user_display_name()
RETURNS TEXT 
LANGUAGE plpgsql 
SECURITY DEFINER 
STABLE
SET search_path = public
AS $$
DECLARE
  user_name TEXT;
BEGIN
  -- Try to get name from profile first
  SELECT TRIM(COALESCE(first_name, '') || ' ' || COALESCE(last_name, ''))
  INTO user_name
  FROM public.profiles 
  WHERE id = auth.uid();
  
  -- If no profile name, fall back to email
  IF user_name IS NULL OR user_name = '' THEN
    SELECT email INTO user_name FROM auth.users WHERE id = auth.uid();
  END IF;
  
  RETURN user_name;
END;
$$;