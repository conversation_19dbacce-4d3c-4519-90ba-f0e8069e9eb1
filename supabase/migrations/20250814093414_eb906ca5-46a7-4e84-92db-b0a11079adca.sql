-- Create domains table for Phase 3
CREATE TABLE public.domains (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  club_id UUID REFERENCES clubs(id) ON DELETE CASCADE NOT NULL,
  domain_name TEXT NOT NULL UNIQUE,
  domain_type TEXT NOT NULL CHECK (domain_type IN ('custom', 'subdomain')),
  is_primary BOOLEAN NOT NULL DEFAULT false,
  is_verified BOOLEAN NOT NULL DEFAULT false,
  ssl_status TEXT NOT NULL DEFAULT 'pending' CHECK (ssl_status IN ('pending', 'active', 'failed', 'expired')),
  ssl_expires_at TIMESTAMP WITH TIME ZONE,
  dns_verified_at TIMESTAMP WITH TIME ZONE,
  verification_token TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create plans table for Phase 5
CREATE TABLE public.plans (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  price_monthly DECIMAL(10,2),
  price_yearly DECIMAL(10,2),
  limits JSONB NOT NULL DEFAULT '{}',
  features JSONB NOT NULL DEFAULT '[]',
  is_active BOOLEAN NOT NULL DEFAULT true,
  sort_order INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create club_subscriptions table for billing
CREATE TABLE public.club_subscriptions (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  club_id UUID REFERENCES clubs(id) ON DELETE CASCADE NOT NULL,
  plan_id UUID REFERENCES plans(id) NOT NULL,
  stripe_subscription_id TEXT UNIQUE,
  status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'trialing', 'past_due', 'canceled', 'unpaid')),
  current_period_start TIMESTAMP WITH TIME ZONE,
  current_period_end TIMESTAMP WITH TIME ZONE,
  trial_end TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create audit_logs table for Phase 6
CREATE TABLE public.audit_logs (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  club_id UUID REFERENCES clubs(id),
  action TEXT NOT NULL,
  resource_type TEXT NOT NULL,
  resource_id TEXT,
  old_values JSONB,
  new_values JSONB,
  ip_address INET,
  user_agent TEXT,
  session_id TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create system_metrics table for monitoring
CREATE TABLE public.system_metrics (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  metric_name TEXT NOT NULL,
  metric_value NUMERIC NOT NULL,
  labels JSONB DEFAULT '{}',
  recorded_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create support_tickets table for Phase 6
CREATE TABLE public.support_tickets (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  club_id UUID REFERENCES clubs(id),
  user_id UUID REFERENCES auth.users(id),
  subject TEXT NOT NULL,
  description TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'open' CHECK (status IN ('open', 'in_progress', 'resolved', 'closed')),
  priority TEXT NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
  assigned_to UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create email_templates table for Phase 7
CREATE TABLE public.email_templates (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  subject TEXT NOT NULL,
  content TEXT NOT NULL,
  template_type TEXT NOT NULL,
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS on all tables
ALTER TABLE public.domains ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.club_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.system_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.support_tickets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.email_templates ENABLE ROW LEVEL SECURITY;

-- Create policies for domains
CREATE POLICY "Super admins can manage all domains"
ON public.domains FOR ALL
TO authenticated
USING (has_meta_admin_role(auth.uid(), 'SUPER_ADMIN'))
WITH CHECK (has_meta_admin_role(auth.uid(), 'SUPER_ADMIN'));

CREATE POLICY "Support admins can view domains"
ON public.domains FOR SELECT
TO authenticated
USING (has_meta_admin_role(auth.uid(), 'SUPPORT_ADMIN') OR has_meta_admin_role(auth.uid(), 'READONLY'));

-- Create policies for plans
CREATE POLICY "Meta admins can view plans"
ON public.plans FOR SELECT
TO authenticated
USING (is_meta_admin(auth.uid()));

CREATE POLICY "Super admins can manage plans"
ON public.plans FOR ALL
TO authenticated
USING (has_meta_admin_role(auth.uid(), 'SUPER_ADMIN'))
WITH CHECK (has_meta_admin_role(auth.uid(), 'SUPER_ADMIN'));

-- Create policies for subscriptions
CREATE POLICY "Meta admins can view subscriptions"
ON public.club_subscriptions FOR SELECT
TO authenticated
USING (is_meta_admin(auth.uid()));

CREATE POLICY "Billing admins can manage subscriptions"
ON public.club_subscriptions FOR ALL
TO authenticated
USING (has_meta_admin_role(auth.uid(), 'SUPER_ADMIN') OR has_meta_admin_role(auth.uid(), 'BILLING_ADMIN'))
WITH CHECK (has_meta_admin_role(auth.uid(), 'SUPER_ADMIN') OR has_meta_admin_role(auth.uid(), 'BILLING_ADMIN'));

-- Create policies for audit logs
CREATE POLICY "Meta admins can view audit logs"
ON public.audit_logs FOR SELECT
TO authenticated
USING (is_meta_admin(auth.uid()));

CREATE POLICY "System can insert audit logs"
ON public.audit_logs FOR INSERT
TO authenticated
WITH CHECK (true);

-- Create policies for metrics
CREATE POLICY "Meta admins can view metrics"
ON public.system_metrics FOR SELECT
TO authenticated
USING (is_meta_admin(auth.uid()));

CREATE POLICY "System can insert metrics"
ON public.system_metrics FOR INSERT
TO authenticated
WITH CHECK (true);

-- Create policies for support tickets
CREATE POLICY "Support admins can manage tickets"
ON public.support_tickets FOR ALL
TO authenticated
USING (has_meta_admin_role(auth.uid(), 'SUPER_ADMIN') OR has_meta_admin_role(auth.uid(), 'SUPPORT_ADMIN'))
WITH CHECK (has_meta_admin_role(auth.uid(), 'SUPER_ADMIN') OR has_meta_admin_role(auth.uid(), 'SUPPORT_ADMIN'));

-- Create policies for email templates
CREATE POLICY "Meta admins can view email templates"
ON public.email_templates FOR SELECT
TO authenticated
USING (is_meta_admin(auth.uid()));

CREATE POLICY "Super admins can manage email templates"
ON public.email_templates FOR ALL
TO authenticated
USING (has_meta_admin_role(auth.uid(), 'SUPER_ADMIN'))
WITH CHECK (has_meta_admin_role(auth.uid(), 'SUPER_ADMIN'));

-- Create triggers for updated_at
CREATE TRIGGER update_domains_updated_at
  BEFORE UPDATE ON public.domains
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_plans_updated_at
  BEFORE UPDATE ON public.plans
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_club_subscriptions_updated_at
  BEFORE UPDATE ON public.club_subscriptions
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_support_tickets_updated_at
  BEFORE UPDATE ON public.support_tickets
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_email_templates_updated_at
  BEFORE UPDATE ON public.email_templates
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

-- Insert default plans
INSERT INTO public.plans (name, description, price_monthly, price_yearly, limits, features) VALUES
('Starter', 'Für kleine Vereine', 29.00, 290.00, 
 '{"members": 100, "courts": 5, "storage_gb": 1, "api_requests": 1000}',
 '["Buchungsverwaltung", "Mitgliederverwaltung", "E-Mail Support"]'),
('Professional', 'Für mittlere Vereine', 79.00, 790.00,
 '{"members": 500, "courts": 20, "storage_gb": 10, "api_requests": 10000}',
 '["Erweiterte Berichte", "API Zugang", "Custom Branding", "Telefon Support"]'),
('Enterprise', 'Für große Vereine', 199.00, 1990.00,
 '{"members": -1, "courts": -1, "storage_gb": 100, "api_requests": 100000}',
 '["Unlimited", "Dedicated Support", "Custom Domains", "White Label"]');

-- Insert default email templates
INSERT INTO public.email_templates (name, subject, content, template_type) VALUES
('welcome_email', 'Willkommen bei {{club_name}}', 
 'Hallo {{user_name}},\n\nwillkommen bei {{club_name}}! Wir freuen uns, Sie als neues Mitglied begrüßen zu dürfen.\n\nIhr Team von {{club_name}}',
 'member_notification'),
('booking_confirmation', 'Buchungsbestätigung für {{court_name}}',
 'Hallo {{user_name}},\n\nIhre Buchung wurde bestätigt:\n\nPlatz: {{court_name}}\nDatum: {{booking_date}}\nUhrzeit: {{booking_time}}\n\nViel Spaß beim Spiel!',
 'booking_notification'),
('password_reset', 'Passwort zurücksetzen',
 'Hallo,\n\nSie haben eine Passwort-Zurücksetzung angefordert. Klicken Sie auf den folgenden Link: {{reset_link}}\n\nFalls Sie diese Anfrage nicht gestellt haben, ignorieren Sie diese E-Mail.',
 'authentication');