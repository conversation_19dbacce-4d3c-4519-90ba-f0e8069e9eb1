-- Fix courts RLS policies to allow meta-admins access
-- Drop existing policies
DROP POLICY IF EXISTS "courts_select" ON public.courts;
DROP POLICY IF EXISTS "courts_write" ON public.courts;

-- Create new policies that include meta-admin access
CREATE POLICY "courts_select_policy" ON public.courts
FOR SELECT USING (
  -- Meta-admins can access all courts
  is_meta_admin(auth.uid()) 
  OR 
  -- Club members can access their club's courts
  (EXISTS ( 
    SELECT 1
    FROM club_memberships m
    WHERE m.user_id = auth.uid() 
    AND m.club_id = courts.club_id 
    AND COALESCE(m.is_active, true) = true
  ))
);

CREATE POLICY "courts_insert_policy" ON public.courts
FOR INSERT WITH CHECK (
  -- Meta-admins can create courts for any club
  is_meta_admin(auth.uid())
  OR 
  -- Club admins can create courts for their club
  (EXISTS ( 
    SELECT 1
    FROM club_memberships m
    WHERE m.user_id = auth.uid() 
    AND m.club_id = courts.club_id 
    AND m.role = ANY (ARRAY['admin'::text, 'trainer'::text]) 
    AND COALESCE(m.is_active, true) = true
  ))
);

CREATE POLICY "courts_update_policy" ON public.courts
FOR UPDATE USING (
  -- Meta-admins can update any court
  is_meta_admin(auth.uid())
  OR 
  -- Club admins can update their club's courts
  (EXISTS ( 
    SELECT 1
    FROM club_memberships m
    WHERE m.user_id = auth.uid() 
    AND m.club_id = courts.club_id 
    AND m.role = ANY (ARRAY['admin'::text, 'trainer'::text]) 
    AND COALESCE(m.is_active, true) = true
  ))
) WITH CHECK (
  -- Meta-admins can update any court
  is_meta_admin(auth.uid())
  OR 
  -- Club admins can update their club's courts
  (EXISTS ( 
    SELECT 1
    FROM club_memberships m
    WHERE m.user_id = auth.uid() 
    AND m.club_id = courts.club_id 
    AND m.role = ANY (ARRAY['admin'::text, 'trainer'::text]) 
    AND COALESCE(m.is_active, true) = true
  ))
);

CREATE POLICY "courts_delete_policy" ON public.courts
FOR DELETE USING (
  -- Meta-admins can delete any court
  is_meta_admin(auth.uid())
  OR 
  -- Club admins can delete their club's courts
  (EXISTS ( 
    SELECT 1
    FROM club_memberships m
    WHERE m.user_id = auth.uid() 
    AND m.club_id = courts.club_id 
    AND m.role = ANY (ARRAY['admin'::text, 'trainer'::text]) 
    AND COALESCE(m.is_active, true) = true
  ))
);