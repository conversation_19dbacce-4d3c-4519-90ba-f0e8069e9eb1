-- Check what columns actually exist in team_members table and fix the structure
-- First, let's see if the table exists and what it looks like

-- Drop the table and recreate it properly
DROP TABLE IF EXISTS team_members CASCADE;

-- Recreate team_members table with correct structure
CREATE TABLE team_members (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  team_id uuid NOT NULL REFERENCES teams(id) ON DELETE CASCADE,
  user_id uuid NOT NULL, -- This should reference users in club_memberships
  created_at timestamp with time zone DEFAULT now(),
  UNIQUE(team_id, user_id) -- Prevent duplicate assignments
);

-- Enable RLS
ALTER TABLE team_members ENABLE ROW LEVEL SECURITY;

-- Simple policy for now
CREATE POLICY "Allow authenticated users to access team members" 
ON team_members FOR ALL 
USING (auth.uid() IS NOT NULL)
WITH CHECK (auth.uid() IS NOT NULL);

-- Grant permissions
GRANT ALL ON team_members TO authenticated;
GRANT ALL ON team_members TO anon;