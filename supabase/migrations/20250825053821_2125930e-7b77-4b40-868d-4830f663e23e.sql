-- Fix the database functions to handle read-only transactions properly
CREATE OR REPLACE FUNCTION public.process_waitlist_auto_booking_enhanced(p_club_id uuid, p_court_id uuid, p_booking_date date, p_start_time time without time zone, p_end_time time without time zone)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
  v_first_entry record;
  v_booking_id uuid;
  v_club_timezone text;
  v_user_membership record;
  v_court_status record;
  v_existing_booking_count integer;
  v_result jsonb := '{}';
BEGIN
  -- Get club timezone and settings
  SELECT timezone, is_active INTO v_club_timezone
  FROM public.clubs
  WHERE id = p_club_id AND is_active = true;
  
  IF NOT FOUND THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'Club not found or inactive',
      'error_code', 'CLUB_NOT_FOUND'
    );
  END IF;

  -- Validate court status
  SELECT id, locked, lock_reason, surface_type INTO v_court_status
  FROM public.courts
  WHERE id = p_court_id AND club_id = p_club_id;
  
  IF NOT FOUND THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'Court not found',
      'error_code', 'COURT_NOT_FOUND'
    );
  END IF;
  
  IF v_court_status.locked THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'Court is locked: ' || COALESCE(v_court_status.lock_reason, 'No reason provided'),
      'error_code', 'COURT_LOCKED'
    );
  END IF;

  -- Check for existing booking conflicts (enhanced overlap detection)
  SELECT COUNT(*) INTO v_existing_booking_count
  FROM public.bookings
  WHERE court_id = p_court_id
    AND booking_date = p_booking_date
    AND NOT (end_time <= p_start_time OR start_time >= p_end_time);
  
  IF v_existing_booking_count > 0 THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'Booking slot already taken',
      'error_code', 'SLOT_TAKEN'
    );
  END IF;

  -- Find the highest priority waitlist entry with FIXED type comparisons
  SELECT * INTO v_first_entry
  FROM public.waitlist_entries
  WHERE club_id = p_club_id
    AND preferred_date = p_booking_date
    AND status = 'waiting'
    AND start_time_range::time <= p_start_time
    AND end_time_range::time >= p_end_time
    AND (
      array_length(preferred_courts, 1) IS NULL 
      OR preferred_courts = '{}' 
      OR p_court_id = ANY(preferred_courts)
    )
  ORDER BY 
    auto_booking_enabled DESC,
    position ASC,
    created_at ASC
  LIMIT 1;

  IF NOT FOUND THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'No matching waitlist entries found',
      'notify_entries', true,
      'error_code', 'NO_ENTRIES'
    );
  END IF;

  -- Validate user membership status
  SELECT is_active, role INTO v_user_membership
  FROM public.club_memberships
  WHERE user_id = v_first_entry.user_id AND club_id = p_club_id;
  
  IF NOT FOUND OR NOT v_user_membership.is_active THEN
    -- Mark entry as invalid and continue to next
    UPDATE public.waitlist_entries
    SET status = 'cancelled',
        updated_at = now()
    WHERE id = v_first_entry.id;
    
    RETURN jsonb_build_object(
      'success', false,
      'message', 'User membership inactive, entry cancelled',
      'retry_next', true,
      'error_code', 'MEMBERSHIP_INACTIVE'
    );
  END IF;

  -- Auto-booking enabled check
  IF NOT v_first_entry.auto_booking_enabled THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'First entry does not have auto-booking enabled',
      'notify_entries', true,
      'first_entry_id', v_first_entry.id,
      'error_code', 'NO_AUTO_BOOKING'
    );
  END IF;

  -- Begin atomic transaction for booking creation
  BEGIN
    -- Create the auto-booking
    INSERT INTO public.bookings (
      club_id,
      court_id,
      booking_date,
      start_time,
      end_time,
      player_name,
      partner_name,
      created_by,
      created_at
    ) VALUES (
      p_club_id,
      p_court_id,
      p_booking_date,
      p_start_time,
      p_end_time,
      v_first_entry.player_name,
      v_first_entry.partner_name,
      v_first_entry.user_id,
      now()
    ) RETURNING id INTO v_booking_id;

    -- Mark waitlist entry as converted
    UPDATE public.waitlist_entries
    SET status = 'converted_to_booking',
        updated_at = now()
    WHERE id = v_first_entry.id;

    -- Try to log successful auto-booking (skip if read-only)
    BEGIN
      INSERT INTO public.audit_logs (
        resource_type,
        resource_id,
        action,
        user_id,
        club_id,
        new_values
      ) VALUES (
        'auto_booking',
        v_booking_id::text,
        'created',
        v_first_entry.user_id,
        p_club_id,
        jsonb_build_object(
          'waitlist_entry_id', v_first_entry.id,
          'court_id', p_court_id,
          'booking_date', p_booking_date,
          'start_time', p_start_time,
          'end_time', p_end_time,
          'auto_booking', true
        )
      );
    EXCEPTION WHEN OTHERS THEN
      -- Ignore audit log errors in read-only mode
      NULL;
    END;

    -- Update positions for remaining entries (optimized)
    WITH ranked_entries AS (
      SELECT id, ROW_NUMBER() OVER (ORDER BY position ASC, created_at ASC) as new_position
      FROM public.waitlist_entries
      WHERE club_id = p_club_id
        AND preferred_date = p_booking_date
        AND status = 'waiting'
    )
    UPDATE public.waitlist_entries
    SET position = ranked_entries.new_position,
        updated_at = now()
    FROM ranked_entries
    WHERE waitlist_entries.id = ranked_entries.id;

    RETURN jsonb_build_object(
      'success', true,
      'message', 'Auto-booking created successfully',
      'booking_id', v_booking_id,
      'user_id', v_first_entry.user_id,
      'entry_id', v_first_entry.id,
      'player_name', v_first_entry.player_name,
      'partner_name', v_first_entry.partner_name,
      'club_timezone', v_club_timezone,
      'court_number', (SELECT number FROM public.courts WHERE id = p_court_id)
    );

  EXCEPTION 
    WHEN OTHERS THEN
      -- Try to log the error (skip if read-only)
      BEGIN
        INSERT INTO public.audit_logs (
          resource_type,
          resource_id,
          action,
          user_id,
          club_id,
          old_values
        ) VALUES (
          'auto_booking_error',
          v_first_entry.id::text,
          'failed',
          v_first_entry.user_id,
          p_club_id,
          jsonb_build_object(
            'error', SQLERRM,
            'court_id', p_court_id,
            'booking_date', p_booking_date,
            'start_time', p_start_time,
            'end_time', p_end_time
          )
        );
      EXCEPTION WHEN OTHERS THEN
        -- Ignore audit log errors in read-only mode
        NULL;
      END;
      
      -- Reset waitlist entry if booking creation failed
      UPDATE public.waitlist_entries
      SET status = 'waiting',
          updated_at = now()
      WHERE id = v_first_entry.id;
      
      RETURN jsonb_build_object(
        'success', false,
        'message', 'Auto-booking failed: ' || SQLERRM,
        'error_code', 'BOOKING_CREATION_FAILED',
        'entry_id', v_first_entry.id,
        'retry_possible', true
      );
  END;
END;
$function$;