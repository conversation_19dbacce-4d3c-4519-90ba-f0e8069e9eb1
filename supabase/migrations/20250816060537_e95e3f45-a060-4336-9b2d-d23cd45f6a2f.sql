-- Lösche alle Dennis Club 1 Einträge und zugehörige Daten
-- IDs der zu löschenden Clubs sammeln
DO $$
DECLARE
    club_ids uuid[] := ARRAY(
        SELECT id FROM clubs 
        WHERE name = 'Dennis Club 1'
    );
    club_id uuid;
BEGIN
    -- <PERSON><PERSON><PERSON> jeden Dennis Club 1
    FOREACH club_id IN ARRAY club_ids
    LOOP
        -- Lösche zugehörige Daten in der richtigen Reihenfolge (abhängige zuerst)
        
        -- Lösche Buchungen
        DELETE FROM bookings WHERE club_id = club_id;
        
        -- Lösche Court-bezogene Daten
        DELETE FROM court_availability WHERE court_id IN (SELECT id FROM courts WHERE club_id = club_id);
        DELETE FROM court_blocks WHERE court_id IN (SELECT id FROM courts WHERE club_id = club_id);
        DELETE FROM courts WHERE club_id = club_id;
        
        -- Lösche Aktivitäten und zugehörige Daten
        DELETE FROM activity_registrations WHERE activity_id IN (SELECT id FROM activities WHERE club_id = club_id);
        DELETE FROM work_log_assignments WHERE work_log_id IN (
            SELECT wl.id FROM work_logs wl 
            JOIN activities a ON wl.activity_id = a.id 
            WHERE a.club_id = club_id
        );
        DELETE FROM work_logs WHERE activity_id IN (SELECT id FROM activities WHERE club_id = club_id);
        DELETE FROM activities WHERE club_id = club_id;
        
        -- Lösche Members und zugehörige Daten
        DELETE FROM members WHERE club_id = club_id;
        
        -- Lösche Accounts und zugehörige Daten für diesen Club
        DELETE FROM login_identities WHERE account_id IN (SELECT id FROM accounts WHERE club_id = club_id);
        DELETE FROM invites WHERE account_id IN (SELECT id FROM accounts WHERE club_id = club_id);
        DELETE FROM fee_assignments WHERE account_id IN (SELECT id FROM accounts WHERE club_id = club_id);
        DELETE FROM guardianships WHERE guardian_account_id IN (SELECT id FROM accounts WHERE club_id = club_id) 
                                   OR dependent_account_id IN (SELECT id FROM accounts WHERE club_id = club_id);
        DELETE FROM household_members WHERE account_id IN (SELECT id FROM accounts WHERE club_id = club_id);
        DELETE FROM group_members WHERE account_id IN (SELECT id FROM accounts WHERE club_id = club_id);
        DELETE FROM billing_agreements WHERE payer_account_id IN (SELECT id FROM accounts WHERE club_id = club_id)
                                        OR beneficiary_account_id IN (SELECT id FROM accounts WHERE club_id = club_id);
        DELETE FROM accounts WHERE club_id = club_id;
        
        -- Lösche Club-Mitgliedschaften
        DELETE FROM club_memberships WHERE club_id = club_id;
        
        -- Lösche Club-Subscriptions
        DELETE FROM club_subscriptions WHERE club_id = club_id;
        
        -- Lösche Domains
        DELETE FROM domains WHERE club_id = club_id;
        
        -- Lösche Audit Logs für diesen Club
        DELETE FROM audit_logs WHERE club_id = club_id;
        
        -- Lösche Support Tickets für diesen Club
        DELETE FROM support_tickets WHERE club_id = club_id;
        
        RAISE NOTICE 'Deleted all data for club_id: %', club_id;
    END LOOP;
    
    -- Lösche schließlich die Club-Einträge selbst
    DELETE FROM clubs WHERE name = 'Dennis Club 1';
    
    RAISE NOTICE 'Successfully deleted all Dennis Club 1 entries and related data';
END $$;