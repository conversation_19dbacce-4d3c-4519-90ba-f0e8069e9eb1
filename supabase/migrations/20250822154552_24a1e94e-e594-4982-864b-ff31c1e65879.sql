-- Create tournament_courts table to link tournaments to courts with availability
CREATE TABLE public.tournament_courts (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  tournament_id UUID NOT NULL REFERENCES public.tournaments(id) ON DELETE CASCADE,
  court_id UUID NOT NULL REFERENCES public.courts(id) ON DELETE CASCADE,
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  available_days INTEGER[] NOT NULL DEFAULT ARRAY[1,2,3,4,5,6,7], -- 1=Monday, 7=Sunday
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(tournament_id, court_id)
);

-- Add scheduling columns to tournament_matches
ALTER TABLE public.tournament_matches 
ADD COLUMN scheduled_date DATE,
ADD COLUMN scheduled_time TIME,
ADD COLUMN court_id UUID REFERENCES public.courts(id),
ADD COLUMN booking_id UUID REFERENCES public.bookings(id),
ADD COLUMN estimated_duration_minutes INTEGER DEFAULT 90;

-- Enable RLS on tournament_courts
ALTER TABLE public.tournament_courts ENABLE ROW LEVEL SECURITY;

-- Tournament courts policies
CREATE POLICY "Club members can view tournament courts in their club"
ON public.tournament_courts FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM public.tournaments t
    JOIN public.club_memberships cm ON cm.club_id = t.club_id
    WHERE t.id = tournament_courts.tournament_id
      AND cm.user_id = auth.uid()
      AND cm.is_active = true
  )
);

CREATE POLICY "Club admins can manage tournament courts in their club"
ON public.tournament_courts FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM public.tournaments t
    JOIN public.club_memberships cm ON cm.club_id = t.club_id
    WHERE t.id = tournament_courts.tournament_id
      AND cm.user_id = auth.uid()
      AND cm.role IN ('admin', 'super_admin')
      AND cm.is_active = true
  )
);

CREATE POLICY "Meta admins can manage all tournament courts"
ON public.tournament_courts FOR ALL
USING (is_meta_admin(auth.uid()));

-- Add trigger for updated_at
CREATE TRIGGER update_tournament_courts_updated_at
  BEFORE UPDATE ON public.tournament_courts
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

-- Add index for better performance
CREATE INDEX idx_tournament_courts_tournament_id ON public.tournament_courts(tournament_id);
CREATE INDEX idx_tournament_courts_court_id ON public.tournament_courts(court_id);
CREATE INDEX idx_tournament_matches_scheduled ON public.tournament_matches(scheduled_date, scheduled_time);
CREATE INDEX idx_tournament_matches_court_id ON public.tournament_matches(court_id);