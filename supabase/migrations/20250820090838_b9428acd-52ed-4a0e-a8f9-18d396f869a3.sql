-- Extend clubs table with work service configuration
ALTER TABLE public.clubs 
ADD COLUMN work_service_enabled boolean NOT NULL DEFAULT false,
ADD COLUMN work_service_minimum_hours_enabled boolean NOT NULL DEFAULT false,
ADD COLUMN default_annual_work_hours numeric DEFAULT 4;

-- Create table for member work service targets and tracking
CREATE TABLE public.member_work_service_targets (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  club_id uuid NOT NULL,
  user_id uuid NOT NULL,
  year integer NOT NULL,
  target_hours numeric NOT NULL DEFAULT 0,
  completed_hours numeric NOT NULL DEFAULT 0,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  UNIQUE(club_id, user_id, year)
);

-- Enable RLS on member_work_service_targets
ALTER TABLE public.member_work_service_targets ENABLE ROW LEVEL SECURITY;

-- Create policies for member_work_service_targets
CREATE POLICY "Club members can view their own targets"
ON public.member_work_service_targets
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid() 
      AND cm.club_id = member_work_service_targets.club_id
      AND cm.is_active = true
  )
);

CREATE POLICY "Club admins can manage all targets"
ON public.member_work_service_targets
FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid() 
      AND cm.club_id = member_work_service_targets.club_id
      AND cm.role IN ('admin', 'super_admin')
      AND cm.is_active = true
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid() 
      AND cm.club_id = member_work_service_targets.club_id
      AND cm.role IN ('admin', 'super_admin')
      AND cm.is_active = true
  )
);

-- Create work_log_assignments table to track individual work assignments
CREATE TABLE public.work_log_assignments (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  club_id uuid NOT NULL,
  user_id uuid NOT NULL,
  activity_id uuid NOT NULL,
  date date NOT NULL,
  duration_hours numeric NOT NULL,
  notes text,
  approved_by uuid,
  approved_at timestamp with time zone,
  status text NOT NULL DEFAULT 'pending',
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now()
);

-- Enable RLS on work_log_assignments
ALTER TABLE public.work_log_assignments ENABLE ROW LEVEL SECURITY;

-- Create policies for work_log_assignments
CREATE POLICY "Club members can view their own assignments"
ON public.work_log_assignments
FOR SELECT
USING (
  user_id = auth.uid() OR
  EXISTS (
    SELECT 1 FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid() 
      AND cm.club_id = work_log_assignments.club_id
      AND cm.role IN ('admin', 'super_admin')
      AND cm.is_active = true
  )
);

CREATE POLICY "Club members can create assignments"
ON public.work_log_assignments
FOR INSERT
WITH CHECK (
  user_id = auth.uid() AND
  EXISTS (
    SELECT 1 FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid() 
      AND cm.club_id = work_log_assignments.club_id
      AND cm.is_active = true
  )
);

CREATE POLICY "Club admins can manage all assignments"
ON public.work_log_assignments
FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid() 
      AND cm.club_id = work_log_assignments.club_id
      AND cm.role IN ('admin', 'super_admin')
      AND cm.is_active = true
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid() 
      AND cm.club_id = work_log_assignments.club_id
      AND cm.role IN ('admin', 'super_admin')
      AND cm.is_active = true
  )
);

-- Function to update member work service targets when assignments change
CREATE OR REPLACE FUNCTION public.update_member_work_targets()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  assignment_year integer;
  current_completed numeric;
BEGIN
  -- Get the year from the assignment date
  assignment_year := EXTRACT(YEAR FROM COALESCE(NEW.date, OLD.date));
  
  -- Calculate completed hours for the user in that year
  SELECT COALESCE(SUM(duration_hours), 0)
  INTO current_completed
  FROM public.work_log_assignments
  WHERE user_id = COALESCE(NEW.user_id, OLD.user_id)
    AND club_id = COALESCE(NEW.club_id, OLD.club_id)
    AND EXTRACT(YEAR FROM date) = assignment_year
    AND status = 'approved';
  
  -- Update or insert the target record
  INSERT INTO public.member_work_service_targets (
    club_id, user_id, year, target_hours, completed_hours
  )
  VALUES (
    COALESCE(NEW.club_id, OLD.club_id),
    COALESCE(NEW.user_id, OLD.user_id),
    assignment_year,
    (SELECT default_annual_work_hours FROM public.clubs WHERE id = COALESCE(NEW.club_id, OLD.club_id)),
    current_completed
  )
  ON CONFLICT (club_id, user_id, year)
  DO UPDATE SET
    completed_hours = current_completed,
    updated_at = now();
  
  RETURN COALESCE(NEW, OLD);
END;
$$;

-- Create trigger for automatic target updates
CREATE TRIGGER update_work_targets_on_assignment_change
  AFTER INSERT OR UPDATE OR DELETE ON public.work_log_assignments
  FOR EACH ROW
  EXECUTE FUNCTION public.update_member_work_targets();

-- Add updated_at trigger to member_work_service_targets
CREATE TRIGGER update_member_work_service_targets_updated_at
  BEFORE UPDATE ON public.member_work_service_targets
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();