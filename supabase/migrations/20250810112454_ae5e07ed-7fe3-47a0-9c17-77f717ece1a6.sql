-- Migration bundle: accounts rights + bookings + households + guardianships + billing + invites + RLS + extensions

-- 1) Core structures and additive changes
-- ======================================
-- From 20250810110125_403d62a0-8221-4bb5-84a6-193883a3738c.sql

-- Block 1: Extensions
CREATE EXTENSION IF NOT EXISTS pgcrypto;
CREATE EXTENSION IF NOT EXISTS btree_gist;

-- Block 1: Optionale Multi-Login-Identitäten
CREATE TABLE IF NOT EXISTS public.login_identities (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id uuid NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
  type text CHECK (type IN ('email','phone','passkey','username')) NOT NULL,
  value text,
  is_primary boolean DEFAULT false,
  created_at timestamptz DEFAULT now(),
  UNIQUE (type, value)
);

-- Block 1: Haushalt
CREATE TABLE IF NOT EXISTS public.households (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text,
  created_at timestamptz DEFAULT now()
);

CREATE TABLE IF NOT EXISTS public.household_members (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  household_id uuid NOT NULL REFERENCES public.households(id) ON DELETE CASCADE,
  account_id uuid NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
  role text CHECK (role IN ('owner','adult','child')) NOT NULL,
  valid_from date NOT NULL DEFAULT current_date,
  valid_to date
);

CREATE UNIQUE INDEX IF NOT EXISTS uniq_household_member_active
  ON public.household_members(household_id, account_id)
  WHERE valid_to IS NULL;

-- Block 1: Sorgeberechtigung (Guardianships)
CREATE TABLE IF NOT EXISTS public.guardianships (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  guardian_account_id uuid NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
  dependent_account_id uuid NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
  valid_from date NOT NULL DEFAULT current_date,
  valid_to date,
  CONSTRAINT guardian_not_self CHECK (guardian_account_id <> dependent_account_id)
);

CREATE UNIQUE INDEX IF NOT EXISTS uniq_guard_active
  ON public.guardianships(guardian_account_id, dependent_account_id)
  WHERE valid_to IS NULL;

CREATE INDEX IF NOT EXISTS idx_guardianships_guardian_active
  ON public.guardianships (guardian_account_id)
  WHERE valid_to IS NULL;

-- Block 1: Billing Agreements (Zahler → Begünstigter)
CREATE TABLE IF NOT EXISTS public.billing_agreements (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  payer_account_id uuid NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
  beneficiary_account_id uuid NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
  share_percent numeric(5,2) CHECK (share_percent >= 0 AND share_percent <= 100),
  valid_from date NOT NULL DEFAULT current_date,
  valid_to date,
  CONSTRAINT payer_not_beneficiary CHECK (payer_account_id <> beneficiary_account_id)
);

-- Block 3: Einladungen (Claim-Flow)
CREATE TABLE IF NOT EXISTS public.invites (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id uuid NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
  method text CHECK (method IN ('email','phone','passkey')) NOT NULL,
  target text,
  status text CHECK (status IN ('pending','accepted','expired','revoked')) DEFAULT 'pending',
  created_at timestamptz DEFAULT now(),
  expires_at timestamptz
);

-- Block 2: Buchungen – neue Spalten (additive Migration)
ALTER TABLE public.bookings
  ADD COLUMN IF NOT EXISTS start_at timestamptz,
  ADD COLUMN IF NOT EXISTS end_at timestamptz,
  ADD COLUMN IF NOT EXISTS acting_account_id uuid REFERENCES public.accounts(id),
  ADD COLUMN IF NOT EXISTS booked_for_account_id uuid REFERENCES public.accounts(id);

-- Zeitrange-Column generieren, falls nicht vorhanden
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_schema = 'public' AND table_name='bookings' AND column_name='time_range'
  ) THEN
    ALTER TABLE public.bookings
      ADD COLUMN time_range tstzrange GENERATED ALWAYS AS (tstzrange(start_at, end_at, '[)')) STORED;
  END IF;
END $$;

-- Overlap-Constraint (GiST) idempotent
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_constraint WHERE conname='bookings_no_overlap'
  ) THEN
    ALTER TABLE public.bookings
      ADD CONSTRAINT bookings_no_overlap EXCLUDE USING gist (
        court_id WITH =,
        time_range WITH &&
      );
  END IF;
END $$;

-- Nützliche Indizes
CREATE INDEX IF NOT EXISTS idx_bookings_court_start ON public.bookings (court_id, start_at);
CREATE INDEX IF NOT EXISTS idx_bookings_booked_for ON public.bookings (booked_for_account_id, start_at);

-- Block 3: Gebühren – auf account_id erweitern (additiv, kein Drop alter Spalten)
ALTER TABLE public.fee_assignments
  ADD COLUMN IF NOT EXISTS account_id uuid REFERENCES public.accounts(id);

-- Block 4: Helper-Funktion effective_rights
CREATE OR REPLACE FUNCTION public.effective_rights(u uuid)
RETURNS TABLE(account_id uuid)
LANGUAGE sql
STABLE
SECURITY DEFINER
SET search_path TO 'public'
AS $$
  -- Eigener Account (mit Login)
  SELECT a.id
  FROM public.accounts a
  WHERE a.user_id = u
  UNION
  -- Abhängige (ohne eigenen Login) aus aktiven Sorgeverhältnissen
  SELECT g.dependent_account_id
  FROM public.guardianships g
  JOIN public.accounts dep ON dep.id = g.dependent_account_id
  WHERE g.guardian_account_id IN (
    SELECT id FROM public.accounts WHERE user_id = u
  )
    AND g.valid_to IS NULL
    AND dep.user_id IS NULL
$$;

-- Block 4: RLS – additive Policies (brechen bestehendes Verhalten nicht)
-- Accounts: RLS aktivieren (falls nicht bereits aktiv)
ALTER TABLE public.accounts ENABLE ROW LEVEL SECURITY;

-- Admins dürfen alles auf accounts
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='accounts' AND policyname='Admins manage accounts'
  ) THEN
    CREATE POLICY "Admins manage accounts"
      ON public.accounts
      AS PERMISSIVE
      FOR ALL
      TO authenticated
      USING (public.has_role(auth.uid(), 'admin'::public.app_role))
      WITH CHECK (public.has_role(auth.uid(), 'admin'::public.app_role));
  END IF;
END $$;

-- Account Owner: select/update eigener Account
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='accounts' AND policyname='Account owners select'
  ) THEN
    CREATE POLICY "Account owners select"
      ON public.accounts
      FOR SELECT
      TO authenticated
      USING (user_id = auth.uid());
  END IF;
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='accounts' AND policyname='Account owners update'
  ) THEN
    CREATE POLICY "Account owners update"
      ON public.accounts
      FOR UPDATE
      TO authenticated
      USING (user_id = auth.uid())
      WITH CHECK (user_id = auth.uid());
  END IF;
END $$;

-- Guardians: select/update auf Dependents ohne Login
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='accounts' AND policyname='Guardians select dependents'
  ) THEN
    CREATE POLICY "Guardians select dependents"
      ON public.accounts
      FOR SELECT
      TO authenticated
      USING (id IN (SELECT account_id FROM public.effective_rights(auth.uid())));
  END IF;
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='accounts' AND policyname='Guardians update dependents'
  ) THEN
    CREATE POLICY "Guardians update dependents"
      ON public.accounts
      FOR UPDATE
      TO authenticated
      USING (id IN (SELECT account_id FROM public.effective_rights(auth.uid())))
      WITH CHECK (id IN (SELECT account_id FROM public.effective_rights(auth.uid())));
  END IF;
END $$;

-- Bookings: RLS aktivieren (falls nicht aktiv)
ALTER TABLE public.bookings ENABLE ROW LEVEL SECURITY;

-- Admins dürfen alles an bookings
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='bookings' AND policyname='Admins manage bookings'
  ) THEN
    CREATE POLICY "Admins manage bookings"
      ON public.bookings
      AS PERMISSIVE
      FOR ALL
      TO authenticated
      USING (public.has_role(auth.uid(), 'admin'::public.app_role))
      WITH CHECK (public.has_role(auth.uid(), 'admin'::public.app_role));
  END IF;
END $$;

-- Additive: Nutzer sehen eigene/vertretbare Buchungen
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='bookings' AND policyname='Users view own and represented bookings'
  ) THEN
    CREATE POLICY "Users view own and represented bookings"
      ON public.bookings
      FOR SELECT
      TO authenticated
      USING (
        created_by = auth.uid()
        OR booked_for_account_id IN (SELECT account_id FROM public.effective_rights(auth.uid()))
      );
  END IF;
END $$;

-- Additive: Nutzer dürfen Buchungen anlegen für Personen in effective_rights
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='bookings' AND policyname='Users create bookings within rights'
  ) THEN
    CREATE POLICY "Users create bookings within rights"
      ON public.bookings
      FOR INSERT
      TO authenticated
      WITH CHECK (
        created_by = auth.uid()
        AND acting_account_id IN (SELECT account_id FROM public.effective_rights(auth.uid()))
        AND booked_for_account_id IN (SELECT account_id FROM public.effective_rights(auth.uid()))
      );
  END IF;
END $$;

-- Additive: Nutzer dürfen eigene Buchungen löschen
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='bookings' AND policyname='Users delete own bookings'
  ) THEN
    CREATE POLICY "Users delete own bookings"
      ON public.bookings
      FOR DELETE
      TO authenticated
      USING (created_by = auth.uid());
  END IF;
END $$;

-- Gebühren: additive RLS für account_id
ALTER TABLE public.fee_assignments ENABLE ROW LEVEL SECURITY;

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='fee_assignments' AND policyname='Users view fee assignments for effective accounts'
  ) THEN
    CREATE POLICY "Users view fee assignments for effective accounts"
      ON public.fee_assignments
      FOR SELECT
      TO authenticated
      USING (account_id IN (SELECT account_id FROM public.effective_rights(auth.uid())));
  END IF;
END $$;


-- 2) RLS for new tables
-- ======================
-- From 20250810110528_889186a5-71d6-4df5-b4b7-514b72431fbc.sql

-- login_identities
ALTER TABLE public.login_identities ENABLE ROW LEVEL SECURITY;
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='login_identities' AND policyname='Admins manage login_identities'
  ) THEN
    CREATE POLICY "Admins manage login_identities"
      ON public.login_identities
      FOR ALL
      TO authenticated
      USING (public.has_role(auth.uid(), 'admin'::public.app_role))
      WITH CHECK (public.has_role(auth.uid(), 'admin'::public.app_role));
  END IF;
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='login_identities' AND policyname='Owners view/manage their login identities'
  ) THEN
    CREATE POLICY "Owners view/manage their login identities"
      ON public.login_identities
      FOR ALL
      TO authenticated
      USING (EXISTS (
        SELECT 1 FROM public.accounts a
        WHERE a.id = login_identities.account_id AND a.user_id = auth.uid()
      ))
      WITH CHECK (EXISTS (
        SELECT 1 FROM public.accounts a
        WHERE a.id = login_identities.account_id AND a.user_id = auth.uid()
      ));
  END IF;
END $$;

-- households
ALTER TABLE public.households ENABLE ROW LEVEL SECURITY;
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='households' AND policyname='Admins manage households'
  ) THEN
    CREATE POLICY "Admins manage households"
      ON public.households
      FOR ALL
      TO authenticated
      USING (public.has_role(auth.uid(), 'admin'::public.app_role))
      WITH CHECK (public.has_role(auth.uid(), 'admin'::public.app_role));
  END IF;
END $$;

-- household_members
ALTER TABLE public.household_members ENABLE ROW LEVEL SECURITY;
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='household_members' AND policyname='Admins manage household_members'
  ) THEN
    CREATE POLICY "Admins manage household_members"
      ON public.household_members
      FOR ALL
      TO authenticated
      USING (public.has_role(auth.uid(), 'admin'::public.app_role))
      WITH CHECK (public.has_role(auth.uid(), 'admin'::public.app_role));
  END IF;
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='household_members' AND policyname='Users view their household membership'
  ) THEN
    CREATE POLICY "Users view their household membership"
      ON public.household_members
      FOR SELECT
      TO authenticated
      USING (account_id IN (SELECT account_id FROM public.effective_rights(auth.uid())));
  END IF;
END $$;

-- guardianships
ALTER TABLE public.guardianships ENABLE ROW LEVEL SECURITY;
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='guardianships' AND policyname='Admins manage guardianships'
  ) THEN
    CREATE POLICY "Admins manage guardianships"
      ON public.guardianships
      FOR ALL
      TO authenticated
      USING (public.has_role(auth.uid(), 'admin'::public.app_role))
      WITH CHECK (public.has_role(auth.uid(), 'admin'::public.app_role));
  END IF;
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='guardianships' AND policyname='Guardians view active guardianships'
  ) THEN
    CREATE POLICY "Guardians view active guardianships"
      ON public.guardianships
      FOR SELECT
      TO authenticated
      USING (
        guardian_account_id IN (SELECT account_id FROM public.effective_rights(auth.uid()))
        OR dependent_account_id IN (SELECT account_id FROM public.effective_rights(auth.uid()))
      );
  END IF;
END $$;

-- billing_agreements
ALTER TABLE public.billing_agreements ENABLE ROW LEVEL SECURITY;
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='billing_agreements' AND policyname='Admins manage billing_agreements'
  ) THEN
    CREATE POLICY "Admins manage billing_agreements"
      ON public.billing_agreements
      FOR ALL
      TO authenticated
      USING (public.has_role(auth.uid(), 'admin'::public.app_role))
      WITH CHECK (public.has_role(auth.uid(), 'admin'::public.app_role));
  END IF;
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='billing_agreements' AND policyname='Users view their billing agreements'
  ) THEN
    CREATE POLICY "Users view their billing agreements"
      ON public.billing_agreements
      FOR SELECT
      TO authenticated
      USING (
        payer_account_id IN (SELECT account_id FROM public.effective_rights(auth.uid()))
        OR beneficiary_account_id IN (SELECT account_id FROM public.effective_rights(auth.uid()))
      );
  END IF;
END $$;

-- invites
ALTER TABLE public.invites ENABLE ROW LEVEL SECURITY;
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='invites' AND policyname='Admins manage invites'
  ) THEN
    CREATE POLICY "Admins manage invites"
      ON public.invites
      FOR ALL
      TO authenticated
      USING (public.has_role(auth.uid(), 'admin'::public.app_role))
      WITH CHECK (public.has_role(auth.uid(), 'admin'::public.app_role));
  END IF;
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='invites' AND policyname='Owners manage their invites'
  ) THEN
    CREATE POLICY "Owners manage their invites"
      ON public.invites
      FOR ALL
      TO authenticated
      USING (account_id IN (SELECT account_id FROM public.effective_rights(auth.uid())))
      WITH CHECK (account_id IN (SELECT account_id FROM public.effective_rights(auth.uid())));
  END IF;
END $$;


-- 3) Extensions schema alignment
-- ===============================
-- From 20250810110942_2c13fc5c-8c8c-4828-a17d-0ed415f6f9b4.sql

-- Move extensions to the 'extensions' schema to satisfy linter recommendations
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_namespace WHERE nspname = 'extensions') THEN
    IF EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'btree_gist') THEN
      EXECUTE 'ALTER EXTENSION btree_gist SET SCHEMA extensions';
    END IF;
    IF EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'pgcrypto') THEN
      EXECUTE 'ALTER EXTENSION pgcrypto SET SCHEMA extensions';
    END IF;
  END IF;
END $$;