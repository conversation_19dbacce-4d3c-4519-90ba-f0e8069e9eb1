-- Add SMTP configuration to clubs.settings JSON field
-- This migration extends the existing clubs.settings JSON structure

-- First, let's ensure all clubs have a settings field initialized
UPDATE public.clubs 
SET settings = COALESCE(settings, '{}'::jsonb)
WHERE settings IS NULL;

-- Add a comment to document the new SMTP configuration structure
COMMENT ON COLUMN public.clubs.settings IS 'Club settings JSON. Can include SMTP configuration: 
{
  "smtp": {
    "enabled": boolean,
    "provider": "gmail|outlook|ionos|strato|custom",
    "host": "smtp.example.com",
    "port": 587,
    "secure": boolean,
    "user": "<EMAIL>", 
    "password_key": "supabase_secret_key_name",
    "from_name": "Club Name",
    "from_email": "<EMAIL>",
    "reply_to": "<EMAIL>"
  }
}';

-- Create an index on settings for better performance when querying SMTP configurations
CREATE INDEX IF NOT EXISTS idx_clubs_settings_smtp 
ON public.clubs 
USING GIN ((settings->'smtp')) 
WHERE (settings->'smtp') IS NOT NULL;