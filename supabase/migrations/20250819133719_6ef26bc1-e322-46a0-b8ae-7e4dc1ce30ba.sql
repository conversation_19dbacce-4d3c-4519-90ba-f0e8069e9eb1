-- Create function to get current user's display name
CREATE OR REPLACE FUNCTION public.get_current_user_display_name()
RETURNS TEXT AS $$
DECLARE
  user_name TEXT;
BEGIN
  -- Try to get name from profile first
  SELECT TRIM(COALESCE(first_name, '') || ' ' || COALESCE(last_name, ''))
  INTO user_name
  FROM public.profiles 
  WHERE id = auth.uid();
  
  -- If no profile name, fall back to email
  IF user_name IS NULL OR user_name = '' THEN
    SELECT email INTO user_name FROM auth.users WHERE id = auth.uid();
  END IF;
  
  RETURN user_name;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER STABLE;

-- Update booking RLS policies to allow involved players to edit
DROP POLICY IF EXISTS "bookings_update" ON public.bookings;
DROP POLICY IF EXISTS "bookings_delete" ON public.bookings;

-- New update policy: creators, admins/trainers, or involved players can update
CREATE POLICY "bookings_update" ON public.bookings
FOR UPDATE 
USING (
  EXISTS (
    SELECT 1 FROM club_memberships m
    WHERE m.user_id = auth.uid() 
      AND m.club_id = bookings.club_id 
      AND COALESCE(m.is_active, true) = true
  )
)
WITH CHECK (
  (auth.uid() = created_by) OR 
  (EXISTS (
    SELECT 1 FROM club_memberships m
    WHERE m.user_id = auth.uid() 
      AND m.club_id = bookings.club_id 
      AND m.role IN ('admin', 'trainer') 
      AND COALESCE(m.is_active, true) = true
  )) OR
  (public.get_current_user_display_name() = player_name OR 
   public.get_current_user_display_name() = partner_name)
);

-- New delete policy: creators, admins/trainers, or involved players can delete
CREATE POLICY "bookings_delete" ON public.bookings
FOR DELETE 
USING (
  (auth.uid() = created_by) OR 
  (EXISTS (
    SELECT 1 FROM club_memberships m
    WHERE m.user_id = auth.uid() 
      AND m.club_id = bookings.club_id 
      AND m.role IN ('admin', 'trainer') 
      AND COALESCE(m.is_active, true) = true
  )) OR
  (public.get_current_user_display_name() = player_name OR 
   public.get_current_user_display_name() = partner_name)
);