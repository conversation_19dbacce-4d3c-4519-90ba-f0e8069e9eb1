-- Add title/salutation field to relevant tables
ALTER TABLE public.profiles 
ADD COLUMN title text;

ALTER TABLE public.club_memberships 
ADD COLUMN title text;

ALTER TABLE public.accounts 
ADD COLUMN title text;

-- Update the sync_master_data function to include the title field
CREATE OR REPLACE FUNCTION public.sync_master_data()
R<PERSON><PERSON><PERSON> trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = 'public'
AS $$
BEGIN
  -- <PERSON><PERSON> profiles aktualisiert wird, synchronisiere zu club_memberships, accounts und waitlist_entries
  IF TG_TABLE_NAME = 'profiles' THEN
    -- Update club_memberships
    UPDATE public.club_memberships 
    SET 
      title = NEW.title,
      first_name = NEW.first_name,
      last_name = NEW.last_name,
      email = NEW.email,
      address_street = NEW.street,
      address_postal_code = NEW.postal_code,
      address_city = NEW.city
    WHERE user_id = NEW.id;
    
    -- Update accounts
    UPDATE public.accounts 
    SET 
      title = NEW.title,
      first_name = NEW.first_name,
      last_name = NEW.last_name,
      email = NEW.email,
      address_street = NEW.street,
      address_house_number = NEW.house_number,
      address_postal_code = NEW.postal_code,
      address_city = NEW.city,
      address_full = CONCAT_WS(' ', NEW.street, NEW.house_number, NEW.postal_code, NEW.city),
      birth_date = NEW.birth_date
    WHERE user_id = NEW.id;
    
    -- Update booking names with title
    UPDATE public.bookings 
    SET 
      player_name = CONCAT_WS(' ', NEW.title, NEW.first_name, NEW.last_name)
    WHERE player_name = CONCAT_WS(' ', OLD.title, OLD.first_name, OLD.last_name) 
      OR created_by = NEW.id;
      
    UPDATE public.bookings 
    SET 
      partner_name = CONCAT_WS(' ', NEW.title, NEW.first_name, NEW.last_name)
    WHERE partner_name = CONCAT_WS(' ', OLD.title, OLD.first_name, OLD.last_name);

    -- Update waitlist_entries names with title
    UPDATE public.waitlist_entries 
    SET 
      player_name = CONCAT_WS(' ', NEW.title, NEW.first_name, NEW.last_name)
    WHERE player_name = CONCAT_WS(' ', OLD.title, OLD.first_name, OLD.last_name) 
      OR user_id = NEW.id;
      
    UPDATE public.waitlist_entries 
    SET 
      partner_name = CONCAT_WS(' ', NEW.title, NEW.first_name, NEW.last_name)
    WHERE partner_name = CONCAT_WS(' ', OLD.title, OLD.first_name, OLD.last_name);
    
  END IF;
  
  -- Wenn club_memberships aktualisiert wird, synchronisiere zurück zu profiles, accounts und waitlist_entries
  IF TG_TABLE_NAME = 'club_memberships' THEN
    -- Update profiles (falls Daten in club_memberships aktueller sind)
    UPDATE public.profiles 
    SET 
      title = NEW.title,
      first_name = NEW.first_name,
      last_name = NEW.last_name,
      email = NEW.email,
      street = NEW.address_street,
      postal_code = NEW.address_postal_code,
      city = NEW.address_city,
      birth_date = NEW.birth_date
    WHERE id = NEW.user_id
      AND (title != NEW.title OR first_name != NEW.first_name OR last_name != NEW.last_name OR email != NEW.email);
    
    -- Update accounts
    UPDATE public.accounts 
    SET 
      title = NEW.title,
      first_name = NEW.first_name,
      last_name = NEW.last_name,
      email = NEW.email,
      phone = NEW.phone,
      address_street = NEW.address_street,
      address_postal_code = NEW.address_postal_code,
      address_city = NEW.address_city,
      address_full = CONCAT_WS(' ', NEW.address_street, NEW.address_postal_code, NEW.address_city),
      birth_date = NEW.birth_date
    WHERE user_id = NEW.user_id;
    
    -- Update booking names with title
    UPDATE public.bookings 
    SET 
      player_name = CONCAT_WS(' ', NEW.title, NEW.first_name, NEW.last_name)
    WHERE player_name = CONCAT_WS(' ', OLD.title, OLD.first_name, OLD.last_name) 
      OR created_by = NEW.user_id;
      
    UPDATE public.bookings 
    SET 
      partner_name = CONCAT_WS(' ', NEW.title, NEW.first_name, NEW.last_name)
    WHERE partner_name = CONCAT_WS(' ', OLD.title, OLD.first_name, OLD.last_name);

    -- Update waitlist_entries names with title
    UPDATE public.waitlist_entries 
    SET 
      player_name = CONCAT_WS(' ', NEW.title, NEW.first_name, NEW.last_name)
    WHERE player_name = CONCAT_WS(' ', OLD.title, OLD.first_name, OLD.last_name) 
      OR user_id = NEW.user_id;
      
    UPDATE public.waitlist_entries 
    SET 
      partner_name = CONCAT_WS(' ', NEW.title, NEW.first_name, NEW.last_name)
    WHERE partner_name = CONCAT_WS(' ', OLD.title, OLD.first_name, OLD.last_name);
  END IF;
  
  -- Wenn accounts aktualisiert wird, synchronisiere zu anderen Tabellen
  IF TG_TABLE_NAME = 'accounts' THEN
    UPDATE public.profiles 
    SET 
      title = NEW.title,
      first_name = NEW.first_name,
      last_name = NEW.last_name,
      email = NEW.email,
      street = NEW.address_street,
      house_number = NEW.address_house_number,
      postal_code = NEW.address_postal_code,
      city = NEW.address_city,
      birth_date = NEW.birth_date
    WHERE id = NEW.user_id;
    
    UPDATE public.club_memberships 
    SET 
      title = NEW.title,
      first_name = NEW.first_name,
      last_name = NEW.last_name,
      email = NEW.email,
      phone = NEW.phone,
      address_street = NEW.address_street,
      address_postal_code = NEW.address_postal_code,
      address_city = NEW.address_city,
      birth_date = NEW.birth_date
    WHERE user_id = NEW.user_id;

    -- Update waitlist_entries names with title
    UPDATE public.waitlist_entries 
    SET 
      player_name = CONCAT_WS(' ', NEW.title, NEW.first_name, NEW.last_name)
    WHERE user_id = NEW.user_id;
  END IF;
  
  RETURN NEW;
END;
$$;