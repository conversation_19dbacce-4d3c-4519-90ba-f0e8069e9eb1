-- Create join table for many-to-many between fee_types and membership_types
create table if not exists public.fee_type_membership_types (
  id uuid primary key default gen_random_uuid(),
  fee_type_id uuid not null references public.fee_types(id) on delete cascade,
  membership_type_id uuid not null references public.membership_types(id) on delete cascade,
  created_at timestamptz not null default now(),
  constraint fee_type_membership_types_unique unique (fee_type_id, membership_type_id)
);

-- Enable RLS
alter table public.fee_type_membership_types enable row level security;

-- Policies (idempotent)
DO $$ BEGIN
  CREATE POLICY "Admins can manage fee mappings"
    ON public.fee_type_membership_types
    FOR ALL
    USING (has_role(auth.uid(), 'admin'::app_role))
    WITH CHECK (has_role(auth.uid(), 'admin'::app_role));
EXCEPTION WHEN duplicate_object THEN NULL; END $$;

DO $$ BEGIN
  CREATE POLICY "Everyone can view fee mappings"
    ON public.fee_type_membership_types
    FOR SELECT
    USING (true);
EXCEPTION WHEN duplicate_object THEN NULL; END $$;

-- Data migration from legacy single mapping on fee_types.membership_type_id
-- Handles both cases: membership_types.id stored as text, or membership_types.value stored
with ft as (
  select id as fee_id, membership_type_id
  from public.fee_types
  where membership_type_id is not null
),
mt_by_id as (
  select ft.fee_id, mt.id as membership_id
  from ft
  join public.membership_types mt
    on mt.id::text = ft.membership_type_id
),
mt_by_value as (
  select ft.fee_id, mt.id as membership_id
  from ft
  join public.membership_types mt
    on mt.value = ft.membership_type_id
),
combined as (
  select fee_id, membership_id from mt_by_id
  union
  select fee_id, membership_id from mt_by_value
)
insert into public.fee_type_membership_types (fee_type_id, membership_type_id)
select distinct fee_id, membership_id
from combined
on conflict (fee_type_id, membership_type_id) do nothing;
