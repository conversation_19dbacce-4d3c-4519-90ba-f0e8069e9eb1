-- Fix the type conversion bug in auto-booking function
CREATE OR REPLACE FUNCTION public.check_all_pending_auto_bookings(p_club_id uuid)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
DECLARE
  v_entry record;
  v_booking_slot record;
  v_result jsonb;
  v_processed_count integer := 0;
  v_successful_bookings jsonb[] := '{}';
  v_failed_attempts jsonb[] := '{}';
  v_club_timezone text;
BEGIN
  -- Get club timezone
  SELECT timezone INTO v_club_timezone
  FROM public.clubs
  WHERE id = p_club_id AND is_active = true;
  
  IF NOT FOUND THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'Club not found or inactive'
    );
  END IF;

  -- Loop through all pending auto-booking entries for this club
  FOR v_entry IN 
    SELECT * FROM public.waitlist_entries
    WHERE club_id = p_club_id
      AND status = 'waiting'
      AND auto_booking_enabled = true
      AND preferred_date >= CURRENT_DATE
    ORDER BY position ASC, created_at ASC
  LOOP
    v_processed_count := v_processed_count + 1;
    
    -- For each entry, check if there are any free slots that match
    -- First check if the exact time slot is available
    FOR v_booking_slot IN
      -- Generate hourly slots between entry's time range
      SELECT 
        c.id as court_id, 
        v_entry.start_time_range::time as start_time,
        v_entry.end_time_range::time as end_time
      FROM public.courts c
      WHERE c.club_id = p_club_id
        AND c.locked = false
        AND (array_length(v_entry.preferred_courts, 1) IS NULL 
             OR v_entry.preferred_courts = '{}' 
             OR c.id = ANY(v_entry.preferred_courts))
        -- Check no existing booking conflicts
        AND NOT EXISTS (
          SELECT 1 FROM public.bookings b
          WHERE b.court_id = c.id
            AND b.booking_date = v_entry.preferred_date::date
            AND NOT (v_entry.end_time_range::time <= b.start_time OR v_entry.start_time_range::time >= b.end_time)
        )
      LIMIT 1
    LOOP
      -- Try to create auto-booking
      SELECT public.process_waitlist_auto_booking_enhanced(
        p_club_id,
        v_booking_slot.court_id,
        v_entry.preferred_date::date,
        v_booking_slot.start_time,
        v_booking_slot.end_time
      ) INTO v_result;
      
      IF (v_result->>'success')::boolean THEN
        v_successful_bookings := v_successful_bookings || v_result;
        EXIT; -- Exit inner loop, move to next entry
      ELSE
        v_failed_attempts := v_failed_attempts || jsonb_build_object(
          'entry_id', v_entry.id,
          'court_id', v_booking_slot.court_id,
          'error', v_result->>'message'
        );
      END IF;
    END LOOP;
  END LOOP;

  RETURN jsonb_build_object(
    'success', true,
    'message', 'Auto-booking check completed',
    'club_timezone', v_club_timezone,
    'processed_entries', v_processed_count,
    'successful_bookings', v_successful_bookings,
    'failed_attempts', v_failed_attempts,
    'successful_count', array_length(v_successful_bookings, 1),
    'failed_count', array_length(v_failed_attempts, 1)
  );
END;
$$;