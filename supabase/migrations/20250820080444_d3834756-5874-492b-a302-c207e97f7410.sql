-- <PERSON><PERSON><PERSON><PERSON>e die Synchronisation um waitlist_entries
-- Füge player_name und partner_name zur waitlist_entries Synchronisation hinzu

-- Aktualisiere die sync_master_data Funktion um auch waitlist_entries zu handhaben
CREATE OR REPLACE FUNCTION public.sync_master_data()
RETURNS TRIGGER AS $$
BEGIN
  -- <PERSON><PERSON> profiles aktualisiert wird, synchronisiere zu club_memberships, accounts und waitlist_entries
  IF TG_TABLE_NAME = 'profiles' THEN
    -- Update club_memberships
    UPDATE public.club_memberships 
    SET 
      first_name = NEW.first_name,
      last_name = NEW.last_name,
      email = NEW.email,
      address_street = NEW.street,
      address_postal_code = NEW.postal_code,
      address_city = NEW.city
    WHERE user_id = NEW.id;
    
    -- Update accounts
    UPDATE public.accounts 
    SET 
      first_name = NEW.first_name,
      last_name = NEW.last_name,
      email = NEW.email,
      address_street = NEW.street,
      address_house_number = NEW.house_number,
      address_postal_code = NEW.postal_code,
      address_city = NEW.city,
      address_full = CONCAT_WS(' ', NEW.street, NEW.house_number, NEW.postal_code, NEW.city),
      birth_date = NEW.birth_date
    WHERE user_id = NEW.id;
    
    -- Update booking names
    UPDATE public.bookings 
    SET 
      player_name = CONCAT(NEW.first_name, ' ', NEW.last_name)
    WHERE player_name = CONCAT(OLD.first_name, ' ', OLD.last_name) 
      OR created_by = NEW.id;
      
    UPDATE public.bookings 
    SET 
      partner_name = CONCAT(NEW.first_name, ' ', NEW.last_name)
    WHERE partner_name = CONCAT(OLD.first_name, ' ', OLD.last_name);

    -- Update waitlist_entries names
    UPDATE public.waitlist_entries 
    SET 
      player_name = CONCAT(NEW.first_name, ' ', NEW.last_name)
    WHERE player_name = CONCAT(OLD.first_name, ' ', OLD.last_name) 
      OR user_id = NEW.id;
      
    UPDATE public.waitlist_entries 
    SET 
      partner_name = CONCAT(NEW.first_name, ' ', NEW.last_name)
    WHERE partner_name = CONCAT(OLD.first_name, ' ', OLD.last_name);
    
  END IF;
  
  -- Wenn club_memberships aktualisiert wird, synchronisiere zurück zu profiles, accounts und waitlist_entries
  IF TG_TABLE_NAME = 'club_memberships' THEN
    -- Update profiles (falls Daten in club_memberships aktueller sind)
    UPDATE public.profiles 
    SET 
      first_name = NEW.first_name,
      last_name = NEW.last_name,
      email = NEW.email,
      street = NEW.address_street,
      postal_code = NEW.address_postal_code,
      city = NEW.address_city,
      birth_date = NEW.birth_date
    WHERE id = NEW.user_id
      AND (first_name != NEW.first_name OR last_name != NEW.last_name OR email != NEW.email);
    
    -- Update accounts
    UPDATE public.accounts 
    SET 
      first_name = NEW.first_name,
      last_name = NEW.last_name,
      email = NEW.email,
      phone = NEW.phone,
      address_street = NEW.address_street,
      address_postal_code = NEW.address_postal_code,
      address_city = NEW.address_city,
      address_full = CONCAT_WS(' ', NEW.address_street, NEW.address_postal_code, NEW.address_city),
      birth_date = NEW.birth_date
    WHERE user_id = NEW.user_id;
    
    -- Update booking names
    UPDATE public.bookings 
    SET 
      player_name = CONCAT(NEW.first_name, ' ', NEW.last_name)
    WHERE player_name = CONCAT(OLD.first_name, ' ', OLD.last_name) 
      OR created_by = NEW.user_id;
      
    UPDATE public.bookings 
    SET 
      partner_name = CONCAT(NEW.first_name, ' ', NEW.last_name)
    WHERE partner_name = CONCAT(OLD.first_name, ' ', OLD.last_name);

    -- Update waitlist_entries names
    UPDATE public.waitlist_entries 
    SET 
      player_name = CONCAT(NEW.first_name, ' ', NEW.last_name)
    WHERE player_name = CONCAT(OLD.first_name, ' ', OLD.last_name) 
      OR user_id = NEW.user_id;
      
    UPDATE public.waitlist_entries 
    SET 
      partner_name = CONCAT(NEW.first_name, ' ', NEW.last_name)
    WHERE partner_name = CONCAT(OLD.first_name, ' ', OLD.last_name);
  END IF;
  
  -- Wenn accounts aktualisiert wird, synchronisiere zu anderen Tabellen
  IF TG_TABLE_NAME = 'accounts' THEN
    UPDATE public.profiles 
    SET 
      first_name = NEW.first_name,
      last_name = NEW.last_name,
      email = NEW.email,
      street = NEW.address_street,
      house_number = NEW.address_house_number,
      postal_code = NEW.address_postal_code,
      city = NEW.address_city,
      birth_date = NEW.birth_date
    WHERE id = NEW.user_id;
    
    UPDATE public.club_memberships 
    SET 
      first_name = NEW.first_name,
      last_name = NEW.last_name,
      email = NEW.email,
      phone = NEW.phone,
      address_street = NEW.address_street,
      address_postal_code = NEW.address_postal_code,
      address_city = NEW.address_city,
      birth_date = NEW.birth_date
    WHERE user_id = NEW.user_id;

    -- Update waitlist_entries names
    UPDATE public.waitlist_entries 
    SET 
      player_name = CONCAT(NEW.first_name, ' ', NEW.last_name)
    WHERE user_id = NEW.user_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = 'public';

-- Einmalige Synchronisation aller bestehenden waitlist_entries Namen
-- Aktualisiere alle waitlist_entries mit aktuellen Namen aus profiles
UPDATE public.waitlist_entries we
SET 
  player_name = CONCAT(p.first_name, ' ', p.last_name)
FROM public.profiles p
WHERE we.user_id = p.id
  AND we.player_name != CONCAT(p.first_name, ' ', p.last_name);

-- Aktualisiere partner_names in waitlist_entries basierend auf profiles
UPDATE public.waitlist_entries we
SET 
  partner_name = CONCAT(p.first_name, ' ', p.last_name)
FROM public.profiles p, public.club_memberships cm
WHERE cm.user_id = p.id
  AND CONCAT(cm.first_name, ' ', cm.last_name) = we.partner_name
  AND we.partner_name != CONCAT(p.first_name, ' ', p.last_name);