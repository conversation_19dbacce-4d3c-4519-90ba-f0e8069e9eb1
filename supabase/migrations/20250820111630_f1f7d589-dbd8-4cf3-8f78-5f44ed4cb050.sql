-- Create activity_assignments table for global multi-club support
CREATE TABLE public.activity_assignments (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  activity_id UUID NOT NULL REFERENCES public.activities(id) ON DELETE CASCADE,
  member_id UUID NOT NULL,
  club_id UUID NOT NULL,
  assigned_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  completed_at TIMESTAMP WITH TIME ZONE,
  status TEXT NOT NULL DEFAULT 'assigned' CHECK (status IN ('assigned', 'completed', 'cancelled')),
  hours_credited NUMERIC DEFAULT 0,
  notes TEXT,
  expected_completion_date DATE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create indexes for performance
CREATE INDEX idx_activity_assignments_activity_id ON public.activity_assignments(activity_id);
CREATE INDEX idx_activity_assignments_member_id ON public.activity_assignments(member_id);
CREATE INDEX idx_activity_assignments_club_id ON public.activity_assignments(club_id);
CREATE INDEX idx_activity_assignments_status ON public.activity_assignments(status);

-- Enable RLS
ALTER TABLE public.activity_assignments ENABLE ROW LEVEL SECURITY;

-- RLS Policies for activity_assignments
CREATE POLICY "Club members can view assignments in their club"
ON public.activity_assignments
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid()
      AND cm.club_id = activity_assignments.club_id
      AND cm.is_active = true
  )
);

CREATE POLICY "Club members can create their own assignments"
ON public.activity_assignments
FOR INSERT
WITH CHECK (
  member_id = auth.uid() AND
  EXISTS (
    SELECT 1 FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid()
      AND cm.club_id = activity_assignments.club_id
      AND cm.is_active = true
  )
);

CREATE POLICY "Members can update their own assignments"
ON public.activity_assignments
FOR UPDATE
USING (member_id = auth.uid())
WITH CHECK (member_id = auth.uid());

CREATE POLICY "Club admins can manage all assignments in their club"
ON public.activity_assignments
FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid()
      AND cm.club_id = activity_assignments.club_id
      AND cm.role IN ('admin', 'super_admin')
      AND cm.is_active = true
  )
);

CREATE POLICY "Meta admins can manage all assignments"
ON public.activity_assignments
FOR ALL
USING (is_meta_admin(auth.uid()));

-- Function to update work service hours when activity is assigned
CREATE OR REPLACE FUNCTION public.update_work_hours_on_assignment()
RETURNS TRIGGER AS $$
BEGIN
  -- When an activity is assigned (INSERT) or completed (UPDATE to completed status)
  IF TG_OP = 'INSERT' OR (TG_OP = 'UPDATE' AND NEW.status = 'completed' AND OLD.status != 'completed') THEN
    -- Get the activity's hourly rate and calculate hours credited
    UPDATE public.member_work_service_targets
    SET completed_hours = completed_hours + COALESCE(
      (SELECT hourly_rate FROM public.activities WHERE id = NEW.activity_id), 0
    ),
    updated_at = now()
    WHERE club_id = NEW.club_id 
      AND user_id = NEW.member_id 
      AND year = EXTRACT(YEAR FROM NEW.assigned_at);
    
    -- Create record if it doesn't exist
    INSERT INTO public.member_work_service_targets (
      club_id, user_id, year, completed_hours, target_hours
    )
    SELECT 
      NEW.club_id,
      NEW.member_id,
      EXTRACT(YEAR FROM NEW.assigned_at)::INTEGER,
      COALESCE((SELECT hourly_rate FROM public.activities WHERE id = NEW.activity_id), 0),
      COALESCE((SELECT default_annual_work_hours FROM public.clubs WHERE id = NEW.club_id), 20)
    WHERE NOT EXISTS (
      SELECT 1 FROM public.member_work_service_targets
      WHERE club_id = NEW.club_id 
        AND user_id = NEW.member_id 
        AND year = EXTRACT(YEAR FROM NEW.assigned_at)
    );
    
    -- Update hours_credited in the assignment
    NEW.hours_credited = COALESCE(
      (SELECT hourly_rate FROM public.activities WHERE id = NEW.activity_id), 0
    );
  END IF;
  
  -- When an activity assignment is cancelled, subtract hours
  IF TG_OP = 'UPDATE' AND NEW.status = 'cancelled' AND OLD.status != 'cancelled' THEN
    UPDATE public.member_work_service_targets
    SET completed_hours = GREATEST(0, completed_hours - OLD.hours_credited),
    updated_at = now()
    WHERE club_id = NEW.club_id 
      AND user_id = NEW.member_id 
      AND year = EXTRACT(YEAR FROM NEW.assigned_at);
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for automatic work hours update
CREATE TRIGGER update_work_hours_on_assignment_trigger
  BEFORE INSERT OR UPDATE ON public.activity_assignments
  FOR EACH ROW
  EXECUTE FUNCTION public.update_work_hours_on_assignment();

-- Add updated_at trigger
CREATE TRIGGER update_activity_assignments_updated_at
  BEFORE UPDATE ON public.activity_assignments
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();