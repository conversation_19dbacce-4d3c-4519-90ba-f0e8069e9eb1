-- Erweitere tournaments Tabelle um Format-Konfiguration
ALTER TABLE public.tournaments 
ADD COLUMN format_type TEXT NOT NULL DEFAULT 'knockout',
ADD COLUMN format_config JSONB NOT NULL DEFAULT '{}',
ADD COLUMN format_state JSONB NOT NULL DEFAULT '{}';

-- <PERSON><PERSON>elle tournament_format_definitions Tabelle für vordefinierte Format-Templates
CREATE TABLE public.tournament_format_definitions (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  format_type TEXT NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  default_config JSONB NOT NULL DEFAULT '{}',
  is_active BOOLEAN NOT NULL DEFAULT true,
  category TEXT NOT NULL DEFAULT 'standard',
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Füge RLS Policies hinzu
ALTER TABLE public.tournament_format_definitions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Everyone can view active format definitions" 
ON public.tournament_format_definitions 
FOR SELECT 
USING (is_active = true);

CREATE POLICY "Meta admins can manage format definitions" 
ON public.tournament_format_definitions 
FOR ALL 
USING (is_meta_admin(auth.uid()));

-- Füge Standard-Formate ein
INSERT INTO public.tournament_format_definitions (format_type, name, description, default_config, category) VALUES
('knockout', 'K.o.-System (Single Elimination)', 'Klassisches Ausscheidungsturnier - wer verliert, ist raus', '{"seeding": "random", "consolation_plate": false, "place_playoffs": false}', 'standard'),
('round_robin', 'Round Robin (Jeder gegen jeden)', 'Jeder spielt gegen jeden - Tabellenmodus', '{"groups": 1, "group_size": null, "tiebreaker": ["head_to_head", "set_ratio", "game_ratio"]}', 'standard'),
('groups_to_ko', 'Gruppen → K.o.', 'Vorrunden in Gruppen, dann K.o.-Phase', '{"groups": 4, "group_size": 4, "advance_per_group": 2, "ko_seeding": "group_position"}', 'standard'),
('double_elimination', 'Doppel-K.o. (Double Elimination)', 'Zwei Niederlagen nötig für Ausscheiden', '{"bracket_reset": true, "consolation": false}', 'standard'),
('swiss', 'Schweizer System', 'Paarung nach aktueller Bilanz', '{"rounds": 5, "cut_to_ko": false, "cut_after_round": null}', 'standard'),
('compass', 'Compass Draw', 'E/W/N/S-Draws für komplette Platzierung', '{"full_placement": true, "consolation_draws": true}', 'advanced'),
('qualifying_main_draw', 'Qualifying → Hauptfeld', 'Qualifikation mit Lucky Loser', '{"qualifying_rounds": 2, "slots_to_main": 8, "lucky_loser_policy": "highest_seeded"}', 'advanced'),
('team_tie', 'Team-Event (Davis Cup Style)', 'Mannschaftskampf mit mehreren Matches', '{"rubbers": [{"type": "singles", "count": 4}, {"type": "doubles", "count": 1}], "scoring": "best_of_rubbers"}', 'team'),
('ladder_box_league', 'Box-Liga / Ladder-Liga', 'Saisonliga mit Auf-/Abstieg', '{"boxes": {"size": 6}, "promotion_relegation": true, "season_length_days": 90}', 'league'),
('challenge_ladder', 'Challenge-Ladder', 'Herausforderungsleiter mit Elo-System', '{"challenge_rules": "up_3_down_1", "rating_system": "elo", "decay_enabled": true}', 'league'),
('league', 'Liga/Medenspiele', 'Fixtermine über mehrere Wochen', '{"matchdays": 10, "promotion": true, "table_scoring": "3_1_0"}', 'league'),
('timeboxed_doubles', 'Rotationsdoppel', 'Partnerwechsel jede Runde', '{"rotation": {"partner_changes_every_round": true, "avoid_repeat_partner": true}, "round_minutes": 20}', 'fun'),
('king_of_court', 'King/Queen of the Court', 'Teams kämpfen um den Hauptplatz', '{"courts": 3, "round_minutes": 15, "promotion_relegation": true}', 'fun'),
('timeboxed', 'Zeitmatches', 'Uhrzeitgesteuerte Matches', '{"round_minutes": 30, "draw_policy": "count_games", "sudden_point": false}', 'fun'),
('social_mixer', 'Social Mixer / Juxturnier', 'Zufällige Partner und Spaß', '{"random_partners": true, "random_opponents": true, "points_per_round": true}', 'fun'),
('tiebreak_series', 'Tiebreak-Series', 'Nur Tiebreaks gespielt', '{"tiebreak_type": "tb7", "best_of": 3}', 'short'),
('pro_set_event', 'Pro-Set Event', 'Ein langer Satz bis 8 oder 9', '{"games_to_win": 8, "tiebreak_at": "7_7", "advantage": false}', 'short'),
('short_sets_event', 'Fast4 / Short-Sets', 'Kurze Sätze bis 4', '{"games_to_win": 4, "tiebreak_at": "3_3", "advantage": false, "deciding_point": true}', 'short');

-- Update trigger für tournament_format_definitions
CREATE TRIGGER update_tournament_format_definitions_updated_at
BEFORE UPDATE ON public.tournament_format_definitions
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();