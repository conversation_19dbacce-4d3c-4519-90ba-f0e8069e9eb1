-- Add new database function for checking all pending auto-bookings
CREATE OR REPLACE FUNCTION public.check_all_pending_auto_bookings(p_club_id uuid)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
DECLARE
  v_entry record;
  v_booking_slot record;
  v_result jsonb;
  v_processed_count integer := 0;
  v_successful_bookings jsonb[] := '{}';
  v_failed_attempts jsonb[] := '{}';
  v_club_timezone text;
BEGIN
  -- Get club timezone
  SELECT timezone INTO v_club_timezone
  FROM public.clubs
  WHERE id = p_club_id AND is_active = true;
  
  IF NOT FOUND THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'Club not found or inactive'
    );
  END IF;

  -- Loop through all pending auto-booking entries for this club
  FOR v_entry IN 
    SELECT * FROM public.waitlist_entries
    WHERE club_id = p_club_id
      AND status = 'waiting'
      AND auto_booking_enabled = true
      AND preferred_date >= CURRENT_DATE
    ORDER BY position ASC, created_at ASC
  LOOP
    v_processed_count := v_processed_count + 1;
    
    -- Check for available slots matching this entry's criteria
    FOR v_booking_slot IN
      SELECT c.id as court_id, ca.start_time, ca.end_time
      FROM public.courts c
      CROSS JOIN public.court_availability ca
      WHERE c.club_id = p_club_id
        AND c.locked = false
        AND (array_length(v_entry.preferred_courts, 1) IS NULL 
             OR v_entry.preferred_courts = '{}' 
             OR c.id = ANY(v_entry.preferred_courts))
        AND ca.court_id = c.id
        AND v_entry.preferred_date::text = ANY(ca.days_of_week::text[])
        AND ca.start_time::text >= v_entry.start_time_range
        AND ca.end_time::text <= v_entry.end_time_range
        -- Check no existing booking
        AND NOT EXISTS (
          SELECT 1 FROM public.bookings b
          WHERE b.court_id = c.id
            AND b.booking_date = v_entry.preferred_date::date
            AND NOT (ca.end_time <= b.start_time OR ca.start_time >= b.end_time)
        )
      LIMIT 1
    LOOP
      -- Try to create auto-booking
      SELECT public.process_waitlist_auto_booking_enhanced(
        p_club_id,
        v_booking_slot.court_id,
        v_entry.preferred_date::date,
        v_booking_slot.start_time,
        v_booking_slot.end_time
      ) INTO v_result;
      
      IF (v_result->>'success')::boolean THEN
        v_successful_bookings := v_successful_bookings || v_result;
        EXIT; -- Exit inner loop, move to next entry
      ELSE
        v_failed_attempts := v_failed_attempts || jsonb_build_object(
          'entry_id', v_entry.id,
          'court_id', v_booking_slot.court_id,
          'error', v_result->>'message'
        );
      END IF;
    END LOOP;
  END LOOP;

  RETURN jsonb_build_object(
    'success', true,
    'message', 'Auto-booking check completed',
    'club_timezone', v_club_timezone,
    'processed_entries', v_processed_count,
    'successful_bookings', v_successful_bookings,
    'failed_attempts', v_failed_attempts,
    'successful_count', array_length(v_successful_bookings, 1),
    'failed_count', array_length(v_failed_attempts, 1)
  );
END;
$$;

-- Add function to immediately check auto-booking for new entries
CREATE OR REPLACE FUNCTION public.check_immediate_auto_booking()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
BEGIN
  -- Only process if auto-booking is enabled
  IF NEW.auto_booking_enabled = true AND NEW.status = 'waiting' THEN
    -- Schedule an immediate check (this would be called by frontend)
    -- For now, we just log it in audit_logs for monitoring
    INSERT INTO public.audit_logs (
      resource_type,
      resource_id,
      action,
      user_id,
      club_id,
      new_values
    ) VALUES (
      'waitlist_auto_booking_trigger',
      NEW.id::text,
      'immediate_check_needed',
      NEW.user_id,
      NEW.club_id,
      jsonb_build_object(
        'entry_id', NEW.id,
        'preferred_date', NEW.preferred_date,
        'start_time_range', NEW.start_time_range,
        'end_time_range', NEW.end_time_range
      )
    );
  END IF;
  
  RETURN NEW;
END;
$$;

-- Create trigger for immediate auto-booking checks
DROP TRIGGER IF EXISTS trigger_immediate_auto_booking_check ON public.waitlist_entries;
CREATE TRIGGER trigger_immediate_auto_booking_check
  AFTER INSERT ON public.waitlist_entries
  FOR EACH ROW
  EXECUTE FUNCTION public.check_immediate_auto_booking();