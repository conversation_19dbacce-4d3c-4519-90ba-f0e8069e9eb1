-- Fix the security warning: Set search_path for the grant_super_admin function
CREATE OR REPLACE FUNCTION public.grant_super_admin(_user_email text)
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
SET search_path = 'public'
AS $$
  INSERT INTO public.meta_admin_permissions (user_id, role, granted_by)
  SELECT 
    u.id,
    'SUPER_ADMIN'::meta_admin_role,
    u.id  -- Self-granted for initial setup
  FROM auth.users u
  WHERE u.email = _user_email
  AND NOT EXISTS (
    SELECT 1 FROM public.meta_admin_permissions 
    WHERE user_id = u.id AND role = 'SUPER_ADMIN'::meta_admin_role
  )
  RETURNING true;
$$;