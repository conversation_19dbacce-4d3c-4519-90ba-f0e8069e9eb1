-- Add 'pending' status to activities if not already exists
DO $$
BEGIN
  -- Check if 'pending' status already exists in the status column
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.check_constraints 
    WHERE constraint_name = 'activities_status_check' 
    AND check_clause LIKE '%pending%'
  ) THEN
    -- Add 'pending' to the allowed values if it's not already there
    ALTER TABLE activities 
    DROP CONSTRAINT IF EXISTS activities_status_check;
    
    ALTER TABLE activities 
    ADD CONSTRAINT activities_status_check 
    CHECK (status IN ('pending', 'approved', 'rejected'));
  END IF;
END $$;