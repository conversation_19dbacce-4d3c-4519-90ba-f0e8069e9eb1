-- Fix double counting of work hours in activity assignments
-- Only credit hours when status changes to 'completed', not on assignment

CREATE OR REPLACE FUNCTION public.update_work_hours_on_assignment()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
  -- When an activity is completed (UPDATE to completed status only)
  IF TG_OP = 'UPDATE' AND NEW.status = 'completed' AND OLD.status != 'completed' THEN
    -- Get the activity's hourly rate and calculate hours credited
    UPDATE public.member_work_service_targets
    SET completed_hours = completed_hours + COALESCE(
      (SELECT hourly_rate FROM public.activities WHERE id = NEW.activity_id), 0
    ),
    updated_at = now()
    WHERE club_id = NEW.club_id 
      AND user_id = NEW.member_id 
      AND year = EXTRACT(YEAR FROM NEW.assigned_at);
    
    -- Create record if it doesn't exist
    INSERT INTO public.member_work_service_targets (
      club_id, user_id, year, completed_hours, target_hours
    )
    SELECT 
      NEW.club_id,
      NEW.member_id,
      EXTRACT(YEAR FROM NEW.assigned_at)::INTEGER,
      COALESCE((SELECT hourly_rate FROM public.activities WHERE id = NEW.activity_id), 0),
      COALESCE((SELECT default_annual_work_hours FROM public.clubs WHERE id = NEW.club_id), 20)
    WHERE NOT EXISTS (
      SELECT 1 FROM public.member_work_service_targets
      WHERE club_id = NEW.club_id 
        AND user_id = NEW.member_id 
        AND year = EXTRACT(YEAR FROM NEW.assigned_at)
    );
  END IF;
  
  -- When an assignment is created (INSERT), only set hours_credited for tracking
  IF TG_OP = 'INSERT' THEN
    NEW.hours_credited = COALESCE(
      (SELECT hourly_rate FROM public.activities WHERE id = NEW.activity_id), 0
    );
  END IF;
  
  -- When an activity assignment is cancelled, subtract hours
  IF TG_OP = 'UPDATE' AND NEW.status = 'cancelled' AND OLD.status != 'cancelled' THEN
    UPDATE public.member_work_service_targets
    SET completed_hours = GREATEST(0, completed_hours - OLD.hours_credited),
    updated_at = now()
    WHERE club_id = NEW.club_id 
      AND user_id = NEW.member_id 
      AND year = EXTRACT(YEAR FROM NEW.assigned_at);
  END IF;
  
  RETURN NEW;
END;
$function$