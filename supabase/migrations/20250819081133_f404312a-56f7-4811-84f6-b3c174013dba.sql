-- Behebe alle Funktionen ohne search_path
ALTER FUNCTION public.is_club_admin_for_club(_club_id uuid) SET search_path = 'public';

-- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> und setze search_path für andere Funktionen falls nötig
DO $$
DECLARE
    func_record record;
BEGIN
    -- Suche alle public functions ohne search_path
    FOR func_record IN 
        SELECT p.proname, pg_get_function_identity_arguments(p.oid) as args
        FROM pg_proc p
        JOIN pg_namespace n ON n.oid = p.pronamespace
        WHERE n.nspname = 'public' 
          AND p.proname NOT LIKE 'pg_%'
          AND p.prosecdef = true  -- Security definer functions
          AND NOT EXISTS (
              SELECT 1 FROM pg_proc_config pc 
              WHERE pc.oid = p.oid 
                AND pc.setting[1] = 'search_path'
          )
    LOOP
        EXECUTE format('ALTER FUNCTION public.%I(%s) SET search_path = ''public''', 
                      func_record.proname, func_record.args);
    END LOOP;
END $$;