-- Update "Member One" references to "<PERSON>" in waitlist_entries
UPDATE public.waitlist_entries 
SET player_name = '<PERSON>'
WHERE player_name = 'Member One';

UPDATE public.waitlist_entries 
SET partner_name = '<PERSON>'
WHERE partner_name = 'Member One';

-- Also update any bookings that might still have "Member One"
UPDATE public.bookings 
SET player_name = '<PERSON>'
WHERE player_name = 'Member One';

UPDATE public.bookings 
SET partner_name = '<PERSON>'
WHERE partner_name = 'Member One';