-- Add is_primary flag to accounts table
ALTER TABLE public.accounts ADD COLUMN is_primary boolean NOT NULL DEFAULT false;

-- Create index for efficient querying
CREATE INDEX idx_accounts_user_id_primary ON public.accounts(user_id, is_primary);

-- Update existing accounts to set one as primary per user_id
-- This assumes each user_id currently has only one account
UPDATE public.accounts SET is_primary = true 
WHERE user_id IS NOT NULL
  AND id IN (
    SELECT DISTINCT ON (user_id) id 
    FROM public.accounts 
    WHERE user_id IS NOT NULL 
    ORDER BY user_id, created_at ASC
  );