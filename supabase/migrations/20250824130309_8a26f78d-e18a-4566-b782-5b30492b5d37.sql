-- Add timezone field to clubs table
ALTER TABLE public.clubs 
ADD COLUMN timezone text DEFAULT 'Europe/Berlin';

-- Add comment to document the field
COMMENT ON COLUMN public.clubs.timezone IS 'Club timezone using IANA timezone identifiers (e.g., Europe/Berlin, America/New_York)';

-- Create function to get club timezone
CREATE OR REPLACE FUNCTION public.get_club_timezone(_club_id uuid)
RETURNS text
LANGUAGE sql
STABLE SECURITY DEFINER
SET search_path = 'public'
AS $$
  SELECT COALESCE(timezone, 'Europe/Berlin')
  FROM public.clubs
  WHERE id = _club_id;
$$;

-- Create function to convert UTC time to club timezone
CREATE OR REPLACE FUNCTION public.utc_to_club_time(_utc_time timestamptz, _club_id uuid)
RETURNS timestamptz
LANGUAGE sql
STABLE SECURITY DEFINER
SET search_path = 'public'
AS $$
  SELECT _utc_time AT TIME ZONE get_club_timezone(_club_id);
$$;

-- Create function to convert club local time to UTC
CREATE OR REPLACE FUNCTION public.club_time_to_utc(_local_time timestamp, _club_id uuid)
RETURNS timestamptz
LANGUAGE sql
STABLE SECURITY DEFINER
SET search_path = 'public'
AS $$
  SELECT timezone(get_club_timezone(_club_id), _local_time);
$$;