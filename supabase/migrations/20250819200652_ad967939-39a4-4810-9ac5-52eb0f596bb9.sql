-- Update courts policies to allow guests to view courts
-- This is necessary for guests to see available courts for booking

-- Update the SELECT policy for courts
DROP POLICY IF EXISTS "courts_select_policy" ON public.courts;
CREATE POLICY "courts_select_policy"
ON public.courts
FOR SELECT
TO authenticated
USING (
  -- Either user has active club membership OR user has guest role
  is_meta_admin(auth.uid()) 
  OR 
  (EXISTS (
    SELECT 1
    FROM public.club_memberships m
    WHERE m.user_id = auth.uid() 
      AND m.club_id = courts.club_id 
      AND COALESCE(m.is_active, true) = true
  ))
  OR
  (EXISTS (
    SELECT 1
    FROM public.user_roles ur
    WHERE ur.user_id = auth.uid() 
      AND ur.role = 'guest'
  ))
);

-- Also update court_availability policies for guests
-- Create SELECT policy for court_availability if it doesn't exist
DROP POLICY IF EXISTS "court_availability_select" ON public.court_availability;
CREATE POLICY "court_availability_select"
ON public.court_availability
FOR SELECT
TO authenticated
USING (
  -- Allow access if user has club membership OR guest role
  EXISTS (
    SELECT 1
    FROM public.club_memberships m
    JOIN public.courts c ON c.id = court_availability.court_id
    WHERE m.user_id = auth.uid() 
      AND m.club_id = c.club_id 
      AND COALESCE(m.is_active, true) = true
  )
  OR
  EXISTS (
    SELECT 1
    FROM public.user_roles ur
    WHERE ur.user_id = auth.uid() 
      AND ur.role = 'guest'
  )
);

-- Also update court_blocks policies for guests
DROP POLICY IF EXISTS "court_blocks_select" ON public.court_blocks;
CREATE POLICY "court_blocks_select"
ON public.court_blocks
FOR SELECT
TO authenticated
USING (
  -- Allow access if user has club membership OR guest role
  EXISTS (
    SELECT 1
    FROM public.club_memberships m
    JOIN public.courts c ON c.id = court_blocks.court_id
    WHERE m.user_id = auth.uid() 
      AND m.club_id = c.club_id 
      AND COALESCE(m.is_active, true) = true
  )
  OR
  EXISTS (
    SELECT 1
    FROM public.user_roles ur
    WHERE ur.user_id = auth.uid() 
      AND ur.role = 'guest'
  )
);