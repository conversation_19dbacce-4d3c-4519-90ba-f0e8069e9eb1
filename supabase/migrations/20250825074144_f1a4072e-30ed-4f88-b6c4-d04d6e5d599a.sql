-- Add custom_data JSONB column to club_memberships for flexible custom fields
ALTER TABLE public.club_memberships 
ADD COLUMN custom_data JSONB DEFAULT '{}';

-- Add index for better performance on custom_data queries
CREATE INDEX idx_club_memberships_custom_data ON public.club_memberships USING GIN(custom_data);

-- Create table for club-specific custom field configurations
CREATE TABLE public.club_custom_fields (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  club_id UUID NOT NULL REFERENCES public.clubs(id) ON DELETE CASCADE,
  field_key TEXT NOT NULL,
  field_name TEXT NOT NULL,
  field_type TEXT NOT NULL CHECK (field_type IN ('text', 'number', 'select', 'tennis_lk')),
  field_options JSONB DEFAULT '[]',
  is_required BOOLEAN NOT NULL DEFAULT false,
  is_active BOOLEAN NOT NULL DEFAULT true,
  display_order INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  created_by UUID REFERENCES auth.users(id),
  
  UNIQUE(club_id, field_key)
);

-- Enable RLS on club_custom_fields
ALTER TABLE public.club_custom_fields ENABLE ROW LEVEL SECURITY;

-- Club admins can manage their club's custom fields
CREATE POLICY "Club admins can manage custom fields"
ON public.club_custom_fields
FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid()
      AND cm.club_id = club_custom_fields.club_id
      AND cm.role IN ('admin', 'super_admin')
      AND cm.is_active = true
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid()
      AND cm.club_id = club_custom_fields.club_id
      AND cm.role IN ('admin', 'super_admin')
      AND cm.is_active = true
  )
);

-- Club members can view custom field configurations
CREATE POLICY "Club members can view custom fields config"
ON public.club_custom_fields
FOR SELECT
USING (
  is_active = true AND
  EXISTS (
    SELECT 1 FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid()
      AND cm.club_id = club_custom_fields.club_id
      AND cm.is_active = true
  )
);

-- Meta admins can manage all custom fields
CREATE POLICY "Meta admins can manage all custom fields"
ON public.club_custom_fields
FOR ALL
USING (is_meta_admin(auth.uid()))
WITH CHECK (is_meta_admin(auth.uid()));

-- Add trigger for updated_at
CREATE TRIGGER update_club_custom_fields_updated_at
BEFORE UPDATE ON public.club_custom_fields
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- Insert default Tennis LK field template for existing clubs
INSERT INTO public.club_custom_fields (
  club_id, 
  field_key, 
  field_name, 
  field_type, 
  field_options, 
  is_required, 
  display_order,
  created_by
)
SELECT 
  id,
  'tennis_lk',
  'Leistungsklasse (LK)',
  'tennis_lk',
  '[]'::jsonb,
  false,
  1,
  NULL
FROM public.clubs 
WHERE is_active = true;