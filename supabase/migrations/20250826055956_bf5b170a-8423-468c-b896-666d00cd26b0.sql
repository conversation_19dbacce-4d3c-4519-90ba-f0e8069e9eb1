-- Communication Hub Foundation Tables

-- Communication campaigns
CREATE TABLE public.communication_campaigns (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  club_id UUID NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  message_content TEXT NOT NULL,
  target_groups JSONB NOT NULL DEFAULT '[]'::jsonb,
  channels JSONB NOT NULL DEFAULT '[]'::jsonb, -- ['email', 'push', 'chat', 'dashboard']
  status TEXT NOT NULL DEFAULT 'draft', -- draft, scheduled, sending, sent, failed
  scheduled_at TIMESTAMP WITH TIME ZONE,
  sent_at TIMESTAMP WITH TIME ZONE,
  created_by <PERSON><PERSON><PERSON>,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  analytics JSONB DEFAULT '{}'::jsonb
);

-- Smart Groups for auto-targeting
CREATE TABLE public.communication_groups (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  club_id UUID NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  criteria JSONB NOT NULL DEFAULT '{}'::jsonb, -- Dynamic targeting rules
  is_dynamic BOOLEAN NOT NULL DEFAULT true,
  created_by UUID,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Communication templates
CREATE TABLE public.communication_templates (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  club_id UUID NOT NULL,
  name TEXT NOT NULL,
  type TEXT NOT NULL, -- email, push, chat, announcement
  subject TEXT,
  content TEXT NOT NULL,
  variables JSONB DEFAULT '[]'::jsonb,
  persona TEXT DEFAULT 'default', -- friendly, formal, urgent, etc.
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_by UUID,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- In-app messages and notifications
CREATE TABLE public.communication_messages (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  club_id UUID NOT NULL,
  type TEXT NOT NULL, -- news, announcement, direct_message, system
  title TEXT,
  content TEXT NOT NULL,
  sender_id UUID,
  recipient_type TEXT NOT NULL, -- individual, group, role, all
  recipient_ids JSONB DEFAULT '[]'::jsonb,
  channel TEXT NOT NULL, -- dashboard, email, push, chat
  priority TEXT DEFAULT 'normal', -- low, normal, high, urgent
  expires_at TIMESTAMP WITH TIME ZONE,
  read_by JSONB DEFAULT '{}'::jsonb, -- user_id -> timestamp mapping
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Communication analytics
CREATE TABLE public.communication_analytics (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  club_id UUID NOT NULL,
  campaign_id UUID,
  message_id UUID,
  event_type TEXT NOT NULL, -- sent, delivered, opened, clicked, replied
  user_id UUID,
  channel TEXT NOT NULL,
  metadata JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS
ALTER TABLE public.communication_campaigns ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.communication_groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.communication_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.communication_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.communication_analytics ENABLE ROW LEVEL SECURITY;

-- RLS Policies for communication_campaigns
CREATE POLICY "Club admins can manage their campaigns" 
ON public.communication_campaigns 
FOR ALL 
USING (EXISTS (
  SELECT 1 FROM club_memberships cm 
  WHERE cm.user_id = auth.uid() 
    AND cm.club_id = communication_campaigns.club_id 
    AND cm.role = ANY(ARRAY['admin', 'super_admin']) 
    AND cm.is_active = true
));

CREATE POLICY "Club members can view campaigns" 
ON public.communication_campaigns 
FOR SELECT 
USING (status = 'sent' AND EXISTS (
  SELECT 1 FROM club_memberships cm 
  WHERE cm.user_id = auth.uid() 
    AND cm.club_id = communication_campaigns.club_id 
    AND cm.is_active = true
));

-- RLS Policies for communication_groups
CREATE POLICY "Club admins can manage their groups" 
ON public.communication_groups 
FOR ALL 
USING (EXISTS (
  SELECT 1 FROM club_memberships cm 
  WHERE cm.user_id = auth.uid() 
    AND cm.club_id = communication_groups.club_id 
    AND cm.role = ANY(ARRAY['admin', 'super_admin']) 
    AND cm.is_active = true
));

-- RLS Policies for communication_templates
CREATE POLICY "Club admins can manage their templates" 
ON public.communication_templates 
FOR ALL 
USING (EXISTS (
  SELECT 1 FROM club_memberships cm 
  WHERE cm.user_id = auth.uid() 
    AND cm.club_id = communication_templates.club_id 
    AND cm.role = ANY(ARRAY['admin', 'super_admin']) 
    AND cm.is_active = true
));

-- RLS Policies for communication_messages
CREATE POLICY "Club admins can manage messages" 
ON public.communication_messages 
FOR ALL 
USING (EXISTS (
  SELECT 1 FROM club_memberships cm 
  WHERE cm.user_id = auth.uid() 
    AND cm.club_id = communication_messages.club_id 
    AND cm.role = ANY(ARRAY['admin', 'super_admin']) 
    AND cm.is_active = true
));

CREATE POLICY "Users can view messages for them" 
ON public.communication_messages 
FOR SELECT 
USING (
  recipient_type = 'all' 
  OR (recipient_type = 'individual' AND auth.uid()::text = ANY(SELECT jsonb_array_elements_text(recipient_ids)))
  OR (recipient_type = 'group' AND EXISTS (
    SELECT 1 FROM club_memberships cm 
    WHERE cm.user_id = auth.uid() 
      AND cm.club_id = communication_messages.club_id 
      AND cm.is_active = true
  ))
);

-- RLS Policies for communication_analytics
CREATE POLICY "Club admins can view analytics" 
ON public.communication_analytics 
FOR SELECT 
USING (EXISTS (
  SELECT 1 FROM club_memberships cm 
  WHERE cm.user_id = auth.uid() 
    AND cm.club_id = communication_analytics.club_id 
    AND cm.role = ANY(ARRAY['admin', 'super_admin']) 
    AND cm.is_active = true
));

CREATE POLICY "System can insert analytics" 
ON public.communication_analytics 
FOR INSERT 
WITH CHECK (true);

-- Update triggers
CREATE TRIGGER update_communication_campaigns_updated_at
  BEFORE UPDATE ON public.communication_campaigns
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_communication_groups_updated_at
  BEFORE UPDATE ON public.communication_groups
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_communication_templates_updated_at
  BEFORE UPDATE ON public.communication_templates
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_communication_messages_updated_at
  BEFORE UPDATE ON public.communication_messages
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();