-- Create more permissive RLS policies for admin interfaces
-- This ensures data loads even during auth transitions

-- Update members table policies
DROP POLICY IF EXISTS "Allow all operations on members" ON public.members;
DROP POLICY IF EXISTS "Ad<PERSON> can view all members" ON public.members;
DROP POLICY IF EXISTS "Ad<PERSON> can insert members" ON public.members; 
DROP POLICY IF EXISTS "Ad<PERSON> can update members" ON public.members;
DROP POLICY IF EXISTS "Ad<PERSON> can delete members" ON public.members;

-- <PERSON>reate simplified policies that work reliably
CREATE POLICY "Authenticated users can view members" 
ON public.members 
FOR SELECT 
USING (auth.uid() IS NOT NULL);

CREATE POLICY "Admins can manage members" 
ON public.members 
FOR ALL 
USING (has_role(auth.uid(), 'admin'::app_role))
WITH CHECK (has_role(auth.uid(), 'admin'::app_role));

-- Update user_roles policies for better reliability
DROP POLICY IF EXISTS "Ad<PERSON> can manage all roles" ON public.user_roles;
DROP POLICY IF EXISTS "Ad<PERSON> can view all roles" ON public.user_roles;

CREATE POLICY "Authenticated users can view roles" 
ON public.user_roles 
FOR SELECT 
USING (auth.uid() IS NOT NULL);

CREATE POLICY "Admins can manage roles" 
ON public.user_roles 
FOR INSERT 
WITH CHECK (has_role(auth.uid(), 'admin'::app_role));

CREATE POLICY "Admins can update roles" 
ON public.user_roles 
FOR UPDATE 
USING (has_role(auth.uid(), 'admin'::app_role))
WITH CHECK (has_role(auth.uid(), 'admin'::app_role));

CREATE POLICY "Admins can delete roles" 
ON public.user_roles 
FOR DELETE 
USING (has_role(auth.uid(), 'admin'::app_role));