-- Create waitlist_entries table for club-wide booking waiting lists
CREATE TABLE public.waitlist_entries (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  club_id UUID NOT NULL,
  user_id UUID NOT NULL,
  player_name TEXT NOT NULL,
  partner_name TEXT,
  
  -- Time range preferences instead of fixed slots
  preferred_date DATE NOT NULL,
  start_time_range TIME WITHOUT TIME ZONE NOT NULL,
  end_time_range TIME WITHOUT TIME ZONE NOT NULL,
  
  -- Array of preferred court IDs (all courts if empty)
  preferred_courts UUID[] NOT NULL DEFAULT '{}',
  
  -- Auto-booking feature (max 1 per user)
  auto_booking_enabled BOOLEAN NOT NULL DEFAULT false,
  
  -- Position in waitlist and status
  position INTEGER NOT NULL,
  status TEXT NOT NULL DEFAULT 'waiting' CHECK (status IN ('waiting', 'converted_to_booking', 'cancelled', 'expired')),
  
  -- <PERSON><PERSON><PERSON>
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  
  -- Constraints
  CONSTRAINT valid_time_range CHECK (start_time_range < end_time_range),
  CONSTRAINT valid_date CHECK (preferred_date >= CURRENT_DATE)
);

-- Enable RLS
ALTER TABLE public.waitlist_entries ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view their own waitlist entries"
ON public.waitlist_entries
FOR SELECT
USING (user_id = auth.uid());

CREATE POLICY "Users can create their own waitlist entries"
ON public.waitlist_entries
FOR INSERT
WITH CHECK (user_id = auth.uid() AND (
  EXISTS (
    SELECT 1 FROM public.club_memberships cm 
    WHERE cm.user_id = auth.uid() 
      AND cm.club_id = waitlist_entries.club_id 
      AND cm.is_active = true
  )
  OR EXISTS (
    SELECT 1 FROM public.user_roles ur 
    WHERE ur.user_id = auth.uid() 
      AND ur.role = 'guest'
  )
));

CREATE POLICY "Users can update their own waitlist entries"
ON public.waitlist_entries
FOR UPDATE
USING (user_id = auth.uid())
WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can delete their own waitlist entries"
ON public.waitlist_entries
FOR DELETE
USING (user_id = auth.uid());

CREATE POLICY "Club admins can manage club waitlist entries"
ON public.waitlist_entries
FOR ALL
USING (EXISTS (
  SELECT 1 FROM public.club_memberships cm
  WHERE cm.user_id = auth.uid()
    AND cm.club_id = waitlist_entries.club_id
    AND cm.role IN ('admin', 'super_admin')
    AND cm.is_active = true
));

CREATE POLICY "Meta admins can manage all waitlist entries"
ON public.waitlist_entries
FOR ALL
USING (is_meta_admin(auth.uid()));

-- Create indexes for performance
CREATE INDEX idx_waitlist_entries_club_id ON public.waitlist_entries(club_id);
CREATE INDEX idx_waitlist_entries_user_id ON public.waitlist_entries(user_id);
CREATE INDEX idx_waitlist_entries_date_time ON public.waitlist_entries(preferred_date, start_time_range, end_time_range);
CREATE INDEX idx_waitlist_entries_status ON public.waitlist_entries(status);
CREATE INDEX idx_waitlist_entries_auto_booking ON public.waitlist_entries(auto_booking_enabled) WHERE auto_booking_enabled = true;

-- Function to update position automatically
CREATE OR REPLACE FUNCTION public.update_waitlist_position()
RETURNS TRIGGER AS $$
BEGIN
  -- Set position as next available position for the same criteria
  IF TG_OP = 'INSERT' THEN
    NEW.position := COALESCE(
      (SELECT MAX(position) + 1 
       FROM public.waitlist_entries 
       WHERE club_id = NEW.club_id 
         AND preferred_date = NEW.preferred_date
         AND status = 'waiting'), 
      1
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for automatic position assignment
CREATE TRIGGER set_waitlist_position
  BEFORE INSERT ON public.waitlist_entries
  FOR EACH ROW
  EXECUTE FUNCTION public.update_waitlist_position();

-- Function to validate auto-booking limit (max 1 per user)
CREATE OR REPLACE FUNCTION public.validate_auto_booking_limit()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.auto_booking_enabled = true THEN
    -- Check if user already has an active auto-booking entry
    IF EXISTS (
      SELECT 1 FROM public.waitlist_entries 
      WHERE user_id = NEW.user_id 
        AND auto_booking_enabled = true 
        AND status = 'waiting'
        AND (TG_OP = 'INSERT' OR id != NEW.id)
    ) THEN
      RAISE EXCEPTION 'Benutzer kann nur eine Auto-Zusage Warteliste zur Zeit haben';
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for auto-booking validation
CREATE TRIGGER validate_auto_booking
  BEFORE INSERT OR UPDATE ON public.waitlist_entries
  FOR EACH ROW
  EXECUTE FUNCTION public.validate_auto_booking_limit();

-- Update trigger for updated_at
CREATE TRIGGER update_waitlist_entries_updated_at
  BEFORE UPDATE ON public.waitlist_entries
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();