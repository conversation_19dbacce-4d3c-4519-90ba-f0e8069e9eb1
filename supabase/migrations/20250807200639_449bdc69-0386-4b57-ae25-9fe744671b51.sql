-- Create admin role for the existing user (for development)
-- This bypasses RLS policies by using superuser privileges

INSERT INTO public.user_roles (user_id, role) 
VALUES ('c7e745d1-f09e-4819-936a-a15c06171e6d', 'admin')
ON CONFLICT (user_id, role) DO NOTHING;

-- Also add a default mitglied role for completeness
INSERT INTO public.user_roles (user_id, role) 
VALUES ('c7e745d1-f09e-4819-936a-a15c06171e6d', 'mitglied')
ON CONFLICT (user_id, role) DO NOTHING;