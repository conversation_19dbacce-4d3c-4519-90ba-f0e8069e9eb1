-- CRITICAL SECURITY FIX: Replace dangerous open RLS policy for activities table
-- Remove the insecure policy that allows all operations
DROP POLICY IF EXISTS "Allow all operations on activities" ON public.activities;

-- Add secure club-specific RLS policies for activities
CREATE POLICY "Club admins can manage their club activities" 
ON public.activities 
FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid() 
      AND cm.club_id = activities.club_id 
      AND cm.role = ANY(ARRAY['admin', 'super_admin']) 
      AND cm.is_active = true
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid() 
      AND cm.club_id = activities.club_id 
      AND cm.role = ANY(ARRAY['admin', 'super_admin']) 
      AND cm.is_active = true
  )
);

CREATE POLICY "Club members can view approved activities in their club" 
ON public.activities 
FOR SELECT
USING (
  status = 'approved' 
  AND EXISTS (
    SELECT 1 FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid() 
      AND cm.club_id = activities.club_id 
      AND cm.is_active = true
  )
);

CREATE POLICY "Club members can create activity suggestions for their club" 
ON public.activities 
FOR INSERT
WITH CHECK (
  status = 'pending'
  AND requested_by = auth.uid()
  AND EXISTS (
    SELECT 1 FROM public.club_memberships cm
    WHERE cm.user_id = auth.uid() 
      AND cm.club_id = activities.club_id 
      AND cm.is_active = true
  )
);

CREATE POLICY "Meta admins can manage all activities" 
ON public.activities 
FOR ALL
USING (is_meta_admin(auth.uid()))
WITH CHECK (is_meta_admin(auth.uid()));