-- Create a membership_types table to properly define membership types
CREATE TABLE public.membership_types (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  value text NOT NULL UNIQUE,
  display_name text NOT NULL,
  description text,
  is_active boolean NOT NULL DEFAULT true,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now()
);

-- Enable RLS on membership_types
ALTER TABLE public.membership_types ENABLE ROW LEVEL SECURITY;

-- Create policies for membership_types
CREATE POLICY "Everyone can view active membership types" 
ON public.membership_types 
FOR SELECT 
USING (is_active = true);

CREATE POLICY "<PERSON><PERSON> can manage membership types" 
ON public.membership_types 
FOR ALL 
USING (has_role(auth.uid(), 'admin'::app_role))
WITH CHECK (has_role(auth.uid(), 'admin'::app_role));

-- Insert default membership types
INSERT INTO public.membership_types (value, display_name, description) VALUES
('Erwachsener', '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'Vollmitgliedschaft für Erwachsene'),
('Jugendlicher', 'Jugendlicher', 'Mitgliedschaft für Jugendliche unter 18 Jahren'),
('Student', 'Student', 'Vergünstigte Mitgliedschaft für Studenten'),
('Senior', 'Senior', 'Mitgliedschaft für Senioren ab 65 Jahren'),
('Familie', 'Familie', 'Familienmitgliedschaft'),
('Ehrenmitglied', 'Ehrenmitglied', 'Kostenlose Ehrenmitgliedschaft');

-- Add membership_type_id to fee_types to link fees to membership types
ALTER TABLE public.fee_types 
ADD COLUMN membership_type_id text REFERENCES public.membership_types(value);

-- Update existing fee_types to link to membership types
UPDATE public.fee_types 
SET membership_type_id = 'Erwachsener' 
WHERE membership_category = 'Erwachsener'::membership_category;

UPDATE public.fee_types 
SET membership_type_id = 'Jugendlicher' 
WHERE membership_category = 'Jugendlicher'::membership_category;

UPDATE public.fee_types 
SET membership_type_id = 'Student' 
WHERE membership_category = 'Student'::membership_category;

UPDATE public.fee_types 
SET membership_type_id = 'Senior' 
WHERE membership_category = 'Senior'::membership_category;

UPDATE public.fee_types 
SET membership_type_id = 'Familie' 
WHERE membership_category = 'Familie'::membership_category;

-- Create default free fee for Ehrenmitglied
INSERT INTO public.fee_types (
  name, 
  description, 
  amount, 
  category, 
  billing_cycle, 
  membership_type_id,
  is_active
) VALUES (
  'Ehrenmitgliedschaft - Kostenfrei',
  'Kostenlose Gebühr für Ehrenmitglieder',
  0.00,
  'membership',
  'annual',
  'Ehrenmitglied',
  true
);

-- Add trigger for updated_at on membership_types
CREATE TRIGGER update_membership_types_updated_at
BEFORE UPDATE ON public.membership_types
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();