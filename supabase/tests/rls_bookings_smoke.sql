-- Smoke Test: Club Isolation für Courts & Bookings
-- <PERSON><PERSON><PERSON> sicher, dass Users nur ihre eigenen Club-Daten sehen

<PERSON>;

-- Test-Clubs erstellen
INSERT INTO public.clubs(id, name, slug) VALUES 
    ('CLUB_A', 'Club A', 'club-a'),
    ('CLUB_B', 'Club B', 'club-b')
ON CONFLICT DO NOTHING;

-- Test-Memberships erstellen
INSERT INTO public.club_memberships(id, club_id, user_id, role, is_active) VALUES 
    ('M_A', 'CLUB_A', 'USER_A', 'member', true),
    ('M_B', 'CLUB_B', 'USER_B', 'member', true)
ON CONFLICT DO NOTHING;

-- Test-Courts erstellen  
INSERT INTO public.courts(id, club_id, number) VALUES 
    ('COURT_A1', 'CLUB_A', 1),
    ('COURT_B1', 'CLUB_B', 1)
ON CONFLICT DO NOTHING;

-- Test-Bookings erstellen
INSERT INTO public.bookings(id, club_id, court_id, booking_date, start_time, end_time, player_name, created_by) VALUES 
    ('B1', 'CLUB_A', 'COURT_A1', CURRENT_DATE + INTERVAL '1 day', '10:00:00', '11:00:00', 'Player A', 'USER_A'),
    ('B2', 'CLUB_B', 'COURT_B1', CURRENT_DATE + INTERVAL '1 day', '10:00:00', '11:00:00', 'Player B', 'USER_B')
ON CONFLICT DO NOTHING;

-- TEST 1: USER_A sieht nur CLUB_A Daten
SET LOCAL "request.jwt.claims" = '{"sub":"USER_A"}';

SELECT 'USER_A Courts Club A' AS test_name, count(*) AS should_be_gt_zero 
FROM public.courts WHERE club_id = 'CLUB_A';

SELECT 'USER_A Courts Club B' AS test_name, count(*) AS should_be_zero 
FROM public.courts WHERE club_id = 'CLUB_B';

SELECT 'USER_A Bookings Club A' AS test_name, count(*) AS should_be_gt_zero 
FROM public.bookings WHERE club_id = 'CLUB_A';

SELECT 'USER_A Bookings Club B' AS test_name, count(*) AS should_be_zero 
FROM public.bookings WHERE club_id = 'CLUB_B';

-- TEST 2: USER_B sieht nur CLUB_B Daten  
SET LOCAL "request.jwt.claims" = '{"sub":"USER_B"}';

SELECT 'USER_B Courts Club B' AS test_name, count(*) AS should_be_gt_zero 
FROM public.courts WHERE club_id = 'CLUB_B';

SELECT 'USER_B Courts Club A' AS test_name, count(*) AS should_be_zero 
FROM public.courts WHERE club_id = 'CLUB_A';

SELECT 'USER_B Bookings Club B' AS test_name, count(*) AS should_be_gt_zero 
FROM public.bookings WHERE club_id = 'CLUB_B';

SELECT 'USER_B Bookings Club A' AS test_name, count(*) AS should_be_zero 
FROM public.bookings WHERE club_id = 'CLUB_A';

ROLLBACK;