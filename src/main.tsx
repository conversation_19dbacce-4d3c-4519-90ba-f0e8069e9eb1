import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import ErrorBoundary from './components/ErrorBoundary.tsx'
import { AuthProvider } from './contexts/AuthContext'
import { ClubProvider } from './contexts/ClubContext'
import { ThemeProvider } from './contexts/ThemeContext'
import './index.css'

// Add error handling for the React app
const rootElement = document.getElementById("root");

if (!rootElement) {
  console.error("Root element not found");
  document.body.innerHTML = '<div style="padding: 20px; color: red;">Error: Root element not found</div>';
} else {
  try {
    const root = createRoot(rootElement);
    root.render(
      <ErrorBoundary>
        <AuthProvider>
          <ClubProvider>
            <ThemeProvider>
              <App />
            </ThemeProvider>
          </ClubProvider>
        </AuthProvider>
      </ErrorBoundary>
    );
  } catch (error) {
    console.error("Error rendering React app:", error);
    rootElement.innerHTML = '<div style="padding: 20px; color: red;">Error loading application: ' + (error as Error).message + '</div>';
  }
}
