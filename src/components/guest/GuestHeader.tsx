import { User } from "@supabase/supabase-js";
import { Button } from "@/components/ui/button";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { useNavigate, useLocation } from "react-router-dom";

interface GuestHeaderProps {
  user: User | null;
}

const GuestHeader = ({ user }: GuestHeaderProps) => {
  const navigate = useNavigate();
  const location = useLocation();
  
  // Extract club slug from current path
  const pathParts = location.pathname.split('/');
  const clubSlug = pathParts[2];

  const handleLogin = () => {
    navigate(`/c/${clubSlug}/auth`);
  };

  const handleRegister = () => {
    navigate(`/c/${clubSlug}/register`);
  };

  return (
    <header className="flex h-14 items-center gap-4 border-b bg-background px-4">
      <SidebarTrigger className="h-4 w-4" />
      
      <div className="flex-1">
        <h1 className="text-lg font-semibold text-foreground">
          <PERSON>t-<PERSON><PERSON><PERSON>
        </h1>
      </div>

      <div className="flex items-center gap-2">
        {!user ? (
          <>
            <Button variant="ghost" size="sm" onClick={handleLogin}>
              Anmelden
            </Button>
            <Button size="sm" onClick={handleRegister}>
              Registrieren
            </Button>
          </>
        ) : (
          <div className="text-sm text-muted-foreground">
            Willkommen, {user.email}
          </div>
        )}
      </div>
    </header>
  );
};

export default GuestHeader;