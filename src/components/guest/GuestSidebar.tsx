import { Calendar, Home, Info, User } from "lucide-react";
import { NavLink, useLocation } from "react-router-dom";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import { useTenant } from "@/contexts/TenantContext";

const navigation = [
  { name: "Dashboard", href: "", icon: Home },
  { name: "Buchungskalender", href: "booking", icon: Calendar },
  { name: "Profil", href: "profile", icon: User },
  { name: "Club Info", href: "info", icon: Info },
];

const GuestSidebar = () => {
  const location = useLocation();
  const { club } = useTenant();
  
  // Extract club slug from current path
  const pathParts = location.pathname.split('/');
  const clubSlug = pathParts[2]; // /c/{clubSlug}/guest/...
  const basePath = `/c/${clubSlug}/guest`;
  
  const getNavClassName = (href: string) => {
    const fullPath = href === "" ? basePath : `${basePath}/${href}`;
    const isActive = location.pathname === fullPath;
    return isActive 
      ? "bg-accent text-accent-foreground font-medium" 
      : "hover:bg-accent/50";
  };

  return (
    <Sidebar className="w-60">
      <SidebarContent>
        <div className="p-4">
          <h2 className="font-bold text-lg text-tennis-green">
            Gast-Bereich
          </h2>
          {club && (
            <p className="text-xs text-muted-foreground mt-1">
              {club.name}
            </p>
          )}
        </div>

        <SidebarGroup>
          <SidebarGroupLabel>Navigation</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {navigation.map((item) => (
                <SidebarMenuItem key={item.name}>
                  <SidebarMenuButton asChild>
                    <NavLink 
                      to={item.href === "" ? basePath : `${basePath}/${item.href}`}
                      className={getNavClassName(item.href)}
                    >
                      <item.icon className="mr-2 h-4 w-4" />
                      <span>{item.name}</span>
                    </NavLink>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
};

export default GuestSidebar;