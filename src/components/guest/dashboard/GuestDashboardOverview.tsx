import { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { supabase } from "@/integrations/supabase/client";
import { Calendar, Megaphone, Trophy, Users } from "lucide-react";
import { format, parse } from "date-fns";
import { de } from "date-fns/locale";
import { useAuth } from "@/contexts/AuthContext";

interface Booking {
  id: string;
  booking_date: string;
  start_time: string;
  end_time: string;
  court_id: string;
  player_name: string;
  partner_name?: string | null;
}

interface Court { 
  id: string; 
  number: number;
}

interface ClubNewsItem { 
  title: string; 
  date?: string; 
  link?: string;
}

const GuestDashboardOverview = () => {
  const { user } = useAuth();
  const [fullName, setFullName] = useState<string>("");
  const [roles, setRoles] = useState<string[]>([]);
  const [upcomingMatches, setUpcomingMatches] = useState<Booking[]>([]);
  const [courtsMap, setCourtsMap] = useState<Record<string, Court>>({});
  const [news, setNews] = useState<ClubNewsItem[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const load = async () => {
      try {
        // Load user's full name and roles if logged in
        if (user) {
          const { data: profile } = await supabase
            .from("profiles")
            .select("first_name, last_name")
            .eq("id", user.id)
            .maybeSingle();
          
          // Set full name or fallback to email
          const firstName = profile?.first_name?.trim() || "";
          const lastName = profile?.last_name?.trim() || "";
          const profileName = `${firstName} ${lastName}`.trim();
          setFullName(profileName || user.email);

          // Load user roles
          const { data: rolesData } = await supabase.rpc('get_user_roles', { _user_id: user.id });
          const globalRoles = (rolesData || []).map((r: { role: string }) => r.role);
          
          // Also check club membership roles
          const { data: clubMemberships } = await supabase
            .from('club_memberships')
            .select('role')
            .eq('user_id', user.id)
            .eq('is_active', true);
          
          const clubRoles = (clubMemberships || []).map(m => m.role);
          
          // Determine actual user status
          const hasActiveClubMembership = clubMemberships && clubMemberships.length > 0;
          const allRoles = [...new Set([...globalRoles, ...clubRoles])];
          const adminRoles = allRoles.filter(r => r !== 'member' && r !== 'mitglied' && r !== 'guest');
          
          // Set roles based on actual membership status
          if (adminRoles.length > 0) {
            setRoles(adminRoles);
          } else if (hasActiveClubMembership) {
            setRoles(['member']);
          } else {
            setRoles(['guest']);
          }
        }

        // Load courts for number mapping
        const { data: courts } = await supabase
          .from("courts")
          .select("id, number")
          .order("number");
        const cMap: Record<string, Court> = {};
        (courts || []).forEach(c => { cMap[c.id] = c; });
        setCourtsMap(cMap);

        // Load upcoming bookings - only user's own matches if logged in
        const todayStr = format(new Date(), "yyyy-MM-dd");
        const { data: upcomingData } = await supabase
          .from("bookings")
          .select("*")
          .gte("booking_date", todayStr)
          .order("booking_date", { ascending: true });

        // Filter for user's own matches if logged in
        const userMatches = fullName ? 
          (upcomingData || []).filter(b => 
            b.player_name === fullName || b.partner_name === fullName
          ) : [];
        
        setUpcomingMatches(userMatches);

        // Load club news from system settings (no fake data)
        const { data: settings } = await supabase
          .from("system_settings")
          .select("value")
          .eq("key", "club_news")
          .maybeSingle();
        
        if (settings?.value && Array.isArray(settings.value)) {
          setNews(settings.value as unknown as ClubNewsItem[]);
        } else {
          setNews([]); // No fake data
        }

      } finally {
        setLoading(false);
      }
    };

    load();
  }, [user, fullName]);

  const toDateTime = (b: Booking) => {
    return parse(`${b.booking_date} ${b.start_time}`, "yyyy-MM-dd HH:mm:ss", new Date());
  };

  const nextMatches = upcomingMatches.slice(0, 5);

  return (
    <div className="grid gap-4 md:grid-cols-3 lg:grid-cols-4">
      {/* Nächste Matches - nur eigene wenn eingeloggt */}
      <Card className="md:col-span-1">
        <CardHeader className="pb-2">
          <CardTitle className="text-xs font-medium flex items-center gap-1.5">
            <Calendar className="h-3 w-3" /> Meine nächsten Matches
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          {loading ? (
            <p className="text-xs text-muted-foreground">Laden…</p>
          ) : !user ? (
            <p className="text-xs text-muted-foreground">Bitte anmelden für eigene Matches</p>
          ) : nextMatches.length === 0 ? (
            <p className="text-xs text-muted-foreground">Keine kommenden Matches</p>
          ) : (
            <ul className="space-y-1">
              {nextMatches.slice(0, 3).map((m) => {
                const court = courtsMap[m.court_id]?.number;
                const other = m.player_name === fullName ? (m.partner_name || "-") : m.player_name;
                return (
                  <li key={m.id} className="text-xs">
                    <div className="flex flex-col gap-0.5">
                      <span className="truncate font-medium">{other}</span>
                      <span className="text-muted-foreground text-xs">
                        {format(toDateTime(m), "dd.MM HH:mm", { locale: de })}
                        {typeof court === 'number' ? ` · P${court}` : ""}
                      </span>
                    </div>
                  </li>
                );
              })}
            </ul>
          )}
        </CardContent>
      </Card>

      {/* Vereinsnews - nur wenn vorhanden */}
      <Card className="md:col-span-1">
        <CardHeader className="pb-2">
          <CardTitle className="text-xs font-medium flex items-center gap-1.5">
            <Megaphone className="h-3 w-3" /> Vereinsnews
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          {loading ? (
            <p className="text-xs text-muted-foreground">Laden…</p>
          ) : news.length === 0 ? (
            <p className="text-xs text-muted-foreground">Keine News vorhanden</p>
          ) : (
            <ul className="space-y-1">
              {news.slice(0, 3).map((n, idx) => (
                <li key={idx} className="text-xs">
                  <div className="flex flex-col gap-0.5">
                    <span className="truncate font-medium">{n.title}</span>
                    {n.date && (
                      <span className="text-muted-foreground text-xs">{format(new Date(n.date), "dd.MM.")}</span>
                    )}
                  </div>
                </li>
              ))}
            </ul>
          )}
        </CardContent>
      </Card>

      {/* Gast-Profil & Status */}
      <Card className="md:col-span-1 lg:col-span-2">
        <CardHeader className="pb-2">
          <CardTitle className="text-xs font-medium flex items-center gap-1.5">
            <Users className="h-3 w-3" /> Profil & Status
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="grid gap-3 lg:grid-cols-2">
            {/* Profil Sektion */}
            <div className="space-y-1">
              <h4 className="font-medium text-xs text-muted-foreground">Profil</h4>
              <div className="space-y-1 text-xs">
                {user ? (
                  <div>
                    <span className="font-medium">{fullName}</span>
                    <div className="text-muted-foreground">
                      {roles.length > 0 ? (
                        roles[0] === 'guest' ? 'Gast' :
                        roles[0] === 'member' ? 'Mitglied' :
                        roles.map(r => r.charAt(0).toUpperCase() + r.slice(1)).join(', ')
                      ) : 'Gast'}
                    </div>
                  </div>
                ) : (
                  <div>
                    <span className="font-medium">Nicht angemeldet</span>
                    <div className="text-muted-foreground">Gast</div>
                  </div>
                )}
              </div>
            </div>

            {/* Status/Berechtigung */}
            <div className="space-y-1">
              <h4 className="font-medium text-xs text-muted-foreground">Berechtigung</h4>
              <div className="text-xs text-muted-foreground">
                <p>{user ? "Angemeldeter Benutzer" : "Gast-Zugang"} zum Club</p>
                <p className="mt-1 text-[10px]">
                  {user ? "Angemeldet - alle verfügbaren Funktionen nutzbar" : "Nicht angemeldet - eingeschränkte Funktionen"}
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default GuestDashboardOverview;