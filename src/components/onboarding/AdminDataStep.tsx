import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { de } from "date-fns/locale";
import { cn } from "@/lib/utils";
import { AdminData } from "@/pages/ClubRegister";

interface AdminDataStepProps {
  data: AdminData;
  onChange: (data: AdminData) => void;
}

export function AdminDataStep({ data, onChange }: AdminDataStepProps) {
  const updateData = (field: keyof AdminData, value: any) => {
    onChange({ ...data, [field]: value });
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="firstName" className="text-sm font-medium">
            Vorname *
          </Label>
          <Input
            id="firstName"
            value={data.firstName}
            onChange={(e) => updateData('firstName', e.target.value)}
            placeholder="Ihr Vorname"
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="lastName" className="text-sm font-medium">
            Nachname *
          </Label>
          <Input
            id="lastName"
            value={data.lastName}
            onChange={(e) => updateData('lastName', e.target.value)}
            placeholder="Ihr Nachname"
            required
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label className="text-sm font-medium">Geburtsdatum</Label>
        <div className="flex gap-2">
          {/* Day Picker */}
          <select 
            value={data.birthDate?.getDate() || ''}
            onChange={(e) => {
              const day = parseInt(e.target.value);
              const month = data.birthDate?.getMonth() || 0;
              const year = data.birthDate?.getFullYear() || 1990;
              if (day) {
                updateData('birthDate', new Date(year, month, day));
              }
            }}
            className="flex h-9 w-16 items-center justify-center rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
          >
            <option value="">Tag</option>
            {Array.from({length: 31}, (_, i) => i + 1).map(day => (
              <option key={day} value={day}>{day}</option>
            ))}
          </select>

          {/* Month Picker */}
          <select 
            value={data.birthDate?.getMonth() || ''}
            onChange={(e) => {
              const month = parseInt(e.target.value);
              const day = data.birthDate?.getDate() || 1;
              const year = data.birthDate?.getFullYear() || 1990;
              if (month >= 0) {
                updateData('birthDate', new Date(year, month, day));
              }
            }}
            className="flex h-9 w-24 items-center justify-center rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
          >
            <option value="">Monat</option>
            {[
              'Januar', 'Februar', 'März', 'April', 'Mai', 'Juni',
              'Juli', 'August', 'September', 'Oktober', 'November', 'Dezember'
            ].map((month, index) => (
              <option key={index} value={index}>{month}</option>
            ))}
          </select>

          {/* Year Picker */}
          <select 
            value={data.birthDate?.getFullYear() || ''}
            onChange={(e) => {
              const year = parseInt(e.target.value);
              const day = data.birthDate?.getDate() || 1;
              const month = data.birthDate?.getMonth() || 0;
              if (year) {
                updateData('birthDate', new Date(year, month, day));
              }
            }}
            className="flex h-9 w-20 items-center justify-center rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
          >
            <option value="">Jahr</option>
            {Array.from({length: 80}, (_, i) => new Date().getFullYear() - i).map(year => (
              <option key={year} value={year}>{year}</option>
            ))}
          </select>
        </div>
        {data.birthDate && (
          <p className="text-xs text-muted-foreground">
            Gewählt: {format(data.birthDate, "dd.MM.yyyy", { locale: de })}
          </p>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="space-y-2 md:col-span-2">
          <Label htmlFor="street" className="text-sm font-medium">
            Straße *
          </Label>
          <Input
            id="street"
            value={data.street}
            onChange={(e) => updateData('street', e.target.value)}
            placeholder="Straßenname"
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="houseNumber" className="text-sm font-medium">
            Hausnummer *
          </Label>
          <Input
            id="houseNumber"
            value={data.houseNumber}
            onChange={(e) => updateData('houseNumber', e.target.value)}
            placeholder="Nr."
            required
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="postalCode" className="text-sm font-medium">
            PLZ *
          </Label>
          <Input
            id="postalCode"
            value={data.postalCode}
            onChange={(e) => updateData('postalCode', e.target.value)}
            placeholder="12345"
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="city" className="text-sm font-medium">
            Stadt *
          </Label>
          <Input
            id="city"
            value={data.city}
            onChange={(e) => updateData('city', e.target.value)}
            placeholder="Stadt"
            required
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="email" className="text-sm font-medium">
          E-Mail-Adresse *
        </Label>
        <Input
          id="email"
          type="email"
          value={data.email}
          onChange={(e) => updateData('email', e.target.value)}
          placeholder="<EMAIL>"
          required
        />
        <p className="text-xs text-muted-foreground">
          Diese E-Mail-Adresse wird für die Anmeldung verwendet.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="password" className="text-sm font-medium">
            Passwort *
          </Label>
          <Input
            id="password"
            type="password"
            value={data.password}
            onChange={(e) => updateData('password', e.target.value)}
            placeholder="Sicheres Passwort"
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="passwordConfirm" className="text-sm font-medium">
            Passwort bestätigen *
          </Label>
          <Input
            id="passwordConfirm"
            type="password"
            value={data.passwordConfirm}
            onChange={(e) => updateData('passwordConfirm', e.target.value)}
            placeholder="Passwort wiederholen"
            required
          />
          {data.password && data.passwordConfirm && data.password !== data.passwordConfirm && (
            <p className="text-xs text-destructive">
              Passwörter stimmen nicht überein
            </p>
          )}
        </div>
      </div>

      <div className="bg-muted/50 rounded-lg p-4">
        <p className="text-sm text-muted-foreground">
          <strong>Hinweis:</strong> Sie werden automatisch als Administrator des Vereins registriert. 
          Nach der Registrierung erhalten Sie eine E-Mail zur Bestätigung Ihrer E-Mail-Adresse.
        </p>
      </div>
    </div>
  );
}