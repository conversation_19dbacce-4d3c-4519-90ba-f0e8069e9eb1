import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { AdminData, ClubData, CourtSetup, PlayingTimes } from "@/pages/ClubRegister";
import { format } from "date-fns";
import { de } from "date-fns/locale";
import { CheckCircle, User, Building, Calendar, Clock } from "lucide-react";

interface ConfirmationStepProps {
  adminData: AdminData;
  clubData: ClubData;
  courtSetup: CourtSetup;
  playingTimes: PlayingTimes;
}

const DAYS_LABELS: Record<string, string> = {
  'monday': 'Montag',
  'tuesday': 'Dienstag', 
  'wednesday': 'Mittwoch',
  'thursday': 'Donnerstag',
  'friday': 'Freitag',
  'saturday': 'Samstag',
  'sunday': 'Sonntag',
};

export function ConfirmationStep({ 
  adminData, 
  clubData, 
  courtSetup, 
  playingTimes 
}: ConfirmationStepProps) {
  const clubSlug = clubData.name
    .toLowerCase()
    .replace(/[^a-z0-9]/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '');

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <CheckCircle className="w-12 h-12 text-green-600 mx-auto mb-2" />
        <h3 className="text-lg font-semibold">Übersicht Ihrer Eingaben</h3>
        <p className="text-sm text-muted-foreground">
          Bitte überprüfen Sie alle Angaben vor der finalen Erstellung
        </p>
      </div>

      <div className="grid gap-4">
        {/* Admin Data */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <User className="w-4 h-4" />
              Administrator
            </CardTitle>
            <CardDescription>Ihre persönlichen Daten</CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Name:</span> {adminData.firstName} {adminData.lastName}
              </div>
              <div>
                <span className="font-medium">E-Mail:</span> {adminData.email}
              </div>
              {adminData.birthDate && (
                <div>
                  <span className="font-medium">Geburtsdatum:</span>{" "}
                  {format(adminData.birthDate, "dd.MM.yyyy", { locale: de })}
                </div>
              )}
              <div>
                <span className="font-medium">Adresse:</span>{" "}
                {adminData.street} {adminData.houseNumber}, {adminData.postalCode} {adminData.city}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Club Data */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <Building className="w-4 h-4" />
              Vereinsdaten
            </CardTitle>
            <CardDescription>Informationen zu Ihrem Tennis-Verein</CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="grid gap-2 text-sm">
              <div>
                <span className="font-medium">Vereinsname:</span> {clubData.name}
              </div>
              <div>
                <span className="font-medium">Adresse:</span>{" "}
                {clubData.street} {clubData.houseNumber}, {clubData.postalCode} {clubData.city}
              </div>
              {clubData.website && (
                <div>
                  <span className="font-medium">Website:</span> {clubData.website}
                </div>
              )}
              <div className="mt-2">
                <span className="font-medium">Club-URL:</span>{" "}
                <Badge variant="secondary">{clubSlug}.tennisplatzbuchung.de</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Court Setup */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <Calendar className="w-4 h-4" />
              Plätze
            </CardTitle>
            <CardDescription>Konfiguration der Tennisplätze</CardDescription>
          </CardHeader>
          <CardContent>
            {courtSetup.skipForNow ? (
              <div className="text-sm text-muted-foreground">
                <Badge variant="outline">Später konfigurieren</Badge>
                <p className="mt-2">
                  Plätze werden später in den Admin-Einstellungen eingerichtet.
                </p>
              </div>
            ) : (
              <div className="text-sm">
                <span className="font-medium">Anzahl Plätze:</span> {courtSetup.numberOfCourts}{" "}
                {courtSetup.numberOfCourts === 1 ? 'Platz' : 'Plätze'}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Playing Times */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <Clock className="w-4 h-4" />
              Spielzeiten
            </CardTitle>
            <CardDescription>Standard-Buchungszeiten</CardDescription>
          </CardHeader>
          <CardContent>
            {playingTimes.skipForNow ? (
              <div className="text-sm text-muted-foreground">
                <Badge variant="outline">Später konfigurieren</Badge>
                <p className="mt-2">
                  Spielzeiten werden später in den Admin-Einstellungen definiert.
                </p>
              </div>
            ) : (
              <div className="space-y-2 text-sm">
                <div>
                  <span className="font-medium">Zeiten:</span> {playingTimes.startTime} - {playingTimes.endTime}
                </div>
                <div>
                  <span className="font-medium">Tage:</span>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {playingTimes.selectedDays.map(day => (
                      <Badge key={day} variant="secondary" className="text-xs">
                        {DAYS_LABELS[day]}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <h4 className="font-medium text-green-800 mb-2">Was passiert als nächstes?</h4>
        <ul className="text-sm text-green-700 space-y-1 list-disc list-inside">
          <li>Ihr Administrator-Account wird erstellt</li>
          <li>Der Tennis-Verein wird in unserem System angelegt</li>
          <li>Sie erhalten eine E-Mail zur Bestätigung Ihrer E-Mail-Adresse</li>
          <li>Nach der Bestätigung können Sie sich in Ihren Admin-Bereich einloggen</li>
          {!courtSetup.skipForNow && <li>Die Plätze werden automatisch erstellt</li>}
          {!playingTimes.skipForNow && <li>Standard-Spielzeiten werden eingerichtet</li>}
        </ul>
      </div>

      <div className="bg-muted/50 rounded-lg p-4">
        <p className="text-sm text-muted-foreground">
          <strong>Hinweis:</strong> Nach der Erstellung können Sie alle Einstellungen in Ihrem 
          Admin-Bereich anpassen und erweitern. Dazu gehören Mitgliederverwaltung, Gebührenstrukturen, 
          erweiterte Platzkonfiguration und vieles mehr.
        </p>
      </div>
    </div>
  );
}