import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Checkbox } from "@/components/ui/checkbox";
import { PlayingTimes } from "@/pages/ClubRegister";

interface PlayingTimesStepProps {
  data: PlayingTimes;
  onChange: (data: PlayingTimes) => void;
}

const DAYS = [
  { value: 'monday', label: 'Montag' },
  { value: 'tuesday', label: '<PERSON>nst<PERSON>' },
  { value: 'wednesday', label: 'Mittwoch' },
  { value: 'thursday', label: 'Donnerstag' },
  { value: 'friday', label: 'Freitag' },
  { value: 'saturday', label: 'Samstag' },
  { value: 'sunday', label: 'Sonntag' },
];

export function PlayingTimesStep({ data, onChange }: PlayingTimesStepProps) {
  const updateData = (field: keyof PlayingTimes, value: any) => {
    onChange({ ...data, [field]: value });
  };

  const toggleDay = (day: string) => {
    const selectedDays = data.selectedDays.includes(day)
      ? data.selectedDays.filter(d => d !== day)
      : [...data.selectedDays, day];
    updateData('selectedDays', selectedDays);
  };

  const selectAllDays = () => {
    updateData('selectedDays', DAYS.map(d => d.value));
  };

  const selectWeekdays = () => {
    updateData('selectedDays', DAYS.slice(0, 5).map(d => d.value));
  };

  const selectWeekend = () => {
    updateData('selectedDays', DAYS.slice(5).map(d => d.value));
  };

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <Switch
            id="skipTimes"
            checked={data.skipForNow}
            onCheckedChange={(checked) => updateData('skipForNow', checked)}
          />
          <Label htmlFor="skipTimes" className="text-sm font-medium">
            Später konfigurieren (überspringen)
          </Label>
        </div>
        
        <p className="text-xs text-muted-foreground">
          Sie können die Spielzeiten auch später in den Admin-Einstellungen detailliert konfigurieren.
        </p>
      </div>

      {!data.skipForNow && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="startTime" className="text-sm font-medium">
                Spielzeiten von
              </Label>
              <Input
                id="startTime"
                type="time"
                value={data.startTime}
                onChange={(e) => updateData('startTime', e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="endTime" className="text-sm font-medium">
                bis
              </Label>
              <Input
                id="endTime"
                type="time"
                value={data.endTime}
                onChange={(e) => updateData('endTime', e.target.value)}
              />
            </div>
          </div>

          <div className="space-y-4">
            <Label className="text-sm font-medium">Verfügbare Wochentage</Label>
            
            <div className="flex flex-wrap gap-2 mb-4">
              <button
                type="button"
                onClick={selectAllDays}
                className="text-xs px-2 py-1 bg-muted rounded hover:bg-muted/80"
              >
                Alle auswählen
              </button>
              <button
                type="button"
                onClick={selectWeekdays}
                className="text-xs px-2 py-1 bg-muted rounded hover:bg-muted/80"
              >
                Werktage
              </button>
              <button
                type="button"
                onClick={selectWeekend}
                className="text-xs px-2 py-1 bg-muted rounded hover:bg-muted/80"
              >
                Wochenende
              </button>
              <button
                type="button"
                onClick={() => updateData('selectedDays', [])}
                className="text-xs px-2 py-1 bg-muted rounded hover:bg-muted/80"
              >
                Alle abwählen
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {DAYS.map((day) => (
                <div key={day.value} className="flex items-center space-x-2">
                  <Checkbox
                    id={day.value}
                    checked={data.selectedDays.includes(day.value)}
                    onCheckedChange={() => toggleDay(day.value)}
                  />
                  <Label
                    htmlFor={day.value}
                    className="text-sm font-medium cursor-pointer"
                  >
                    {day.label}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-muted/50 rounded-lg p-4">
            <p className="text-sm text-muted-foreground">
              <strong>Hinweis:</strong> Diese Einstellungen werden als Standard für alle Plätze 
              übernommen. Sie können später für jeden Platz individuelle Zeiten und Verfügbarkeiten 
              definieren.
            </p>
          </div>
        </div>
      )}

      {data.skipForNow && (
        <div className="bg-info/10 border border-info/20 rounded-lg p-4">
          <p className="text-sm text-info-foreground">
            <strong>Übersprungen:</strong> Sie können die Spielzeiten später über die Admin-Einstellungen → 
            Plätze → Verfügbarkeiten konfigurieren. Dort können Sie auch unterschiedliche Zeiten für 
            verschiedene Plätze und Wochentage einrichten.
          </p>
        </div>
      )}
    </div>
  );
}