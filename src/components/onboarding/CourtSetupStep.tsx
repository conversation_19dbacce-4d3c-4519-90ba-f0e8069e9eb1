import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { CourtSetup } from "@/pages/ClubRegister";
import { MinusCircle, PlusCircle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

interface CourtSetupStepProps {
  data: CourtSetup;
  onChange: (data: CourtSetup) => void;
}

export function CourtSetupStep({ data, onChange }: CourtSetupStepProps) {
  const updateData = (field: keyof CourtSetup, value: any) => {
    onChange({ ...data, [field]: value });
  };

  const incrementCourts = () => {
    if (data.numberOfCourts < 20) {
      updateData('numberOfCourts', data.numberOfCourts + 1);
    }
  };

  const decrementCourts = () => {
    if (data.numberOfCourts > 1) {
      updateData('numberOfCourts', data.numberOfCourts - 1);
    }
  };

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <Switch
            id="skipCourts"
            checked={data.skipForNow}
            onCheckedChange={(checked) => updateData('skipForNow', checked)}
          />
          <Label htmlFor="skipCourts" className="text-sm font-medium">
            Später konfigurieren (überspringen)
          </Label>
        </div>
        
        <p className="text-xs text-muted-foreground">
          Sie können die Plätze auch später in den Admin-Einstellungen einrichten.
        </p>
      </div>

      {!data.skipForNow && (
        <div className="space-y-4">
          <Label className="text-sm font-medium">Anzahl der Tennisplätze</Label>
          
          <div className="flex items-center justify-center space-x-4">
            <Button
              type="button"
              variant="outline"
              size="icon"
              onClick={decrementCourts}
              disabled={data.numberOfCourts <= 1}
            >
              <MinusCircle className="h-4 w-4" />
            </Button>
            
            <div className="text-center">
              <div className="text-3xl font-bold text-primary">
                {data.numberOfCourts}
              </div>
              <div className="text-sm text-muted-foreground">
                {data.numberOfCourts === 1 ? 'Platz' : 'Plätze'}
              </div>
            </div>
            
            <Button
              type="button"
              variant="outline"
              size="icon"
              onClick={incrementCourts}
              disabled={data.numberOfCourts >= 20}
            >
              <PlusCircle className="h-4 w-4" />
            </Button>
          </div>

          <div className="text-center">
            <Input
              type="number"
              min="1"
              max="20"
              value={data.numberOfCourts}
              onChange={(e) => {
                const value = Math.max(1, Math.min(20, parseInt(e.target.value) || 1));
                updateData('numberOfCourts', value);
              }}
              className="w-24 mx-auto text-center"
            />
          </div>

          <div className="bg-muted/50 rounded-lg p-4">
            <p className="text-sm text-muted-foreground">
              <strong>Hinweis:</strong> Die Plätze werden automatisch nummeriert (Platz 1, Platz 2, etc.). 
              Sie können später weitere Details wie Belag, Gruppen und Verfügbarkeiten konfigurieren.
            </p>
          </div>
        </div>
      )}

      {data.skipForNow && (
        <div className="bg-info/10 border border-info/20 rounded-lg p-4">
          <p className="text-sm text-info-foreground">
            <strong>Übersprungen:</strong> Sie können die Plätze später über die Admin-Einstellungen → 
            Plätze konfigurieren. Dort können Sie auch erweiterte Funktionen wie Platzsperrungen 
            und individuelle Verfügbarkeiten einrichten.
          </p>
        </div>
      )}
    </div>
  );
}