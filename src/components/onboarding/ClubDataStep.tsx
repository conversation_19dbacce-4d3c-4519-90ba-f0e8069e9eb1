import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { ClubData } from "@/pages/ClubRegister";

interface ClubDataStepProps {
  data: ClubData;
  onChange: (data: ClubData) => void;
}

export function ClubDataStep({ data, onChange }: ClubDataStepProps) {
  const updateData = (field: keyof ClubData, value: string) => {
    onChange({ ...data, [field]: value });
  };

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label htmlFor="clubName" className="text-sm font-medium">
          Vereinsname *
        </Label>
        <Input
          id="clubName"
          value={data.name}
          onChange={(e) => updateData('name', e.target.value)}
          placeholder="Tennis Club Beispielstadt"
          required
        />
        <p className="text-xs text-muted-foreground">
          Der Name wird für Ihre Club-URL verwendet (z.B. tennis-club-beispielstadt.tennisplatzbuchung.de)
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="space-y-2 md:col-span-2">
          <Label htmlFor="clubStreet" className="text-sm font-medium">
            Straße *
          </Label>
          <Input
            id="clubStreet"
            value={data.street}
            onChange={(e) => updateData('street', e.target.value)}
            placeholder="Vereinsstraße"
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="clubHouseNumber" className="text-sm font-medium">
            Hausnummer *
          </Label>
          <Input
            id="clubHouseNumber"
            value={data.houseNumber}
            onChange={(e) => updateData('houseNumber', e.target.value)}
            placeholder="Nr."
            required
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="clubPostalCode" className="text-sm font-medium">
            PLZ *
          </Label>
          <Input
            id="clubPostalCode"
            value={data.postalCode}
            onChange={(e) => updateData('postalCode', e.target.value)}
            placeholder="12345"
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="clubCity" className="text-sm font-medium">
            Stadt *
          </Label>
          <Input
            id="clubCity"
            value={data.city}
            onChange={(e) => updateData('city', e.target.value)}
            placeholder="Stadt"
            required
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="website" className="text-sm font-medium">
          Website
        </Label>
        <Input
          id="website"
          type="url"
          value={data.website}
          onChange={(e) => updateData('website', e.target.value)}
          placeholder="https://www.ihr-tennis-club.de"
        />
        <p className="text-xs text-muted-foreground">
          Optional - kann später geändert oder hinzugefügt werden
        </p>
      </div>

      <div className="bg-muted/50 rounded-lg p-4">
        <p className="text-sm text-muted-foreground">
          <strong>Hinweis:</strong> Diese Informationen werden für die Erstellung Ihres Vereinsprofils 
          verwendet. Sie können alle Angaben später in den Einstellungen ändern.
        </p>
      </div>
    </div>
  );
}