import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Dialog<PERSON>eader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Clock, Calendar, Users, MapPin, AlertTriangle, Info, Settings, Target } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { formatDateTime } from '@/lib/utils';
import { config as envConfig } from '@/config/environments';

interface Tournament {
  id: string;
  name: string;
  format_type: string;
  format_config: Record<string, any>;
  tournament_start?: string;
  tournament_end?: string;
  club_id: string;
}

interface Court {
  id: string;
  number: number;
  surface_type?: string;
  court_group?: string;
  is_main_court?: boolean;
}

interface SmartSchedulingConfig {
  // Timing Configuration
  tournament_start_time: string;
  daily_start_time: string;
  daily_end_time: string;
  
  // Break and Buffer Times
  minimum_break_between_matches: number; // minutes
  warmup_time: number; // minutes
  changeover_time: number; // minutes
  court_preparation_time: number; // minutes
  
  // Daily Limits
  max_matches_per_player_per_day: number;
  max_consecutive_matches: number;
  mandatory_rest_after_consecutive: number; // minutes
  
  // Court Preferences
  use_main_court_for_finals: boolean;
  court_preference_order: string[];
  avoid_surface_changes: boolean;
  
  // Conflict Resolution
  delay_handling: 'reschedule' | 'compress' | 'extend_day';
  rain_delay_buffer: number; // minutes
  parallel_scheduling_enabled: boolean;
  
  // Format-specific Settings
  round_gap_hours: number; // Hours between rounds
  group_stage_parallel: boolean;
  knockout_sequential: boolean;
}

interface SmartSchedulingDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  tournament: Tournament;
  availableCourts: Court[];
  onConfirm: (config: SmartSchedulingConfig) => void;
  onSmartScheduleGenerate?: (config: SmartSchedulingConfig) => void;
}

export function SmartSchedulingDialog({
  open,
  onOpenChange,
  tournament,
  availableCourts,
  onConfirm,
  onSmartScheduleGenerate
}: SmartSchedulingDialogProps) {
  const [config, setConfig] = useState<SmartSchedulingConfig>({
    tournament_start_time: '09:00',
    daily_start_time: '09:00',
    daily_end_time: '21:00',
    minimum_break_between_matches: 30,
    warmup_time: 10,
    changeover_time: 5,
    court_preparation_time: 10,
    max_matches_per_player_per_day: 2,
    max_consecutive_matches: 1,
    mandatory_rest_after_consecutive: 60,
    use_main_court_for_finals: true,
    court_preference_order: [],
    avoid_surface_changes: true,
    delay_handling: 'reschedule',
    rain_delay_buffer: 30,
    parallel_scheduling_enabled: true,
    round_gap_hours: 2,
    group_stage_parallel: true,
    knockout_sequential: false
  });

  const [activeTab, setActiveTab] = useState('timing');
  const [warnings, setWarnings] = useState<string[]>([]);
  const [estimatedDuration, setEstimatedDuration] = useState<{
    days: number;
    hours_per_day: number;
    total_matches: number;
  } | null>(null);
  const [participantCount, setParticipantCount] = useState<number>(0);
  const [isGenerating, setIsGenerating] = useState(false);

  useEffect(() => {
    if (open && tournament) {
      // Initialize court preference order
      const courtOrder = availableCourts
        .sort((a, b) => {
          // Main court first, then by number
          if (a.is_main_court && !b.is_main_court) return -1;
          if (!a.is_main_court && b.is_main_court) return 1;
          return a.number - b.number;
        })
        .map(court => court.id);
      
      setConfig(prev => ({
        ...prev,
        court_preference_order: courtOrder
      }));

      // Load participant count
      loadParticipantCount();
    }
  }, [open, tournament, availableCourts]);

  useEffect(() => {
    validateConfiguration();
    calculateEstimatedDuration();
  }, [config, participantCount]);

  const loadParticipantCount = async () => {
    try {
      const { data, error } = await supabase
        .from('tournament_participants')
        .select('id')
        .eq('tournament_id', tournament.id);

      if (error) {
        console.error('Error loading participants:', error);
        setParticipantCount(0);
      } else {
        setParticipantCount(data?.length || 0);
      }
    } catch (error) {
      console.error('Error loading participants:', error);
      setParticipantCount(0);
    }
  };

  const validateConfiguration = () => {
    const newWarnings: string[] = [];
    
    // Time validation
    const startTime = new Date(`2024-01-01T${config.daily_start_time}`);
    const endTime = new Date(`2024-01-01T${config.daily_end_time}`);
    const dailyHours = (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60);
    
    if (dailyHours < 6) {
      newWarnings.push('Weniger als 6 Stunden pro Tag könnte zu einem sehr langen Turnier führen');
    }
    
    if (dailyHours > 12) {
      newWarnings.push('Mehr als 12 Stunden pro Tag könnte zu anspruchsvoll für Spieler sein');
    }

    // Break time validation
    if (config.minimum_break_between_matches < 15) {
      newWarnings.push('Weniger als 15 Minuten Pause zwischen Spielen könnte zu wenig sein');
    }

    // Court validation
    if (availableCourts.length < 2 && config.parallel_scheduling_enabled) {
      newWarnings.push('Parallelspiele nicht möglich mit nur einem Platz');
    }

    // Participant validation
    if (participantCount < 2) {
      newWarnings.push('Mindestens 2 Teilnehmer erforderlich für ein Turnier');
    }

    // Format-specific warnings
    if (tournament.format_type === 'round_robin' && !config.group_stage_parallel) {
      newWarnings.push('Round Robin ohne Parallelspiele kann sehr lange dauern');
    }

    setWarnings(newWarnings);
  };

  const calculateEstimatedDuration = () => {
    if (participantCount < 2) {
      setEstimatedDuration({
        days: 0,
        hours_per_day: 0,
        total_matches: 0
      });
      return;
    }

    const baseMatchDuration = getBaseMatchDuration(tournament.format_type);
    const totalDuration = baseMatchDuration + config.minimum_break_between_matches + config.court_preparation_time;
    
    // Calculate matches based on actual participant count and format
    const estimatedMatches = calculateMatchesForFormat(tournament.format_type, participantCount);
    
    const dailyHours = calculateDailyAvailableHours();
    
    if (dailyHours <= 0 || availableCourts.length === 0) {
      setEstimatedDuration({
        days: Infinity,
        hours_per_day: dailyHours,
        total_matches: estimatedMatches
      });
      return;
    }

    const matchesPerDay = Math.floor((dailyHours * 60) / totalDuration) * availableCourts.length;
    const estimatedDays = matchesPerDay > 0 ? Math.ceil(estimatedMatches / matchesPerDay) : Infinity;

    setEstimatedDuration({
      days: estimatedDays,
      hours_per_day: dailyHours,
      total_matches: estimatedMatches
    });
  };

  const calculateMatchesForFormat = (formatType: string, participants: number): number => {
    if (participants < 2) return 0;

    switch (formatType) {
      case 'knockout':
        // For knockout: n-1 matches (where n is number of participants)
        return participants - 1;
      
      case 'round_robin':
        // For round robin: n*(n-1)/2 matches (complete round robin)
        return Math.floor((participants * (participants - 1)) / 2);
      
      case 'uts':
      case 'timeboxed':
        // Similar to round robin but might be less matches
        return Math.floor((participants * (participants - 1)) / 2);
      
      case 'team_tie':
        // Estimate 4-5 matches per team tie, depends on number of teams
        const estimatedTeams = Math.floor(participants / 4); // Assume 4 players per team
        return estimatedTeams > 1 ? (estimatedTeams - 1) * 4 : 0;
      
      case 'social_mixer':
        // Multiple short matches, estimate based on rotation
        return Math.max(participants, 8);
      
      case 'challenge_ladder':
        // Estimate based on ladder structure
        return Math.max(participants - 1, 1);
      
      default:
        // Default estimation
        return Math.max(participants - 1, 1);
    }
  };

  const getBaseMatchDuration = (formatType: string): number => {
    const durations = {
      'knockout': 120,
      'round_robin': 90,
      'uts': 90,
      'team_tie': 180,
      'timeboxed': 60,
      'social_mixer': 30,
      'pro_set': 60,
      'challenge_ladder': 90
    };
    return durations[formatType as keyof typeof durations] || 90;
  };

  const calculateDailyAvailableHours = (): number => {
    const startTime = new Date(`2024-01-01T${config.daily_start_time}`);
    const endTime = new Date(`2024-01-01T${config.daily_end_time}`);
    const hours = (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60);
    return Math.max(0, hours);
  };

  const updateConfig = (key: keyof SmartSchedulingConfig, value: any) => {
    setConfig(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const moveCourt = (courtId: string, direction: 'up' | 'down') => {
    const currentOrder = [...config.court_preference_order];
    const index = currentOrder.indexOf(courtId);
    
    if (direction === 'up' && index > 0) {
      [currentOrder[index], currentOrder[index - 1]] = [currentOrder[index - 1], currentOrder[index]];
    } else if (direction === 'down' && index < currentOrder.length - 1) {
      [currentOrder[index], currentOrder[index + 1]] = [currentOrder[index + 1], currentOrder[index]];
    }
    
    updateConfig('court_preference_order', currentOrder);
  };

  const handleSmartScheduleGenerate = async () => {
    if (!onSmartScheduleGenerate) return;
    
    setIsGenerating(true);
    try {
      await onSmartScheduleGenerate(config);
      onOpenChange(false);
    } catch (error) {
      console.error('Smart scheduling failed:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const getFormatSpecificSettings = () => {
    switch (tournament.format_type) {
      case 'knockout':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="roundGap">Stunden zwischen Runden</Label>
              <Input
                id="roundGap"
                type="number"
                min="0.5"
                max="24"
                step="0.5"
                value={config.round_gap_hours}
                onChange={(e) => updateConfig('round_gap_hours', parseFloat(e.target.value) || 2)}
              />
              <p className="text-sm text-muted-foreground mt-1">
                Mindestzeit zwischen verschiedenen K.O.-Runden
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="knockoutSequential"
                checked={config.knockout_sequential}
                onCheckedChange={(checked) => updateConfig('knockout_sequential', checked)}
              />
              <Label htmlFor="knockoutSequential">Runden strikt nacheinander</Label>
            </div>
          </div>
        );

      case 'round_robin':
        return (
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="groupParallel"
                checked={config.group_stage_parallel}
                onCheckedChange={(checked) => updateConfig('group_stage_parallel', checked)}
              />
              <Label htmlFor="groupParallel">Gruppenspiele parallel terminieren</Label>
            </div>
          </div>
        );

      case 'team_tie':
        return (
          <div className="space-y-4">
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                Team-Ties erfordern spezielle Scheduling-Regeln: Einzel → Doppel → Einzel Sequenz
              </AlertDescription>
            </Alert>
          </div>
        );

      default:
        return (
          <div className="text-sm text-muted-foreground">
            Standardeinstellungen für {tournament.format_type}
          </div>
        );
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Intelligente Turnier-Terminierung
          </DialogTitle>
          <DialogDescription>
            Konfigurieren Sie die automatische Spielansetzung für {tournament.name} ({participantCount} Teilnehmer)
          </DialogDescription>
          {tournament.tournament_start && (
            <Alert className="mt-4">
              <Calendar className="h-4 w-4" />
              <AlertDescription className="flex items-center gap-2">
                <strong>Turnier-Startdatum:</strong> 
                <Badge variant="outline">
                  {new Date(tournament.tournament_start).toLocaleDateString('de-DE', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </Badge>
                <span className="text-muted-foreground">
                  (Bitte bestätigen Sie, dass dies korrekt ist)
                </span>
              </AlertDescription>
            </Alert>
          )}
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="timing">Zeiten</TabsTrigger>
            <TabsTrigger value="breaks">Pausen</TabsTrigger>
            <TabsTrigger value="courts">Plätze</TabsTrigger>
            <TabsTrigger value="rules">Regeln</TabsTrigger>
            <TabsTrigger value="summary">Übersicht</TabsTrigger>
          </TabsList>

          <TabsContent value="timing" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  Zeitfenster
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {tournament.tournament_start && (
                  <div>
                    <Label>Turnier-Startdatum</Label>
                    <div className="flex items-center gap-2 p-3 bg-muted rounded-md">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">
                        {formatDateTime(tournament.tournament_start, {
                          weekday: 'long',
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </span>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      Aus den Turnier-Grundeinstellungen - kann dort geändert werden
                    </p>
                    <p className="text-xs text-blue-600 mt-1">
                      🕒 Zeitzone: {envConfig.timezone} • Locale: {envConfig.locale}
                    </p>
                  </div>
                )}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="tournamentStart">Turnier-Startzeit (1. Spiel)</Label>
                    <Input
                      id="tournamentStart"
                      type="time"
                      value={config.tournament_start_time}
                      onChange={(e) => updateConfig('tournament_start_time', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="dailyStart">Täglich verfügbar ab</Label>
                    <Input
                      id="dailyStart"
                      type="time"
                      value={config.daily_start_time}
                      onChange={(e) => updateConfig('daily_start_time', e.target.value)}
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="dailyEnd">Täglich verfügbar bis</Label>
                  <Input
                    id="dailyEnd"
                    type="time"
                    value={config.daily_end_time}
                    onChange={(e) => updateConfig('daily_end_time', e.target.value)}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="breaks" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-4 w-4" />
                  Pausen & Puffer
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="minBreak">Min. Pause zwischen Spielen (Min.)</Label>
                    <Input
                      id="minBreak"
                      type="number"
                      min="5"
                      max="120"
                      value={config.minimum_break_between_matches}
                      onChange={(e) => updateConfig('minimum_break_between_matches', parseInt(e.target.value) || 30)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="warmup">Aufwärmzeit (Min.)</Label>
                    <Input
                      id="warmup"
                      type="number"
                      min="0"
                      max="30"
                      value={config.warmup_time}
                      onChange={(e) => updateConfig('warmup_time', parseInt(e.target.value) || 10)}
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="changeover">Seitenwechsel (Min.)</Label>
                    <Input
                      id="changeover"
                      type="number"
                      min="0"
                      max="15"
                      value={config.changeover_time}
                      onChange={(e) => updateConfig('changeover_time', parseInt(e.target.value) || 5)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="courtPrep">Platzvorbereitung (Min.)</Label>
                    <Input
                      id="courtPrep"
                      type="number"
                      min="0"
                      max="30"
                      value={config.court_preparation_time}
                      onChange={(e) => updateConfig('court_preparation_time', parseInt(e.target.value) || 10)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Tagesaufteilung</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="maxDaily">Max. Spiele pro Spieler/Tag</Label>
                  <Input
                    id="maxDaily"
                    type="number"
                    min="1"
                    max="5"
                    value={config.max_matches_per_player_per_day}
                    onChange={(e) => updateConfig('max_matches_per_player_per_day', parseInt(e.target.value) || 2)}
                  />
                </div>
                <div>
                  <Label htmlFor="mandatoryRest">Pflichtpause nach aufeinanderfolgenden Spielen (Min.)</Label>
                  <Input
                    id="mandatoryRest"
                    type="number"
                    min="30"
                    max="240"
                    value={config.mandatory_rest_after_consecutive}
                    onChange={(e) => updateConfig('mandatory_rest_after_consecutive', parseInt(e.target.value) || 60)}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="courts" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  Platz-Präferenzen
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="mainCourtFinals"
                    checked={config.use_main_court_for_finals}
                    onCheckedChange={(checked) => updateConfig('use_main_court_for_finals', checked)}
                  />
                  <Label htmlFor="mainCourtFinals">Hauptplatz für Finals verwenden</Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Switch
                    id="avoidSurface"
                    checked={config.avoid_surface_changes}
                    onCheckedChange={(checked) => updateConfig('avoid_surface_changes', checked)}
                  />
                  <Label htmlFor="avoidSurface">Belagwechsel vermeiden</Label>
                </div>

                <div>
                  <Label>Platz-Priorität (Drag & Drop oder Pfeile)</Label>
                  <div className="space-y-2 mt-2">
                    {config.court_preference_order.map((courtId, index) => {
                      const court = availableCourts.find(c => c.id === courtId);
                      return (
                        <div key={courtId} className="flex items-center justify-between p-2 bg-muted rounded">
                          <div className="flex items-center gap-2">
                            <Badge variant="outline">{index + 1}</Badge>
                            <span>Platz {court?.number}</span>
                            {court?.surface_type && (
                              <Badge variant="secondary">{court.surface_type}</Badge>
                            )}
                            {court?.is_main_court && (
                              <Badge variant="default">Hauptplatz</Badge>
                            )}
                          </div>
                          <div className="flex gap-1">
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => moveCourt(courtId, 'up')}
                              disabled={index === 0}
                            >
                              ↑
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => moveCourt(courtId, 'down')}
                              disabled={index === config.court_preference_order.length - 1}
                            >
                              ↓
                            </Button>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="rules" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Konflikt-Management</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="delayHandling">Bei Verzögerungen</Label>
                  <Select
                    value={config.delay_handling}
                    onValueChange={(value: any) => updateConfig('delay_handling', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="reschedule">Spiele verschieben</SelectItem>
                      <SelectItem value="compress">Pausen verkürzen</SelectItem>
                      <SelectItem value="extend_day">Tag verlängern</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="rainBuffer">Regenpausen-Puffer (Min.)</Label>
                  <Input
                    id="rainBuffer"
                    type="number"
                    min="0"
                    max="120"
                    value={config.rain_delay_buffer}
                    onChange={(e) => updateConfig('rain_delay_buffer', parseInt(e.target.value) || 30)}
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="parallelEnabled"
                    checked={config.parallel_scheduling_enabled}
                    onCheckedChange={(checked) => updateConfig('parallel_scheduling_enabled', checked)}
                  />
                  <Label htmlFor="parallelEnabled">Parallelspiele aktivieren</Label>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Format-spezifische Einstellungen</CardTitle>
              </CardHeader>
              <CardContent>
                {getFormatSpecificSettings()}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="summary" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  Schätzung
                </CardTitle>
              </CardHeader>
              <CardContent>
                {estimatedDuration && (
                  <div className="grid grid-cols-3 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary">
                        {estimatedDuration.days === Infinity ? '∞' : estimatedDuration.days}
                      </div>
                      <div className="text-sm text-muted-foreground">Tage</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary">{estimatedDuration.hours_per_day.toFixed(1)}</div>
                      <div className="text-sm text-muted-foreground">Stunden/Tag</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary">{estimatedDuration.total_matches}</div>
                      <div className="text-sm text-muted-foreground">Spiele</div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {warnings.length > 0 && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <div className="space-y-1">
                    {warnings.map((warning, index) => (
                      <div key={index}>• {warning}</div>
                    ))}
                  </div>
                </AlertDescription>
              </Alert>
            )}

            <Card>
              <CardHeader>
                <CardTitle>Konfiguration Übersicht</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>Teilnehmer: {participantCount}</div>
                  <div>Tägliche Spielzeit: {config.daily_start_time} - {config.daily_end_time}</div>
                  <div>Pause zwischen Spielen: {config.minimum_break_between_matches} Min.</div>
                  <div>Max. Spiele/Spieler/Tag: {config.max_matches_per_player_per_day}</div>
                  <div>Verfügbare Plätze: {availableCourts.length}</div>
                  <div>Parallelspiele: {config.parallel_scheduling_enabled ? 'Ja' : 'Nein'}</div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <Separator />

        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isGenerating}>
            Abbrechen
          </Button>
          {onSmartScheduleGenerate ? (
            <Button 
              onClick={handleSmartScheduleGenerate} 
              disabled={participantCount < 2 || isGenerating}
            >
              {isGenerating ? 'Generiere Spiele...' : 'Spiele mit Smart Scheduling erstellen'}
            </Button>
          ) : (
            <Button onClick={() => onConfirm(config)} disabled={participantCount < 2}>
              Intelligente Terminierung starten
            </Button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
