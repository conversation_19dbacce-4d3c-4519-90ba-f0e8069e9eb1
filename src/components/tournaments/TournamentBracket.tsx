import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Trophy, Users, Clock } from 'lucide-react';

interface BracketMatch {
  id: string;
  participant1?: { id: string; user_name: string };
  participant2?: { id: string; user_name: string };
  sets?: Array<{ player1_score: number; player2_score: number }>;
  winner_id?: string;
  status: 'scheduled' | 'in_progress' | 'completed' | 'walkover';
  round_number: number;
  match_number: number;
}

interface TournamentBracketProps {
  matches: BracketMatch[];
  tournamentName: string;
}

export function TournamentBracket({ matches, tournamentName }: TournamentBracketProps) {
  // Group matches by round
  const matchesByRound = matches.reduce((acc, match) => {
    if (!acc[match.round_number]) {
      acc[match.round_number] = [];
    }
    acc[match.round_number].push(match);
    return acc;
  }, {} as Record<number, BracketMatch[]>);

  const rounds = Object.keys(matchesByRound).map(Number).sort((a, b) => a - b);
  const maxRound = Math.max(...rounds);

  const getRoundTitle = (round: number) => {
    const roundsFromEnd = maxRound - round;
    switch (roundsFromEnd) {
      case 0: return 'Finale';
      case 1: return 'Halbfinale';
      case 2: return 'Viertelfinale';
      default: return `Runde ${round}`;
    }
  };

  const formatScore = (sets: Array<{ player1_score: number; player2_score: number }> | undefined) => {
    if (!sets || sets.length === 0) return '';
    return sets.map(set => `${set.player1_score}:${set.player2_score}`).join(', ');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-50 border-green-200';
      case 'in_progress': return 'bg-blue-50 border-blue-200';
      case 'scheduled': return 'bg-gray-50 border-gray-200';
      default: return 'bg-gray-50 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <Trophy className="h-3 w-3 text-green-600" />;
      case 'in_progress': return <Clock className="h-3 w-3 text-blue-600" />;
      case 'scheduled': return <Users className="h-3 w-3 text-gray-600" />;
      default: return null;
    }
  };

  if (!matches || matches.length === 0) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <Trophy className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">Keine Spiele verfügbar</h3>
          <p className="text-muted-foreground">
            Das Turnier hat noch keine generierten Spiele.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold flex items-center justify-center gap-2">
          <Trophy className="h-6 w-6 text-yellow-500" />
          {tournamentName}
        </h2>
        <p className="text-muted-foreground">Tournament Bracket</p>
      </div>

      <div className="overflow-x-auto">
        <div className="flex gap-8 min-w-max pb-4">
          {rounds.map((round) => (
            <div key={round} className="flex flex-col gap-4 min-w-[280px]">
              <div className="text-center">
                <Badge variant="outline" className="text-sm font-medium">
                  {getRoundTitle(round)}
                </Badge>
              </div>
              
              <div className="space-y-4">
                {matchesByRound[round]
                  .sort((a, b) => a.match_number - b.match_number)
                  .map((match) => (
                    <Card 
                      key={match.id} 
                      className={`transition-all hover:shadow-md ${getStatusColor(match.status)}`}
                    >
                      <CardContent className="p-4">
                        <div className="space-y-3">
                          {/* Match Header */}
                          <div className="flex items-center justify-between">
                            <span className="text-xs font-medium text-muted-foreground">
                              Spiel #{match.match_number}
                            </span>
                            <div className="flex items-center gap-1">
                              {getStatusIcon(match.status)}
                              <span className="text-xs capitalize text-muted-foreground">
                                {match.status === 'scheduled' ? 'Geplant' :
                                 match.status === 'in_progress' ? 'Läuft' :
                                 match.status === 'completed' ? 'Beendet' : match.status}
                              </span>
                            </div>
                          </div>

                          {/* Players */}
                          <div className="space-y-2">
                            {/* Player 1 */}
                            <div className={`flex items-center justify-between p-2 rounded ${
                              match.winner_id === match.participant1?.id 
                                ? 'bg-yellow-50 border border-yellow-200' 
                                : 'bg-white border'
                            }`}>
                              <div className="flex items-center gap-2">
                                {match.winner_id === match.participant1?.id && (
                                  <Trophy className="h-4 w-4 text-yellow-500" />
                                )}
                                <span className={`font-medium ${
                                  match.winner_id === match.participant1?.id 
                                    ? 'text-yellow-700' 
                                    : 'text-foreground'
                                }`}>
                                  {match.participant1?.user_name || 'TBD'}
                                </span>
                              </div>
                              {match.sets && match.sets.length > 0 && (
                                <div className="text-sm font-mono">
                                  {match.sets.map((set, i) => (
                                    <span key={i} className="ml-1">
                                      {set.player1_score}
                                    </span>
                                  ))}
                                </div>
                              )}
                            </div>

                            {/* VS Divider */}
                            <div className="text-center text-xs text-muted-foreground font-medium">
                              VS
                            </div>

                            {/* Player 2 */}
                            <div className={`flex items-center justify-between p-2 rounded ${
                              match.winner_id === match.participant2?.id 
                                ? 'bg-yellow-50 border border-yellow-200' 
                                : 'bg-white border'
                            }`}>
                              <div className="flex items-center gap-2">
                                {match.winner_id === match.participant2?.id && (
                                  <Trophy className="h-4 w-4 text-yellow-500" />
                                )}
                                <span className={`font-medium ${
                                  match.winner_id === match.participant2?.id 
                                    ? 'text-yellow-700' 
                                    : 'text-foreground'
                                }`}>
                                  {match.participant2?.user_name || 'Bye'}
                                </span>
                              </div>
                              {match.sets && match.sets.length > 0 && (
                                <div className="text-sm font-mono">
                                  {match.sets.map((set, i) => (
                                    <span key={i} className="ml-1">
                                      {set.player2_score}
                                    </span>
                                  ))}
                                </div>
                              )}
                            </div>
                          </div>

                          {/* Score Summary */}
                          {match.sets && match.sets.length > 0 && (
                            <div className="text-center pt-2 border-t">
                              <span className="text-xs text-muted-foreground">
                                {formatScore(match.sets)}
                              </span>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Tournament Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-8">
        <Card>
          <CardContent className="p-4 text-center">
            <Users className="h-8 w-8 mx-auto text-blue-500 mb-2" />
            <div className="text-2xl font-bold">
              {new Set([
                ...matches.map(m => m.participant1?.id).filter(Boolean),
                ...matches.map(m => m.participant2?.id).filter(Boolean)
              ]).size}
            </div>
            <div className="text-sm text-muted-foreground">Teilnehmer</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <Trophy className="h-8 w-8 mx-auto text-green-500 mb-2" />
            <div className="text-2xl font-bold">
              {matches.filter(m => m.status === 'completed').length}
            </div>
            <div className="text-sm text-muted-foreground">Beendete Spiele</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <Clock className="h-8 w-8 mx-auto text-orange-500 mb-2" />
            <div className="text-2xl font-bold">
              {matches.filter(m => m.status === 'scheduled' || m.status === 'in_progress').length}
            </div>
            <div className="text-sm text-muted-foreground">Ausstehende Spiele</div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}