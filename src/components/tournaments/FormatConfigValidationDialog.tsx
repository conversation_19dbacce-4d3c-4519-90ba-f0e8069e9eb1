import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { AlertTriangle, Info, Settings, Users, Trophy, Target, Calendar } from 'lucide-react';
import { tournamentFormatEngine } from '@/lib/tournament/TournamentFormatEngine';
import { SmartSchedulingDialog } from './SmartSchedulingDialog';

interface Tournament {
  id: string;
  name: string;
  format_type: string;
  format_config: Record<string, any>;
  max_participants: number;
  club_id: string;
}

interface Participant {
  id: string;
  user_name: string;
  seed_number?: number;
}

interface Court {
  id: string;
  number: number;
  surface_type?: string;
  court_group?: string;
  is_main_court?: boolean;
}

interface FormatConfigValidationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  tournament: Tournament;
  participants: Participant[];
  availableCourts?: Court[];
  onConfirm: (validatedConfig: Record<string, any>, smartSchedulingConfig?: any) => void;
}

export function FormatConfigValidationDialog({
  open,
  onOpenChange,
  tournament,
  participants,
  availableCourts = [],
  onConfirm
}: FormatConfigValidationDialogProps) {
  const [config, setConfig] = useState<Record<string, any>>({});
  const [errors, setErrors] = useState<string[]>([]);
  const [warnings, setWarnings] = useState<string[]>([]);
  const [showSmartScheduling, setShowSmartScheduling] = useState(false);
  const [smartSchedulingConfig, setSmartSchedulingConfig] = useState<any>(null);

  useEffect(() => {
    if (open && tournament) {
      // Initialize with tournament's current config or format defaults
      const formatInfo = tournamentFormatEngine.getAvailableFormats()
        .find(f => f.type === tournament.format_type);
      
      const initialConfig = {
        ...formatInfo?.config,
        ...tournament.format_config
      };
      
      setConfig(initialConfig);
      validateConfiguration(initialConfig);
    }
  }, [open, tournament]);

  const validateConfiguration = (currentConfig: Record<string, any>) => {
    const newErrors: string[] = [];
    const newWarnings: string[] = [];

    try {
      // Check minimum participants
      const requirements = tournamentFormatEngine.getRequiredParticipants(
        tournament.format_type, 
        currentConfig
      );

      if (participants.length < requirements.min) {
        newErrors.push(`Mindestens ${requirements.min} Teilnehmer erforderlich (aktuell: ${participants.length})`);
      }

      if (requirements.max && participants.length > requirements.max) {
        newErrors.push(`Maximal ${requirements.max} Teilnehmer erlaubt (aktuell: ${participants.length})`);
      }

      // Format-specific validations
      switch (tournament.format_type) {
        case 'knockout':
          if (participants.length > 0 && !isPowerOfTwo(participants.length)) {
            newWarnings.push('Teilnehmerzahl ist keine Potenz von 2. Einige Spieler erhalten ein Freilos.');
          }
          break;

        case 'round_robin':
          const groups = currentConfig.groups || 1;
          if (participants.length % groups !== 0) {
            newWarnings.push(`Bei ${groups} Gruppen sind die Gruppen nicht gleich groß.`);
          }
          if (participants.length / groups < 3) {
            newWarnings.push('Weniger als 3 Spieler pro Gruppe - sehr kurzes Turnier.');
          }
          break;

        case 'team_tie':
          if (participants.length % 2 !== 0) {
            newErrors.push('Team-Format benötigt eine gerade Anzahl von Teams.');
          }
          break;

        case 'uts':
          if (!currentConfig.best_of || currentConfig.best_of < 1) {
            newErrors.push('UTS Format benötigt eine gültige "Best of" Einstellung.');
          }
          break;
      }

      // Validate using engine
      if (!tournamentFormatEngine.validateFormat(tournament.format_type, currentConfig)) {
        newErrors.push('Konfiguration ist für dieses Format nicht gültig.');
      }

    } catch (error) {
      newErrors.push(`Validierungsfehler: ${error.message}`);
    }

    setErrors(newErrors);
    setWarnings(newWarnings);
  };

  const isPowerOfTwo = (n: number) => n > 0 && (n & (n - 1)) === 0;

  const updateConfig = (key: string, value: any) => {
    const newConfig = { ...config, [key]: value };
    setConfig(newConfig);
    validateConfiguration(newConfig);
  };

  const getConfigSummary = () => {
    const items = [];
    
    switch (tournament.format_type) {
      case 'round_robin':
        const groups = config.groups || 1;
        const tiebreaker = config.tiebreaker?.[0] === 'head_to_head' ? 'Direkter Vergleich' : 
                          config.tiebreaker?.[0] === 'set_ratio' ? 'Satzverhältnis' : 'Spielverhältnis';
        items.push(
          { icon: Users, label: 'Gruppen', value: `${groups} ${groups === 1 ? 'Gruppe' : 'Gruppen'}` },
          { icon: Trophy, label: 'Tie-Breaker', value: `${tiebreaker} → Satzverhältnis` }
        );
        break;
        
      case 'knockout':
        const seeding = config.seeding === 'random' ? 'Zufällige Setzung' : 
                       config.seeding === 'rating' ? 'Setzung nach Spielstärke' : 'Manuelle Setzung';
        const consolation = config.consolation_plate ? 'Mit Trostturnier' : 'Kein Trostturnier';
        items.push(
          { icon: Target, label: 'Setzung', value: seeding },
          { icon: Trophy, label: 'Trostturnier', value: consolation }
        );
        break;
        
      case 'uts':
        const bestOf = config.best_of || 3;
        const setsToWin = config.sets_to_win || 2;
        items.push(
          { icon: Calendar, label: 'Spielmodus', value: `Best of ${bestOf}` },
          { icon: Trophy, label: 'Sätze zum Sieg', value: `${setsToWin} ${setsToWin === 1 ? 'Satz' : 'Sätze'}` }
        );
        break;
        
      case 'team_tie':
        const rubbers = config.rubbers || [];
        const rubberNames = [];
        if (rubbers.includes('singles1')) rubberNames.push('Einzel 1');
        if (rubbers.includes('singles2')) rubberNames.push('Einzel 2');
        if (rubbers.includes('doubles')) rubberNames.push('Doppel');
        if (rubbers.includes('singles3')) rubberNames.push('Einzel 3');
        if (rubbers.includes('singles4')) rubberNames.push('Einzel 4');
        
        items.push(
          { icon: Users, label: 'Rubbers', value: rubberNames.length ? rubberNames.join(', ') : 'Keine ausgewählt' }
        );
        break;
        
      default:
        items.push(
          { icon: Settings, label: 'Konfiguration', value: 'Standard-Einstellungen' }
        );
    }
    
    return items;
  };

  const getFormatSpecificFields = () => {
    switch (tournament.format_type) {
      case 'round_robin':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="groups">Anzahl Gruppen</Label>
              <Input
                id="groups"
                type="number"
                min="1"
                max="8"
                value={config.groups || 1}
                onChange={(e) => updateConfig('groups', parseInt(e.target.value) || 1)}
              />
            </div>
            <div>
              <Label htmlFor="tiebreaker">Tie-Breaker Reihenfolge</Label>
              <Select 
                value={config.tiebreaker?.[0] || 'head_to_head'}
                onValueChange={(value) => updateConfig('tiebreaker', [value, 'set_ratio'])}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="head_to_head">Direkter Vergleich</SelectItem>
                  <SelectItem value="set_ratio">Satzverhältnis</SelectItem>
                  <SelectItem value="game_ratio">Spielverhältnis</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        );

      case 'knockout':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="seeding">Setzung</Label>
              <Select 
                value={config.seeding || 'random'}
                onValueChange={(value) => updateConfig('seeding', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="random">Zufällig</SelectItem>
                  <SelectItem value="rating">Nach Spielstärke</SelectItem>
                  <SelectItem value="manual">Manuell</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="consolation"
                checked={config.consolation_plate || false}
                onCheckedChange={(checked) => updateConfig('consolation_plate', checked)}
              />
              <Label htmlFor="consolation">Trostturnier für Erstrundenverlierer</Label>
            </div>
          </div>
        );

      case 'uts':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="bestOf">Best of</Label>
              <Select 
                value={config.best_of?.toString() || '3'}
                onValueChange={(value) => updateConfig('best_of', parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">Best of 1</SelectItem>
                  <SelectItem value="3">Best of 3</SelectItem>
                  <SelectItem value="5">Best of 5</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="setsToWin">Sätze zum Sieg</Label>
              <Input
                id="setsToWin"
                type="number"
                min="1"
                max="3"
                value={config.sets_to_win || 2}
                onChange={(e) => updateConfig('sets_to_win', parseInt(e.target.value) || 2)}
              />
            </div>
          </div>
        );

      case 'team_tie':
        return (
          <div className="space-y-4">
            <div>
              <Label>Rubber-Zusammensetzung</Label>
              <div className="grid grid-cols-2 gap-2 mt-2">
                {['Einzel 1', 'Einzel 2', 'Doppel', 'Einzel 3', 'Einzel 4'].map((rubber, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <Switch
                      checked={config.rubbers?.includes(`singles${index + 1}`) || config.rubbers?.includes('doubles') && rubber === 'Doppel'}
                      onCheckedChange={(checked) => {
                        const rubbers = config.rubbers || [];
                        const rubberKey = rubber === 'Doppel' ? 'doubles' : `singles${index + 1}`;
                        if (checked) {
                          updateConfig('rubbers', [...rubbers.filter(r => r !== rubberKey), rubberKey]);
                        } else {
                          updateConfig('rubbers', rubbers.filter(r => r !== rubberKey));
                        }
                      }}
                    />
                    <Label>{rubber}</Label>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );

      default:
        return (
          <div className="text-sm text-muted-foreground">
            Keine spezifischen Konfigurationen für dieses Format erforderlich.
          </div>
        );
    }
  };

  const canProceed = errors.length === 0;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Turnier-Konfiguration validieren
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Tournament Info */}
          <div className="grid grid-cols-2 gap-4 p-4 bg-muted rounded-lg">
            <div>
              <Label className="text-sm font-medium">Turnier</Label>
              <p className="text-sm">{tournament.name}</p>
            </div>
            <div>
              <Label className="text-sm font-medium">Format</Label>
              <Badge variant="outline">
                {tournamentFormatEngine.getAvailableFormats()
                  .find(f => f.type === tournament.format_type)?.name || tournament.format_type}
              </Badge>
            </div>
            <div>
              <Label className="text-sm font-medium">Teilnehmer</Label>
              <p className="text-sm">{participants.length} angemeldet</p>
            </div>
            <div>
              <Label className="text-sm font-medium">Max. Teilnehmer</Label>
              <p className="text-sm">{tournament.max_participants || 'Unbegrenzt'}</p>
            </div>
          </div>

          {/* Errors */}
          {errors.length > 0 && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-1">
                  {errors.map((error, index) => (
                    <div key={index}>• {error}</div>
                  ))}
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Warnings */}
          {warnings.length > 0 && (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-1">
                  {warnings.map((warning, index) => (
                    <div key={index}>• {warning}</div>
                  ))}
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Format-specific configuration */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Format-Einstellungen</h3>
            {getFormatSpecificFields()}
          </div>

          {/* Configuration Summary */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Zusammenfassung</h3>
            <div className="grid gap-3">
              {getConfigSummary().map((item, index) => {
                const IconComponent = item.icon;
                return (
                  <div key={index} className="flex items-center gap-3 p-3 bg-muted rounded-lg">
                    <div className="flex-shrink-0">
                      <IconComponent className="h-4 w-4 text-muted-foreground" />
                    </div>
                    <div className="flex-1">
                      <div className="text-sm font-medium">{item.label}</div>
                      <div className="text-sm text-muted-foreground">{item.value}</div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Actions */}
          <div className="flex gap-2 pt-4">
            <Button 
              onClick={() => onConfirm(config)}
              disabled={!canProceed}
              variant="outline"
              className="flex-1"
            >
              Spiele nur generieren
            </Button>
            <Button 
              onClick={() => setShowSmartScheduling(true)}
              disabled={!canProceed}
              className="flex-1"
            >
              Intelligente Terminierung
            </Button>
            <Button 
              variant="outline" 
              onClick={() => onOpenChange(false)}
            >
              Abbrechen
            </Button>
          </div>
        </div>
      </DialogContent>

      {/* Smart Scheduling Dialog */}
      <SmartSchedulingDialog
        open={showSmartScheduling}
        onOpenChange={setShowSmartScheduling}
        tournament={tournament}
        availableCourts={availableCourts}
        onConfirm={(schedulingConfig) => {
          setSmartSchedulingConfig(schedulingConfig);
          setShowSmartScheduling(false);
          onConfirm(config, schedulingConfig);
        }}
        onSmartScheduleGenerate={(schedulingConfig) => {
          setSmartSchedulingConfig(schedulingConfig);
          setShowSmartScheduling(false);
          onConfirm(config, schedulingConfig);
        }}
      />
    </Dialog>
  );
}