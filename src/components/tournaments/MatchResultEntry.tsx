import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON>le, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Trophy, AlertCircle, Clock } from 'lucide-react';
import { useTournamentFormatConfig } from '@/hooks/useTournamentFormatConfig';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

export interface MatchResultEntryProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  match: {
    id: string;
    tournament_id: string;
    participant1_id: string;
    participant2_id: string;
    participant1_name: string;
    participant2_name: string;
    format_type: string;
    court_name?: string;
    scheduled_start?: string;
  };
  onResultSubmitted?: () => void;
}

// Standard format configurations
const STANDARD_FORMATS: Record<string, any> = {
  uts: {
    name: 'UTS (Ultimate Tennis Showdown)',
    scoring: 'first_to_games',
    target_games: 4,
    sudden_death_at: 3,
    tiebreak_type: 'sudden_death',
    description: 'Erstes zu 4 Games, Sudden Death bei 3:3'
  },
  standard_tennis: {
    name: 'Standard Tennis',
    scoring: 'sets_and_games',
    sets_to_win: 2,
    games_per_set: 6,
    tiebreak_at: 6,
    tiebreak_type: 'standard_7',
    description: 'Best of 3 Sets, Tiebreak bei 6:6'
  },
  pro_set: {
    name: 'Pro Set',
    scoring: 'first_to_games',
    target_games: 8,
    tiebreak_at: 8,
    tiebreak_type: 'standard_7',
    description: 'Erstes zu 8 Games, Tiebreak bei 8:8'
  }
};

interface MatchResult {
  result_type: 'completed' | 'walkover' | 'forfeit' | 'retired';
  winner_participant_id: string;
  score_data: Record<string, any>;
  notes?: string;
}

export function MatchResultEntry({ open, onOpenChange, match, onResultSubmitted }: MatchResultEntryProps) {
  const [resultType, setResultType] = useState<'completed' | 'walkover' | 'forfeit' | 'retired'>('completed');
  const [winner, setWinner] = useState<string>('');
  const [scoreData, setScoreData] = useState<Record<string, any>>({});
  const [notes, setNotes] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  // Get format configuration
  const { getFormatConfig, isLoading: configLoading } = useTournamentFormatConfig(
    match.tournament_id,
    match.format_type
  );

  // Determine effective format configuration
  const formatConfig: any = getFormatConfig(match.format_type, match.tournament_id) || 
                           STANDARD_FORMATS[match.format_type] || 
                           STANDARD_FORMATS.standard_tennis;

  // Reset form when dialog opens
  useEffect(() => {
    if (open) {
      setResultType('completed');
      setWinner('');
      setScoreData({});
      setNotes('');
      setValidationErrors([]);
      
      // Initialize score data based on format
      if (formatConfig) {
        initializeScoreData(formatConfig);
      }
    }
  }, [open, formatConfig]);

  const initializeScoreData = (config: any) => {
    const customRules = config.custom_rules || {};
    
    if (config.scoring === 'sets_and_games' || customRules.scoring === 'sets_and_games') {
      // Set-based scoring
      const maxSets = customRules.sets_to_win ? customRules.sets_to_win * 2 - 1 : 3;
      const sets = Array.from({ length: maxSets }, () => ({ player1: '', player2: '' }));
      setScoreData({ sets, tiebreaks: [] });
    } else if (config.scoring === 'first_to_games' || customRules.scoring === 'first_to_games') {
      // Game-based scoring (UTS, Pro Set)
      setScoreData({ 
        player1_games: '', 
        player2_games: '',
        sudden_death: false,
        tiebreak_score: { player1: '', player2: '' }
      });
    } else {
      // Custom scoring system
      setScoreData({ custom_score: '' });
    }
  };

  const validateScore = (config: any, data: Record<string, any>): string[] => {
    const errors: string[] = [];
    const customRules = config.custom_rules || {};
    
    if (resultType !== 'completed') return errors;

    if (config.scoring === 'sets_and_games' || customRules.scoring === 'sets_and_games') {
      // Validate set-based scoring
      const sets = data.sets || [];
      const setsToWin = customRules.sets_to_win || config.sets_to_win || 2;
      
      let player1Sets = 0;
      let player2Sets = 0;
      let hasValidSets = false;

      for (const set of sets) {
        if (set.player1 && set.player2) {
          hasValidSets = true;
          const p1Games = parseInt(set.player1);
          const p2Games = parseInt(set.player2);
          
          if (p1Games > p2Games) player1Sets++;
          else if (p2Games > p1Games) player2Sets++;
        }
      }

      if (!hasValidSets) {
        errors.push('Mindestens ein Satz muss eingegeben werden');
      } else if (player1Sets < setsToWin && player2Sets < setsToWin) {
        errors.push(`Ein Spieler muss ${setsToWin} Sätze gewinnen`);
      }

    } else if (config.scoring === 'first_to_games' || customRules.scoring === 'first_to_games') {
      // Validate game-based scoring (UTS, Pro Set, etc.)
      const p1Games = parseInt(data.player1_games);
      const p2Games = parseInt(data.player2_games);
      const targetGames = customRules.target_games || config.target_games || 4;
      const suddenDeathAt = customRules.sudden_death_at || config.sudden_death_at;

      if (isNaN(p1Games) || isNaN(p2Games)) {
        errors.push('Beide Spieler müssen eine gültige Anzahl Games haben');
      } else if (p1Games < targetGames && p2Games < targetGames) {
        errors.push(`Ein Spieler muss mindestens ${targetGames} Games erreichen`);
      } else {
        // Check if valid winning scenario
        const maxGames = Math.max(p1Games, p2Games);
        const minGames = Math.min(p1Games, p2Games);
        
        // UTS/Sudden Death format logic
        if (suddenDeathAt && (config.name?.includes('UTS') || config.format_type === 'uts')) {
          // UTS: First to targetGames OR sudden death winner
          if (maxGames >= targetGames) {
            // Someone reached target - either they won normally or it's sudden death
            if (maxGames === targetGames && minGames === suddenDeathAt) {
              // Sudden death scenario (e.g., 4:3) - valid
            } else if (maxGames === targetGames && minGames < targetGames) {
              // Normal win (e.g., 4:2) - valid
            } else if (maxGames > targetGames) {
              errors.push(`Bei UTS kann niemand mehr als ${targetGames} Games haben (außer Sudden Death)`);
            }
          } else {
            errors.push(`Ein Spieler muss ${targetGames} Games erreichen oder Sudden Death gewonnen haben`);
          }
        } else {
          // Standard game-based format (Pro Set, etc.) - needs 2-game lead
          if (Math.abs(p1Games - p2Games) < 2 && maxGames < targetGames + 1) {
            errors.push('Gewinner muss mindestens 2 Games Vorsprung haben oder Tiebreak gespielt werden');
          }
        }
      }
    }

    if (!winner) {
      errors.push('Gewinner muss ausgewählt werden');
    }

    return errors;
  };

  const handleSubmit = async () => {
    if (!formatConfig) {
      toast.error('Format-Konfiguration konnte nicht geladen werden');
      return;
    }

    // Validate only on submit attempt
    const errors = validateScore(formatConfig, scoreData);
    setValidationErrors(errors);

    if (errors.length > 0) return;

    setIsSubmitting(true);

    try {
      // Convert the score data to the format expected by the edge function
      const sets = [];
      if (scoreData.player1_games && scoreData.player2_games) {
        sets.push({
          player1: parseInt(scoreData.player1_games),
          player2: parseInt(scoreData.player2_games),
          tiebreak: scoreData.tiebreak_score?.player1 && scoreData.tiebreak_score?.player2 ? {
            player1: parseInt(scoreData.tiebreak_score.player1),
            player2: parseInt(scoreData.tiebreak_score.player2)
          } : null
        });
      }

      const { error } = await supabase.functions.invoke('tournament-matches', {
        body: {
          action: 'update_score',
          tournament_id: match.tournament_id,
          match_id: match.id,
          sets: sets,
          winner_id: winner === 'participant1' ? match.participant1_id : match.participant2_id
        }
      });

      if (error) {
        console.error('Result submission error:', error);
        toast.error('Fehler beim Speichern des Ergebnisses');
        return;
      }

      toast.success('Ergebnis erfolgreich gespeichert');
      onResultSubmitted?.();
      onOpenChange(false);

    } catch (error) {
      console.error('Submission error:', error);
      toast.error('Fehler beim Speichern des Ergebnisses');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderScoreInputs = () => {
    if (!formatConfig) return null;

    const customRules = formatConfig.custom_rules || {};
    const scoring = customRules.scoring || formatConfig.scoring || 'sets_and_games';

    if (resultType !== 'completed') {
      return (
        <div className="space-y-4">
          <div className="flex items-center gap-2 text-muted-foreground">
            <AlertCircle className="h-4 w-4" />
            <span>Bei {resultType === 'walkover' ? 'Walkover' : resultType === 'forfeit' ? 'Aufgabe' : 'Verletzung'} ist keine Scoreeingabe erforderlich.</span>
          </div>
        </div>
      );
    }

    if (scoring === 'sets_and_games') {
      return renderSetBasedInputs();
    } else if (scoring === 'first_to_games') {
      return renderGameBasedInputs();
    } else {
      return renderCustomInputs();
    }
  };

  const renderSetBasedInputs = () => {
    const sets = scoreData.sets || [];
    const customRules = formatConfig.custom_rules || {};
    const setsToWin = customRules.sets_to_win || formatConfig.sets_to_win || 2;
    const maxSets = setsToWin * 2 - 1;

    return (
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Trophy className="h-4 w-4" />
          <span className="font-medium">Satz-Ergebnisse (Best of {maxSets})</span>
        </div>
        
        {Array.from({ length: maxSets }).map((_, setIndex) => {
          const set = sets[setIndex] || { player1: '', player2: '' };
          return (
            <Card key={setIndex} className="p-4">
              <div className="flex items-center gap-4">
                <Label className="w-16">{setIndex + 1}. Satz:</Label>
                <div className="flex items-center gap-2">
                  <Label className="w-32 text-sm">{match.participant1_name}</Label>
                  <Input
                    type="number"
                    min="0"
                    max="99"
                    value={set.player1}
                    onChange={(e) => {
                      const newSets = [...sets];
                      newSets[setIndex] = { ...set, player1: e.target.value };
                      setScoreData({ ...scoreData, sets: newSets });
                    }}
                    className="w-16 text-center"
                  />
                  <span className="text-muted-foreground">:</span>
                  <Input
                    type="number"
                    min="0"
                    max="99"
                    value={set.player2}
                    onChange={(e) => {
                      const newSets = [...sets];
                      newSets[setIndex] = { ...set, player2: e.target.value };
                      setScoreData({ ...scoreData, sets: newSets });
                    }}
                    className="w-16 text-center"
                  />
                  <Label className="w-32 text-sm">{match.participant2_name}</Label>
                </div>
              </div>
            </Card>
          );
        })}
      </div>
    );
  };

  const renderGameBasedInputs = () => {
    const customRules = formatConfig.custom_rules || {};
    const targetGames = customRules.target_games || formatConfig.target_games || 4;
    const suddenDeathAt = customRules.sudden_death_at || formatConfig.sudden_death_at;

    return (
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Trophy className="h-4 w-4" />
          <span className="font-medium">
            {formatConfig.custom_name || formatConfig.name || `Erstes zu ${targetGames} Games`}
            {suddenDeathAt && ` (Sudden Death bei ${suddenDeathAt}:${suddenDeathAt})`}
          </span>
        </div>
        
        <Card className="p-4">
          <div className="flex items-center gap-4">
            <Label className="w-16">Ergebnis:</Label>
            <div className="flex items-center gap-2">
              <Label className="w-32 text-sm">{match.participant1_name}</Label>
              <Input
                type="number"
                min="0"
                max="20"
                value={scoreData.player1_games || ''}
                onChange={(e) => setScoreData({ ...scoreData, player1_games: e.target.value })}
                className="w-16 text-center"
              />
              <span className="text-muted-foreground">:</span>
              <Input
                type="number"
                min="0"
                max="20"
                value={scoreData.player2_games || ''}
                onChange={(e) => setScoreData({ ...scoreData, player2_games: e.target.value })}
                className="w-16 text-center"
              />
              <Label className="w-32 text-sm">{match.participant2_name}</Label>
            </div>
          </div>
          
          {suddenDeathAt && (
            <div className="mt-4">
              <Label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={scoreData.sudden_death || false}
                  onChange={(e) => setScoreData({ ...scoreData, sudden_death: e.target.checked })}
                />
                Sudden Death gespielt
              </Label>
            </div>
          )}
        </Card>
      </div>
    );
  };

  const renderCustomInputs = () => {
    return (
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Trophy className="h-4 w-4" />
          <span className="font-medium">Custom Format: {formatConfig.custom_name || formatConfig.name || 'Benutzerdefiniert'}</span>
        </div>
        
        <Card className="p-4">
          <Label htmlFor="custom-score">Ergebnis</Label>
          <Input
            id="custom-score"
            value={scoreData.custom_score || ''}
            onChange={(e) => setScoreData({ ...scoreData, custom_score: e.target.value })}
            placeholder="Geben Sie das Ergebnis ein..."
            className="mt-2"
          />
          {(formatConfig.custom_description || formatConfig.description) && (
            <p className="text-sm text-muted-foreground mt-2">
              {formatConfig.custom_description || formatConfig.description}
            </p>
          )}
        </Card>
      </div>
    );
  };

  if (configLoading) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-2xl">
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Trophy className="h-5 w-5" />
            Spielergebnis eintragen
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Match Info */}
          <Card className="p-4 bg-muted/50">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="font-medium">{match.participant1_name}</span>
                <span className="text-muted-foreground">vs</span>
                <span className="font-medium">{match.participant2_name}</span>
              </div>
              
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                {match.court_name && (
                  <span>{match.court_name}</span>
                )}
                {match.scheduled_start && (
                  <span className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    {new Date(match.scheduled_start).toLocaleString('de-DE')}
                  </span>
                )}
              </div>
              
              <Badge variant="outline">
                {formatConfig?.custom_name || formatConfig?.name || match.format_type || 'Standard'}
              </Badge>
            </div>
          </Card>

          {/* Result Type Selection */}
          <div className="space-y-3">
            <Label className="text-base font-medium">Art des Ergebnisses</Label>
            <RadioGroup value={resultType} onValueChange={(value: any) => setResultType(value)}>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="completed" id="completed" />
                <Label htmlFor="completed">Reguläres Spiel beendet</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="walkover" id="walkover" />
                <Label htmlFor="walkover">Walkover (Gegner nicht angetreten)</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="forfeit" id="forfeit" />
                <Label htmlFor="forfeit">Aufgabe während des Spiels</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="retired" id="retired" />
                <Label htmlFor="retired">Verletzungsbedingte Aufgabe</Label>
              </div>
            </RadioGroup>
          </div>

          <Separator />

          {/* Winner Selection */}
          <div className="space-y-3">
            <Label className="text-base font-medium">Gewinner</Label>
            <Select value={winner} onValueChange={setWinner}>
              <SelectTrigger>
                <SelectValue placeholder="Gewinner auswählen..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="participant1">{match.participant1_name}</SelectItem>
                <SelectItem value="participant2">{match.participant2_name}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Separator />

          {/* Score Inputs */}
          {renderScoreInputs()}

          {/* Validation Errors */}
          {validationErrors.length > 0 && (
            <Card className="p-4 border-destructive bg-destructive/10">
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-destructive">
                  <AlertCircle className="h-4 w-4" />
                  <span className="font-medium">Eingabefehler:</span>
                </div>
                <ul className="list-disc list-inside space-y-1 text-sm text-destructive">
                  {validationErrors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </div>
            </Card>
          )}

          {/* Notes */}
          <div className="space-y-3">
            <Label htmlFor="notes">Anmerkungen (optional)</Label>
            <textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Zusätzliche Informationen zum Spiel..."
              className="w-full p-3 border rounded-md resize-none"
              rows={3}
            />
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isSubmitting}
          >
            Abbrechen
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Speichern...' : 'Ergebnis speichern'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}