
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Clock, Calendar, MapPin, AlertCircle, Globe } from 'lucide-react';
import { useClubTimezone } from '@/hooks/useClubTimezone';
import { formatTimeInClubTZ } from '@/lib/timezone-utils';

interface TimeConfiguration {
  match_duration_minutes: number;
  warmup_minutes: number;
  changeover_minutes: number;
  set_break_minutes: number;
  between_match_buffer_minutes: number;
  court_preparation_minutes: number;
}

interface TimeConfigPreviewProps {
  timeConfig: TimeConfiguration;
  availableCourts?: number;
  participantCount?: number;
  formatType?: string;
}

export function TimeConfigPreview({ 
  timeConfig, 
  availableCourts = 0, 
  participantCount = 0,
  formatType = 'knockout'
}: TimeConfigPreviewProps) {
  const { timezone, isClubTimezone } = useClubTimezone();
  const getTotalMatchTime = () => {
    return Object.values(timeConfig).reduce((sum, minutes) => sum + minutes, 0);
  };

  const getEstimatedTournamentDuration = () => {
    const totalMatchTime = getTotalMatchTime();
    let estimatedMatches = 0;

    // Estimate matches based on format
    switch (formatType) {
      case 'knockout':
      case 'single_elimination':
        estimatedMatches = Math.max(0, participantCount - 1);
        break;
      case 'round_robin':
        estimatedMatches = participantCount > 1 ? (participantCount * (participantCount - 1)) / 2 : 0;
        break;
      default:
        estimatedMatches = participantCount;
    }

    if (availableCourts > 0) {
      const parallelMatches = Math.ceil(estimatedMatches / availableCourts);
      return parallelMatches * totalMatchTime;
    }

    return estimatedMatches * totalMatchTime;
  };

  const getMatchesPerCourtPerDay = () => {
    const totalMatchTime = getTotalMatchTime();
    const dailyHours = 10; // Assume 10 hours playing time per day
    return Math.floor((dailyHours * 60) / totalMatchTime);
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}min`;
    }
    return `${mins}min`;
  };

  const totalMatchTime = getTotalMatchTime();
  const estimatedDuration = getEstimatedTournamentDuration();
  const matchesPerCourtPerDay = getMatchesPerCourtPerDay();

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg">
            <Clock className="h-5 w-5" />
            Zeitübersicht
            {isClubTimezone && (
              <Badge variant="outline" className="text-xs flex items-center gap-1">
                <Globe className="h-3 w-3" />
                {timezone}
              </Badge>
            )}
          </CardTitle>
          <CardDescription>
            Berechnete Zeiten basierend auf Ihrer Konfiguration
            {isClubTimezone && ` (${timezone})`}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Gesamtzeit pro Spiel:</span>
                <Badge variant="secondary" className="font-mono">
                  {formatDuration(totalMatchTime)}
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Reine Spielzeit:</span>
                <span className="font-mono text-sm">
                  {formatDuration(timeConfig.match_duration_minutes)}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Zusatzzeiten:</span>
                <span className="font-mono text-sm">
                  {formatDuration(totalMatchTime - timeConfig.match_duration_minutes)}
                </span>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Spiele pro Platz/Tag:</span>
                <Badge variant="outline" className="font-mono">
                  {matchesPerCourtPerDay}
                </Badge>
              </div>
              {availableCourts > 0 && (
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Verfügbare Plätze:</span>
                  <span className="font-mono text-sm">{availableCourts}</span>
                </div>
              )}
              {participantCount > 0 && (
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Teilnehmer:</span>
                  <span className="font-mono text-sm">{participantCount}</span>
                </div>
              )}
            </div>
          </div>

          {participantCount > 0 && availableCourts > 0 && (
            <div className="pt-4 border-t">
              <div className="flex items-center gap-2 mb-2">
                <Calendar className="h-4 w-4" />
                <span className="font-medium">Turnierdauer-Schätzung</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Geschätzte Gesamtdauer:</span>
                <Badge variant="default" className="font-mono">
                  {formatDuration(estimatedDuration)}
                </Badge>
              </div>
              <div className="flex justify-between items-center mt-1">
                <span className="text-sm text-muted-foreground">Tägliche Kapazität:</span>
                <span className="font-mono text-sm">
                  {matchesPerCourtPerDay * availableCourts} Spiele
                </span>
              </div>
            </div>
          )}

          {totalMatchTime > 180 && (
            <div className="flex items-start gap-2 p-3 bg-amber-50 border border-amber-200 rounded-md">
              <AlertCircle className="h-4 w-4 text-amber-600 mt-0.5" />
              <div className="text-sm">
                <p className="font-medium text-amber-800">Lange Spielzeiten</p>
                <p className="text-amber-700">
                  Spiele über 3 Stunden können für die Teilnehmer anstrengend werden.
                </p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
