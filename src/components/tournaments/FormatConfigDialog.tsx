import React, { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Info, Settings, Clock, MapPin, Trophy, Sliders, Calendar, Save } from 'lucide-react';
import { TimeConfigPreview } from './TimeConfigPreview';
import { useTournamentScheduling } from '@/hooks/useTournamentScheduling';

interface TimeConfiguration {
  match_duration_minutes: number;
  warmup_minutes: number;
  changeover_minutes: number;
  set_break_minutes: number;
  between_match_buffer_minutes: number;
  court_preparation_minutes: number;
}

interface FormatDefinition {
  format_type: string;
  name: string;
  description: string;
  default_config: Record<string, any>;
  default_time_config: TimeConfiguration;
  category: string;
}

interface FormatConfigDialogProps {
  format: FormatDefinition | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: (config: Record<string, any>) => void;
  initialConfig?: Record<string, any>;
}

export function FormatConfigDialog({ format, isOpen, onClose, onSave, initialConfig }: FormatConfigDialogProps) {
  const [config, setConfig] = useState<Record<string, any>>(initialConfig || {});
  const [timeConfig, setTimeConfig] = useState<TimeConfiguration>(format?.default_time_config || getDefaultTimeConfig(format?.format_type || ''));
  const [activeTab, setActiveTab] = useState('general');

  const { syncToBookingSystemMutation, validateTimeConfigMutation } = useTournamentScheduling();

  React.useEffect(() => {
    if (format) {
      setConfig(initialConfig || format.default_config);
      setTimeConfig(format.default_time_config || getDefaultTimeConfig(format.format_type));
    }
  }, [format, initialConfig]);

  if (!format) return null;

  const updateConfig = (key: string, value: any) => {
    setConfig(prev => ({ ...prev, [key]: value }));
  };

  const handleSave = async () => {
    const finalConfig = { 
      ...config, 
      time_config: timeConfig,
      integrated_scheduling: true, // Mark as integrated with scheduling system
      last_updated: new Date().toISOString()
    };

    // Validate time configuration if we have enough data
    if (config.participants_count && config.tournament_id) {
      try {
        await validateTimeConfigMutation.mutateAsync({
          tournament_id: config.tournament_id,
          time_config: timeConfig
        });
      } catch (error) {
        console.warn('Time config validation failed:', error);
        // Don't block saving, just warn
      }
    }

    onSave(finalConfig);
    onClose();
  };

  const handleSyncToBookingSystem = async () => {
    if (!config.tournament_id) {
      console.warn('No tournament ID available for sync');
      return;
    }

    try {
      await syncToBookingSystemMutation.mutateAsync({
        tournament_id: config.tournament_id,
        time_config: timeConfig
      });
    } catch (error) {
      console.error('Sync to booking system failed:', error);
    }
  };

  function getDefaultTimeConfig(formatType: string): TimeConfiguration {
    switch (formatType) {
      case 'knockout':
        return {
          match_duration_minutes: 120,
          warmup_minutes: 5,
          changeover_minutes: 3,
          set_break_minutes: 2,
          between_match_buffer_minutes: 15,
          court_preparation_minutes: 10
        };
      case 'round_robin':
        return {
          match_duration_minutes: 90,
          warmup_minutes: 5,
          changeover_minutes: 2,
          set_break_minutes: 1,
          between_match_buffer_minutes: 10,
          court_preparation_minutes: 5
        };
      case 'uts':
        return {
          match_duration_minutes: 90,
          warmup_minutes: 5,
          changeover_minutes: 1,
          set_break_minutes: 2,
          between_match_buffer_minutes: 15,
          court_preparation_minutes: 5
        };
      case 'pro_set':
        return {
          match_duration_minutes: 60,
          warmup_minutes: 3,
          changeover_minutes: 2,
          set_break_minutes: 1,
          between_match_buffer_minutes: 5,
          court_preparation_minutes: 5
        };
      case 'social_mixer':
        return {
          match_duration_minutes: 30,
          warmup_minutes: 2,
          changeover_minutes: 1,
          set_break_minutes: 1,
          between_match_buffer_minutes: 5,
          court_preparation_minutes: 3
        };
      default:
        return {
          match_duration_minutes: 90,
          warmup_minutes: 5,
          changeover_minutes: 3,
          set_break_minutes: 2,
          between_match_buffer_minutes: 10,
          court_preparation_minutes: 5
        };
    }
  }

  const updateTimeConfig = (key: keyof TimeConfiguration, value: number) => {
    setTimeConfig(prev => ({ ...prev, [key]: value }));
  };

  const getTotalTimePerMatch = () => {
    return timeConfig.warmup_minutes + 
           timeConfig.match_duration_minutes + 
           timeConfig.between_match_buffer_minutes + 
           timeConfig.court_preparation_minutes;
  };

  const renderConfigField = (key: string, value: any, type?: string) => {
    const fieldId = `config-${key}`;
    
    if (typeof value === 'boolean') {
      return (
        <div className="flex items-center justify-between">
          <Label htmlFor={fieldId} className="text-sm font-medium">
            {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
          </Label>
          <Switch
            id={fieldId}
            checked={config[key] ?? value}
            onCheckedChange={(checked) => updateConfig(key, checked)}
          />
        </div>
      );
    }

    if (typeof value === 'number') {
      return (
        <div className="space-y-2">
          <Label htmlFor={fieldId} className="text-sm font-medium">
            {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
          </Label>
          <Input
            id={fieldId}
            type="number"
            value={config[key] ?? value}
            onChange={(e) => updateConfig(key, parseInt(e.target.value) || 0)}
            min={0}
          />
        </div>
      );
    }

    if (key === 'seeding') {
      return (
        <div className="space-y-2">
          <Label htmlFor={fieldId} className="text-sm font-medium">Setzung</Label>
          <Select value={config[key] ?? value} onValueChange={(val) => updateConfig(key, val)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="random">Zufällig</SelectItem>
              <SelectItem value="rating">Nach Rating</SelectItem>
              <SelectItem value="manual">Manuell</SelectItem>
            </SelectContent>
          </Select>
        </div>
      );
    }

    if (key === 'draw_policy') {
      return (
        <div className="space-y-2">
          <Label htmlFor={fieldId} className="text-sm font-medium">Unentschieden-Regel</Label>
          <Select value={config[key] ?? value} onValueChange={(val) => updateConfig(key, val)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="count_games">Spiele zählen</SelectItem>
              <SelectItem value="tb3">Tiebreak bis 3</SelectItem>
              <SelectItem value="sudden_point">Sudden Point</SelectItem>
            </SelectContent>
          </Select>
        </div>
      );
    }

    if (key.includes('tiebreak') && Array.isArray(value)) {
      return (
        <div className="space-y-2">
          <Label className="text-sm font-medium">Tiebreaker-Reihenfolge</Label>
          <div className="flex flex-wrap gap-2">
            {value.map((rule, index) => (
              <Badge key={index} variant="secondary" className="text-xs">
                {index + 1}. {rule.replace(/_/g, ' ')}
              </Badge>
            ))}
          </div>
        </div>
      );
    }

    if (Array.isArray(value) || typeof value === 'object') {
      return (
        <div className="space-y-2">
          <Label className="text-sm font-medium">
            {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
          </Label>
          <div className="p-3 bg-muted rounded-md">
            <code className="text-xs">{JSON.stringify(value, null, 2)}</code>
          </div>
        </div>
      );
    }

    return (
      <div className="space-y-2">
        <Label htmlFor={fieldId} className="text-sm font-medium">
          {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
        </Label>
        <Input
          id={fieldId}
          value={config[key] ?? value}
          onChange={(e) => updateConfig(key, e.target.value)}
        />
      </div>
    );
  };

  const getFormatSpecificHelp = () => {
    switch (format.format_type) {
      case 'knockout':
        return (
          <div className="text-sm text-muted-foreground">
            <p>• <strong>Setzung:</strong> Wie Teilnehmer gepaart werden</p>
            <p>• <strong>Consolation Plate:</strong> Zusätzliche Runde für Verlierer der 1. Runde</p>
            <p>• <strong>Platzierungsspiele:</strong> Spiele um die Plätze 3-4, 5-8, etc.</p>
          </div>
        );
      case 'round_robin':
        return (
          <div className="text-sm text-muted-foreground">
            <p>• <strong>Gruppen:</strong> Aufteilen in mehrere Gruppen (1 = alle zusammen)</p>
            <p>• <strong>Gruppengröße:</strong> Maximale Teilnehmer pro Gruppe</p>
            <p>• <strong>Tiebreaker:</strong> Reihenfolge bei Punktgleichstand</p>
          </div>
        );
      case 'swiss':
        return (
          <div className="text-sm text-muted-foreground">
            <p>• <strong>Runden:</strong> Anzahl der Runden</p>
            <p>• <strong>Cut to KO:</strong> Nach X Runden ins K.o.-System wechseln</p>
          </div>
        );
      default:
        return null;
    }
  };

  const renderTimeConfigTab = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg">
            <Clock className="h-5 w-5" />
            Zeitplanung
          </CardTitle>
          <CardDescription>
            Konfigurieren Sie die zeitlichen Abläufe für dieses Turnierformat
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Spieldauer (Minuten)</Label>
              <Input
                type="number"
                value={timeConfig.match_duration_minutes}
                onChange={(e) => updateTimeConfig('match_duration_minutes', parseInt(e.target.value) || 0)}
                min={15}
                max={300}
              />
            </div>
            <div className="space-y-2">
              <Label>Einspielzeit (Minuten)</Label>
              <Input
                type="number"
                value={timeConfig.warmup_minutes}
                onChange={(e) => updateTimeConfig('warmup_minutes', parseInt(e.target.value) || 0)}
                min={0}
                max={30}
              />
            </div>
            <div className="space-y-2">
              <Label>Seitenwechsel (Minuten)</Label>
              <Input
                type="number"
                value={timeConfig.changeover_minutes}
                onChange={(e) => updateTimeConfig('changeover_minutes', parseInt(e.target.value) || 0)}
                min={1}
                max={10}
              />
            </div>
            <div className="space-y-2">
              <Label>Satzpause (Minuten)</Label>
              <Input
                type="number"
                value={timeConfig.set_break_minutes}
                onChange={(e) => updateTimeConfig('set_break_minutes', parseInt(e.target.value) || 0)}
                min={1}
                max={10}
              />
            </div>
            <div className="space-y-2">
              <Label>Puffer zwischen Spielen (Minuten)</Label>
              <Input
                type="number"
                value={timeConfig.between_match_buffer_minutes}
                onChange={(e) => updateTimeConfig('between_match_buffer_minutes', parseInt(e.target.value) || 0)}
                min={0}
                max={60}
              />
            </div>
            <div className="space-y-2">
              <Label>Platzvorbereitung (Minuten)</Label>
              <Input
                type="number"
                value={timeConfig.court_preparation_minutes}
                onChange={(e) => updateTimeConfig('court_preparation_minutes', parseInt(e.target.value) || 0)}
                min={0}
                max={30}
              />
            </div>
          </div>
          
          <Separator />
          
          {/* Time Planning Integration */}
          <div className="flex justify-between items-center p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div>
              <h4 className="font-medium text-blue-900">Zeitplanung aktualisieren</h4>
              <p className="text-sm text-blue-700">
                Überträgt neue Zeiteinstellungen ins Buchungssystem und aktualisiert die Spielplanung. 
                Funktioniert sowohl vor als auch nach der Spielgenerierung.
              </p>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleSyncToBookingSystem}
              disabled={syncToBookingSystemMutation.isPending}
              className="flex items-center gap-2"
            >
              <Clock className="h-4 w-4" />
              {syncToBookingSystemMutation.isPending ? 'Aktualisiere...' : 'Zeitplanung aktualisieren'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Time Preview */}
      <TimeConfigPreview 
        timeConfig={timeConfig}
        availableCourts={config.available_courts}
        participantCount={config.participants_count}
        formatType={format.format_type}
      />
    </div>
  );

  const renderCourtsLogisticsTab = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg">
            <MapPin className="h-5 w-5" />
            Plätze & Logistik
          </CardTitle>
          <CardDescription>
            Platzmanagement und logistische Einstellungen
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-center p-8 text-muted-foreground">
            <MapPin className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>Platz- und Logistik-Einstellungen</p>
            <p className="text-sm">Wird in einer zukünftigen Version implementiert</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderRulesScoringTab = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg">
            <Trophy className="h-5 w-5" />
            Regeln & Scoring
          </CardTitle>
          <CardDescription>
            Format-spezifische Spielregeln und Zählweise
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {Object.entries(format.default_config).map(([key, value]) => (
            <div key={key}>
              {renderConfigField(key, value)}
            </div>
          ))}
        </CardContent>
      </Card>
    </div>
  );

  const renderAdvancedTab = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg">
            <Sliders className="h-5 w-5" />
            Erweiterte Einstellungen
          </CardTitle>
          <CardDescription>
            Zusätzliche Konfigurationsoptionen
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-center p-8 text-muted-foreground">
            <Sliders className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>Erweiterte Optionen</p>
            <p className="text-sm">Zusätzliche Features werden hier angezeigt</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[95vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            {format.name} - Konfiguration
            {config.integrated_scheduling && (
              <Badge variant="secondary" className="ml-2">
                <Clock className="h-3 w-3 mr-1" />
                Integriert
              </Badge>
            )}
          </DialogTitle>
          <DialogDescription>
            {format.description}
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="general" className="flex items-center gap-2">
                <Info className="h-4 w-4" />
                Allgemein
              </TabsTrigger>
              <TabsTrigger value="time" className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Zeitplanung
              </TabsTrigger>
              <TabsTrigger value="courts" className="flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                Plätze
              </TabsTrigger>
              <TabsTrigger value="advanced" className="flex items-center gap-2">
                <Sliders className="h-4 w-4" />
                Erweitert
              </TabsTrigger>
            </TabsList>

            <div className="flex-1 overflow-y-auto mt-4">
              <TabsContent value="general" className="space-y-6 mt-0">
                {/* Format Help */}
                {getFormatSpecificHelp() && (
                  <div className="flex gap-3 p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <Info className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                    <div>
                      <h4 className="font-medium text-blue-900 mb-2">Konfigurationshilfe</h4>
                      {getFormatSpecificHelp()}
                    </div>
                  </div>
                )}

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-lg">
                      <Trophy className="h-5 w-5" />
                      Grundeinstellungen
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {Object.entries(format.default_config).map(([key, value]) => (
                      <div key={key}>
                        {renderConfigField(key, value)}
                      </div>
                    ))}
                  </CardContent>
                </Card>

                {/* Current Config Preview */}
                <Card>
                  <CardHeader>
                    <CardTitle>Konfigurationsvorschau</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="p-3 bg-muted rounded-md">
                      <pre className="text-xs overflow-x-auto">
                        {JSON.stringify({ ...config, time_config: timeConfig }, null, 2)}
                      </pre>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="time" className="mt-0">
                {renderTimeConfigTab()}
              </TabsContent>

              <TabsContent value="courts" className="mt-0">
                {renderCourtsLogisticsTab()}
              </TabsContent>

              <TabsContent value="advanced" className="mt-0">
                {renderAdvancedTab()}
              </TabsContent>
            </div>
          </Tabs>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Abbrechen
          </Button>
          <Button onClick={handleSave} disabled={syncToBookingSystemMutation.isPending}>
            <Save className="h-4 w-4 mr-2" />
            {syncToBookingSystemMutation.isPending ? 'Speichere...' : 'Konfiguration speichern'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
