import React, { useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { TournamentBracket } from '@/components/tournaments/TournamentBracket';

interface Match {
  id: string;
  participant1?: { id: string; user_name: string };
  participant2?: { id: string; user_name: string };
  sets?: Array<{ player1_score: number; player2_score: number }>;
  winner_id?: string;
  status: 'scheduled' | 'in_progress' | 'completed' | 'walkover';
  round_number: number;
  match_number: number;
}

export function LiveTournamentBracket() {
  const { tournamentId } = useParams<{ tournamentId: string }>();
  const queryClient = useQueryClient();

  // Setup real-time updates
  useEffect(() => {
    if (!tournamentId) return;

    const channel = supabase
      .channel('tournament-updates')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'tournament_matches',
          filter: `tournament_id=eq.${tournamentId}`
        },
        (payload) => {
          console.log('Match updated:', payload);
          // Trigger refetch
          queryClient.invalidateQueries({ queryKey: ['tournament-matches-live', tournamentId] });
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [tournamentId]);

  const { data: matches, isLoading } = useQuery({
    queryKey: ['tournament-matches-live', tournamentId],
    queryFn: async () => {
      if (!tournamentId) return [];
      
      const { data, error } = await supabase
        .from('tournament_matches')
        .select(`
          id, round_number, match_number, status, score_json, winner_id,
          participant1_id, participant2_id
        `)
        .eq('tournament_id', tournamentId)
        .order('round_number')
        .order('match_number');
      
      if (error) throw error;
      
      // Fetch participants separately
      const participantData = await supabase
        .from('tournament_participants')
        .select('id, guest_name')
        .eq('tournament_id', tournamentId);
      
      const participantMap = (participantData.data || []).reduce((acc, p) => {
        acc[p.id] = { id: p.id, user_name: p.guest_name || 'Unknown' };
        return acc;
      }, {} as Record<string, { id: string; user_name: string }>);
      
      return data?.map(match => ({
        ...match,
        sets: match.score_json as Array<{ player1_score: number; player2_score: number }> || [],
        participant1: match.participant1_id ? participantMap[match.participant1_id] : undefined,
        participant2: match.participant2_id ? participantMap[match.participant2_id] : undefined
      })) as Match[];
    },
    enabled: !!tournamentId,
    refetchInterval: 10000 // Refresh every 10 seconds as fallback
  });

  const { data: tournament } = useQuery({
    queryKey: ['tournament-name', tournamentId],
    queryFn: async () => {
      if (!tournamentId) return null;
      
      const { data, error } = await supabase
        .from('tournaments')
        .select('name')
        .eq('id', tournamentId)
        .single();
      
      if (error) throw error;
      return data;
    },
    enabled: !!tournamentId
  });

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center">
        <div className="animate-pulse text-center">
          <div className="h-8 w-64 bg-white rounded mb-4 mx-auto"></div>
          <div className="h-96 w-full max-w-4xl bg-white rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 p-4">
      <div className="max-w-7xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <TournamentBracket 
            matches={matches || []} 
            tournamentName={tournament?.name || 'Unbekanntes Turnier'}
          />
        </div>
      </div>
    </div>
  );
}