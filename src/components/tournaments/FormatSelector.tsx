import React from 'react';
import { <PERSON>, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Trophy, Users, Clock, Target, Zap, Timer } from 'lucide-react';

interface FormatDefinition {
  format_type: string;
  name: string;
  description: string;
  default_config: Record<string, any>;
  category: string;
}

interface FormatSelectorProps {
  formats: FormatDefinition[];
  selectedFormat?: string;
  onFormatSelect: (format: FormatDefinition) => void;
  onConfigureFormat?: (format: FormatDefinition) => void;
}

const getCategoryIcon = (category: string) => {
  switch (category) {
    case 'standard': return <Trophy className="h-4 w-4" />;
    case 'advanced': return <Target className="h-4 w-4" />;
    case 'team': return <Users className="h-4 w-4" />;
    case 'league': return <Clock className="h-4 w-4" />;
    case 'fun': return <Zap className="h-4 w-4" />;
    case 'short': return <Timer className="h-4 w-4" />;
    default: return <Trophy className="h-4 w-4" />;
  }
};

const getCategoryColor = (category: string) => {
  switch (category) {
    case 'standard': return 'bg-blue-100 text-blue-800 border-blue-200';
    case 'advanced': return 'bg-purple-100 text-purple-800 border-purple-200';
    case 'team': return 'bg-green-100 text-green-800 border-green-200';
    case 'league': return 'bg-orange-100 text-orange-800 border-orange-200';
    case 'fun': return 'bg-pink-100 text-pink-800 border-pink-200';
    case 'short': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    default: return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

const getCategoryName = (category: string) => {
  switch (category) {
    case 'standard': return 'Standard';
    case 'advanced': return 'Erweitert';
    case 'team': return 'Team-Events';
    case 'league': return 'Liga-Formate';
    case 'fun': return 'Spaß-Formate';
    case 'short': return 'Kurz-Formate';
    default: return 'Sonstige';
  }
};

export function FormatSelector({ formats, selectedFormat, onFormatSelect, onConfigureFormat }: FormatSelectorProps) {
  // Group formats by category
  const formatsByCategory = formats.reduce((acc, format) => {
    const category = format.category || 'standard';
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(format);
    return acc;
  }, {} as Record<string, FormatDefinition[]>);

  const categories = Object.keys(formatsByCategory).sort((a, b) => {
    const order = ['standard', 'advanced', 'team', 'league', 'fun', 'short'];
    return order.indexOf(a) - order.indexOf(b);
  });

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-2">Turnier-Format auswählen</h3>
        <p className="text-muted-foreground">
          Wählen Sie das gewünschte Format für Ihr Turnier. Jedes Format hat unterschiedliche Regeln und Konfigurationsmöglichkeiten.
        </p>
      </div>

      <Tabs defaultValue={categories[0]} className="w-full">
        <TabsList className="grid grid-cols-3 lg:grid-cols-6 w-full">
          {categories.map((category) => (
            <TabsTrigger 
              key={category} 
              value={category}
              className="flex items-center gap-2 text-xs"
            >
              {getCategoryIcon(category)}
              <span className="hidden sm:inline">{getCategoryName(category)}</span>
            </TabsTrigger>
          ))}
        </TabsList>

        {categories.map((category) => (
          <TabsContent key={category} value={category} className="space-y-4">
            <div className="flex items-center gap-2 mb-4">
              {getCategoryIcon(category)}
              <h4 className="text-base font-medium">{getCategoryName(category)}-Formate</h4>
              <Badge variant="outline" className={getCategoryColor(category)}>
                {formatsByCategory[category].length} Format{formatsByCategory[category].length !== 1 ? 'e' : ''}
              </Badge>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {formatsByCategory[category].map((format) => (
                <Card 
                  key={format.format_type}
                  className={`cursor-pointer transition-all hover:shadow-md ${
                    selectedFormat === format.format_type 
                      ? 'ring-2 ring-primary ring-offset-2' 
                      : 'hover:border-primary/50'
                  }`}
                  onClick={() => onFormatSelect(format)}
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-sm font-medium leading-tight">
                          {format.name}
                        </CardTitle>
                        <Badge 
                          variant="outline" 
                          className={`mt-2 text-xs ${getCategoryColor(category)}`}
                        >
                          {getCategoryName(category)}
                        </Badge>
                      </div>
                      {getCategoryIcon(category)}
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <CardDescription className="text-xs leading-relaxed mb-3">
                      {format.description}
                    </CardDescription>
                    
                    {/* Show key config options */}
                    <div className="space-y-2 mb-4">
                      {Object.entries(format.default_config).slice(0, 2).map(([key, value]) => (
                        <div key={key} className="flex justify-between text-xs">
                          <span className="text-muted-foreground capitalize">
                            {key.replace(/_/g, ' ')}:
                          </span>
                          <span className="font-mono">
                            {typeof value === 'object' ? 'konfigurierbar' : String(value)}
                          </span>
                        </div>
                      ))}
                    </div>

                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant={selectedFormat === format.format_type ? "default" : "outline"}
                        className="flex-1 text-xs"
                        onClick={(e) => {
                          e.stopPropagation();
                          onFormatSelect(format);
                        }}
                      >
                        {selectedFormat === format.format_type ? 'Ausgewählt' : 'Auswählen'}
                      </Button>
                      
                      {onConfigureFormat && (
                        <Button
                          size="sm"
                          variant="ghost"
                          className="text-xs"
                          onClick={(e) => {
                            e.stopPropagation();
                            onConfigureFormat(format);
                          }}
                        >
                          Config
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
}