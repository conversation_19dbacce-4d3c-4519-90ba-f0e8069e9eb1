import { Calendar, Users, Trophy, BookOpen, BarChart3, Home } from "lucide-react";
import { NavLink, useLocation } from "react-router-dom";
import { useClubUrl } from "@/contexts/TenantContext";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
} from "@/components/ui/sidebar";

const trainerItems = [
  { title: "Dashboard", url: "/trainer", icon: Home },
  { title: "<PERSON><PERSON> verwalten", url: "/trainer/courses", icon: BookOpen },
  { title: "<PERSON><PERSON><PERSON>hm<PERSON>", url: "/trainer/participants", icon: Users },
  { title: "Turniere", url: "/trainer/tournaments", icon: Trophy },
  { title: "Plätze buchen", url: "/trainer/booking", icon: Calendar },
  { title: "Berichte", url: "/trainer/reports", icon: BarChart3 },
];

const TrainerSidebar = () => {
  const location = useLocation();
  const buildClubUrl = useClubUrl();
  const currentPath = location.pathname;

  const isActive = (path: string) => currentPath === path;

  return (
    <Sidebar className="w-60">
      <SidebarContent>
        <div className="p-4">
          <h2 className="font-bold text-lg text-tennis-green">
            Trainerbereich
          </h2>
        </div>

        <SidebarGroup>
          <SidebarGroupLabel>Navigation</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {trainerItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton asChild>
                    <NavLink
                      to={buildClubUrl(item.url)}
                      end={item.url === "/trainer"}
                      className={({ isActive }) =>
                        isActive
                          ? "bg-muted text-primary font-medium"
                          : "hover:bg-muted/50"
                      }
                    >
                      <item.icon className="mr-2 h-4 w-4" />
                      <span>{item.title}</span>
                    </NavLink>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
};

export default TrainerSidebar;