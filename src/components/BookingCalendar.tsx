import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ChevronLeft, ChevronRight, Calendar } from "lucide-react";
import { useState, useEffect, useRef } from "react";
import { format } from "date-fns";
import { de } from "date-fns/locale";
import { supabase } from "@/integrations/supabase/client";
import { useClub } from "@/contexts/ClubContext";
import { useClubTimezone } from "@/hooks/useClubTimezone";
import { useToast } from "@/hooks/use-toast";
import { useBookingRuleValidation } from "@/hooks/useBookingRuleValidation";
import { isPastSlotInClubTZ, formatDateTimeInClubTZ } from "@/lib/timezone-utils";
import { ViewSelector } from "./booking/ViewSelector";
import { CompactHeader } from "./booking/CompactHeader";
import { DayView } from "./booking/DayView";
import { WeekView } from "./booking/WeekView";
import { CompactWeekView } from "./booking/CompactWeekView";
import { BookingDetailsDialog, type BookingDetails } from "@/components/booking/BookingDetailsDialog";

interface Court {
  id: string;
  number: number;
  locked: boolean;
  lock_reason: string | null;
}

interface TimeSlot {
  time: string;
  available: boolean;
  price?: number;
}

const BookingCalendar = () => {
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [selectedCourt, setSelectedCourt] = useState<string | null>(null);
  const [selectedTime, setSelectedTime] = useState<string | null>(null);
  const [showBookingDialog, setShowBookingDialog] = useState(false);
  const [courts, setCourts] = useState<Court[]>([]);
  const [courtAvailability, setCourtAvailability] = useState<any[]>([]);
  const [bookings, setBookings] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [view, setView] = useState<'day' | 'week' | 'compact'>('compact');
  
  console.log('🔍 BOOKING DEBUG - Current View State:', view);
  const viewAutoSet = useRef(false);
  const { toast } = useToast();
  const { currentClubId } = useClub();
  const { timezone } = useClubTimezone();
  const { validateAndShowErrors, isValidating } = useBookingRuleValidation();

  useEffect(() => {
    console.log('🔍 BOOKING DEBUG - useEffect triggered:', {
      currentClubId,
      selectedDate: selectedDate.toISOString(),
      hasCurrentClubId: !!currentClubId
    });
    
    if (currentClubId) {
      loadCourts();
      loadCourtAvailability();
      loadBookings();
    }
  }, [currentClubId, selectedDate]);

  // Reload availability when component regains focus or when court availability is updated
  useEffect(() => {
    const handleFocus = () => {
      loadCourtAvailability();
    };
    
    const handleCourtAvailabilityUpdate = (event: CustomEvent) => {
      console.log('Court availability updated, reloading...', event.detail);
      setCourtAvailability(event.detail || []);
    };
    
    window.addEventListener('focus', handleFocus);
    window.addEventListener('courtAvailabilityUpdated', handleCourtAvailabilityUpdate as EventListener);
    
    return () => {
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('courtAvailabilityUpdated', handleCourtAvailabilityUpdate as EventListener);
    };
  }, []);

  const loadCourts = async () => {
    if (!currentClubId) return;
    
    try {
      const { data, error } = await supabase
        .from("courts")
        .select("id,number,locked,lock_reason")
        .eq("club_id", currentClubId)
        .order("number");

      if (error) throw error;
      setCourts(data || []);
      if (!viewAutoSet.current) {
        const count = data?.length ?? 0;
        if (count > 6) {
          setView('day');
        } else {
          setView('compact');
        }
        viewAutoSet.current = true;
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load courts",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const loadBookings = async () => {
    if (!currentClubId) return;
    
    try {
      // Load bookings for current week - fix Sunday issue
      const weekStart = new Date(selectedDate);
      const dayOfWeek = weekStart.getDay();
      const daysToSubtract = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Handle Sunday (0) correctly
      weekStart.setDate(weekStart.getDate() - daysToSubtract); // Monday
      const weekEnd = new Date(weekStart);
      weekEnd.setDate(weekStart.getDate() + 6); // Sunday
      
      const { data, error } = await supabase
        .from("bookings")
        .select("*")
        .eq("club_id", currentClubId)
        .gte("booking_date", format(weekStart, "yyyy-MM-dd"))
        .lte("booking_date", format(weekEnd, "yyyy-MM-dd"));

      if (error) throw error;
      setBookings(data || []);
    } catch (error) {
      console.error("Failed to load bookings:", error);
    }
  };

  const loadCourtAvailability = async () => {
    if (!currentClubId) return;
    
    try {
      const { data, error } = await supabase
        .from("court_availability")
        .select(`
          *,
          courts!inner (
            club_id
          )
        `)
        .eq("courts.club_id", currentClubId);

      if (error) throw error;
      setCourtAvailability(data || []);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load court availability",
        variant: "destructive"
      });
    }
  };

  // Generate dynamic time slots based on actual court availability
  const generateTimeSlots = (): TimeSlot[] => {
    if (courtAvailability.length === 0) {
      // CRITICAL FIX: Generate comprehensive fallback slots during loading
      // This prevents race condition where no slots appear before court availability loads
      const slots: TimeSlot[] = [];
      // Generate 30-minute interval slots from 6:00 to 22:00 (matches typical court availability)
      for (let hour = 6; hour <= 22; hour++) {
        for (let minute = 0; minute < 60; minute += 30) {
          const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
          slots.push({
            time: timeString,
            available: true, // All slots available during loading (defensive approach)
            price: hour < 9 || hour > 19 ? 20 : hour < 12 || hour > 17 ? 25 : hour < 16 ? 30 : 35
          });
        }
      }
      console.log('⚠️ Using fallback time slots during court availability loading');
      return slots;
    }

    // For day view, we want to show all possible time slots (including past ones)
    // Generate 30-minute interval slots from 6:00 to 22:00 to include early morning and late evening slots
    const slots: TimeSlot[] = [];
    
    // Always show a full day's range to include past slots with 30-minute intervals
    for (let hour = 6; hour <= 22; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
        slots.push({
          time: timeString,
          available: true,
          price: hour < 9 || hour > 19 ? 20 : hour < 12 || hour > 17 ? 25 : hour < 16 ? 30 : 35
        });
      }
    }

    return slots;
  };

  const timeSlots = generateTimeSlots();
  
  // CRITICAL DEBUG: Check if data is loading properly
  console.log('🔍 BOOKING DEBUG - Courts:', courts.length, courts);
  console.log('🔍 BOOKING DEBUG - Court Availability:', courtAvailability.length, courtAvailability);
  console.log('🔍 BOOKING DEBUG - Time Slots:', timeSlots.length, timeSlots);
  console.log('🔍 BOOKING DEBUG - Bookings:', bookings.length, bookings);
  console.log('🔍 BOOKING DEBUG - Loading:', loading);
  console.log('🔍 BOOKING DEBUG - Current Club ID:', currentClubId);
  console.log('🔍 BOOKING DEBUG - Selected Date:', selectedDate);
  console.log('🔍 BOOKING DEBUG - Current View:', view);
  console.log('🔍 BOOKING DEBUG - Timezone:', timezone);

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const isPastSlot = (date: Date, time: string) => {
    const result = isPastSlotInClubTZ(date, time, timezone);
    console.log(`🕐 isPastSlot Debug - Date: ${date.toDateString()}, Time: ${time}, Timezone: ${timezone}, Result: ${result}`);
    return result;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available':
        return 'bg-tennis-green text-white';
      case 'booked':
        return 'bg-destructive text-white';
      case 'booked-past':
        return 'bg-muted text-muted-foreground opacity-70';
      case 'maintenance':
        return 'bg-muted text-muted-foreground';
      case 'past':
        return 'bg-muted/50 text-muted-foreground line-through';
      default:
        return 'bg-muted text-muted-foreground';
    }
  };

  const isTimeSlotAvailable = (courtId: string, time: string, date: Date) => {
    // CRITICAL: Use club timezone to get the correct day of week
    const clubDate = new Date(date.toLocaleString('en-US', { timeZone: timezone }));
    const dayOfWeek = clubDate.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const availability = courtAvailability.find(a => a.court_id === courtId);
    
    // CRITICAL FIX: If no availability data, assume available (defensive programming)
    // This prevents race condition where availability loads after courts
    if (!availability) {
      console.log(`⚠️ No availability data for court ${courtId}, assuming available`);
      return true; // Changed from false to true - prevents blocking all slots during loading
    }
    
    // Check if this day is included in the court's available days
    if (!availability.days_of_week.includes(dayOfWeek)) return false;
    
    // Check if time is within availability window
    const timeInMinutes = parseInt(time.split(':')[0]) * 60 + parseInt(time.split(':')[1]);
    const startInMinutes = parseInt(availability.start_time.split(':')[0]) * 60 + parseInt(availability.start_time.split(':')[1]);
    const endInMinutes = parseInt(availability.end_time.split(':')[0]) * 60 + parseInt(availability.end_time.split(':')[1]);
    
    return timeInMinutes >= startInMinutes && timeInMinutes <= endInMinutes;
  };

  const getSlotStatus = (courtId: string, time: string, date?: Date) => {
    const checkDate = date || selectedDate;
    
    // Check if time slot is within court availability
    if (!isTimeSlotAvailable(courtId, time, checkDate)) {
      return 'unavailable';
    }
    
    // Check for existing bookings - look for bookings that overlap with this time slot
    const dateString = format(checkDate, "yyyy-MM-dd");
    
    // Convert time slot to minutes for easier comparison
    const [slotHour, slotMinute] = time.split(':').map(Number);
    const slotStartMinutes = slotHour * 60 + slotMinute;
    const slotEndMinutes = slotStartMinutes + 30; // 30-minute slots
    
    const existingBooking = bookings.find(booking => {
      if (booking.court_id !== courtId || booking.booking_date !== dateString) {
        return false;
      }
      
      // Convert booking times to minutes
      const [bookingStartHour, bookingStartMinute] = booking.start_time.split(':').map(Number);
      const [bookingEndHour, bookingEndMinute] = booking.end_time.split(':').map(Number);
      const bookingStartMinutes = bookingStartHour * 60 + bookingStartMinute;
      const bookingEndMinutes = bookingEndHour * 60 + bookingEndMinute;
      
      // Check if this time slot overlaps with the booking
      return slotStartMinutes < bookingEndMinutes && slotEndMinutes > bookingStartMinutes;
    });
    
    if (existingBooking) {
      // If there's a booking and it's in the past, return 'booked-past'
      return isPastSlot(checkDate, time) ? 'booked-past' : 'booked';
    }
    
    // If no booking and slot is in the past, return 'past'
    const pastResult = isPastSlot(checkDate, time);
    console.log(`🎯 getSlotStatus Debug - Court: ${courtId}, Time: ${time}, Date: ${checkDate.toDateString()}, isPast: ${pastResult}`);
    if (pastResult) {
      return 'past';
    }
    
    return 'available';
  };

  const getBookedPlayerNames = (courtId: string, time: string, date?: Date) => {
    const checkDate = date || selectedDate;
    const dateString = format(checkDate, "yyyy-MM-dd");
    
    // Convert time slot to minutes for easier comparison
    const [slotHour, slotMinute] = time.split(':').map(Number);
    const slotStartMinutes = slotHour * 60 + slotMinute;
    const slotEndMinutes = slotStartMinutes + 30; // 30-minute slots
    
    const booking = bookings.find(booking => {
      if (booking.court_id !== courtId || booking.booking_date !== dateString) {
        return false;
      }
      
      // Convert booking times to minutes
      const [bookingStartHour, bookingStartMinute] = booking.start_time.split(':').map(Number);
      const [bookingEndHour, bookingEndMinute] = booking.end_time.split(':').map(Number);
      const bookingStartMinutes = bookingStartHour * 60 + bookingStartMinute;
      const bookingEndMinutes = bookingEndHour * 60 + bookingEndMinute;
      
      // Check if this time slot overlaps with the booking
      return slotStartMinutes < bookingEndMinutes && slotEndMinutes > bookingStartMinutes;
    });
    
    if (!booking) return [];
    
    const names = [booking.player_name];
    if (booking.partner_name) {
      names.push(booking.partner_name);
    }
    
    return names;
  };

  // Get specific player name for a slot based on whether it's first or second 30-min slot of 1-hour booking
  const getPlayerForSpecificSlot = (courtId: string, time: string, date?: Date) => {
    const booking = getBooking(courtId, time, date);
    console.log('🎾 getPlayerForSpecificSlot Debug:', { courtId, time, booking: booking ? { player_name: booking.player_name, partner_name: booking.partner_name, start_time: booking.start_time, end_time: booking.end_time } : null });
    
    if (!booking || !booking.partner_name) {
      // Single player or no booking - return first player or empty
      return booking?.player_name ? [booking.player_name] : [];
    }

    // For 1-hour bookings with 2 players, determine which slot this is
    const [slotHour, slotMinute] = time.split(':').map(Number);
    const [bookingStartHour, bookingStartMinute] = booking.start_time.split(':').map(Number);
    
    const isFirstSlot = slotHour === bookingStartHour && slotMinute === bookingStartMinute;
    console.log('🎾 First slot check:', { slotHour, slotMinute, bookingStartHour, bookingStartMinute, isFirstSlot });
    
    if (isFirstSlot) {
      return [booking.player_name];
    } else {
      return [booking.partner_name];
    }
  };

  // Check if current slot is part of a 1-hour booking with 2 players
  const isPartOfHourBooking = (courtId: string, time: string, date?: Date) => {
    const booking = getBooking(courtId, time, date);
    console.log('🎾 isPartOfHourBooking Debug:', { courtId, time, hasBooking: !!booking, hasPartner: !!booking?.partner_name });
    
    if (!booking || !booking.partner_name) return false;
    
    // Check if it's a 1-hour booking (60 minutes duration)
    const [startHour, startMinute] = booking.start_time.split(':').map(Number);
    const [endHour, endMinute] = booking.end_time.split(':').map(Number);
    const durationMinutes = (endHour * 60 + endMinute) - (startHour * 60 + startMinute);
    
    console.log('🎾 Duration check:', { start_time: booking.start_time, end_time: booking.end_time, durationMinutes });
    return durationMinutes === 60;
  };

  // Check if VS badge should be shown after this slot
  const shouldShowVsBadge = (courtId: string, time: string, date?: Date) => {
    const isHourBooking = isPartOfHourBooking(courtId, time, date);
    console.log('🎾 shouldShowVsBadge Debug:', { courtId, time, isHourBooking });
    
    if (!isHourBooking) return false;
    
    const booking = getBooking(courtId, time, date);
    if (!booking) return false;
    
    // Only show VS badge after the first 30-min slot of a 1-hour booking
    const [slotHour, slotMinute] = time.split(':').map(Number);
    const [bookingStartHour, bookingStartMinute] = booking.start_time.split(':').map(Number);
    
    const shouldShow = slotHour === bookingStartHour && slotMinute === bookingStartMinute;
    console.log('🎾 VS badge decision:', { slotHour, slotMinute, bookingStartHour, bookingStartMinute, shouldShow });
    return shouldShow;
  };

  const getBooking = (courtId: string, time: string, date?: Date) => {
    const checkDate = date || selectedDate;
    const dateString = format(checkDate, "yyyy-MM-dd");
    
    // Convert time slot to minutes for easier comparison
    const [slotHour, slotMinute] = time.split(':').map(Number);
    const slotStartMinutes = slotHour * 60 + slotMinute;
    const slotEndMinutes = slotStartMinutes + 30; // 30-minute slots
    
    const booking = bookings.find(booking => {
      if (booking.court_id !== courtId || booking.booking_date !== dateString) {
        return false;
      }
      
      // Convert booking times to minutes
      const [bookingStartHour, bookingStartMinute] = booking.start_time.split(':').map(Number);
      const [bookingEndHour, bookingEndMinute] = booking.end_time.split(':').map(Number);
      const bookingStartMinutes = bookingStartHour * 60 + bookingStartMinute;
      const bookingEndMinutes = bookingEndHour * 60 + bookingEndMinute;
      
      // Check if this time slot overlaps with the booking
      return slotStartMinutes < bookingEndMinutes && slotEndMinutes > bookingStartMinutes;
    });
    
    return booking || null;
  };

  const handleBookingUpdated = () => {
    loadBookings(); // Reload bookings data
  };

  const handleWaitlistAdded = () => {
    loadBookings(); // Reload bookings to refresh the view
  };

  const navigateDate = (direction: 'prev' | 'next') => {
    const newDate = new Date(selectedDate);
    const daysToMove = view === 'week' ? 7 : 1;
    if (direction === 'prev') {
      newDate.setDate(newDate.getDate() - daysToMove);
    } else {
      newDate.setDate(newDate.getDate() + daysToMove);
    }
    setSelectedDate(newDate);
  };

  const handleSlotSelect = (courtId: string, time: string, date?: Date) => {
    setSelectedCourt(courtId);
    setSelectedTime(time);
    if (date) {
      setSelectedDate(date);
    }
    setShowBookingDialog(true);
  };

  const handleConfirmBooking = async (details: BookingDetails) => {
    try {
      // Check if the slot is in the past using club timezone
      if (isPastSlotInClubTZ(details.date, details.startTime, timezone)) {
        toast({ 
          title: "Buchung nicht möglich", 
          description: "Buchungen in der Vergangenheit sind nicht erlaubt.", 
          variant: "destructive" 
        });
        return;
      }

      const { data: auth } = await supabase.auth.getUser();
      const userId = auth?.user?.id;

      if (!userId) {
        toast({ title: "Anmeldung erforderlich", description: "Bitte melde dich an, um eine Buchung zu erstellen.", variant: "destructive" });
        return;
      }

      let playerName = "Unbekannter Spieler";
      if (userId) {
        const { data: profile } = await supabase
          .from("profiles")
          .select("first_name,last_name,email")
          .eq("id", userId)
          .maybeSingle();
        if (profile?.first_name || profile?.last_name) {
          playerName = `${profile.first_name ?? ""} ${profile.last_name ?? ""}`.trim();
        } else if (auth.user.email) {
          playerName = auth.user.email;
        }
      }

      if (!selectedCourt) {
        toast({ title: "Fehler", description: "Kein Platz ausgewählt", variant: "destructive" });
        return;
      }

      // Partnerliste als String
      const partnerStr = details.partners.map((p) => p.label).join(", ");

      // Eigenen Account ermitteln (Standard: Selbstbuchung)
      const { data: account } = await supabase
        .from("accounts")
        .select("id")
        .eq("user_id", userId)
        .limit(1)
        .maybeSingle();

      // Start/End als Timestamp (timestamptz) ableiten
      const makeDateTime = (date: Date, time: string) => {
        const [h, m] = time.split(":").map(Number);
        const dt = new Date(date);
        dt.setHours(h || 0, m || 0, 0, 0);
        return dt.toISOString();
      };
      const startAt = makeDateTime(details.date, details.startTime);
      const endAt = makeDateTime(details.date, details.endTime);

      const payload: any = {
        club_id: currentClubId,
        court_id: selectedCourt,
        booking_date: format(details.date, "yyyy-MM-dd"),
        start_time: `${details.startTime}:00`,
        end_time: `${details.endTime}:00`,
        player_name: playerName,
        partner_name: partnerStr || null,
        created_by: userId,
        start_at: startAt,
        end_at: endAt,
      };

      // Buchungsregeln validieren
      if (currentClubId) {
        const isValid = await validateAndShowErrors({
          club_id: currentClubId,
          booking_date: format(details.date, "yyyy-MM-dd"),
          start_time: details.startTime,
          end_time: details.endTime,
          player_name: playerName,
          created_by: userId,
          court_id: selectedCourt
        });

        if (!isValid) {
          return;
        }
      }

      const actingId = (details as any)?.actingAccountId ?? account?.id;
      const forId = (details as any)?.bookedForAccountId ?? actingId;

      if (actingId) {
        payload.acting_account_id = actingId;
        payload.booked_for_account_id = forId || actingId;
      }

      const { error } = await supabase.from("bookings").insert([payload]);
      if (error) throw error;

      toast({ title: "Buchung erstellt", description: `Platz gebucht am ${format(details.date, "PP", { locale: de })} um ${details.startTime}` });
      setShowBookingDialog(false);
      // Reload bookings to show the new booking immediately
      loadBookings();
    } catch (e: any) {
      console.error(e);
      toast({ title: "Fehler bei der Buchung", description: e?.message || "Unbekannter Fehler", variant: "destructive" });
    }
  };

  return (
    <div className="space-y-4 p-4">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-foreground mb-1">Buche Deinen Platz</h2>
        <p className="text-sm text-muted-foreground">Wähle ein Datum, einen Platz und eine Uhrzeit für deine Reservierung</p>
      </div>

      {/* Compact Header with View Selector and Date Navigation */}
      <CompactHeader 
        view={view}
        onViewChange={setView}
        selectedDate={selectedDate}
        onDateChange={setSelectedDate}
        onNavigate={navigateDate}
      />

      {/* Courts View */}
      {view === 'day' ? (
        <>
          {console.log('🎾 Rendering DayView with functions:', {
            getPlayerForSpecificSlot: !!getPlayerForSpecificSlot,
            isPartOfHourBooking: !!isPartOfHourBooking,
            shouldShowVsBadge: !!shouldShowVsBadge
          })}
          <DayView
            selectedDate={selectedDate}
            courts={courts}
            timeSlots={timeSlots}
            onSlotSelect={handleSlotSelect}
            getSlotStatus={getSlotStatus}
            getBookedPlayerNames={getBookedPlayerNames}
            getPlayerForSpecificSlot={getPlayerForSpecificSlot}
            isPartOfHourBooking={isPartOfHourBooking}
            shouldShowVsBadge={shouldShowVsBadge}
            clubId={currentClubId}
            onWaitlistAdded={handleWaitlistAdded}
          />
        </>
      ) : view === 'week' ? (
        <>
          <div style={{background: 'red', color: 'white', padding: '10px', fontSize: '20px'}}>
            🚨 WEEK VIEW IS RENDERING! 🚨
          </div>
          {console.log('🎾 Rendering WeekView with functions:', {
            getPlayerForSpecificSlot: !!getPlayerForSpecificSlot,
            isPartOfHourBooking: !!isPartOfHourBooking,
            shouldShowVsBadge: !!shouldShowVsBadge
          })}
          <WeekView
            selectedDate={selectedDate}
            courts={courts}
            timeSlots={timeSlots}
            onSlotSelect={handleSlotSelect}
            getSlotStatus={getSlotStatus}
            getBookedPlayerNames={getBookedPlayerNames}
            getPlayerForSpecificSlot={getPlayerForSpecificSlot}
            isPartOfHourBooking={isPartOfHourBooking}
            shouldShowVsBadge={shouldShowVsBadge}
          />
        </>
      ) : (
        <CompactWeekView
          selectedDate={selectedDate}
          courts={courts}
          timeSlots={timeSlots}
          onSlotSelect={handleSlotSelect}
          getSlotStatus={getSlotStatus}
          getBookedPlayerNames={getBookedPlayerNames}
          getBooking={getBooking}
          onBookingUpdated={handleBookingUpdated}
          clubId={currentClubId}
          onWaitlistAdded={handleWaitlistAdded}
        />
      )}

      {/* Booking Summary */}
      {(selectedCourt && selectedTime) && (
        <Card>
          <CardContent className="p-6">
            <div className="p-4 bg-gradient-court rounded-lg border">
              <h4 className="font-semibold text-tennis-green mb-2">Ausgewählte Buchung</h4>
              <p className="text-sm text-muted-foreground mb-4">
                Platz {courts.find(c => c.id === selectedCourt)?.number} • {formatDate(selectedDate)} um {selectedTime} • €30
              </p>
              <Button variant="tennis" className="w-full">
                Buchung Abschließen
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Buchungs-Dialog */}
      <BookingDetailsDialog
        open={showBookingDialog}
        onOpenChange={setShowBookingDialog}
        courtNumber={courts.find((c) => c.id === selectedCourt)?.number}
        defaultDate={selectedDate}
        defaultStartTime={selectedTime || "08:00"}
        defaultEndTime={selectedTime ? (() => { const [h,m] = selectedTime.split(":").map(Number); const end = new Date(); end.setHours(h); end.setMinutes((m||0)+60); return `${String(end.getHours()).padStart(2,'0')}:${String(end.getMinutes()).padStart(2,'0')}` })() : "09:00"}
        onConfirm={handleConfirmBooking}
      />
    </div>
  );
};

export default BookingCalendar;
