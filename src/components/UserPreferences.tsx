import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "sonner";
import { useAdminRole } from "@/hooks/useAdminRole";

export function UserPreferences() {
  const { user } = useAuth();
  const { isClubAdmin } = useAdminRole();
  const [defaultLandingPage, setDefaultLandingPage] = useState<string>('member');
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (user) {
      loadPreferences();
    }
  }, [user]);

  const loadPreferences = async () => {
    if (!user) return;
    
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('user_preferences')
        .select('default_landing_page')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows found
        console.error('Error loading preferences:', error);
        return;
      }

      if (data?.default_landing_page) {
        setDefaultLandingPage(data.default_landing_page);
      }
    } catch (error) {
      console.error('Error loading preferences:', error);
    } finally {
      setLoading(false);
    }
  };

  const savePreferences = async () => {
    if (!user) return;

    setSaving(true);
    try {
      const { error } = await supabase
        .from('user_preferences')
        .upsert({
          user_id: user.id,
          default_landing_page: defaultLandingPage
        });

      if (error) {
        console.error('Error saving preferences:', error);
        toast.error('Fehler beim Speichern der Einstellungen');
        return;
      }

      toast.success('Einstellungen erfolgreich gespeichert');
    } catch (error) {
      console.error('Error saving preferences:', error);
      toast.error('Fehler beim Speichern der Einstellungen');
    } finally {
      setSaving(false);
    }
  };

  if (!isClubAdmin) {
    return null; // Only show for club admins
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Benutzereinstellungen</CardTitle>
        <CardDescription>
          Konfiguriere deine Standard-Startseite nach dem Login
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">Standard-Startseite</label>
          <Select 
            value={defaultLandingPage} 
            onValueChange={setDefaultLandingPage}
            disabled={loading}
          >
            <SelectTrigger>
              <SelectValue placeholder="Startseite auswählen" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="member">Mitgliederbereich</SelectItem>
              <SelectItem value="admin">Admin-Dashboard</SelectItem>
            </SelectContent>
          </Select>
          <p className="text-xs text-muted-foreground">
            Als Club-Admin kannst du wählen, ob du nach dem Login im Mitgliederbereich oder Admin-Dashboard landest
          </p>
        </div>
        
        <Button 
          onClick={savePreferences} 
          disabled={saving || loading}
          className="w-full"
        >
          {saving ? 'Wird gespeichert...' : 'Einstellungen speichern'}
        </Button>
      </CardContent>
    </Card>
  );
}