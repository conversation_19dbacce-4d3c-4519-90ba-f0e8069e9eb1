import React from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { CustomField } from '@/hooks/useCustomFields';

interface CustomFieldsSectionProps {
  customFields: CustomField[];
  customFieldValues: Record<string, string>;
  onChange: (values: Record<string, string>) => void;
  errors?: Record<string, string>;
}

export const CustomFieldsSection: React.FC<CustomFieldsSectionProps> = ({
  customFields,
  customFieldValues,
  onChange,
  errors = {}
}) => {
  const handleFieldChange = (fieldKey: string, value: string) => {
    onChange({
      ...customFieldValues,
      [fieldKey]: value
    });
  };

  const renderField = (field: CustomField) => {
    const value = customFieldValues[field.field_key] || '';
    const hasError = !!errors[field.field_key];

    switch (field.field_type) {
      case 'text':
      case 'tennis_lk':
        return (
          <Input
            value={value}
            onChange={(e) => handleFieldChange(field.field_key, e.target.value)}
            placeholder={field.field_type === 'tennis_lk' ? 'z.B. LK1, LK14,2, Keine' : ''}
            className={hasError ? 'border-destructive' : ''}
          />
        );
      
      case 'number':
        return (
          <Input
            type="number"
            value={value}
            onChange={(e) => handleFieldChange(field.field_key, e.target.value)}
            className={hasError ? 'border-destructive' : ''}
          />
        );
      
      case 'select':
        const options = Array.isArray(field.field_options) ? field.field_options : [];
        return (
          <Select value={value} onValueChange={(val) => handleFieldChange(field.field_key, val)}>
            <SelectTrigger className={hasError ? 'border-destructive' : ''}>
              <SelectValue placeholder="Bitte wählen" />
            </SelectTrigger>
            <SelectContent>
              {options.map((option: string) => (
                <SelectItem key={option} value={option}>
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
      
      default:
        return (
          <Input
            value={value}
            onChange={(e) => handleFieldChange(field.field_key, e.target.value)}
            className={hasError ? 'border-destructive' : ''}
          />
        );
    }
  };

  if (customFields.length === 0) {
    return null;
  }

  return (
    <div className="space-y-6 p-6 border rounded-lg bg-card">
      <h3 className="text-lg font-semibold">Zusätzliche Informationen</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {customFields.map((field) => (
          <div key={field.id} className="space-y-2">
            <Label className="text-sm font-medium">
              {field.field_name}
              {field.is_required && <span className="text-destructive ml-1">*</span>}
            </Label>
            {renderField(field)}
            {errors[field.field_key] && (
              <p className="text-sm text-destructive">{errors[field.field_key]}</p>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};