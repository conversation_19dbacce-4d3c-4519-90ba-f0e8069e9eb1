import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

const FAQ = () => {
  const faqs = [
    {
      question: "Wie kann ich Regeln festlegen?",
      answer: "Im Klartext; der KI-Assistent erzeugt aktive Buchungsregeln."
    },
    {
      question: "Kann ich die Warteliste automatisieren?",
      answer: "Ja, Auto-Nachrücken mit Benachrichtigungen."
    },
    {
      question: "Wie funktioniert die Arbeitsdienst-Erfassung?",
      answer: "Tätigkeit übernehmen/abschließen; Stunden zum Jahresziel."
    },
    {
      question: "Können Gäste das System nutzen?",
      answer: "Ja, eigene Rolle inkl. Gebühren & Sichtbarkeiten."
    },
    {
      question: "Wie sicher sind meine Daten?",
      answer: "DSGVO-konform, Hosting in der EU."
    },
    {
      question: "Wie kann ich kündigen?",
      answer: "<PERSON><PERSON><PERSON> kündbar, 14 Tage kostenlos testen."
    },
    {
      question: "Was passiert bei Buchungskonflikten?",
      answer: "Die KI erkennt Überschneidungen automatisch und warnt vor Problemen."
    },
    {
      question: "Ist Support verfügbar?",
      answer: "Ja, deutscher Support via E-Mail und Chat während der Geschäftszeiten."
    }
  ];

  return (
    <section className="py-24 bg-bg">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-marketing-h2 font-bold mb-4 text-fg-strong">
            Häufige Fragen
          </h2>
          <p className="text-marketing-body text-fg-muted">
            Alles was Sie über Courtwaive wissen müssen
          </p>
        </div>

        <Accordion type="single" collapsible className="w-full space-y-4">
          {faqs.map((faq, index) => (
            <AccordionItem 
              key={index} 
              value={`item-${index}`}
              className="border border-gray-200 rounded-lg px-6 data-[state=open]:bg-bg-alt"
            >
              <AccordionTrigger className="text-left hover:no-underline py-6">
                <span className="font-semibold text-fg-strong">{faq.question}</span>
              </AccordionTrigger>
              <AccordionContent className="pb-6 text-marketing-body text-fg-muted">
                {faq.answer}
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>
    </section>
  );
};

export default FAQ;