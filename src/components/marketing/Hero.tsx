import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, Zap, ChevronRight, Play, UserPlus } from "lucide-react";
import { useNavigate } from "react-router-dom";
import DashboardShowcase from "./DashboardShowcase";

const Hero = () => {
  const navigate = useNavigate();

  const handleTrialClick = () => {
    navigate("/club-register");
  };

  const handleDemoClick = () => {
    navigate("/c/demo/auth");
  };

  return (
    <section className="relative min-h-screen flex flex-col items-center justify-center overflow-hidden" style={{ background: 'var(--gradient-hero)' }}>
      {/* Enhanced Background Pattern with AI Particles */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute top-10 left-10 w-40 h-40 rounded-full blur-3xl opacity-20 animate-pulse"
             style={{ background: 'linear-gradient(135deg, hsl(var(--accent-purple)), hsl(var(--accent-blue)))' }}></div>
        <div className="absolute bottom-20 right-20 w-48 h-48 rounded-full blur-3xl opacity-15 animate-pulse delay-300"
             style={{ background: 'linear-gradient(135deg, hsl(var(--accent-blue)), hsl(var(--accent-purple)))' }}></div>
        <div className="absolute top-1/3 right-1/4 w-32 h-32 rounded-full blur-2xl animate-pulse delay-700"
             style={{ background: 'hsl(var(--accent-blue) / 0.4)' }}></div>
        <div className="absolute top-1/2 left-1/4 w-24 h-24 rounded-full blur-xl animate-pulse delay-1000"
             style={{ background: 'hsl(var(--accent-purple) / 0.4)' }}></div>
        {/* AI Particles */}
        <div className="absolute top-20 right-1/3 w-3 h-3 rounded-full animate-pulse"
             style={{ background: 'hsl(var(--accent-purple))' }}></div>
        <div className="absolute bottom-32 left-1/3 w-2 h-2 rounded-full animate-pulse delay-500"
             style={{ background: 'hsl(var(--accent-blue))' }}></div>
        <div className="absolute top-40 left-20 w-1.5 h-1.5 rounded-full animate-pulse delay-200"
             style={{ background: 'hsl(var(--accent-purple))' }}></div>
      </div>
      
      {/* Content Container */}
      <div className="relative z-10 w-full px-4 py-20 bg-white">
        {/* Header Section - Central H1 */}
        <div className="text-center mb-20 space-y-8">
          {/* KI-First Badge */}
          <div className="flex justify-center">
            <Badge 
              variant="secondary" 
              className="px-4 py-2.5 text-marketing-micro font-semibold text-white border-0 flex items-center gap-2"
              style={{ background: 'linear-gradient(135deg, hsl(var(--accent-purple)), hsl(var(--accent-blue)))' }}
            >
              <Bot className="h-4 w-4" />
              Die erste KI-native Vereinsverwaltung für Tennis
            </Badge>
          </div>

          {/* Main Headline - Central & Impact */}
           <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold leading-tight max-w-5xl mx-auto text-slate-800">
             Tennisverein Management.{" "}
             <span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
               Mehr Power, weniger Kosten.
             </span>
           </h1>
          
          <p className="text-xl md:text-2xl text-slate-600 max-w-4xl mx-auto leading-relaxed">
            KI-native Vereinsplattform, die Verwaltung, Buchung, Turniere und Kommunikation auf ein neues Niveau hebt – bezahlbar für jeden Verein.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              size="default" 
              className="hover:opacity-90 text-white px-6 py-3 text-base font-semibold border-0 transition-all duration-200 flex items-center gap-2"
              style={{ background: 'linear-gradient(135deg, hsl(var(--accent-purple)), hsl(var(--accent-blue)))' }}
              onClick={handleTrialClick}
            >
              <UserPlus className="h-4 w-4" />
              Verein kostenfrei registrieren
              <ChevronRight className="ml-2 h-4 w-4" />
            </Button>
            <Button 
              variant="outline" 
              size="default"
              className="px-8 py-4 text-base font-semibold border-2 border-slate-300 bg-white hover:bg-slate-50 transition-all duration-300 hover:scale-105 hover:shadow-lg rounded-lg backdrop-blur-sm text-slate-800 hover:text-slate-900"
              style={{ 
                background: 'hsl(0 0% 100% / 0.4)',
                borderColor: 'hsl(0 0% 100% / 0.3)',
                backdropFilter: 'blur(10px)'
              }}
              onClick={handleDemoClick}
            >
              <Play className="mr-2 h-4 w-4" />
              Demo Club auswählen
            </Button>
          </div>
        </div>

        {/* Dashboard Showcase - Central Bottom */}
        <div className="animate-fade-in animation-delay-500">
          <DashboardShowcase />
        </div>
      </div>
    </section>
  );
};

export default Hero;
