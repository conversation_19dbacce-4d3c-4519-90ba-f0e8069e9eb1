import { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Users, Calendar, Sparkles, Wrench, Maximize2, Minimize2, Clock, TrendingUp, Trophy } from "lucide-react";

const DashboardShowcase = () => {
  const [stats, setStats] = useState({
    members: 0,
    bookings: 0,
    aiActivity: 0,
    workHours: 0
  });
  const [isVisible, setIsVisible] = useState(false);

  // Animate numbers on mount and trigger entrance animation
  useEffect(() => {
    const visibilityTimer = setTimeout(() => {
      setIsVisible(true);
    }, 800);

    const statsTimer = setTimeout(() => {
      setStats({
        members: 247,
        bookings: 89,
        aiActivity: 156,
        workHours: 124
      });
    }, 1300);

    return () => {
      clearTimeout(visibilityTimer);
      clearTimeout(statsTimer);
    };
  }, []);

  const StatCard = ({ 
    title, 
    value, 
    icon: Icon, 
    hasAI = false, 
    className = "" 
  }: {
    title: string;
    value: number;
    icon: any;
    hasAI?: boolean;
    className?: string;
  }) => (
    <Card className={hasAI ? 
        `bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 relative overflow-hidden hover:scale-105 transition-all duration-300 ${className}` : 
        `relative overflow-hidden backdrop-blur-lg hover:scale-105 transition-all duration-300 ${className}`
      }
      style={!hasAI ? { 
        background: 'hsl(0 0% 100% / 0.4)',
        border: '1px solid hsl(0 0% 100% / 0.2)',
        boxShadow: '0 4px 16px hsl(0 0% 0% / 0.1)'
      } : undefined}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-slate-600">
          {title}
        </CardTitle>
        <Icon className="h-4 w-4" 
              style={{ color: hasAI ? 'hsl(var(--accent-purple))' : 'hsl(215 15% 45%)' }} />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold"
             style={{ color: hasAI ? 'hsl(var(--accent-purple))' : 'hsl(215 25% 25%)' }}>
          {value.toLocaleString()}
        </div>
      </CardContent>
    </Card>
  );

  const BookingCalendar = () => {
    const timeSlots = ['08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00'];
    const courts = ['Platz 1', 'Platz 2', 'Platz 3'];
    
    const bookings = [
      { court: 0, time: 1, player: 'M. Schmidt', type: 'einzeln' },
      { court: 0, time: 3, player: 'A. Weber', type: 'doppel' },
      { court: 1, time: 0, player: 'T. Müller', type: 'training' },
      { court: 1, time: 2, player: 'S. Klein', type: 'einzeln' },
      { court: 1, time: 5, player: 'Vereinsmeisterschaft', type: 'turnier' },
      { court: 2, time: 1, player: 'J. Fischer', type: 'doppel' },
      { court: 2, time: 4, player: 'KI-Vorschlag', type: 'ai-suggested' },
    ];

    const getBookingStyle = (type: string) => {
      switch (type) {
        case 'ai-suggested':
          return { 
            background: 'linear-gradient(135deg, hsl(var(--accent-purple) / 0.2), hsl(var(--accent-blue) / 0.2))',
            border: '1px solid hsl(var(--accent-purple))',
            color: 'hsl(var(--accent-purple))'
          };
        case 'turnier':
          return { 
            background: 'hsl(var(--accent-blue) / 0.2)',
            border: '1px solid hsl(var(--accent-blue))',
            color: 'hsl(var(--accent-blue))'
          };
        case 'training':
          return { 
            background: 'linear-gradient(135deg, hsl(220 90% 60% / 0.15), hsl(200 85% 55% / 0.15))',
            border: '1px solid hsl(220 90% 60%)',
            color: 'hsl(220 90% 60%)'
          };
        default:
          return { 
            background: 'hsl(210 20% 98%)',
            border: '1px solid hsl(210 15% 85%)',
            color: 'hsl(215 25% 35%)'
          };
      }
    };

    return (
      <div className="backdrop-blur-lg rounded-xl p-6 border"
           style={{ 
             background: 'hsl(0 0% 100% / 0.4)',
             borderColor: 'hsl(0 0% 100% / 0.3)',
             boxShadow: '0 8px 32px hsl(0 0% 0% / 0.1)'
           }}>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold flex items-center gap-2 text-slate-800">
            <Calendar className="w-5 h-5" />
            Buchungskalender - Heute
          </h3>
          <div className="flex items-center gap-2 text-sm text-purple-600">
            <Clock className="w-4 h-4" />
            Live-Sync
          </div>
        </div>
        
        <div style={{ display: 'grid', gridTemplateColumns: '70px 1fr 1fr 1fr', gap: '8px' }}>
          {/* Header */}
          <div className="text-xs font-medium text-slate-600 p-1">Zeit</div>
          {courts.map((court) => (
            <div key={court} className="text-xs font-medium text-center p-2 rounded border text-slate-700"
                 style={{ 
                   background: 'hsl(0 0% 100% / 0.6)',
                   borderColor: 'hsl(0 0% 100% / 0.4)'
                 }}>
              {court}
            </div>
          ))}
          
          {/* Time Slots and Bookings */}
          {timeSlots.map((time, timeIndex) => (
            <>
              {/* Time Column */}
              <div key={`time-${timeIndex}`} className="text-xs font-medium text-slate-600 p-1 flex items-center">
                {time}
              </div>
              
              {/* Court Columns */}
              {courts.map((_, courtIndex) => {
                const booking = bookings.find(b => b.court === courtIndex && b.time === timeIndex);
                return (
                  <div key={`${courtIndex}-${timeIndex}`} className="h-12">
                    {booking ? (
                      <div className="h-full p-2 rounded text-xs font-medium flex flex-col justify-center text-center relative overflow-hidden"
                           style={getBookingStyle(booking.type)}>
                        {booking.type === 'ai-suggested' && (
                          <Sparkles className="w-3 h-3 absolute top-1 right-1 animate-pulse" />
                        )}
                        <div className="truncate text-[10px] font-semibold">{booking.player}</div>
                        <div className="text-[9px] opacity-70 capitalize">{booking.type}</div>
                      </div>
                    ) : (
                      <div className="h-full rounded border border-dashed opacity-30"
                           style={{ borderColor: 'hsl(210 15% 70%)' }}></div>
                    )}
                  </div>
                );
              })}
            </>
          ))}
        </div>
        
        {/* Calendar Legend */}
        <div className="flex flex-wrap gap-4 mt-4 text-xs">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded" style={{ background: 'linear-gradient(135deg, hsl(var(--accent-purple) / 0.3), hsl(var(--accent-blue) / 0.3))', border: '1px solid hsl(var(--accent-purple))' }}></div>
            <span className="text-slate-600">KI-Vorschlag</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded" style={{ background: 'hsl(var(--accent-blue) / 0.3)', border: '1px solid hsl(var(--accent-blue))' }}></div>
            <span className="text-slate-600">Turnier</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded" style={{ background: 'hsl(220 90% 60% / 0.2)', border: '1px solid hsl(220 90% 60%)' }}></div>
            <span className="text-slate-600">Training</span>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="w-full max-w-6xl mx-auto px-4">
      {/* Dashboard Screen Frame */}
      <div className={`
        relative 
        transform transition-all duration-1000 ease-out
        ${isVisible ? 'translate-y-0 opacity-100 scale-100' : 'translate-y-20 opacity-0 scale-95'}
      `}>
        {/* Screen Frame with Glassmorphism */}
        <div className="relative backdrop-blur-xl rounded-2xl p-8 overflow-hidden border"
             style={{ 
               background: 'hsl(0 0% 100% / 0.25)',
               borderColor: 'hsl(0 0% 100% / 0.2)',
               boxShadow: '0 20px 60px hsl(0 0% 0% / 0.1)'
             }}>
          {/* Screen Header - Window Controls */}
          <div className="flex items-center justify-between mb-6 pb-4 border-b"
               style={{ borderColor: 'hsl(0 0% 100% / 0.3)' }}>
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full" style={{ background: 'hsl(var(--accent-purple))' }}></div>
                <div className="w-3 h-3 rounded-full" style={{ background: 'hsl(var(--accent-blue))' }}></div>
              </div>
              <span className="text-sm font-medium ml-4 text-slate-800">
                Club Dashboard
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Minimize2 className="w-4 h-4 text-slate-500" />
            </div>
          </div>

          {/* Dashboard Content */}
          <div className="space-y-6">
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Special Mitglieder Card */}
              <Card className="relative overflow-hidden backdrop-blur-lg hover:scale-105 transition-all duration-300 animate-fade-in animation-delay-100"
                    style={{ 
                      background: 'hsl(0 0% 100% / 0.4)',
                      border: '1px solid hsl(0 0% 100% / 0.2)',
                      boxShadow: '0 4px 16px hsl(0 0% 0% / 0.1)'
                    }}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-slate-600">
                    Mitglieder
                  </CardTitle>
                  <Users className="h-4 w-4" style={{ color: 'hsl(215 15% 45%)' }} />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold" style={{ color: 'hsl(215 25% 25%)' }}>
                    488
                  </div>
                  {/* New Members Indicator */}
                  <div className="mt-3">
                    <div className="flex items-center justify-between text-xs text-slate-600 mb-1">
                      <span>Neue Mitglieder</span>
                      <span className="text-green-600 font-medium">+ 12</span>
                    </div>
                    <div className="h-2"></div>
                  </div>
                </CardContent>
              </Card>
              {/* Special Buchungen Card with Trend Indicator */}
              <Card className="bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 relative overflow-hidden hover:scale-105 transition-all duration-300 animate-fade-in animation-delay-200">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-slate-600">
                    Buchungen
                  </CardTitle>
                  <Calendar className="h-4 w-4" style={{ color: 'hsl(var(--accent-purple))' }} />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold" style={{ color: 'hsl(var(--accent-purple))' }}>
                    {stats.bookings.toLocaleString()}
                  </div>
                  {/* Weekly Trend - Same height structure as Progress Bar */}
                  <div className="mt-3">
                    <div className="flex items-center justify-between text-xs text-slate-600 mb-1">
                      <span>Wöchentlich</span>
                      <div className="flex items-center gap-1">
                        <TrendingUp className="w-3 h-3 text-green-600" />
                        <span className="text-green-600 font-medium">+8%</span>
                      </div>
                    </div>
                    <div className="h-2"></div>
                  </div>
                </CardContent>
              </Card>
              {/* Turniere Card */}
              <Card className="bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 relative overflow-hidden hover:scale-105 transition-all duration-300 animate-fade-in animation-delay-300">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-slate-600">
                    Turniere
                  </CardTitle>
                  <Trophy className="h-4 w-4" style={{ color: 'hsl(var(--accent-purple))' }} />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold" style={{ color: 'hsl(var(--accent-purple))' }}>
                    3
                  </div>
                  {/* Active Tournaments Indicator */}
                  <div className="mt-3">
                    <div className="flex items-center justify-between text-xs text-slate-600 mb-1">
                      <span>Aktiv</span>
                      <span className="text-blue-600 font-medium">2 laufend</span>
                    </div>
                    <div className="h-2"></div>
                  </div>
                </CardContent>
              </Card>
              {/* Special Arbeitsstunden Card with Progress Bar */}
              <Card className="bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 relative overflow-hidden hover:scale-105 transition-all duration-300 animate-fade-in animation-delay-400">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-slate-600">
                    Arbeitsstunden
                  </CardTitle>
                  <Wrench className="h-4 w-4" style={{ color: 'hsl(var(--accent-purple))' }} />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold" style={{ color: 'hsl(var(--accent-purple))' }}>
                    124 h
                  </div>
                  {/* Progress Bar */}
                  <div className="mt-3">
                    <div className="flex items-center justify-between text-xs text-slate-600 mb-1">
                      <span>Fortschritt</span>
                      <span>34% erledigt</span>
                    </div>
                    <div className="w-full bg-slate-200 rounded-full h-2">
                      <div 
                        className="h-2 rounded-full transition-all duration-300"
                        style={{ 
                          width: '34%',
                          background: 'linear-gradient(90deg, hsl(var(--accent-purple)), hsl(var(--accent-blue)))'
                        }}
                      ></div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Booking Calendar */}
            <div className="animate-fade-in animation-delay-500">
              <BookingCalendar />
            </div>

            {/* AI Activity Section */}
            <div className="backdrop-blur-lg rounded-xl p-6 animate-fade-in animation-delay-600 border"
                 style={{ 
                   background: 'hsl(0 0% 100% / 0.4)',
                   borderColor: 'hsl(0 0% 100% / 0.3)'
                 }}>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-slate-800">
                  Live KI-Dashboard
                </h3>
                <div className="flex items-center gap-2 text-sm text-purple-600">
                  <div className="w-2 h-2 rounded-full animate-pulse bg-purple-500"></div>
                  Live
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div className="flex items-center gap-2 p-3 rounded-lg border"
                     style={{ 
                       background: 'hsl(0 0% 100% / 0.6)',
                       borderColor: 'hsl(0 0% 100% / 0.4)'
                     }}>
                  <Sparkles className="w-4 h-4 text-purple-600" />
                  <span className="text-slate-600">
                    KI generiert Buchungsregeln...
                  </span>
                </div>
                <div className="flex items-center gap-2 p-3 rounded-lg border"
                     style={{ 
                       background: 'hsl(0 0% 100% / 0.6)',
                       borderColor: 'hsl(0 0% 100% / 0.4)'
                     }}>
                  <Sparkles className="w-4 h-4 text-blue-600" />
                  <span className="text-slate-600">
                    Smart Warteliste aktiv
                  </span>
                </div>
                <div className="flex items-center gap-2 p-3 rounded-lg border"
                     style={{ 
                       background: 'hsl(0 0% 100% / 0.6)',
                       borderColor: 'hsl(0 0% 100% / 0.4)'
                     }}>
                  <Sparkles className="w-4 h-4 text-indigo-600" />
                  <span className="text-slate-600">
                    Auto-Gebühren berechnet
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Screen Glow Effect */}
          <div className="absolute inset-0 -z-10 rounded-2xl" 
               style={{ 
                 background: 'linear-gradient(135deg, hsl(var(--accent-blue) / 0.04), hsl(var(--accent-blue) / 0.06))',
                 filter: 'blur(15px)',
                 transform: 'scale(1.02)'
               }}></div>
        </div>
      </div>
    </div>
  );
};

export default DashboardShowcase;