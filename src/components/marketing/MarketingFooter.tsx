import { Shield, MapPin, Star, Mail, Phone } from "lucide-react";

const MarketingFooter = () => {
  const handleLinkClick = (link: string) => {
    // TODO: Handle navigation to legal pages
    console.log(`Navigate to: ${link}`);
  };

  return (
    <footer className="bg-fg-strong text-white py-16">
      <div className="max-w-7xl mx-auto px-4">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12">
          {/* Brand Column */}
          <div className="space-y-4">
            <h3 className="font-bold text-xl">Courtwaive</h3>
            <p className="text-gray-300 text-sm">
              Tennis-Management, das mitdenkt. Weniger Verwaltung, mehr Tennis.
            </p>
            <div className="flex space-x-4 text-sm text-gray-300">
              <div className="flex items-center gap-1">
                <Shield className="h-3 w-3" />
                <span>DSGVO</span>
              </div>
              <div className="flex items-center gap-1">
                <MapPin className="h-3 w-3" />
                <span>EU</span>
              </div>
              <div className="flex items-center gap-1">
                <Star className="h-3 w-3" />
                <span>DE</span>
              </div>
            </div>
          </div>

          {/* Produkt */}
          <div className="space-y-4">
            <h4 className="font-semibold">Produkt</h4>
            <ul className="space-y-2 text-gray-300 text-sm">
              <li>
                <button onClick={() => handleLinkClick('features')} className="hover:text-white transition-colors">
                  Features
                </button>
              </li>
              <li>
                <button onClick={() => handleLinkClick('pricing')} className="hover:text-white transition-colors">
                  Preise
                </button>
              </li>
              <li>
                <button onClick={() => handleLinkClick('demo')} className="hover:text-white transition-colors">
                  Demo
                </button>
              </li>
              <li>
                <button onClick={() => handleLinkClick('trial')} className="hover:text-white transition-colors">
                  Kostenlos testen
                </button>
              </li>
            </ul>
          </div>

          {/* Rechtliches */}
          <div className="space-y-4">
            <h4 className="font-semibold">Rechtliches</h4>
            <ul className="space-y-2 text-gray-300 text-sm">
              <li>
                <a href="/impressum" className="hover:text-white transition-colors">
                  Impressum
                </a>
              </li>
              <li>
                <button onClick={() => handleLinkClick('datenschutz')} className="hover:text-white transition-colors">
                  Datenschutz
                </button>
              </li>
              <li>
                <button onClick={() => handleLinkClick('agb')} className="hover:text-white transition-colors">
                  AGB
                </button>
              </li>
              <li>
                <button onClick={() => handleLinkClick('cookies')} className="hover:text-white transition-colors">
                  Cookie-Einstellungen
                </button>
              </li>
            </ul>
          </div>

          {/* Kontakt */}
          <div className="space-y-4">
            <h4 className="font-semibold">Kontakt & Support</h4>
            <div className="space-y-3 text-gray-300 text-sm">
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4" />
                <span>+49 (0) 123 456 789</span>
              </div>
              <div className="pt-2">
                <p className="text-xs text-gray-400">
                  Support: Mo-Fr 9:00-17:00 Uhr
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-700 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-sm text-gray-400">
              © 2024 Courtwaive. Alle Rechte vorbehalten.
            </div>
            <div className="text-sm text-gray-400">
              Sicheres Hosting in Deutschland 🇩🇪
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default MarketingFooter;