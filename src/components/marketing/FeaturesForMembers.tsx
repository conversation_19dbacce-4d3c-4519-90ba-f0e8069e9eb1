import { CheckCir<PERSON>, Clock, Users, Award } from "lucide-react";

const FeaturesForMembers = () => {
  const features = [
    {
      icon: CheckCircle,
      title: "Smartes Platz-Booking",
      description: "Schnell vom Wunschslot zur Bestätigung.",
    },
    {
      icon: Clock,
      title: "Intelligente Warteliste",
      description: "Auto-Nachrücken, transparente Reihenfolge.",
    },
    {
      icon: Users,
      title: "Match-Pool",
      description: "<PERSON><PERSON>, me<PERSON><PERSON> – wer zu<PERSON>t zusagt, spielt.",
    },
    {
      icon: Award,
      title: "Arbeitsdienst",
      description: "Übernehmen, abschließen, automatisch gutschreiben.",
    },
  ];

  return (
    <section className="py-24 lg:py-32 bg-bg-alt">
      <div className="max-w-7xl mx-auto px-4">
        <div className="grid lg:grid-cols-2 gap-16 lg:gap-24 items-center">
          {/* Visual first on this section */}
          <div className="relative order-2 lg:order-1">
            <div className="bg-white rounded-xl shadow-card border border-bg-subtle p-6">
              <div className="space-y-4">
                {/* Mock mobile interface */}
                <div className="bg-gradient-purple/5 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-semibold text-fg-strong text-sm">Platz buchen</h4>
                    <div className="w-2 h-2 bg-accent-purple rounded-full"></div>
                  </div>
                  <div className="space-y-2">
                    <div className="bg-white p-2 rounded border border-bg-subtle text-xs">
                      <span className="text-fg-muted">Heute, 18:00-20:00</span>
                      <div className="text-accent-purple font-medium">Platz 3 verfügbar</div>
                    </div>
                    <div className="bg-accent-purple/10 p-2 rounded text-xs">
                      <span className="text-accent-purple">Position 2 auf Warteliste</span>
                    </div>
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <div className="flex-1 bg-brand/5 p-3 rounded-lg text-center">
                    <CheckCircle className="h-4 w-4 text-brand mx-auto mb-1" />
                    <span className="text-xs text-fg-strong">Buchen</span>
                  </div>
                  <div className="flex-1 bg-accent-purple/5 p-3 rounded-lg text-center">
                    <Clock className="h-4 w-4 text-accent-purple mx-auto mb-1" />
                    <span className="text-xs text-fg-strong">Warteliste</span>
                  </div>
                </div>
              </div>
            </div>
            {/* Decorative gradient */}
            <div className="absolute -bottom-6 -left-6 w-32 h-32 bg-gradient-brand rounded-full opacity-10 blur-xl"></div>
          </div>

          {/* Content */}
          <div className="order-1 lg:order-2">
            <div className="mb-6">
              <span className="text-marketing-micro font-semibold text-accent-purple uppercase tracking-wider">
                Für Mitglieder
              </span>
            </div>
            <h2 className="text-marketing-h2 lg:text-4xl font-bold mb-6 text-fg-strong leading-tight">
              Schneller buchen & spielen
            </h2>
            <p className="text-marketing-body text-fg-muted mb-8 leading-relaxed">
              Intuitive Buchung, intelligente Wartelisten und faire Match-Verteilung – alles in wenigen Klicks.
            </p>
            
            <div className="space-y-6">
              {features.map((feature, index) => {
                const IconComponent = feature.icon;
                return (
                  <div key={index} className="flex gap-4">
                    <div className="flex-shrink-0 w-10 h-10 bg-brand/10 rounded-lg flex items-center justify-center">
                      <IconComponent className="h-5 w-5 text-brand" />
                    </div>
                    <div>
                      <h3 className="text-marketing-body-sm font-semibold mb-2 text-fg-strong">
                        {feature.title}
                      </h3>
                      <p className="text-marketing-body-sm text-fg-muted leading-relaxed">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeaturesForMembers;