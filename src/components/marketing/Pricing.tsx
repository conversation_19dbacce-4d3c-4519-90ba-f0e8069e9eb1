import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { CheckCircle, MessageSquare } from "lucide-react";

const Pricing = () => {
  const features = [
    "📅 Intelligentes Buchungs-System mit KI-basierter Platzverwaltung",
    "⏰ Wartelisten-Management mit automatischen Benachrichtigungen", 
    "👥 Umfassende Mitgliederverwaltung & Rollensystem",
    "⚙️ Arbeitsdienst-Verwaltung mit KI-Assistent für Tätigkeiten",
    "💶 Gebühren- & Beitragsmanagement mit flexiblen Kategorien",
    "🏆 Team- & Turnierverwaltung für alle Mannschaften",
    "📊 Detaillierte Reports & Dashboard-Übersichten",
    "🔐 DSGVO-konform, EU-Hosting, höchste Sicherheitsstandards"
  ];

  const handleRegisterClick = () => {
    window.location.href = '/club-register';
  };

  const handleContactClick = () => {
    // TODO: Navigate to contact
    console.log("Contact clicked");
  };

  return (
    <section className="relative py-24 overflow-hidden" style={{ background: 'var(--gradient-hero)' }}>
      
      {/* Content Container with white background like Hero */}
      <div className="relative z-10 w-full px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-marketing-h2 font-bold mb-4 text-slate-800">
              Ein Preis für <span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">Alles</span>
            </h2>
            <p className="text-marketing-body text-slate-600">
              KI-gestütztes Tennis-Management, das Vereinen und Mitgliedern mehr Freude bereitet
            </p>
          </div>

        <div className="max-w-5xl mx-auto">
          {/* Gradient border wrapper */}
          <div 
            className="rounded-2xl p-0.5"
            style={{ 
              background: 'linear-gradient(135deg, hsl(var(--accent-purple)), hsl(var(--accent-blue)))'
            }}
          >
            <div 
              className="rounded-2xl relative overflow-hidden bg-gradient-to-r from-purple-50 to-blue-50"
              style={{ 
                boxShadow: 'var(--shadow-glass)'
              }}
            >
            {/* Hero Pricing Section */}
            <div className="text-center py-12 px-8">
              <h3 className="text-3xl mb-3 font-bold text-fg-strong">
                Courtw<span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">ai</span>ve
              </h3>
              <div className="mb-6">
                <span className="text-6xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">14,95 €</span>
                <span className="text-xl text-fg-muted ml-2">/Monat</span>
              </div>
              <p className="text-xl text-fg-muted mb-8 max-w-2xl mx-auto">
                Perfekt für Tennisvereine jeder Größe
              </p>

              {/* CTAs Side by Side */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center max-w-lg mx-auto mb-8">
                <Button 
                  size="lg" 
                  className="w-full sm:w-auto bg-white text-slate-800 hover:scale-105 px-8 py-3 font-semibold border border-white/20 transition-all duration-300"
                  style={{
                    boxShadow: '0 10px 30px -10px rgba(147, 51, 234, 0.3), 0 0 20px rgba(59, 130, 246, 0.2)'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.background = 'linear-gradient(135deg, hsl(var(--accent-purple)), hsl(var(--accent-blue)))';
                    e.currentTarget.style.color = 'white';
                    e.currentTarget.style.boxShadow = '0 20px 40px -10px rgba(147, 51, 234, 0.5), 0 0 30px rgba(59, 130, 246, 0.4)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.background = 'white';
                    e.currentTarget.style.color = 'rgb(30, 41, 59)';
                    e.currentTarget.style.boxShadow = '0 10px 30px -10px rgba(147, 51, 234, 0.3), 0 0 20px rgba(59, 130, 246, 0.2)';
                  }}
                  onClick={handleRegisterClick}
                >
                  Jetzt registrieren
                </Button>
                
                <Button 
                  variant="outline" 
                  size="lg" 
                  className="w-full sm:w-auto px-8 backdrop-blur-sm transition-all duration-300 hover:bg-gradient-to-r hover:from-purple-600 hover:to-blue-600 hover:bg-clip-text hover:text-transparent"
                  style={{ 
                    background: 'var(--glass-bg-weak)',
                    borderColor: 'var(--glass-border)'
                  }}
                  onClick={handleContactClick}
                >
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Mit uns sprechen
                </Button>
              </div>
            </div>

            {/* Features Grid */}
            <div 
              className="px-8 pb-12 border-t"
              style={{ borderColor: 'var(--glass-border)' }}
            >
              <div className="grid md:grid-cols-2 gap-6 max-w-3xl mx-auto pt-8">
                {features.map((feature, index) => (
                  <div 
                    key={index} 
                    className="flex items-center gap-4 p-4 rounded-xl bg-white border border-gray-100 shadow-sm"
                  >
                    <div 
                      className="p-2 rounded-full"
                      style={{ background: 'var(--brand-light)' }}
                    >
                      <CheckCircle className="h-5 w-5" style={{ color: 'hsl(var(--accent-purple))' }} />
                    </div>
                    <span className="text-sm font-medium text-fg-strong">{feature}</span>
                  </div>
                ))}
              </div>

              {/* Fine Print */}
              <p className="text-marketing-micro text-fg-muted text-center pt-8">
                zzgl. USt, falls zutreffend.
              </p>
            </div>
            </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Pricing;