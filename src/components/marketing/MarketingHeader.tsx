
import { Button } from "@/components/ui/button";
import { Menu } from "lucide-react";
import { useState } from "react";

const MarketingHeader = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const scrollToSection = (sectionId: string) => {
    document.getElementById(sectionId)?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleRegisterClick = () => {
    window.location.href = '/club-register';
  };

  const handleDemoClick = () => {
    window.location.href = '/c/demo/auth';
  };

  return (
    <header className="sticky top-0 z-50 w-full bg-white/80 backdrop-blur-md border-b border-bg-subtle">
      <div className="max-w-7xl mx-auto px-4 flex h-16 items-center">
        {/* Logo */}
        <div className="mr-8">
          <h1 className="text-xl font-bold text-fg-strong">
            Courtw<span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">ai</span>ve
          </h1>
        </div>

        {/* Navigation - Desktop */}
        <nav className="hidden md:flex items-center space-x-8 text-marketing-body-sm font-medium flex-1">
          <button onClick={() => scrollToSection('features')} className="text-fg-muted hover:text-fg-strong transition-colors">
            Features
          </button>
          <button onClick={() => scrollToSection('ai-features')} className="text-fg-muted hover:text-fg-strong transition-colors">
            KI
          </button>
          <button onClick={() => scrollToSection('pricing')} className="text-fg-muted hover:text-fg-strong transition-colors">
            Preise
          </button>
          <button onClick={() => scrollToSection('faq')} className="text-fg-muted hover:text-fg-strong transition-colors">
            FAQ
          </button>
        </nav>

        {/* CTAs */}
        <div className="flex items-center space-x-3">
          <Button 
            onClick={handleRegisterClick}
            className="bg-white text-slate-800 hover:scale-105 px-6 py-2.5 text-marketing-body-sm font-semibold shadow-button border border-slate-200 transition-all duration-300"
            onMouseEnter={(e) => {
              e.currentTarget.style.background = 'linear-gradient(135deg, hsl(var(--accent-purple)), hsl(var(--accent-blue)))';
              e.currentTarget.style.color = 'white';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = 'white';
              e.currentTarget.style.color = 'rgb(30, 41, 59)';
            }}
          >
            Verein registrieren
          </Button>
        </div>

        {/* Mobile Menu Button */}
        <Button
          variant="ghost"
          size="icon"
          className="md:hidden ml-2"
          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
        >
          <Menu className="h-5 w-5" />
        </Button>
      </div>

      {/* Mobile Menu */}
      {mobileMenuOpen && (
        <div className="md:hidden border-t border-bg-subtle bg-white/95 backdrop-blur-md">
          <nav className="flex flex-col space-y-4 p-4">
            <button onClick={() => { scrollToSection('features'); setMobileMenuOpen(false); }} className="text-left text-fg-muted hover:text-fg-strong transition-colors">
              Features
            </button>
            <button onClick={() => { scrollToSection('ai-features'); setMobileMenuOpen(false); }} className="text-left text-fg-muted hover:text-fg-strong transition-colors">
              KI
            </button>
            <button onClick={() => { scrollToSection('pricing'); setMobileMenuOpen(false); }} className="text-left text-fg-muted hover:text-fg-strong transition-colors">
              Preise
            </button>
            <button onClick={() => { scrollToSection('faq'); setMobileMenuOpen(false); }} className="text-left text-fg-muted hover:text-fg-strong transition-colors">
              FAQ
            </button>
          </nav>
        </div>
      )}
    </header>
  );
};

export default MarketingHeader;
