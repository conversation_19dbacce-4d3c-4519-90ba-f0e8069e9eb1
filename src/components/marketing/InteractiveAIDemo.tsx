import { useState, useEffect } from "react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, Zap, ArrowRight, Check } from "lucide-react";

const demoExamples = [
  {
    input: "Mitglieder dürfen maximal 2 Stunden am Tag buchen",
    output: "✓ Regel erstellt: Max. 2h/Tag pro Mitglied",
    category: "Buchungsregeln"
  },
  {
    input: "Jedes Mitglied muss 4 Stunden Arbeitsdienst leisten",
    output: "✓ Arbeitsdienst: 4h/Jahr pro Mitglied",
    category: "Arbeitsdienste"
  },
  {
    input: "Schreibe eine Nachricht über das neue Turnier",
    output: "✓ Nachricht generiert: Turnier-Ankündigung",
    category: "Kommunikation"
  }
];

const InteractiveAIDemo = () => {
  const [currentExample, setCurrentExample] = useState(0);
  const [typedText, setTypedText] = useState("");
  const [isTyping, setIsTyping] = useState(true);
  const [showOutput, setShowOutput] = useState(false);

  useEffect(() => {
    const example = demoExamples[currentExample];
    let index = 0;
    setTypedText("");
    setShowOutput(false);
    setIsTyping(true);

    const typeInterval = setInterval(() => {
      if (index < example.input.length) {
        setTypedText(example.input.slice(0, index + 1));
        index++;
      } else {
        setIsTyping(false);
        setShowOutput(true);
        clearInterval(typeInterval);
        
        // Switch to next example after delay
        setTimeout(() => {
          setCurrentExample((prev) => (prev + 1) % demoExamples.length);
        }, 3000);
      }
    }, 50);

    return () => clearInterval(typeInterval);
  }, [currentExample]);

  return (
    <div 
      className="relative p-6 rounded-2xl border backdrop-blur-md"
      style={{ 
        background: 'var(--glass-bg-strong)',
        borderColor: 'var(--glass-border)',
        boxShadow: 'var(--shadow-glass)'
      }}
    >
      {/* AI Particles Animation */}
      <div className="absolute inset-0 overflow-hidden rounded-2xl">
        <div className="absolute top-4 right-4 w-2 h-2 bg-brand rounded-full animate-pulse"></div>
        <div className="absolute bottom-6 left-6 w-1.5 h-1.5 bg-accent-blue rounded-full animate-pulse delay-300"></div>
        <div className="absolute top-1/2 right-1/3 w-1 h-1 bg-gradient-purple rounded-full animate-pulse delay-700"></div>
      </div>

      {/* Header */}
      <div className="flex items-center gap-3 mb-6">
        <div 
          className="p-2 rounded-lg"
          style={{ background: 'var(--gradient-brand)' }}
        >
          <Bot className="h-5 w-5 text-white" />
        </div>
        <div>
          <h3 className="font-semibold text-fg-strong">KI-Assistant</h3>
          <p className="text-sm text-fg-muted">Natürliche Sprache → Automatische Regeln</p>
        </div>
        <Badge 
          className="ml-auto text-xs px-2 py-1 bg-brand/10 text-brand border-brand/20"
        >
          Live Demo
        </Badge>
      </div>

      {/* Input Field with Typing Animation */}
      <div 
        className="mb-4 p-4 rounded-lg border"
        style={{ 
          background: 'var(--bg-primary)',
          borderColor: 'var(--border-subtle)'
        }}
      >
        <div className="flex items-center gap-2 mb-2">
          <span className="text-sm font-medium text-fg-muted">Eingabe:</span>
          <Badge variant="outline" className="text-xs">
            {demoExamples[currentExample].category}
          </Badge>
        </div>
        <div className="text-fg-strong">
          {typedText}
          {isTyping && <span className="animate-pulse">|</span>}
        </div>
      </div>

      {/* Output with Animation */}
      {showOutput && (
        <div 
          className="p-4 rounded-lg border animate-fade-in"
          style={{ 
            background: 'var(--bg-success-subtle)',
            borderColor: 'var(--border-success)'
          }}
        >
          <div className="flex items-center gap-2 mb-2">
            <Check className="h-4 w-4 text-success" />
            <span className="text-sm font-medium text-success">KI-Ergebnis:</span>
            <Zap className="h-3 w-3 text-success" />
          </div>
          <div className="text-success font-medium">
            {demoExamples[currentExample].output}
          </div>
        </div>
      )}

      {/* Quick Examples */}
      <div className="mt-6 pt-4 border-t" style={{ borderColor: 'var(--border-subtle)' }}>
        <p className="text-xs text-fg-muted mb-3">Weitere KI-Funktionen:</p>
        <div className="flex flex-wrap gap-2">
          {demoExamples.map((example, index) => (
            <Button
              key={index}
              variant="ghost"
              size="sm"
              className={`text-xs px-3 py-1 h-auto ${
                index === currentExample ? 'bg-brand/10 text-brand' : 'text-fg-muted hover:text-fg-strong'
              }`}
              onClick={() => setCurrentExample(index)}
            >
              {example.category}
            </Button>
          ))}
        </div>
      </div>

      {/* CTA */}
      <Button 
        className="w-full mt-4 bg-gradient-purple hover:opacity-90 text-white border-0"
        size="sm"
      >
        KI jetzt testen
        <ArrowRight className="ml-2 h-4 w-4" />
      </Button>
    </div>
  );
};

export default InteractiveAIDemo;