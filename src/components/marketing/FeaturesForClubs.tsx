import * as React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger, Ta<PERSON>Content } from "@/components/ui/tabs";
import { Users, Brain, ClipboardList, ArrowRight, Calendar, CheckCircle, UserCheck, Shield, Car } from "lucide-react";

const FeaturesForClubs = () => {
  const [activeTab, setActiveTab] = React.useState("admin");
  const roleFeatures = {
    admin: [
      {
        icon: Users,
        title: "Mitgliederverwaltung",
        description: "Rollen, Status, Beiträge: sauber organisiert und nachvollziehbar.",
      },
      {
        icon: Brain,
        title: "Buchungsregeln (KI)",
        description: "Klartext eingeben, Logik erhalten – aktivieren mit einem Klick.",
      },
      {
        icon: ClipboardList,
        title: "Arbeitsdienste & Berichte",
        description: "Stunden erfassen, Top-Mitglieder sehen, Berichte exportieren.",
      },
    ],
    members: [
      {
        icon: Calendar,
        title: "Einfache Buchungen",
        description: "Plätze reservieren, Wartelisten verwalten, Termine planen.",
      },
      {
        icon: Users,
        title: "Community Features",
        description: "Mit anderen Mitgliedern vernetzen und gemeinsam spielen.",
      },
      {
        icon: CheckCircle,
        title: "Eigene Statistiken",
        description: "Spielzeit verfolgen, Fortschritte sehen, Erfolge feiern.",
      },
    ],
    guests: [
      {
        icon: UserCheck,
        title: "Gastzugang",
        description: "Schneller Zugang zu verfügbaren Plätzen ohne Registrierung.",
      },
      {
        icon: Calendar,
        title: "Tagesbuchungen",
        description: "Flexible Buchungen für gelegentliche Besuche.",
      },
      {
        icon: ClipboardList,
        title: "Vereinsinfos",
        description: "Alle wichtigen Informationen und Kontaktdaten auf einen Blick.",
      },
    ],
    trainers: [
      {
        icon: Shield,
        title: "Kursmanagement",
        description: "Trainingseinheiten planen, Teilnehmer verwalten, Fortschritte dokumentieren.",
      },
      {
        icon: Users,
        title: "Teilnehmerverwaltung",
        description: "Anwesenheit erfassen, Gruppen organisieren, Kommunikation vereinfachen.",
      },
      {
        icon: ClipboardList,
        title: "Berichte & Analysen",
        description: "Trainingsstatistiken, Teilnehmerfortschritte und Erfolgsmessung.",
      },
    ],
  };

  const handleScrollToScreens = () => {
    document.getElementById('screens-gallery')?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <section className="py-24 lg:py-32 relative" style={{ background: 'var(--gradient-section)' }}>
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-20 right-10 w-28 h-28 bg-accent-purple/20 rounded-full blur-2xl"></div>
        <div className="absolute bottom-32 left-20 w-36 h-36 bg-brand/15 rounded-full blur-3xl"></div>
      </div>
      
      <div className="relative max-w-7xl mx-auto px-4">
        <div className="grid lg:grid-cols-2 gap-16 lg:gap-24 items-center">
          {/* Left Content */}
          <div>
            <div className="mb-6">
              <span className="text-marketing-micro font-semibold text-accent-purple uppercase tracking-wider">
                Für Vereine
              </span>
            </div>
            <h2 className="text-marketing-h2 lg:text-4xl font-bold mb-6 text-fg-strong leading-tight">
              Verwaltung vereinfachen,<br />Zeit sparen
            </h2>
            <p className="text-marketing-body text-fg-muted mb-8 leading-relaxed">
              Alle wichtigen Vereinsfunktionen in einer übersichtlichen Plattform. Von der Mitgliederverwaltung bis zur automatischen Gebührenabrechnung.
            </p>

            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-4 mb-6">
                <TabsTrigger value="admin">Admin</TabsTrigger>
                <TabsTrigger value="members">Mitglieder</TabsTrigger>
                <TabsTrigger value="guests">Gäste</TabsTrigger>
                <TabsTrigger value="trainers">Trainer</TabsTrigger>
              </TabsList>
              
              {Object.entries(roleFeatures).map(([role, features]) => (
                <TabsContent key={role} value={role} className="space-y-6">
                  {features.map((feature, index) => {
                    const IconComponent = feature.icon;
                    return (
                      <div key={index} className="flex gap-4">
                        <div className="flex-shrink-0 w-10 h-10 bg-brand/10 rounded-lg flex items-center justify-center">
                          <IconComponent className="h-5 w-5 text-brand" />
                        </div>
                        <div>
                          <h3 className="text-marketing-body-sm font-semibold mb-2 text-fg-strong">
                            {feature.title}
                          </h3>
                          <p className="text-marketing-body-sm text-fg-muted leading-relaxed">
                            {feature.description}
                          </p>
                        </div>
                      </div>
                    );
                  })}
                </TabsContent>
              ))}
            </Tabs>

            <Button 
              variant="ghost" 
              className="mt-8 p-0 text-accent-purple hover:text-accent-purple/80 font-semibold"
              onClick={handleScrollToScreens}
            >
              Alle Features entdecken <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>

          {/* Right Content - Enhanced Visual with Glassmorphism */}
          <div className="relative">
            <div 
              className="rounded-xl border p-6 backdrop-blur-md"
              style={{ 
                background: 'var(--glass-bg-strong)',
                borderColor: 'var(--glass-border)',
                boxShadow: 'var(--shadow-glass)'
              }}
            >
              <div className="space-y-4">
                {/* Mock interface elements */}
                <div className="flex items-center justify-between pb-3 border-b mb-4" style={{ borderColor: 'var(--glass-border)' }}>
                  <h4 className="font-semibold text-fg-strong">
                    {activeTab === 'admin' && 'Admin Dashboard'}
                    {activeTab === 'members' && 'Mitglieder Bereich'}
                    {activeTab === 'guests' && 'Gast Zugang'}
                    {activeTab === 'trainers' && 'Trainer Portal'}
                  </h4>
                  <div className="flex gap-2">
                    <div className="w-3 h-3 bg-brand/30 rounded-full"></div>
                    <div className="w-3 h-3 bg-accent-purple/30 rounded-full"></div>
                    <div className="w-3 h-3 bg-fg-light/30 rounded-full"></div>
                  </div>
                </div>
                
                <div className="space-y-3">
                  {activeTab === 'admin' && (
                    <>
                      <div 
                        className="flex items-center gap-3 p-3 rounded-lg backdrop-blur-sm border"
                        style={{ 
                          background: 'var(--glass-bg)',
                          borderColor: 'var(--glass-border)'
                        }}
                      >
                        <Users className="h-4 w-4 text-brand" />
                        <span className="text-marketing-body-sm">147 aktive Mitglieder</span>
                      </div>
                      <div 
                        className="flex items-center gap-3 p-3 rounded-lg backdrop-blur-sm border"
                        style={{ 
                          background: 'var(--glass-bg)',
                          borderColor: 'var(--glass-border)'
                        }}
                      >
                        <Brain className="h-4 w-4 text-accent-purple" />
                        <span className="text-marketing-body-sm">KI-Regeln aktiv</span>
                      </div>
                      <div 
                        className="flex items-center gap-3 p-3 rounded-lg backdrop-blur-sm border"
                        style={{ 
                          background: 'var(--glass-bg)',
                          borderColor: 'var(--glass-border)'
                        }}
                      >
                        <ClipboardList className="h-4 w-4 text-brand" />
                        <span className="text-marketing-body-sm">8 Arbeitsdienste heute</span>
                      </div>
                    </>
                  )}
                  
                  {activeTab === 'members' && (
                    <>
                      <div 
                        className="flex items-center gap-3 p-3 rounded-lg backdrop-blur-sm border"
                        style={{ 
                          background: 'var(--glass-bg)',
                          borderColor: 'var(--glass-border)'
                        }}
                      >
                        <Calendar className="h-4 w-4 text-brand" />
                        <span className="text-marketing-body-sm">Meine Buchungen: 3</span>
                      </div>
                      <div 
                        className="flex items-center gap-3 p-3 rounded-lg backdrop-blur-sm border"
                        style={{ 
                          background: 'var(--glass-bg)',
                          borderColor: 'var(--glass-border)'
                        }}
                      >
                        <Users className="h-4 w-4 text-accent-purple" />
                        <span className="text-marketing-body-sm">Freunde online: 12</span>
                      </div>
                      <div 
                        className="flex items-center gap-3 p-3 rounded-lg backdrop-blur-sm border"
                        style={{ 
                          background: 'var(--glass-bg)',
                          borderColor: 'var(--glass-border)'
                        }}
                      >
                        <CheckCircle className="h-4 w-4 text-brand" />
                        <span className="text-marketing-body-sm">Spielzeit: 42h</span>
                      </div>
                    </>
                  )}
                  
                  {activeTab === 'guests' && (
                    <>
                      <div 
                        className="flex items-center gap-3 p-3 rounded-lg backdrop-blur-sm border"
                        style={{ 
                          background: 'var(--glass-bg)',
                          borderColor: 'var(--glass-border)'
                        }}
                      >
                        <Calendar className="h-4 w-4 text-brand" />
                        <span className="text-marketing-body-sm">Freie Plätze: 5</span>
                      </div>
                      <div 
                        className="flex items-center gap-3 p-3 rounded-lg backdrop-blur-sm border"
                        style={{ 
                          background: 'var(--glass-bg)',
                          borderColor: 'var(--glass-border)'
                        }}
                      >
                        <UserCheck className="h-4 w-4 text-accent-purple" />
                        <span className="text-marketing-body-sm">Schnellbuchung verfügbar</span>
                      </div>
                      <div 
                        className="flex items-center gap-3 p-3 rounded-lg backdrop-blur-sm border"
                        style={{ 
                          background: 'var(--glass-bg)',
                          borderColor: 'var(--glass-border)'
                        }}
                      >
                        <ClipboardList className="h-4 w-4 text-brand" />
                        <span className="text-marketing-body-sm">Preise & Infos</span>
                      </div>
                    </>
                  )}
                  
                  {activeTab === 'trainers' && (
                    <>
                      <div 
                        className="flex items-center gap-3 p-3 rounded-lg backdrop-blur-sm border"
                        style={{ 
                          background: 'var(--glass-bg)',
                          borderColor: 'var(--glass-border)'
                        }}
                      >
                        <Shield className="h-4 w-4 text-brand" />
                        <span className="text-marketing-body-sm">Kurse heute: 4</span>
                      </div>
                      <div 
                        className="flex items-center gap-3 p-3 rounded-lg backdrop-blur-sm border"
                        style={{ 
                          background: 'var(--glass-bg)',
                          borderColor: 'var(--glass-border)'
                        }}
                      >
                        <Users className="h-4 w-4 text-accent-purple" />
                        <span className="text-marketing-body-sm">Teilnehmer: 28</span>
                      </div>
                      <div 
                        className="flex items-center gap-3 p-3 rounded-lg backdrop-blur-sm border"
                        style={{ 
                          background: 'var(--glass-bg)',
                          borderColor: 'var(--glass-border)'
                        }}
                      >
                        <ClipboardList className="h-4 w-4 text-brand" />
                        <span className="text-marketing-body-sm">Berichte bereit</span>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>
            {/* Enhanced Decorative elements */}
            <div className="absolute -top-8 -right-8 w-40 h-40 bg-gradient-purple rounded-full opacity-20 blur-2xl"></div>
            <div className="absolute -bottom-4 -left-4 w-24 h-24 bg-brand/20 rounded-full blur-xl"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeaturesForClubs;