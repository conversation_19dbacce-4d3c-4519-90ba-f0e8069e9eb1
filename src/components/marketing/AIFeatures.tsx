import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Brain, Zap, MessageSquare, CheckCircle, ArrowRight, Sparkles, Calendar, Settings } from "lucide-react";

const AIFeatures = () => {
  const aiFeatures = [
    {
      icon: Brain,
      title: "Intelligente Buchungsregeln",
      description: "KI versteht natürliche Sprache und erstellt automatisch komplexe Buchungslogiken für euren Verein.",
      mockup: (
        <div className="space-y-3">
          <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-3 border border-purple-200">
            <div className="text-xs text-purple-700 font-medium mb-1">Eingabe:</div>
            <div className="text-xs text-gray-600">"Mitglieder dürfen max. 2h buchen, 24h im Voraus"</div>
          </div>
          <div className="flex items-center justify-center py-2">
            <div className="w-6 h-6 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 flex items-center justify-center">
              <Sparkles className="w-3 h-3 text-white" />
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex items-center gap-2 bg-green-50 rounded-md p-2 border border-green-200">
              <CheckCircle className="w-3 h-3 text-green-600" />
              <span className="text-xs text-green-700">Zeitlimit: 2 Stunden</span>
            </div>
            <div className="flex items-center gap-2 bg-green-50 rounded-md p-2 border border-green-200">
              <CheckCircle className="w-3 h-3 text-green-600" />
              <span className="text-xs text-green-700">Vorlaufzeit: 24 Stunden</span>
            </div>
          </div>
        </div>
      )
    },
    {
      icon: MessageSquare,
      title: "Kommunikations-Support",
      description: "KI erstellt automatisch Templates für Vereinsnachrichten, Newsletter und Events. Personalisierte Kommunikation für jedes Mitglied.",
      mockup: (
        <div className="space-y-3">
          <div className="bg-white rounded-lg border border-gray-200 p-3 shadow-sm">
            <div className="flex items-center gap-2 mb-2">
              <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
              <span className="text-xs font-medium text-gray-800">KI-Template</span>
            </div>
            <div className="text-xs text-gray-600 leading-relaxed mb-2">
              "🎾 Vereinsturnier am 15. Juni - Jetzt anmelden!"
            </div>
            <div className="space-y-1">
              <div className="text-xs bg-blue-50 text-blue-700 p-1 rounded">✅ Personalisiert für 127 Mitglieder</div>
              <div className="text-xs bg-green-50 text-green-700 p-1 rounded">📧 Automatischer Versand</div>
            </div>
          </div>
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>📝 5 Templates erstellt</span>
            <span>🎯 92% Öffnungsrate</span>
          </div>
        </div>
      )
    },
    {
      icon: Settings,
      title: "Intelligente Arbeitsdienste",
      description: "KI generiert automatisch Arbeitsaufgaben mit detaillierten Beschreibungen und Stundenaufwand für Events und Vereinsarbeit.",
      mockup: (
        <div className="space-y-3">
          <div className="bg-white rounded-lg border border-gray-200 p-3">
            <div className="flex items-center justify-between mb-2">
              <span className="text-xs font-medium text-gray-800">Neue Aufgabe generiert</span>
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            </div>
            <div className="space-y-2 text-xs">
              <div className="bg-blue-50 rounded p-2 border border-blue-200">
                <div className="font-medium text-blue-800">Platzpflege Sandplätze</div>
                <div className="text-blue-600 text-xs mt-1">Bewässerung, Linien ziehen, Netze prüfen</div>
              </div>
              <div className="flex items-center gap-2">
                <span className="bg-purple-100 text-purple-700 px-2 py-1 rounded text-xs">⏱️ 3 Std.</span>
                <span className="bg-green-100 text-green-700 px-2 py-1 rounded text-xs">👥 2 Personen</span>
              </div>
            </div>
          </div>
          <div className="text-xs text-center text-gray-500">🤖 15 Aufgaben automatisch erstellt</div>
        </div>
      )
    },
    {
      icon: Calendar,
      title: "Smarte Wartelisten-Verwaltung",
      description: "Erweiterte Wartelisten mit Auto-Zusage, First-Come-First-Serve und intelligenter Benachrichtigung für optimale Platzauslastung.",
      mockup: (
        <div className="space-y-3">
          <div className="bg-white rounded-lg border border-gray-200 p-3">
            <div className="flex items-center justify-between mb-2">
              <span className="text-xs font-medium text-gray-800">Warteliste Platz 3</span>
              <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 bg-gradient-to-r from-green-400 to-blue-400 rounded-full flex items-center justify-center">
                  <span className="text-xs text-white font-bold">1</span>
                </div>
                <span className="text-xs text-gray-600">Anna M. - Auto-Zusage</span>
                <div className="ml-auto w-2 h-2 bg-green-500 rounded-full"></div>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full flex items-center justify-center">
                  <span className="text-xs text-white font-bold">2</span>
                </div>
                <span className="text-xs text-gray-600">Tom K. - First-Come</span>
                <div className="ml-auto w-2 h-2 bg-orange-500 rounded-full"></div>
              </div>
            </div>
            <div className="mt-2 text-xs bg-purple-50 text-purple-700 p-1 rounded border border-purple-200">
              🚀 Auto-Nachrücken aktiviert
            </div>
          </div>
          <div className="text-xs text-center text-gray-500">⚡ +40% bessere Platzauslastung</div>
        </div>
      )
    }
  ];

  return (
    <section className="py-24 lg:py-32 relative overflow-hidden" style={{ background: 'var(--gradient-section)' }}>
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute top-20 left-20 w-40 h-40 bg-gradient-to-r from-purple-400 to-blue-400 rounded-full blur-3xl opacity-20"></div>
        <div className="absolute bottom-20 right-20 w-32 h-32 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full blur-2xl opacity-25"></div>
        <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-purple-300 rounded-full blur-xl opacity-15"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-16">
          <Badge 
            variant="secondary" 
            className="mb-6 px-4 py-2 text-marketing-micro font-semibold text-white border-0 shadow-button"
            style={{ background: 'var(--gradient-purple)' }}
          >
            <Brain className="w-4 h-4 mr-2" />
            Künstliche Intelligenz
          </Badge>
          
          <h2 className="text-marketing-h2 lg:text-5xl font-bold mb-6 text-fg-strong leading-tight">
            KI, die euren Verein <br />
            <span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
              revolutioniert
            </span>
          </h2>
          
          <p className="text-marketing-body text-fg-muted max-w-3xl mx-auto leading-relaxed">
            Unsere fortschrittliche KI automatisiert komplexe Verwaltungsaufgaben, optimiert Buchungen und sorgt für reibungslose Abläufe – damit ihr euch aufs Tennis konzentrieren könnt.
          </p>
        </div>

        {/* AI Features Grid */}
        <div className="grid lg:grid-cols-2 gap-12 lg:gap-16">
          {aiFeatures.map((feature, index) => (
            <div key={index} className="group h-full">
              <div 
                className="rounded-2xl border p-6 backdrop-blur-md transition-all duration-300 hover:shadow-xl hover:-translate-y-1 h-full flex flex-col"
                style={{ 
                  background: 'var(--glass-bg-strong)',
                  borderColor: 'var(--glass-border)',
                  boxShadow: 'var(--shadow-glass)'
                }}
              >
                {/* Feature Header */}
                <div className="flex items-center gap-4 mb-4">
                  <div 
                    className="w-12 h-12 rounded-xl flex items-center justify-center"
                    style={{ background: 'var(--gradient-purple)' }}
                  >
                    <feature.icon className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-marketing-h3 font-bold text-fg-strong mb-1">
                      {feature.title}
                    </h3>
                  </div>
                </div>

                {/* Feature Description */}
                <p className="text-marketing-body-sm text-fg-muted mb-6 leading-relaxed">
                  {feature.description}
                </p>

                {/* Mockup */}
                <div 
                  className="rounded-xl border p-4 mt-auto"
                  style={{ 
                    background: 'var(--glass-bg)',
                    borderColor: 'var(--glass-border)'
                  }}
                >
                  {feature.mockup}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* CTA Section */}
        <div className="mt-20 text-center">
          <div 
            className="inline-flex items-center gap-4 rounded-2xl border p-8 backdrop-blur-md"
            style={{ 
              background: 'var(--glass-bg-strong)',
              borderColor: 'var(--glass-border)',
              boxShadow: 'var(--shadow-glass)'
            }}
          >
            <div>
              <h3 className="text-marketing-h3 font-bold text-fg-strong mb-2">
                Bereit für intelligente Vereinsverwaltung?
              </h3>
              <p className="text-marketing-body-sm text-fg-muted mb-6">
                Testet alle KI-Features 14 Tage kostenlos
              </p>
              <Button 
                size="lg"
                className="px-8 py-4 text-marketing-body-sm font-semibold shadow-button border-0 transition-all duration-200"
                style={{ 
                  background: 'var(--gradient-purple)',
                  color: '#ffffff'
                }}
              >
                Jetzt KI-Demo starten
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AIFeatures;