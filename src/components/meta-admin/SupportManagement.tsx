import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useMetaAdminRole } from '@/hooks/useMetaAdminRole';
import { HelpCircle, Plus, MessageSquare, Clock, User, AlertTriangle, CheckCircle } from 'lucide-react';

interface SupportTicket {
  id: string;
  club_id?: string;
  user_id?: string;
  subject: string;
  description: string;
  status: string;
  priority: string;
  assigned_to?: string;
  created_at: string;
  updated_at: string;
  clubs?: { name: string };
  profiles?: any;
}

interface Club {
  id: string;
  name: string;
}

export function SupportManagement() {
  const { hasRole, canModify } = useMetaAdminRole();
  const { toast } = useToast();
  const [tickets, setTickets] = useState<SupportTicket[]>([]);
  const [clubs, setClubs] = useState<Club[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedClubId, setSelectedClubId] = useState<string>('');
  const [ticketForm, setTicketForm] = useState({
    subject: '',
    description: '',
    priority: 'medium',
  });
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterPriority, setFilterPriority] = useState<string>('all');

  const canManage = hasRole('SUPER_ADMIN') || hasRole('SUPPORT_ADMIN');

  useEffect(() => {
    loadTickets();
    loadClubs();
  }, []);

  const loadTickets = async () => {
    try {
      let query = supabase
        .from('support_tickets')
        .select(`
          *,
          clubs(name),
          profiles!support_tickets_user_id_fkey(first_name, last_name, email)
        `)
        .order('created_at', { ascending: false });

      if (filterStatus !== 'all') {
        query = query.eq('status', filterStatus);
      }

      if (filterPriority !== 'all') {
        query = query.eq('priority', filterPriority);
      }

      const { data, error } = await query;

      if (error) throw error;
      setTickets(data || []);
    } catch (err) {
      console.error('Error loading tickets:', err);
      toast({
        title: 'Fehler',
        description: 'Fehler beim Laden der Support-Tickets',
        variant: 'destructive',
      });
    }
  };

  const loadClubs = async () => {
    try {
      const { data, error } = await supabase
        .from('clubs')
        .select('id, name')
        .eq('is_active', true)
        .order('name');

      if (error) throw error;
      setClubs(data || []);
    } catch (err) {
      console.error('Error loading clubs:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const createTicket = async () => {
    if (!selectedClubId || !ticketForm.subject || !canManage) return;

    try {
      const { error } = await supabase
        .from('support_tickets')
        .insert({
          club_id: selectedClubId,
          subject: ticketForm.subject,
          description: ticketForm.description,
          priority: ticketForm.priority,
        });

      if (error) throw error;

      toast({
        title: 'Erfolg',
        description: 'Support-Ticket erfolgreich erstellt',
      });

      setIsDialogOpen(false);
      setSelectedClubId('');
      setTicketForm({
        subject: '',
        description: '',
        priority: 'medium',
      });
      loadTickets();
    } catch (err) {
      console.error('Error creating ticket:', err);
      toast({
        title: 'Fehler',
        description: 'Fehler beim Erstellen des Support-Tickets',
        variant: 'destructive',
      });
    }
  };

  const updateTicketStatus = async (ticketId: string, newStatus: string) => {
    if (!canManage) return;

    try {
      const { error } = await supabase
        .from('support_tickets')
        .update({ 
          status: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', ticketId);

      if (error) throw error;

      toast({
        title: 'Erfolg',
        description: 'Ticket-Status erfolgreich aktualisiert',
      });

      loadTickets();
    } catch (err) {
      console.error('Error updating ticket status:', err);
      toast({
        title: 'Fehler',
        description: 'Fehler beim Aktualisieren des Ticket-Status',
        variant: 'destructive',
      });
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'open':
        return <Badge variant="destructive">Offen</Badge>;
      case 'in_progress':
        return <Badge variant="default">In Bearbeitung</Badge>;
      case 'resolved':
        return <Badge variant="secondary">Gelöst</Badge>;
      case 'closed':
        return <Badge variant="outline">Geschlossen</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return <Badge variant="destructive">Dringend</Badge>;
      case 'high':
        return <Badge variant="destructive">Hoch</Badge>;
      case 'medium':
        return <Badge variant="default">Mittel</Badge>;
      case 'low':
        return <Badge variant="outline">Niedrig</Badge>;
      default:
        return <Badge variant="outline">{priority}</Badge>;
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'urgent':
      case 'high':
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case 'medium':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'low':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusStats = () => {
    const stats = {
      open: tickets.filter(t => t.status === 'open').length,
      in_progress: tickets.filter(t => t.status === 'in_progress').length,
      resolved: tickets.filter(t => t.status === 'resolved').length,
      closed: tickets.filter(t => t.status === 'closed').length,
    };
    return stats;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  const stats = getStatusStats();

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Support-Tools</h1>
          <p className="text-muted-foreground">
            Verwalten Sie Support-Tickets und Anfragen von allen Vereinen
          </p>
        </div>
        {canManage && (
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Neues Ticket
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Neues Support-Ticket erstellen</DialogTitle>
                <DialogDescription>
                  Erstellen Sie ein neues Support-Ticket für einen Verein.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="club">Verein</Label>
                  <Select value={selectedClubId} onValueChange={setSelectedClubId}>
                    <SelectTrigger>
                      <SelectValue placeholder="Verein auswählen" />
                    </SelectTrigger>
                    <SelectContent>
                      {clubs.map(club => (
                        <SelectItem key={club.id} value={club.id}>
                          {club.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="subject">Betreff</Label>
                  <Input
                    id="subject"
                    value={ticketForm.subject}
                    onChange={(e) => setTicketForm({ ...ticketForm, subject: e.target.value })}
                  />
                </div>
                <div>
                  <Label htmlFor="priority">Priorität</Label>
                  <Select value={ticketForm.priority} onValueChange={(value) => setTicketForm({ ...ticketForm, priority: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Niedrig</SelectItem>
                      <SelectItem value="medium">Mittel</SelectItem>
                      <SelectItem value="high">Hoch</SelectItem>
                      <SelectItem value="urgent">Dringend</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="description">Beschreibung</Label>
                  <textarea
                    id="description"
                    rows={4}
                    className="w-full p-2 border border-input rounded-md"
                    value={ticketForm.description}
                    onChange={(e) => setTicketForm({ ...ticketForm, description: e.target.value })}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Abbrechen
                </Button>
                <Button onClick={createTicket} disabled={!selectedClubId || !ticketForm.subject}>
                  Ticket erstellen
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Offene Tickets</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.open}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">In Bearbeitung</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.in_progress}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Gelöst</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.resolved}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Geschlossen</CardTitle>
            <MessageSquare className="h-4 w-4 text-gray-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.closed}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex gap-4">
            <div>
              <Label htmlFor="status-filter">Status Filter</Label>
              <Select value={filterStatus} onValueChange={(value) => { setFilterStatus(value); loadTickets(); }}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Alle Status</SelectItem>
                  <SelectItem value="open">Offen</SelectItem>
                  <SelectItem value="in_progress">In Bearbeitung</SelectItem>
                  <SelectItem value="resolved">Gelöst</SelectItem>
                  <SelectItem value="closed">Geschlossen</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="priority-filter">Priorität Filter</Label>
              <Select value={filterPriority} onValueChange={(value) => { setFilterPriority(value); loadTickets(); }}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Alle Prioritäten</SelectItem>
                  <SelectItem value="urgent">Dringend</SelectItem>
                  <SelectItem value="high">Hoch</SelectItem>
                  <SelectItem value="medium">Mittel</SelectItem>
                  <SelectItem value="low">Niedrig</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tickets Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <HelpCircle className="h-5 w-5" />
            Support-Tickets
          </CardTitle>
          <CardDescription>
            Übersicht aller Support-Anfragen und deren Bearbeitungsstatus
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Betreff</TableHead>
                <TableHead>Verein</TableHead>
                <TableHead>Priorität</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Erstellt</TableHead>
                <TableHead>Aktualisiert</TableHead>
                {canManage && <TableHead>Aktionen</TableHead>}
              </TableRow>
            </TableHeader>
            <TableBody>
              {tickets.map((ticket) => (
                <TableRow key={ticket.id}>
                  <TableCell>
                    <div className="font-medium">{ticket.subject}</div>
                    <div className="text-sm text-muted-foreground line-clamp-2">
                      {ticket.description}
                    </div>
                  </TableCell>
                  <TableCell>{ticket.clubs?.name || 'System'}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {getPriorityIcon(ticket.priority)}
                      {getPriorityBadge(ticket.priority)}
                    </div>
                  </TableCell>
                  <TableCell>{getStatusBadge(ticket.status)}</TableCell>
                  <TableCell>
                    {new Date(ticket.created_at).toLocaleDateString('de-DE')}
                  </TableCell>
                  <TableCell>
                    {new Date(ticket.updated_at).toLocaleDateString('de-DE')}
                  </TableCell>
                  {canManage && (
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {ticket.status === 'open' && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => updateTicketStatus(ticket.id, 'in_progress')}
                          >
                            Bearbeiten
                          </Button>
                        )}
                        {ticket.status === 'in_progress' && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => updateTicketStatus(ticket.id, 'resolved')}
                          >
                            Lösen
                          </Button>
                        )}
                        {ticket.status === 'resolved' && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => updateTicketStatus(ticket.id, 'closed')}
                          >
                            Schließen
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  )}
                </TableRow>
              ))}
              {tickets.length === 0 && (
                <TableRow>
                  <TableCell colSpan={canManage ? 7 : 6} className="text-center py-8 text-muted-foreground">
                    Keine Support-Tickets vorhanden
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}