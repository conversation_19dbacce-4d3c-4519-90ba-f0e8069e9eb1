import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useMetaAdminRole, type MetaAdminRole, type MetaAdminPermission } from '@/hooks/useMetaAdminRole';
import { UserPlus, Shield, Trash2, Calendar, Users, Settings, Mail, User, Plus, Edit } from 'lucide-react';

interface User {
  id: string;
  email?: string;
  user_metadata?: {
    full_name?: string;
    first_name?: string;
    last_name?: string;
  };
}

type MembershipCategory = 'Erwachsener' | 'Jugendlicher' | 'Kind' | 'Student' | 'Familie';

interface Profile {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  birth_date: string;
  street: string;
  house_number: string;
  postal_code: string;
  city: string;
  membership_category: MembershipCategory;
  created_at: string;
}

export function MetaAdminUserManagement() {
  const { user } = useAuth();
  const { hasRole } = useMetaAdminRole();
  const { toast } = useToast();
  
  // State for role management
  const [permissions, setPermissions] = useState<MetaAdminPermission[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [profiles, setProfiles] = useState<Profile[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  
  // Dialog states
  const [isRoleDialogOpen, setIsRoleDialogOpen] = useState(false);
  const [isCreateUserDialogOpen, setIsCreateUserDialogOpen] = useState(false);
  const [isEditUserDialogOpen, setIsEditUserDialogOpen] = useState(false);
  
  // Role assignment state
  const [selectedUserId, setSelectedUserId] = useState<string>('');
  const [selectedRole, setSelectedRole] = useState<MetaAdminRole>('READONLY');
  const [expiresInDays, setExpiresInDays] = useState<string>('');
  
  // New user form state
  const [newUser, setNewUser] = useState({
    email: '',
    password: '',
    firstName: '',
    lastName: '',
    birthDate: '',
    street: '',
    houseNumber: '',
    postalCode: '',
    city: '',
    membershipCategory: 'Erwachsener' as MembershipCategory
  });

  // Edit user form state
  const [editingUser, setEditingUser] = useState<Profile | null>(null);
  const [editUser, setEditUser] = useState({
    firstName: '',
    lastName: '',
    email: '',
    birthDate: '',
    street: '',
    houseNumber: '',
    postalCode: '',
    city: '',
    membershipCategory: 'Erwachsener' as MembershipCategory
  });

  // Only super admins can manage roles
  const canManage = hasRole('SUPER_ADMIN');

  useEffect(() => {
    loadPermissions();
    loadUsers();
    loadProfiles();
  }, []);

  const loadPermissions = async () => {
    try {
      const { data, error } = await supabase
        .from('meta_admin_permissions')
        .select(`
          *,
          profiles!inner(first_name, last_name, email)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setPermissions(data || []);
    } catch (err) {
      console.error('Error loading permissions:', err);
      toast({
        title: 'Fehler',
        description: 'Fehler beim Laden der Berechtigungen',
        variant: 'destructive',
      });
    }
  };

  const loadUsers = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, first_name, last_name, email');

      if (error) throw error;
      
      const formattedUsers = data?.map(profile => ({
        id: profile.id,
        email: profile.email,
        user_metadata: {
          first_name: profile.first_name,
          last_name: profile.last_name,
          full_name: `${profile.first_name} ${profile.last_name}`,
        }
      })) || [];

      setUsers(formattedUsers);
    } catch (err) {
      console.error('Error loading users:', err);
      toast({
        title: 'Fehler',
        description: 'Fehler beim Laden der Benutzer',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const loadProfiles = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setProfiles(data || []);
    } catch (err) {
      console.error('Error loading profiles:', err);
      toast({
        title: 'Fehler',
        description: 'Fehler beim Laden der Profile',
        variant: 'destructive',
      });
    }
  };

  const createUser = async () => {
    if (!newUser.email || !newUser.password || !newUser.firstName || !newUser.lastName) {
      toast({
        title: 'Fehler',
        description: 'Bitte füllen Sie alle Pflichtfelder aus',
        variant: 'destructive',
      });
      return;
    }

    try {
      // Create user via Supabase auth with metadata
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: newUser.email,
        password: newUser.password,
        options: {
          data: {
            first_name: newUser.firstName,
            last_name: newUser.lastName,
            birth_date: newUser.birthDate,
            street: newUser.street,
            house_number: newUser.houseNumber,
            postal_code: newUser.postalCode,
            city: newUser.city,
            membership_category: newUser.membershipCategory
          }
        }
      });

      if (authError) throw authError;

      if (!authData.user) {
        throw new Error('User creation failed - no user returned');
      }

      toast({
        title: 'Erfolg',
        description: 'Benutzer erfolgreich erstellt',
      });

      // Reset form and close dialog
      setNewUser({
        email: '',
        password: '',
        firstName: '',
        lastName: '',
        birthDate: '',
        street: '',
        houseNumber: '',
        postalCode: '',
        city: '',
        membershipCategory: 'Erwachsener'
      });
      setIsCreateUserDialogOpen(false);
      
      // Reload data
      await loadUsers();
      await loadProfiles();
    } catch (err) {
      console.error('Error creating user:', err);
      toast({
        title: 'Fehler',
        description: 'Fehler beim Erstellen des Benutzers: ' + (err instanceof Error ? err.message : 'Unbekannter Fehler'),
        variant: 'destructive',
      });
    }
  };

  const grantRole = async () => {
    if (!selectedUserId || !selectedRole || !user) return;

    try {
      let expiresAt = null;
      if (expiresInDays) {
        const days = parseInt(expiresInDays);
        if (days > 0) {
          expiresAt = new Date(Date.now() + days * 24 * 60 * 60 * 1000).toISOString();
        }
      }

      const { error } = await supabase
        .from('meta_admin_permissions')
        .insert({
          user_id: selectedUserId,
          role: selectedRole,
          granted_by: user.id,
          expires_at: expiresAt,
        });

      if (error) throw error;

      toast({
        title: 'Erfolg',
        description: 'Rolle erfolgreich zugewiesen',
      });

      setIsRoleDialogOpen(false);
      setSelectedUserId('');
      setSelectedRole('READONLY');
      setExpiresInDays('');
      loadPermissions();
    } catch (err) {
      console.error('Error granting role:', err);
      toast({
        title: 'Fehler',
        description: 'Fehler beim Zuweisen der Rolle',
        variant: 'destructive',
      });
    }
  };

  const revokeRole = async (permissionId: string) => {
    try {
      const { error } = await supabase
        .from('meta_admin_permissions')
        .delete()
        .eq('id', permissionId);

      if (error) throw error;

      toast({
        title: 'Erfolg',
        description: 'Rolle erfolgreich entzogen',
      });

      loadPermissions();
    } catch (err) {
      console.error('Error revoking role:', err);
      toast({
        title: 'Fehler',
        description: 'Fehler beim Entziehen der Rolle',
        variant: 'destructive',
      });
    }
  };

  const openEditDialog = (profile: Profile) => {
    setEditingUser(profile);
    setEditUser({
      firstName: profile.first_name,
      lastName: profile.last_name,
      email: profile.email,
      birthDate: profile.birth_date,
      street: profile.street,
      houseNumber: profile.house_number,
      postalCode: profile.postal_code,
      city: profile.city,
      membershipCategory: profile.membership_category
    });
    setIsEditUserDialogOpen(true);
  };

  const updateUser = async () => {
    if (!editingUser || !editUser.firstName || !editUser.lastName || !editUser.email) {
      toast({
        title: 'Fehler',
        description: 'Bitte füllen Sie alle Pflichtfelder aus',
        variant: 'destructive',
      });
      return;
    }

    try {
      const { error } = await supabase
        .from('profiles')
        .update({
          first_name: editUser.firstName,
          last_name: editUser.lastName,
          email: editUser.email,
          birth_date: editUser.birthDate,
          street: editUser.street,
          house_number: editUser.houseNumber,
          postal_code: editUser.postalCode,
          city: editUser.city,
          membership_category: editUser.membershipCategory
        })
        .eq('id', editingUser.id);

      if (error) throw error;

      toast({
        title: 'Erfolg',
        description: 'Benutzer erfolgreich aktualisiert',
      });

      setIsEditUserDialogOpen(false);
      setEditingUser(null);
      
      // Reload data
      await loadProfiles();
      await loadUsers();
    } catch (err) {
      console.error('Error updating user:', err);
      toast({
        title: 'Fehler',
        description: 'Fehler beim Aktualisieren des Benutzers: ' + (err instanceof Error ? err.message : 'Unbekannter Fehler'),
        variant: 'destructive',
      });
    }
  };

  const getRoleBadgeVariant = (role: MetaAdminRole) => {
    switch (role) {
      case 'SUPER_ADMIN':
        return 'destructive';
      case 'SUPPORT_ADMIN':
        return 'default';
      case 'BILLING_ADMIN':
        return 'secondary';
      case 'READONLY':
        return 'outline';
      default:
        return 'outline';
    }
  };

  const getUserDisplayName = (userId: string) => {
    const permission = permissions.find(p => p.user_id === userId);
    if (permission && 'profiles' in permission) {
      const profile = permission.profiles as any;
      return `${profile.first_name} ${profile.last_name}`;
    }
    
    const user = users.find(u => u.id === userId);
    return user?.user_metadata?.full_name || user?.email || 'Unbekannt';
  };

  const getUserEmail = (userId: string) => {
    const permission = permissions.find(p => p.user_id === userId);
    if (permission && 'profiles' in permission) {
      const profile = permission.profiles as any;
      return profile.email;
    }
    
    const user = users.find(u => u.id === userId);
    return user?.email || '';
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Benutzer-Verwaltung</h1>
          <p className="text-muted-foreground">
            Verwalten Sie Benutzer und deren Meta-Admin Zugriffsrechte
          </p>
        </div>
        {canManage && (
          <div className="flex gap-2">
            {/* Create User Dialog */}
            <Dialog open={isCreateUserDialogOpen} onOpenChange={setIsCreateUserDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Neuer Benutzer
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Neuen Benutzer erstellen</DialogTitle>
                  <DialogDescription>
                    Erstellen Sie einen neuen Benutzer mit Profildaten.
                  </DialogDescription>
                </DialogHeader>
                <div className="grid grid-cols-2 gap-4 py-4">
                  <div className="space-y-2">
                    <Label htmlFor="email">E-Mail *</Label>
                    <Input
                      id="email"
                      type="email"
                      value={newUser.email}
                      onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="password">Passwort *</Label>
                    <Input
                      id="password"
                      type="password"
                      value={newUser.password}
                      onChange={(e) => setNewUser({ ...newUser, password: e.target.value })}
                      placeholder="Mindestens 6 Zeichen"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="firstName">Vorname *</Label>
                    <Input
                      id="firstName"
                      value={newUser.firstName}
                      onChange={(e) => setNewUser({ ...newUser, firstName: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="lastName">Nachname *</Label>
                    <Input
                      id="lastName"
                      value={newUser.lastName}
                      onChange={(e) => setNewUser({ ...newUser, lastName: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="birthDate">Geburtsdatum</Label>
                    <Input
                      id="birthDate"
                      type="date"
                      value={newUser.birthDate}
                      onChange={(e) => setNewUser({ ...newUser, birthDate: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="membershipCategory">Mitgliedschaftskategorie</Label>
                    <Select 
                      value={newUser.membershipCategory} 
                      onValueChange={(value: MembershipCategory) => 
                        setNewUser({ ...newUser, membershipCategory: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Erwachsener">Erwachsener</SelectItem>
                        <SelectItem value="Jugendlicher">Jugendlicher</SelectItem>
                        <SelectItem value="Kind">Kind</SelectItem>
                        <SelectItem value="Student">Student</SelectItem>
                        <SelectItem value="Familie">Familie</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="street">Straße</Label>
                    <Input
                      id="street"
                      value={newUser.street}
                      onChange={(e) => setNewUser({ ...newUser, street: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="houseNumber">Hausnummer</Label>
                    <Input
                      id="houseNumber"
                      value={newUser.houseNumber}
                      onChange={(e) => setNewUser({ ...newUser, houseNumber: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="postalCode">PLZ</Label>
                    <Input
                      id="postalCode"
                      value={newUser.postalCode}
                      onChange={(e) => setNewUser({ ...newUser, postalCode: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="city">Stadt</Label>
                    <Input
                      id="city"
                      value={newUser.city}
                      onChange={(e) => setNewUser({ ...newUser, city: e.target.value })}
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsCreateUserDialogOpen(false)}>
                    Abbrechen
                  </Button>
                  <Button onClick={createUser}>
                    Benutzer erstellen
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            {/* Assign Role Dialog */}
            <Dialog open={isRoleDialogOpen} onOpenChange={setIsRoleDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline">
                  <UserPlus className="h-4 w-4 mr-2" />
                  Rolle zuweisen
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Meta-Admin Rolle zuweisen</DialogTitle>
                  <DialogDescription>
                    Weisen Sie einem Benutzer eine Meta-Admin Rolle zu. Diese Berechtigung kann optional zeitlich begrenzt werden.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="user">Benutzer</Label>
                    <Select value={selectedUserId} onValueChange={setSelectedUserId}>
                      <SelectTrigger>
                        <SelectValue placeholder="Benutzer auswählen" />
                      </SelectTrigger>
                      <SelectContent>
                        {users.map(user => (
                          <SelectItem key={user.id} value={user.id}>
                            {user.user_metadata?.full_name || user.email}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="role">Rolle</Label>
                    <Select value={selectedRole} onValueChange={(value) => setSelectedRole(value as MetaAdminRole)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="SUPER_ADMIN">Super Admin (Vollzugriff)</SelectItem>
                        <SelectItem value="SUPPORT_ADMIN">Support Admin (ohne Billing)</SelectItem>
                        <SelectItem value="BILLING_ADMIN">Billing Admin (nur Abrechnung)</SelectItem>
                        <SelectItem value="READONLY">Readonly (nur Lesen)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="expires">Gültig für (Tage, optional)</Label>
                    <Input
                      id="expires"
                      type="number"
                      placeholder="z.B. 30 für 30 Tage"
                      value={expiresInDays}
                      onChange={(e) => setExpiresInDays(e.target.value)}
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsRoleDialogOpen(false)}>
                    Abbrechen
                  </Button>
                  <Button onClick={grantRole} disabled={!selectedUserId || !selectedRole}>
                    Rolle zuweisen
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        )}
      </div>

      <Tabs defaultValue="users" className="space-y-4">
        <TabsList>
          <TabsTrigger value="users" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Alle Benutzer
          </TabsTrigger>
          <TabsTrigger value="roles" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Meta-Admin Rollen
          </TabsTrigger>
        </TabsList>

        <TabsContent value="users">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Alle Benutzer
              </CardTitle>
              <CardDescription>
                Übersicht aller registrierten Benutzer im System
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                     <TableHead>Name</TableHead>
                     <TableHead>E-Mail</TableHead>
                     <TableHead>Mitgliedschaft</TableHead>
                     <TableHead>Erstellt am</TableHead>
                     <TableHead>Stadt</TableHead>
                     {canManage && <TableHead>Aktionen</TableHead>}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {profiles.map((profile) => (
                    <TableRow key={profile.id}>
                      <TableCell className="font-medium">
                        {profile.first_name} {profile.last_name}
                      </TableCell>
                      <TableCell>{profile.email}</TableCell>
                      <TableCell>
                        <Badge variant="outline">{profile.membership_category}</Badge>
                      </TableCell>
                       <TableCell>
                         {new Date(profile.created_at).toLocaleDateString('de-DE')}
                       </TableCell>
                       <TableCell>{profile.city || '-'}</TableCell>
                       {canManage && (
                         <TableCell>
                           <Button
                             variant="ghost"
                             size="sm"
                             onClick={() => openEditDialog(profile)}
                           >
                             <Edit className="h-4 w-4" />
                           </Button>
                         </TableCell>
                       )}
                    </TableRow>
                  ))}
                  {profiles.length === 0 && (
                    <TableRow>
                       <TableCell colSpan={canManage ? 6 : 5} className="text-center py-8 text-muted-foreground">
                         Keine Benutzer gefunden
                       </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="roles">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Aktive Meta-Admin Berechtigungen
              </CardTitle>
              <CardDescription>
                Übersicht aller aktiven Meta-Admin Rollen und deren Gültigkeitsdauer
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Benutzer</TableHead>
                    <TableHead>E-Mail</TableHead>
                    <TableHead>Rolle</TableHead>
                    <TableHead>Zugewiesen am</TableHead>
                    <TableHead>Läuft ab</TableHead>
                    {canManage && <TableHead>Aktionen</TableHead>}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {permissions.map((permission) => (
                    <TableRow key={permission.id}>
                      <TableCell className="font-medium">
                        {getUserDisplayName(permission.user_id)}
                      </TableCell>
                      <TableCell>{getUserEmail(permission.user_id)}</TableCell>
                      <TableCell>
                        <Badge variant={getRoleBadgeVariant(permission.role)}>
                          {permission.role}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          {new Date(permission.granted_at).toLocaleDateString('de-DE')}
                        </div>
                      </TableCell>
                      <TableCell>
                        {permission.expires_at ? (
                          <div className="flex items-center gap-2">
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                            {new Date(permission.expires_at).toLocaleDateString('de-DE')}
                          </div>
                        ) : (
                          <Badge variant="outline">Unbegrenzt</Badge>
                        )}
                      </TableCell>
                      {canManage && (
                        <TableCell>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="outline" size="sm">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Rolle entziehen</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Sind Sie sicher, dass Sie diese Rolle entziehen möchten? Diese Aktion kann nicht rückgängig gemacht werden.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Abbrechen</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => revokeRole(permission.id)}
                                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                >
                                  Rolle entziehen
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </TableCell>
                      )}
                    </TableRow>
                  ))}
                  {permissions.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={canManage ? 6 : 5} className="text-center py-8 text-muted-foreground">
                        Keine Meta-Admin Berechtigungen vorhanden
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Edit User Dialog */}
      <Dialog open={isEditUserDialogOpen} onOpenChange={setIsEditUserDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Benutzer bearbeiten</DialogTitle>
            <DialogDescription>
              Bearbeiten Sie die Profildaten des Benutzers.
            </DialogDescription>
          </DialogHeader>
          <div className="grid grid-cols-2 gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="edit-firstName">Vorname *</Label>
              <Input
                id="edit-firstName"
                value={editUser.firstName}
                onChange={(e) => setEditUser({ ...editUser, firstName: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-lastName">Nachname *</Label>
              <Input
                id="edit-lastName"
                value={editUser.lastName}
                onChange={(e) => setEditUser({ ...editUser, lastName: e.target.value })}
              />
            </div>
            <div className="space-y-2 col-span-2">
              <Label htmlFor="edit-email">E-Mail *</Label>
              <Input
                id="edit-email"
                type="email"
                value={editUser.email}
                onChange={(e) => setEditUser({ ...editUser, email: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-birthDate">Geburtsdatum</Label>
              <Input
                id="edit-birthDate"
                type="date"
                value={editUser.birthDate}
                onChange={(e) => setEditUser({ ...editUser, birthDate: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-membershipCategory">Mitgliedschaftskategorie</Label>
              <Select 
                value={editUser.membershipCategory} 
                onValueChange={(value: MembershipCategory) => 
                  setEditUser({ ...editUser, membershipCategory: value })
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Erwachsener">Erwachsener</SelectItem>
                  <SelectItem value="Jugendlicher">Jugendlicher</SelectItem>
                  <SelectItem value="Kind">Kind</SelectItem>
                  <SelectItem value="Student">Student</SelectItem>
                  <SelectItem value="Familie">Familie</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-street">Straße</Label>
              <Input
                id="edit-street"
                value={editUser.street}
                onChange={(e) => setEditUser({ ...editUser, street: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-houseNumber">Hausnummer</Label>
              <Input
                id="edit-houseNumber"
                value={editUser.houseNumber}
                onChange={(e) => setEditUser({ ...editUser, houseNumber: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-postalCode">PLZ</Label>
              <Input
                id="edit-postalCode"
                value={editUser.postalCode}
                onChange={(e) => setEditUser({ ...editUser, postalCode: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-city">Stadt</Label>
              <Input
                id="edit-city"
                value={editUser.city}
                onChange={(e) => setEditUser({ ...editUser, city: e.target.value })}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditUserDialogOpen(false)}>
              Abbrechen
            </Button>
            <Button onClick={updateUser}>
              Änderungen speichern
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
