import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useMetaAdminRole } from '@/hooks/useMetaAdminRole';
import { Globe, Shield, Plus, CheckCircle, AlertTriangle, ExternalLink, Trash2, RefreshCw } from 'lucide-react';

interface Domain {
  id: string;
  club_id: string;
  domain_name: string;
  domain_type: string;
  is_primary: boolean;
  is_verified: boolean;
  ssl_status: string;
  ssl_expires_at?: string;
  dns_verified_at?: string;
  created_at: string;
  clubs?: {
    name: string;
  };
}

interface Club {
  id: string;
  name: string;
}

export function DomainManagement() {
  const { hasRole, canModify } = useMetaAdminRole();
  const { toast } = useToast();
  const [domains, setDomains] = useState<Domain[]>([]);
  const [clubs, setClubs] = useState<Club[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedClubId, setSelectedClubId] = useState<string>('');
  const [domainName, setDomainName] = useState<string>('');
  const [domainType, setDomainType] = useState<string>('custom');

  const canManage = hasRole('SUPER_ADMIN');

  useEffect(() => {
    loadDomains();
    loadClubs();
  }, []);

  const loadDomains = async () => {
    try {
      const { data, error } = await supabase
        .from('domains')
        .select(`
          *,
          clubs!inner(name)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setDomains(data || []);
    } catch (err) {
      console.error('Error loading domains:', err);
      toast({
        title: 'Fehler',
        description: 'Fehler beim Laden der Domains',
        variant: 'destructive',
      });
    }
  };

  const loadClubs = async () => {
    try {
      const { data, error } = await supabase
        .from('clubs')
        .select('id, name')
        .eq('is_active', true)
        .order('name');

      if (error) throw error;
      setClubs(data || []);
    } catch (err) {
      console.error('Error loading clubs:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const addDomain = async () => {
    if (!selectedClubId || !domainName || !canManage) return;

    try {
      const { error } = await supabase
        .from('domains')
        .insert({
          club_id: selectedClubId,
          domain_name: domainName.toLowerCase(),
          domain_type: domainType,
          verification_token: crypto.randomUUID(),
        });

      if (error) throw error;

      toast({
        title: 'Erfolg',
        description: 'Domain erfolgreich hinzugefügt',
      });

      setIsDialogOpen(false);
      setSelectedClubId('');
      setDomainName('');
      setDomainType('custom');
      loadDomains();
    } catch (err) {
      console.error('Error adding domain:', err);
      toast({
        title: 'Fehler',
        description: 'Fehler beim Hinzufügen der Domain',
        variant: 'destructive',
      });
    }
  };

  const verifyDomain = async (domainId: string) => {
    if (!canManage) return;

    try {
      const { error } = await supabase
        .from('domains')
        .update({
          is_verified: true,
          dns_verified_at: new Date().toISOString(),
          ssl_status: 'active',
        })
        .eq('id', domainId);

      if (error) throw error;

      toast({
        title: 'Erfolg',
        description: 'Domain erfolgreich verifiziert',
      });

      loadDomains();
    } catch (err) {
      console.error('Error verifying domain:', err);
      toast({
        title: 'Fehler',
        description: 'Fehler beim Verifizieren der Domain',
        variant: 'destructive',
      });
    }
  };

  const setPrimaryDomain = async (domainId: string, clubId: string) => {
    if (!canManage) return;

    try {
      // First, unset all other primary domains for this club
      await supabase
        .from('domains')
        .update({ is_primary: false })
        .eq('club_id', clubId);

      // Then set this domain as primary
      const { error } = await supabase
        .from('domains')
        .update({ is_primary: true })
        .eq('id', domainId);

      if (error) throw error;

      toast({
        title: 'Erfolg',
        description: 'Primäre Domain erfolgreich gesetzt',
      });

      loadDomains();
    } catch (err) {
      console.error('Error setting primary domain:', err);
      toast({
        title: 'Fehler',
        description: 'Fehler beim Setzen der primären Domain',
        variant: 'destructive',
      });
    }
  };

  const deleteDomain = async (domainId: string) => {
    if (!canManage) return;

    try {
      const { error } = await supabase
        .from('domains')
        .delete()
        .eq('id', domainId);

      if (error) throw error;

      toast({
        title: 'Erfolg',
        description: 'Domain erfolgreich gelöscht',
      });

      loadDomains();
    } catch (err) {
      console.error('Error deleting domain:', err);
      toast({
        title: 'Fehler',
        description: 'Fehler beim Löschen der Domain',
        variant: 'destructive',
      });
    }
  };

  const getDomainStatusBadge = (domain: Domain) => {
    if (!domain.is_verified) {
      return <Badge variant="outline">Nicht verifiziert</Badge>;
    }
    
    switch (domain.ssl_status) {
      case 'active':
        return <Badge variant="default">Aktiv</Badge>;
      case 'pending':
        return <Badge variant="secondary">SSL Ausstehend</Badge>;
      case 'failed':
        return <Badge variant="destructive">SSL Fehler</Badge>;
      case 'expired':
        return <Badge variant="destructive">SSL Abgelaufen</Badge>;
      default:
        return <Badge variant="outline">Unbekannt</Badge>;
    }
  };

  const getClubName = (clubId: string) => {
    return clubs.find(club => club.id === clubId)?.name || 'Unbekannt';
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Domain Verwaltung</h1>
          <p className="text-muted-foreground">
            Verwalten Sie Custom Domains und SSL-Zertifikate für alle Vereine
          </p>
        </div>
        {canManage && (
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Domain hinzufügen
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Neue Domain hinzufügen</DialogTitle>
                <DialogDescription>
                  Fügen Sie eine Custom Domain oder Subdomain für einen Verein hinzu.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="club">Verein</Label>
                  <Select value={selectedClubId} onValueChange={setSelectedClubId}>
                    <SelectTrigger>
                      <SelectValue placeholder="Verein auswählen" />
                    </SelectTrigger>
                    <SelectContent>
                      {clubs.map(club => (
                        <SelectItem key={club.id} value={club.id}>
                          {club.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="domain">Domain Name</Label>
                  <Input
                    id="domain"
                    placeholder="beispiel.de"
                    value={domainName}
                    onChange={(e) => setDomainName(e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="type">Domain Typ</Label>
                  <Select value={domainType} onValueChange={setDomainType}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="custom">Custom Domain</SelectItem>
                      <SelectItem value="subdomain">Subdomain</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Abbrechen
                </Button>
                <Button onClick={addDomain} disabled={!selectedClubId || !domainName}>
                  Domain hinzufügen
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Alle Domains
          </CardTitle>
          <CardDescription>
            Übersicht aller registrierten Domains und deren Status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Domain</TableHead>
                <TableHead>Verein</TableHead>
                <TableHead>Typ</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>SSL Status</TableHead>
                <TableHead>Primär</TableHead>
                <TableHead>Erstellt</TableHead>
                {canManage && <TableHead>Aktionen</TableHead>}
              </TableRow>
            </TableHeader>
            <TableBody>
              {domains.map((domain) => (
                <TableRow key={domain.id}>
                  <TableCell className="font-medium">
                    <div className="flex items-center gap-2">
                      <ExternalLink className="h-4 w-4 text-muted-foreground" />
                      {domain.domain_name}
                    </div>
                  </TableCell>
                  <TableCell>
                    {domain.clubs?.name || getClubName(domain.club_id)}
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {domain.domain_type === 'custom' ? 'Custom' : 'Subdomain'}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {getDomainStatusBadge(domain)}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {domain.ssl_status === 'active' ? (
                        <Shield className="h-4 w-4 text-green-600" />
                      ) : (
                        <AlertTriangle className="h-4 w-4 text-yellow-600" />
                      )}
                      <span className="capitalize">{domain.ssl_status}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    {domain.is_primary ? (
                      <Badge variant="default">Primär</Badge>
                    ) : canManage ? (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setPrimaryDomain(domain.id, domain.club_id)}
                        disabled={!domain.is_verified}
                      >
                        Als Primär setzen
                      </Button>
                    ) : (
                      <span className="text-muted-foreground">-</span>
                    )}
                  </TableCell>
                  <TableCell>
                    {new Date(domain.created_at).toLocaleDateString('de-DE')}
                  </TableCell>
                  {canManage && (
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {!domain.is_verified && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => verifyDomain(domain.id)}
                          >
                            <CheckCircle className="h-4 w-4" />
                          </Button>
                        )}
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button variant="outline" size="sm">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Domain löschen</AlertDialogTitle>
                              <AlertDialogDescription>
                                Sind Sie sicher, dass Sie diese Domain löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Abbrechen</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => deleteDomain(domain.id)}
                                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                              >
                                Löschen
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </TableCell>
                  )}
                </TableRow>
              ))}
              {domains.length === 0 && (
                <TableRow>
                  <TableCell colSpan={canManage ? 8 : 7} className="text-center py-8 text-muted-foreground">
                    Keine Domains konfiguriert
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* DNS Setup Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>DNS Konfiguration</CardTitle>
          <CardDescription>
            Anweisungen für die DNS-Konfiguration von Custom Domains
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">A-Record für Root Domain:</h4>
              <code className="block bg-muted p-2 rounded text-sm">
                Type: A<br />
                Name: @<br />
                Value: *************
              </code>
            </div>
            <div>
              <h4 className="font-medium mb-2">A-Record für www Subdomain:</h4>
              <code className="block bg-muted p-2 rounded text-sm">
                Type: A<br />
                Name: www<br />
                Value: *************
              </code>
            </div>
            <p className="text-sm text-muted-foreground">
              Nach der DNS-Konfiguration kann es bis zu 48 Stunden dauern, bis die Änderungen propagiert sind.
              SSL-Zertifikate werden automatisch über Let's Encrypt bereitgestellt.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}