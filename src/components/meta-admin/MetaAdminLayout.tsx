import { ReactNode } from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useIsMetaAdmin } from '@/contexts/TenantContext';
import { useMetaAdminRole } from '@/hooks/useMetaAdminRole';
import { MetaAdminSidebar } from './MetaAdminSidebar';
import { MetaAdminHeader } from './MetaAdminHeader';
import { toast } from 'sonner';

interface MetaAdminLayoutProps {
  children: ReactNode;
}

export function MetaAdminLayout({ children }: MetaAdminLayoutProps) {
  const { user, isLoading } = useAuth();
  const isMetaAdmin = useIsMetaAdmin();
  const { hasAnyRole, isLoading: rolesLoading, roles, error } = useMetaAdminRole();

  // Debug logging
  console.log('🔍 MetaAdminLayout Debug:', {
    user: user ? { id: user.id, email: user.email } : null,
    isLoading,
    rolesLoading,
    isMetaAdmin,
    hasAnyRole: hasAnyRole(),
    roles,
    error,
    hostname: window.location.hostname,
    pathname: window.location.pathname
  });

  // Show loading while checking authentication
  if (isLoading) {
    console.log('🔄 MetaAdminLayout: Auth loading');
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Redirect to auth if not logged in
  if (!user) {
    console.log('❌ MetaAdminLayout: No user, redirecting to /auth');
    return <Navigate to="/auth" replace />;
  }

  // Check domain/path first
  if (!isMetaAdmin) {
    console.log('❌ MetaAdminLayout: Not meta admin domain/path, redirecting to /');
    return <Navigate to="/" replace />;
  }

  // Show loading while roles are being loaded
  if (rolesLoading) {
    console.log('🔄 MetaAdminLayout: Roles loading');
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Check if user has meta admin role - only after roles are fully loaded
  if (!hasAnyRole()) {
    console.log('❌ MetaAdminLayout: No meta admin roles, redirecting to /', {
      roles,
      hasAnyRole: hasAnyRole(),
      error
    });
    toast.error('Sie haben keine Berechtigung für den Meta-Admin-Bereich');
    return <Navigate to="/" replace />;
  }

  console.log('✅ MetaAdminLayout: Access granted, rendering layout');

  return (
    <div className="flex h-screen bg-background">
      <MetaAdminSidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <MetaAdminHeader />
        <main className="flex-1 overflow-auto p-6">
          {children}
        </main>
      </div>
    </div>
  );
}