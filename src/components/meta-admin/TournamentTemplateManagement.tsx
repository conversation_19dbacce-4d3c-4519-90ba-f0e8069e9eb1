import React, { useState } from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Plus, Edit, Trash2, Trophy, Settings } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

interface TournamentTemplate {
  id: string;
  name: string;
  description?: string;
  format_config: {
    type: 'single_elimination' | 'double_elimination' | 'round_robin' | 'swiss';
    best_of: number;
  };
  scoring_config: {
    scoring_type: 'standard' | 'no_ad' | 'fast4';
    tiebreak: boolean;
  };
  is_global: boolean;
  is_active: boolean;
  created_at: string;
}

export function TournamentTemplateManagement() {
  const [isCreateOpen, setIsCreateOpen] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<TournamentTemplate | null>(null);
  const queryClient = useQueryClient();

  const { data: templates, isLoading } = useQuery({
    queryKey: ['tournament-templates'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('tournament_templates')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      return data as any[];
    }
  });

  const createTemplateMutation = useMutation({
    mutationFn: async (templateData: Partial<TournamentTemplate>) => {
      const { data, error } = await supabase
        .from('tournament_templates')
        .insert([{
          name: templateData.name!,
          description: templateData.description,
          format_config: templateData.format_config,
          scoring_config: templateData.scoring_config,
          is_global: templateData.is_global,
          is_active: templateData.is_active
        }])
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tournament-templates'] });
      setIsCreateOpen(false);
      toast.success('Turnier-Template erfolgreich erstellt');
    },
    onError: (error) => {
      toast.error('Fehler beim Erstellen des Templates: ' + error.message);
    }
  });

  const updateTemplateMutation = useMutation({
    mutationFn: async ({ id, ...updates }: Partial<TournamentTemplate> & { id: string }) => {
      const { data, error } = await supabase
        .from('tournament_templates')
        .update(updates)
        .eq('id', id)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tournament-templates'] });
      setEditingTemplate(null);
      toast.success('Template erfolgreich aktualisiert');
    },
    onError: (error) => {
      toast.error('Fehler beim Aktualisieren: ' + error.message);
    }
  });

  const deleteTemplateMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('tournament_templates')
        .delete()
        .eq('id', id);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tournament-templates'] });
      toast.success('Template erfolgreich gelöscht');
    },
    onError: (error) => {
      toast.error('Fehler beim Löschen: ' + error.message);
    }
  });

  const TemplateForm = ({ 
    template, 
    onSubmit, 
    onCancel 
  }: { 
    template?: TournamentTemplate; 
    onSubmit: (data: Partial<TournamentTemplate>) => void;
    onCancel: () => void;
  }) => {
    const [formData, setFormData] = useState({
      name: template?.name || '',
      description: template?.description || '',
      format_type: template?.format_config.type || 'single_elimination',
      best_of: template?.format_config.best_of || 3,
      scoring_type: template?.scoring_config.scoring_type || 'standard',
      tiebreak: template?.scoring_config.tiebreak ?? true,
      is_global: template?.is_global ?? true,
      is_active: template?.is_active ?? true
    });

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();
      onSubmit({
        name: formData.name,
        description: formData.description,
        format_config: {
          type: formData.format_type as any,
          best_of: formData.best_of
        },
        scoring_config: {
          scoring_type: formData.scoring_type as any,
          tiebreak: formData.tiebreak
        },
        is_global: formData.is_global,
        is_active: formData.is_active
      });
    };

    return (
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <Label htmlFor="name">Template Name</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            placeholder="z.B. Standard Einzelturnier"
            required
          />
        </div>

        <div>
          <Label htmlFor="description">Beschreibung</Label>
          <Textarea
            id="description"
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            placeholder="Beschreibung des Turnier-Formats"
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="format_type">Turnier-Format</Label>
            <Select
              value={formData.format_type}
              onValueChange={(value: 'single_elimination' | 'double_elimination' | 'round_robin' | 'swiss') => setFormData({ ...formData, format_type: value })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="single_elimination">K.O.-System</SelectItem>
                <SelectItem value="double_elimination">Doppel-K.O.</SelectItem>
                <SelectItem value="round_robin">Jeder gegen Jeden</SelectItem>
                <SelectItem value="swiss">Schweizer System</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="best_of">Best of</Label>
            <Select
              value={formData.best_of.toString()}
              onValueChange={(value) => setFormData({ ...formData, best_of: parseInt(value) })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1">Best of 1</SelectItem>
                <SelectItem value="3">Best of 3</SelectItem>
                <SelectItem value="5">Best of 5</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="scoring_type">Zählweise</Label>
            <Select
              value={formData.scoring_type}
              onValueChange={(value: 'standard' | 'no_ad' | 'fast4') => setFormData({ ...formData, scoring_type: value })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="standard">Standard</SelectItem>
                <SelectItem value="no_ad">No-Ad</SelectItem>
                <SelectItem value="fast4">Fast4</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center space-x-2 pt-6">
            <Switch
              id="tiebreak"
              checked={formData.tiebreak}
              onCheckedChange={(checked) => setFormData({ ...formData, tiebreak: checked })}
            />
            <Label htmlFor="tiebreak">Tiebreak</Label>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="flex items-center space-x-2">
            <Switch
              id="is_global"
              checked={formData.is_global}
              onCheckedChange={(checked) => setFormData({ ...formData, is_global: checked })}
            />
            <Label htmlFor="is_global">Global verfügbar</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="is_active"
              checked={formData.is_active}
              onCheckedChange={(checked) => setFormData({ ...formData, is_active: checked })}
            />
            <Label htmlFor="is_active">Aktiv</Label>
          </div>
        </div>

        <div className="flex gap-2 pt-4">
          <Button type="submit" disabled={createTemplateMutation.isPending || updateTemplateMutation.isPending}>
            {template ? 'Aktualisieren' : 'Erstellen'}
          </Button>
          <Button type="button" variant="outline" onClick={onCancel}>
            Abbrechen
          </Button>
        </div>
      </form>
    );
  };

  if (isLoading) {
    return <div>Lade Templates...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Turnier-Templates</h1>
          <p className="text-muted-foreground">
            Verwalten Sie globale Turnier-Templates für alle Clubs
          </p>
        </div>
        <Dialog open={isCreateOpen} onOpenChange={setIsCreateOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Neues Template
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Neues Turnier-Template erstellen</DialogTitle>
            </DialogHeader>
            <TemplateForm
              onSubmit={(data) => createTemplateMutation.mutate(data)}
              onCancel={() => setIsCreateOpen(false)}
            />
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Trophy className="h-5 w-5" />
            Template Übersicht
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Format</TableHead>
                <TableHead>Zählweise</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Erstellt</TableHead>
                <TableHead>Aktionen</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {templates?.map((template) => (
                <TableRow key={template.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{template.name}</div>
                      {template.description && (
                        <div className="text-sm text-muted-foreground">{template.description}</div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <Badge variant="outline">
                        {template.format_config.type === 'single_elimination' ? 'K.O.-System' :
                         template.format_config.type === 'double_elimination' ? 'Doppel-K.O.' :
                         template.format_config.type === 'round_robin' ? 'Jeder gegen Jeden' :
                         'Schweizer System'}
                      </Badge>
                      <div className="text-sm text-muted-foreground">
                        Best of {template.format_config.best_of}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <Badge variant="secondary">
                        {template.scoring_config.scoring_type === 'standard' ? 'Standard' :
                         template.scoring_config.scoring_type === 'no_ad' ? 'No-Ad' : 'Fast4'}
                      </Badge>
                      {template.scoring_config.tiebreak && (
                        <div className="text-sm text-muted-foreground">Mit Tiebreak</div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <Badge variant={template.is_active ? 'default' : 'secondary'}>
                        {template.is_active ? 'Aktiv' : 'Inaktiv'}
                      </Badge>
                      {template.is_global && (
                        <div className="text-sm text-muted-foreground">Global</div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    {new Date(template.created_at).toLocaleDateString('de-DE')}
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Dialog open={editingTemplate?.id === template.id} onOpenChange={(open) => {
                        if (!open) setEditingTemplate(null);
                        else setEditingTemplate(template);
                      }}>
                        <DialogTrigger asChild>
                          <Button variant="outline" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl">
                          <DialogHeader>
                            <DialogTitle>Template bearbeiten</DialogTitle>
                          </DialogHeader>
                          <TemplateForm
                            template={template}
                            onSubmit={(data) => updateTemplateMutation.mutate({ id: template.id, ...data })}
                            onCancel={() => setEditingTemplate(null)}
                          />
                        </DialogContent>
                      </Dialog>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => deleteTemplateMutation.mutate(template.id)}
                        disabled={deleteTemplateMutation.isPending}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}