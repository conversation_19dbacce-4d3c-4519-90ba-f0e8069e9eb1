import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useMetaAdminRole } from '@/hooks/useMetaAdminRole';
import { 
  Database, 
  Key, 
  Download, 
  Upload, 
  Shield, 
  Webhook,
  FileText,
  Calendar,
  Filter,
  Search
} from 'lucide-react';

interface AuditLog {
  id: string;
  user_id?: string;
  club_id?: string;
  action: string;
  resource_type: string;
  resource_id?: string;
  old_values?: any;
  new_values?: any;
  ip_address?: any;
  user_agent?: string;
  created_at: string;
}

interface SystemMetric {
  id: string;
  metric_name: string;
  metric_value: number;
  labels: any;
  recorded_at: string;
}

export function MonitoringLogsManagement() {
  const { hasRole, canAccessSection } = useMetaAdminRole();
  const { toast } = useToast();
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
  const [systemMetrics, setSystemMetrics] = useState<SystemMetric[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [logFilter, setLogFilter] = useState({
    action: '',
    resource_type: '',
    user_id: '',
    date_from: '',
    date_to: '',
  });
  const [metricsFilter, setMetricsFilter] = useState({
    metric_name: '',
    date_from: '',
    date_to: '',
  });
  const [searchTerm, setSearchTerm] = useState('');

  const canView = canAccessSection('monitoring');

  useEffect(() => {
    if (canView) {
      loadAuditLogs();
      loadSystemMetrics();
    }
  }, [canView]);

  const loadAuditLogs = async () => {
    try {
      let query = supabase
        .from('audit_logs')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(500);

      // Apply filters
      if (logFilter.action) {
        query = query.eq('action', logFilter.action);
      }
      if (logFilter.resource_type) {
        query = query.eq('resource_type', logFilter.resource_type);
      }
      if (logFilter.user_id) {
        query = query.eq('user_id', logFilter.user_id);
      }
      if (logFilter.date_from) {
        query = query.gte('created_at', logFilter.date_from);
      }
      if (logFilter.date_to) {
        query = query.lte('created_at', logFilter.date_to);
      }

      const { data, error } = await query;
      if (error) throw error;

      setAuditLogs(data || []);
    } catch (err) {
      console.error('Error loading audit logs:', err);
      toast({
        title: 'Fehler',
        description: 'Fehler beim Laden der Audit-Logs',
        variant: 'destructive',
      });
    }
  };

  const loadSystemMetrics = async () => {
    try {
      let query = supabase
        .from('system_metrics')
        .select('*')
        .order('recorded_at', { ascending: false })
        .limit(200);

      // Apply filters
      if (metricsFilter.metric_name) {
        query = query.eq('metric_name', metricsFilter.metric_name);
      }
      if (metricsFilter.date_from) {
        query = query.gte('recorded_at', metricsFilter.date_from);
      }
      if (metricsFilter.date_to) {
        query = query.lte('recorded_at', metricsFilter.date_to);
      }

      const { data, error } = await query;
      if (error) throw error;

      setSystemMetrics(data || []);
    } catch (err) {
      console.error('Error loading system metrics:', err);
      toast({
        title: 'Fehler',
        description: 'Fehler beim Laden der System-Metriken',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const exportLogs = async (type: 'audit' | 'metrics') => {
    try {
      const data = type === 'audit' ? auditLogs : systemMetrics;
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${type}_logs_${new Date().toISOString().split('T')[0]}.json`;
      a.click();
      URL.revokeObjectURL(url);

      toast({
        title: 'Export erfolgreich',
        description: `${type === 'audit' ? 'Audit-Logs' : 'System-Metriken'} wurden exportiert`,
      });
    } catch (err) {
      console.error('Error exporting logs:', err);
      toast({
        title: 'Fehler',
        description: 'Fehler beim Export der Logs',
        variant: 'destructive',
      });
    }
  };

  const getActionBadge = (action: string) => {
    switch (action.toLowerCase()) {
      case 'create':
      case 'insert':
        return <Badge variant="default">CREATE</Badge>;
      case 'update':
      case 'modify':
        return <Badge variant="secondary">UPDATE</Badge>;
      case 'delete':
      case 'remove':
        return <Badge variant="destructive">DELETE</Badge>;
      case 'login':
      case 'authenticate':
        return <Badge variant="outline">AUTH</Badge>;
      default:
        return <Badge variant="outline">{action.toUpperCase()}</Badge>;
    }
  };

  const getResourceTypeIcon = (resourceType: string) => {
    switch (resourceType.toLowerCase()) {
      case 'club':
      case 'clubs':
        return <Database className="h-4 w-4" />;
      case 'user':
      case 'users':
        return <Shield className="h-4 w-4" />;
      case 'booking':
      case 'bookings':
        return <Calendar className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const filteredAuditLogs = auditLogs.filter(log => {
    if (!searchTerm) return true;
    return (
      log.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.resource_type.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (log.resource_id && log.resource_id.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  });

  const filteredMetrics = systemMetrics.filter(metric => {
    if (!searchTerm) return true;
    return metric.metric_name.toLowerCase().includes(searchTerm.toLowerCase());
  });

  if (!canView) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <Shield className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium">Zugriff verweigert</h3>
          <p className="text-muted-foreground">Sie haben keine Berechtigung für diesen Bereich.</p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Monitoring & Logs</h1>
          <p className="text-muted-foreground">
            System-Logs, Audit-Trail und Performance-Metriken
          </p>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Audit Events (24h)</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {auditLogs.filter(log => 
                new Date(log.created_at) > new Date(Date.now() - 24 * 60 * 60 * 1000)
              ).length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Metriken</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{systemMetrics.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Login Events (24h)</CardTitle>
            <Key className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {auditLogs.filter(log => 
                log.action.toLowerCase().includes('login') &&
                new Date(log.created_at) > new Date(Date.now() - 24 * 60 * 60 * 1000)
              ).length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Error Rate</CardTitle>
            <Webhook className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">0.02%</div>
            <p className="text-xs text-green-600">Normal</p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="audit" className="space-y-6">
        <TabsList>
          <TabsTrigger value="audit">Audit Logs</TabsTrigger>
          <TabsTrigger value="metrics">System Metriken</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
        </TabsList>

        <TabsContent value="audit" className="space-y-6">
          {/* Audit Logs Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                Filter & Suche
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                <div>
                  <Label htmlFor="search">Suche</Label>
                  <div className="relative">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="search"
                      placeholder="Suchen..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-8"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="action-filter">Aktion</Label>
                  <Select 
                    value={logFilter.action} 
                    onValueChange={(value) => {
                      setLogFilter({ ...logFilter, action: value });
                      loadAuditLogs();
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Alle Aktionen" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">Alle</SelectItem>
                      <SelectItem value="create">Create</SelectItem>
                      <SelectItem value="update">Update</SelectItem>
                      <SelectItem value="delete">Delete</SelectItem>
                      <SelectItem value="login">Login</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="resource-filter">Resource Typ</Label>
                  <Select 
                    value={logFilter.resource_type} 
                    onValueChange={(value) => {
                      setLogFilter({ ...logFilter, resource_type: value });
                      loadAuditLogs();
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Alle Typen" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">Alle</SelectItem>
                      <SelectItem value="club">Club</SelectItem>
                      <SelectItem value="user">User</SelectItem>
                      <SelectItem value="booking">Booking</SelectItem>
                      <SelectItem value="domain">Domain</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="date-from">Von Datum</Label>
                  <Input
                    id="date-from"
                    type="date"
                    value={logFilter.date_from}
                    onChange={(e) => setLogFilter({ ...logFilter, date_from: e.target.value })}
                  />
                </div>
                <div className="flex items-end">
                  <Button onClick={() => exportLogs('audit')} className="w-full">
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Audit Logs Table */}
          <Card>
            <CardHeader>
              <CardTitle>Audit Trail</CardTitle>
              <CardDescription>
                Vollständige Nachverfolgung aller Systemaktivitäten
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Zeitstempel</TableHead>
                    <TableHead>Aktion</TableHead>
                    <TableHead>Resource</TableHead>
                    <TableHead>Resource ID</TableHead>
                    <TableHead>IP Adresse</TableHead>
                    <TableHead>Details</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredAuditLogs.map((log) => (
                    <TableRow key={log.id}>
                      <TableCell className="font-mono text-sm">
                        {new Date(log.created_at).toLocaleString('de-DE')}
                      </TableCell>
                      <TableCell>{getActionBadge(log.action)}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getResourceTypeIcon(log.resource_type)}
                          <span>{log.resource_type}</span>
                        </div>
                      </TableCell>
                      <TableCell className="font-mono text-sm">
                        {log.resource_id?.substring(0, 8) || '-'}
                      </TableCell>
                      <TableCell className="font-mono text-sm">
                        {log.ip_address || '-'}
                      </TableCell>
                      <TableCell>
                        {log.old_values || log.new_values ? (
                          <Button variant="outline" size="sm">
                            Details
                          </Button>
                        ) : (
                          '-'
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                  {filteredAuditLogs.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                        Keine Audit-Logs gefunden
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="metrics" className="space-y-6">
          {/* System Metrics Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                Metriken Filter
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <Label htmlFor="metric-search">Metrik suchen</Label>
                  <Input
                    id="metric-search"
                    placeholder="Metrik Name..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="metric-name">Metrik Typ</Label>
                  <Select 
                    value={metricsFilter.metric_name} 
                    onValueChange={(value) => {
                      setMetricsFilter({ ...metricsFilter, metric_name: value });
                      loadSystemMetrics();
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Alle Metriken" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">Alle</SelectItem>
                      <SelectItem value="cpu_usage">CPU Usage</SelectItem>
                      <SelectItem value="memory_usage">Memory Usage</SelectItem>
                      <SelectItem value="db_connections">DB Connections</SelectItem>
                      <SelectItem value="response_time">Response Time</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="metrics-date-from">Von Datum</Label>
                  <Input
                    id="metrics-date-from"
                    type="date"
                    value={metricsFilter.date_from}
                    onChange={(e) => setMetricsFilter({ ...metricsFilter, date_from: e.target.value })}
                  />
                </div>
                <div className="flex items-end">
                  <Button onClick={() => exportLogs('metrics')} className="w-full">
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* System Metrics Table */}
          <Card>
            <CardHeader>
              <CardTitle>System Metriken</CardTitle>
              <CardDescription>
                Performance-Daten und Systemauslastung
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Zeitstempel</TableHead>
                    <TableHead>Metrik</TableHead>
                    <TableHead>Wert</TableHead>
                    <TableHead>Labels</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredMetrics.map((metric) => (
                    <TableRow key={metric.id}>
                      <TableCell className="font-mono text-sm">
                        {new Date(metric.recorded_at).toLocaleString('de-DE')}
                      </TableCell>
                      <TableCell className="font-medium">{metric.metric_name}</TableCell>
                      <TableCell className="font-mono">
                        {metric.metric_value.toFixed(2)}
                      </TableCell>
                      <TableCell>
                        {metric.labels && Object.keys(metric.labels).length > 0 ? (
                          <div className="flex gap-1">
                            {Object.entries(metric.labels).map(([key, value]) => (
                              <Badge key={key} variant="outline" className="text-xs">
                                {key}:{String(value)}
                              </Badge>
                            ))}
                          </div>
                        ) : (
                          '-'
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                  {filteredMetrics.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={4} className="text-center py-8 text-muted-foreground">
                        Keine Metriken gefunden
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Performance Übersicht</CardTitle>
              <CardDescription>
                System-Performance und Auslastungsstatistiken
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">99.9%</div>
                  <div className="text-sm text-muted-foreground">Uptime (30 Tage)</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">147ms</div>
                  <div className="text-sm text-muted-foreground">Avg. Response Time</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">1,234</div>
                  <div className="text-sm text-muted-foreground">Requests/min</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-600">3.2GB</div>
                  <div className="text-sm text-muted-foreground">Memory Usage</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}