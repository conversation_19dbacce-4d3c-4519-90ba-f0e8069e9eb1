import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useMetaAdminRole } from '@/hooks/useMetaAdminRole';
import { CreditCard, Plus, Edit, Trash2, Users, Building2 } from 'lucide-react';

interface Plan {
  id: string;
  name: string;
  description?: string;
  price_monthly?: number;
  price_yearly?: number;
  limits: any;
  features: any;
  is_active: boolean;
  sort_order: number;
  created_at: string;
}

interface Subscription {
  id: string;
  club_id: string;
  plan_id: string;
  status: string;
  current_period_start?: string;
  current_period_end?: string;
  trial_end?: string;
  created_at: string;
  clubs?: { name: string };
  plans?: { name: string };
}

export function BillingManagement() {
  const { hasRole, canModify } = useMetaAdminRole();
  const { toast } = useToast();
  const [plans, setPlans] = useState<Plan[]>([]);
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isPlanDialogOpen, setIsPlanDialogOpen] = useState(false);
  const [editingPlan, setEditingPlan] = useState<Plan | null>(null);
  const [planForm, setPlanForm] = useState({
    name: '',
    description: '',
    price_monthly: '',
    price_yearly: '',
    limits: {
      members: '',
      courts: '',
      storage_gb: '',
      api_requests: '',
    },
    features: '',
  });

  const canManage = hasRole('SUPER_ADMIN') || hasRole('BILLING_ADMIN');

  useEffect(() => {
    loadPlans();
    loadSubscriptions();
  }, []);

  const loadPlans = async () => {
    try {
      const { data, error } = await supabase
        .from('plans')
        .select('*')
        .order('sort_order');

      if (error) throw error;
      setPlans(data || []);
    } catch (err) {
      console.error('Error loading plans:', err);
      toast({
        title: 'Fehler',
        description: 'Fehler beim Laden der Pläne',
        variant: 'destructive',
      });
    }
  };

  const loadSubscriptions = async () => {
    try {
      const { data, error } = await supabase
        .from('club_subscriptions')
        .select(`
          *,
          clubs!inner(name),
          plans!inner(name)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setSubscriptions(data || []);
    } catch (err) {
      console.error('Error loading subscriptions:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const openPlanDialog = (plan?: Plan) => {
    if (plan) {
      setEditingPlan(plan);
      setPlanForm({
        name: plan.name,
        description: plan.description || '',
        price_monthly: plan.price_monthly?.toString() || '',
        price_yearly: plan.price_yearly?.toString() || '',
        limits: {
          members: plan.limits.members?.toString() || '',
          courts: plan.limits.courts?.toString() || '',
          storage_gb: plan.limits.storage_gb?.toString() || '',
          api_requests: plan.limits.api_requests?.toString() || '',
        },
        features: plan.features.join('\n'),
      });
    } else {
      setEditingPlan(null);
      setPlanForm({
        name: '',
        description: '',
        price_monthly: '',
        price_yearly: '',
        limits: {
          members: '',
          courts: '',
          storage_gb: '',
          api_requests: '',
        },
        features: '',
      });
    }
    setIsPlanDialogOpen(true);
  };

  const savePlan = async () => {
    if (!canManage) return;

    try {
      const planData = {
        name: planForm.name,
        description: planForm.description || null,
        price_monthly: planForm.price_monthly ? parseFloat(planForm.price_monthly) : null,
        price_yearly: planForm.price_yearly ? parseFloat(planForm.price_yearly) : null,
        limits: {
          members: planForm.limits.members ? parseInt(planForm.limits.members) : -1,
          courts: planForm.limits.courts ? parseInt(planForm.limits.courts) : -1,
          storage_gb: planForm.limits.storage_gb ? parseInt(planForm.limits.storage_gb) : 1,
          api_requests: planForm.limits.api_requests ? parseInt(planForm.limits.api_requests) : 1000,
        },
        features: planForm.features.split('\n').filter(f => f.trim()),
      };

      if (editingPlan) {
        const { error } = await supabase
          .from('plans')
          .update(planData)
          .eq('id', editingPlan.id);
        if (error) throw error;
      } else {
        const { error } = await supabase
          .from('plans')
          .insert(planData);
        if (error) throw error;
      }

      toast({
        title: 'Erfolg',
        description: `Plan erfolgreich ${editingPlan ? 'aktualisiert' : 'erstellt'}`,
      });

      setIsPlanDialogOpen(false);
      loadPlans();
    } catch (err) {
      console.error('Error saving plan:', err);
      toast({
        title: 'Fehler',
        description: 'Fehler beim Speichern des Plans',
        variant: 'destructive',
      });
    }
  };

  const togglePlanStatus = async (planId: string, currentStatus: boolean) => {
    if (!canManage) return;

    try {
      const { error } = await supabase
        .from('plans')
        .update({ is_active: !currentStatus })
        .eq('id', planId);

      if (error) throw error;

      toast({
        title: 'Erfolg',
        description: `Plan ${!currentStatus ? 'aktiviert' : 'deaktiviert'}`,
      });

      loadPlans();
    } catch (err) {
      console.error('Error toggling plan status:', err);
      toast({
        title: 'Fehler',
        description: 'Fehler beim Ändern des Plan-Status',
        variant: 'destructive',
      });
    }
  };

  const getSubscriptionStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default">Aktiv</Badge>;
      case 'trialing':
        return <Badge variant="secondary">Testphase</Badge>;
      case 'past_due':
        return <Badge variant="destructive">Überfällig</Badge>;
      case 'canceled':
        return <Badge variant="outline">Gekündigt</Badge>;
      case 'unpaid':
        return <Badge variant="destructive">Unbezahlt</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const formatPrice = (price?: number) => {
    return price ? `€${price.toFixed(2)}` : 'Kostenlos';
  };

  const formatLimits = (limits: any) => {
    const parts = [];
    if (limits.members !== -1) parts.push(`${limits.members} Mitglieder`);
    if (limits.courts !== -1) parts.push(`${limits.courts} Plätze`);
    if (limits.storage_gb) parts.push(`${limits.storage_gb}GB Storage`);
    return parts.join(', ') || 'Unlimited';
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Pläne & Abrechnung</h1>
          <p className="text-muted-foreground">
            Verwalten Sie Preispläne und Abonnements für alle Vereine
          </p>
        </div>
        {canManage && (
          <Button onClick={() => openPlanDialog()}>
            <Plus className="h-4 w-4 mr-2" />
            Neuer Plan
          </Button>
        )}
      </div>

      {/* Plans Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Preispläne
          </CardTitle>
          <CardDescription>
            Verwalten Sie die verfügbaren Preispläne und deren Features
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Plan</TableHead>
                <TableHead>Beschreibung</TableHead>
                <TableHead>Monatlich</TableHead>
                <TableHead>Jährlich</TableHead>
                <TableHead>Limits</TableHead>
                <TableHead>Status</TableHead>
                {canManage && <TableHead>Aktionen</TableHead>}
              </TableRow>
            </TableHeader>
            <TableBody>
              {plans.map((plan) => (
                <TableRow key={plan.id}>
                  <TableCell className="font-medium">{plan.name}</TableCell>
                  <TableCell>{plan.description}</TableCell>
                  <TableCell>{formatPrice(plan.price_monthly)}</TableCell>
                  <TableCell>{formatPrice(plan.price_yearly)}</TableCell>
                  <TableCell className="text-sm">{formatLimits(plan.limits)}</TableCell>
                  <TableCell>
                    <Badge variant={plan.is_active ? 'default' : 'outline'}>
                      {plan.is_active ? 'Aktiv' : 'Inaktiv'}
                    </Badge>
                  </TableCell>
                  {canManage && (
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => openPlanDialog(plan)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => togglePlanStatus(plan.id, plan.is_active)}
                        >
                          {plan.is_active ? 'Deaktivieren' : 'Aktivieren'}
                        </Button>
                      </div>
                    </TableCell>
                  )}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Subscriptions Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Aktive Abonnements
          </CardTitle>
          <CardDescription>
            Übersicht aller Vereinsabonnements und deren Status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Verein</TableHead>
                <TableHead>Plan</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Periode</TableHead>
                <TableHead>Erstellt</TableHead>
                {canManage && <TableHead>Aktionen</TableHead>}
              </TableRow>
            </TableHeader>
            <TableBody>
              {subscriptions.map((subscription) => (
                <TableRow key={subscription.id}>
                  <TableCell className="font-medium">
                    {subscription.clubs?.name || 'Unbekannt'}
                  </TableCell>
                  <TableCell>{subscription.plans?.name || 'Unbekannt'}</TableCell>
                  <TableCell>{getSubscriptionStatusBadge(subscription.status)}</TableCell>
                  <TableCell>
                    {subscription.current_period_start && subscription.current_period_end ? (
                      <span className="text-sm">
                        {new Date(subscription.current_period_start).toLocaleDateString('de-DE')} - {' '}
                        {new Date(subscription.current_period_end).toLocaleDateString('de-DE')}
                      </span>
                    ) : (
                      '-'
                    )}
                  </TableCell>
                  <TableCell>
                    {new Date(subscription.created_at).toLocaleDateString('de-DE')}
                  </TableCell>
                  {canManage && (
                    <TableCell>
                      <Button variant="outline" size="sm">
                        Details
                      </Button>
                    </TableCell>
                  )}
                </TableRow>
              ))}
              {subscriptions.length === 0 && (
                <TableRow>
                  <TableCell colSpan={canManage ? 6 : 5} className="text-center py-8 text-muted-foreground">
                    Keine Abonnements vorhanden
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Plan Creation/Edit Dialog */}
      <Dialog open={isPlanDialogOpen} onOpenChange={setIsPlanDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {editingPlan ? 'Plan bearbeiten' : 'Neuen Plan erstellen'}
            </DialogTitle>
            <DialogDescription>
              Konfigurieren Sie die Details und Limits für diesen Preisplan.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 max-h-96 overflow-y-auto">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Plan Name</Label>
                <Input
                  id="name"
                  value={planForm.name}
                  onChange={(e) => setPlanForm({ ...planForm, name: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="description">Beschreibung</Label>
                <Input
                  id="description"
                  value={planForm.description}
                  onChange={(e) => setPlanForm({ ...planForm, description: e.target.value })}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="monthly">Monatspreis (€)</Label>
                <Input
                  id="monthly"
                  type="number"
                  step="0.01"
                  value={planForm.price_monthly}
                  onChange={(e) => setPlanForm({ ...planForm, price_monthly: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="yearly">Jahrespreis (€)</Label>
                <Input
                  id="yearly"
                  type="number"
                  step="0.01"
                  value={planForm.price_yearly}
                  onChange={(e) => setPlanForm({ ...planForm, price_yearly: e.target.value })}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="members">Max. Mitglieder (-1 für unlimited)</Label>
                <Input
                  id="members"
                  type="number"
                  value={planForm.limits.members}
                  onChange={(e) => setPlanForm({ 
                    ...planForm, 
                    limits: { ...planForm.limits, members: e.target.value }
                  })}
                />
              </div>
              <div>
                <Label htmlFor="courts">Max. Plätze (-1 für unlimited)</Label>
                <Input
                  id="courts"
                  type="number"
                  value={planForm.limits.courts}
                  onChange={(e) => setPlanForm({ 
                    ...planForm, 
                    limits: { ...planForm.limits, courts: e.target.value }
                  })}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="storage">Storage (GB)</Label>
                <Input
                  id="storage"
                  type="number"
                  value={planForm.limits.storage_gb}
                  onChange={(e) => setPlanForm({ 
                    ...planForm, 
                    limits: { ...planForm.limits, storage_gb: e.target.value }
                  })}
                />
              </div>
              <div>
                <Label htmlFor="api">API Requests/Monat</Label>
                <Input
                  id="api"
                  type="number"
                  value={planForm.limits.api_requests}
                  onChange={(e) => setPlanForm({ 
                    ...planForm, 
                    limits: { ...planForm.limits, api_requests: e.target.value }
                  })}
                />
              </div>
            </div>
            <div>
              <Label htmlFor="features">Features (eine pro Zeile)</Label>
              <textarea
                id="features"
                rows={4}
                className="w-full p-2 border border-input rounded-md"
                value={planForm.features}
                onChange={(e) => setPlanForm({ ...planForm, features: e.target.value })}
                placeholder="Buchungsverwaltung&#10;Mitgliederverwaltung&#10;E-Mail Support"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsPlanDialogOpen(false)}>
              Abbrechen
            </Button>
            <Button onClick={savePlan} disabled={!planForm.name}>
              {editingPlan ? 'Aktualisieren' : 'Erstellen'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}