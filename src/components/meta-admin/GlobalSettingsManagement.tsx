import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { useMetaAdminRole } from '@/hooks/useMetaAdminRole';
import { 
  Settings, 
  Save, 
  Globe, 
  Shield, 
  Mail, 
  Database, 
  Palette,
  Clock,
  Key,
  AlertTriangle
} from 'lucide-react';

interface GlobalSettings {
  // Security Settings
  password_min_length: number;
  password_require_symbols: boolean;
  password_require_numbers: boolean;
  password_require_uppercase: boolean;
  two_factor_required: boolean;
  session_timeout_minutes: number;
  max_login_attempts: number;
  
  // System Settings
  maintenance_mode: boolean;
  maintenance_message: string;
  default_timezone: string;
  default_language: string;
  default_currency: string;
  
  // Email Settings
  smtp_host: string;
  smtp_port: number;
  smtp_username: string;
  smtp_from_email: string;
  smtp_from_name: string;
  
  // Legal Settings
  company_name: string;
  company_address: string;
  privacy_policy_url: string;
  terms_of_service_url: string;
  imprint_url: string;
  
  // Feature Flags
  enable_custom_domains: boolean;
  enable_white_label: boolean;
  enable_api_access: boolean;
  enable_webhooks: boolean;
  
  // Branding
  platform_name: string;
  platform_logo_url: string;
  primary_color: string;
  secondary_color: string;
}

export function GlobalSettingsManagement() {
  const { hasRole } = useMetaAdminRole();
  const { toast } = useToast();
  const [settings, setSettings] = useState<GlobalSettings>({
    // Security defaults
    password_min_length: 8,
    password_require_symbols: true,
    password_require_numbers: true,
    password_require_uppercase: true,
    two_factor_required: false,
    session_timeout_minutes: 480,
    max_login_attempts: 5,
    
    // System defaults
    maintenance_mode: false,
    maintenance_message: 'Die Plattform wird gewartet. Bitte versuchen Sie es später erneut.',
    default_timezone: 'Europe/Berlin',
    default_language: 'de',
    default_currency: 'EUR',
    
    // Email defaults
    smtp_host: '',
    smtp_port: 587,
    smtp_username: '',
    smtp_from_email: '<EMAIL>',
    smtp_from_name: 'Tennis Club Platform',
    
    // Legal defaults
    company_name: 'Tennis Club Platform GmbH',
    company_address: '',
    privacy_policy_url: '',
    terms_of_service_url: '',
    imprint_url: '',
    
    // Feature flags
    enable_custom_domains: true,
    enable_white_label: false,
    enable_api_access: true,
    enable_webhooks: true,
    
    // Branding
    platform_name: 'Tennis Club Platform',
    platform_logo_url: '',
    primary_color: '#3b82f6',
    secondary_color: '#1e40af',
  });
  const [isLoading, setIsLoading] = useState(true);
  const [hasChanges, setHasChanges] = useState(false);

  const canManage = hasRole('SUPER_ADMIN');

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      // In a real implementation, this would load from a settings table
      // For now, we use the default values
      setIsLoading(false);
    } catch (err) {
      console.error('Error loading settings:', err);
      toast({
        title: 'Fehler',
        description: 'Fehler beim Laden der Einstellungen',
        variant: 'destructive',
      });
      setIsLoading(false);
    }
  };

  const updateSetting = (key: keyof GlobalSettings, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
    setHasChanges(true);
  };

  const saveSettings = async () => {
    if (!canManage) return;

    try {
      // In a real implementation, this would save to the database
      toast({
        title: 'Erfolg',
        description: 'Einstellungen erfolgreich gespeichert',
      });
      setHasChanges(false);
    } catch (err) {
      console.error('Error saving settings:', err);
      toast({
        title: 'Fehler',
        description: 'Fehler beim Speichern der Einstellungen',
        variant: 'destructive',
      });
    }
  };

  const resetToDefaults = () => {
    if (confirm('Möchten Sie wirklich alle Einstellungen auf die Standardwerte zurücksetzen?')) {
      loadSettings();
      setHasChanges(false);
    }
  };

  if (!canManage) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <Shield className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium">Zugriff verweigert</h3>
          <p className="text-muted-foreground">Nur Super-Admins können globale Einstellungen verwalten.</p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Globale Einstellungen</h1>
          <p className="text-muted-foreground">
            Plattformweite Konfiguration und Richtlinien
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={resetToDefaults}>
            Zurücksetzen
          </Button>
          <Button onClick={saveSettings} disabled={!hasChanges}>
            <Save className="h-4 w-4 mr-2" />
            Speichern
          </Button>
        </div>
      </div>

      {hasChanges && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-yellow-800">
              <AlertTriangle className="h-4 w-4" />
              <span className="text-sm">Sie haben ungespeicherte Änderungen.</span>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Security Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Sicherheitseinstellungen
            </CardTitle>
            <CardDescription>
              Passwort-Richtlinien und Authentifizierung
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="password-min-length">Minimale Passwort-Länge</Label>
              <Input
                id="password-min-length"
                type="number"
                value={settings.password_min_length}
                onChange={(e) => updateSetting('password_min_length', parseInt(e.target.value))}
              />
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label htmlFor="require-symbols">Sonderzeichen erforderlich</Label>
                <Switch
                  id="require-symbols"
                  checked={settings.password_require_symbols}
                  onCheckedChange={(checked) => updateSetting('password_require_symbols', checked)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <Label htmlFor="require-numbers">Zahlen erforderlich</Label>
                <Switch
                  id="require-numbers"
                  checked={settings.password_require_numbers}
                  onCheckedChange={(checked) => updateSetting('password_require_numbers', checked)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <Label htmlFor="require-uppercase">Großbuchstaben erforderlich</Label>
                <Switch
                  id="require-uppercase"
                  checked={settings.password_require_uppercase}
                  onCheckedChange={(checked) => updateSetting('password_require_uppercase', checked)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <Label htmlFor="two-factor-required">2FA Pflicht</Label>
                <Switch
                  id="two-factor-required"
                  checked={settings.two_factor_required}
                  onCheckedChange={(checked) => updateSetting('two_factor_required', checked)}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="session-timeout">Session Timeout (Min.)</Label>
                <Input
                  id="session-timeout"
                  type="number"
                  value={settings.session_timeout_minutes}
                  onChange={(e) => updateSetting('session_timeout_minutes', parseInt(e.target.value))}
                />
              </div>
              <div>
                <Label htmlFor="max-login-attempts">Max. Login-Versuche</Label>
                <Input
                  id="max-login-attempts"
                  type="number"
                  value={settings.max_login_attempts}
                  onChange={(e) => updateSetting('max_login_attempts', parseInt(e.target.value))}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* System Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              System-Einstellungen
            </CardTitle>
            <CardDescription>
              Grundkonfiguration der Plattform
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="maintenance-mode">Wartungsmodus</Label>
                <p className="text-xs text-muted-foreground">Deaktiviert Zugriff für normale Benutzer</p>
              </div>
              <Switch
                id="maintenance-mode"
                checked={settings.maintenance_mode}
                onCheckedChange={(checked) => updateSetting('maintenance_mode', checked)}
              />
            </div>

            <div>
              <Label htmlFor="maintenance-message">Wartungsmeldung</Label>
              <Textarea
                id="maintenance-message"
                value={settings.maintenance_message}
                onChange={(e) => updateSetting('maintenance_message', e.target.value)}
                rows={3}
              />
            </div>

            <div className="grid grid-cols-1 gap-4">
              <div>
                <Label htmlFor="default-timezone">Standard-Zeitzone</Label>
                <Select 
                  value={settings.default_timezone} 
                  onValueChange={(value) => updateSetting('default_timezone', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Europe/Berlin">Europe/Berlin</SelectItem>
                    <SelectItem value="Europe/Vienna">Europe/Vienna</SelectItem>
                    <SelectItem value="Europe/Zurich">Europe/Zurich</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="default-language">Standard-Sprache</Label>
                <Select 
                  value={settings.default_language} 
                  onValueChange={(value) => updateSetting('default_language', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="de">Deutsch</SelectItem>
                    <SelectItem value="en">English</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="default-currency">Standard-Währung</Label>
                <Select 
                  value={settings.default_currency} 
                  onValueChange={(value) => updateSetting('default_currency', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="EUR">EUR</SelectItem>
                    <SelectItem value="CHF">CHF</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Email Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5" />
              E-Mail Einstellungen
            </CardTitle>
            <CardDescription>
              SMTP-Konfiguration für System-E-Mails
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="smtp-host">SMTP Host</Label>
                <Input
                  id="smtp-host"
                  value={settings.smtp_host}
                  onChange={(e) => updateSetting('smtp_host', e.target.value)}
                  placeholder="smtp.example.com"
                />
              </div>
              <div>
                <Label htmlFor="smtp-port">SMTP Port</Label>
                <Input
                  id="smtp-port"
                  type="number"
                  value={settings.smtp_port}
                  onChange={(e) => updateSetting('smtp_port', parseInt(e.target.value))}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="smtp-username">SMTP Benutzername</Label>
              <Input
                id="smtp-username"
                value={settings.smtp_username}
                onChange={(e) => updateSetting('smtp_username', e.target.value)}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="smtp-from-email">Absender E-Mail</Label>
                <Input
                  id="smtp-from-email"
                  type="email"
                  value={settings.smtp_from_email}
                  onChange={(e) => updateSetting('smtp_from_email', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="smtp-from-name">Absender Name</Label>
                <Input
                  id="smtp-from-name"
                  value={settings.smtp_from_name}
                  onChange={(e) => updateSetting('smtp_from_name', e.target.value)}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Feature Flags */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Key className="h-5 w-5" />
              Feature-Einstellungen
            </CardTitle>
            <CardDescription>
              Aktivieren oder deaktivieren Sie Plattform-Features
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="enable-custom-domains">Custom Domains</Label>
                  <p className="text-xs text-muted-foreground">Erlaubt Vereinen eigene Domains</p>
                </div>
                <Switch
                  id="enable-custom-domains"
                  checked={settings.enable_custom_domains}
                  onCheckedChange={(checked) => updateSetting('enable_custom_domains', checked)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="enable-white-label">White Label</Label>
                  <p className="text-xs text-muted-foreground">Versteckt Plattform-Branding</p>
                </div>
                <Switch
                  id="enable-white-label"
                  checked={settings.enable_white_label}
                  onCheckedChange={(checked) => updateSetting('enable_white_label', checked)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="enable-api-access">API Zugang</Label>
                  <p className="text-xs text-muted-foreground">Aktiviert REST API für Vereine</p>
                </div>
                <Switch
                  id="enable-api-access"
                  checked={settings.enable_api_access}
                  onCheckedChange={(checked) => updateSetting('enable_api_access', checked)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="enable-webhooks">Webhooks</Label>
                  <p className="text-xs text-muted-foreground">Erlaubt Webhook-Konfiguration</p>
                </div>
                <Switch
                  id="enable-webhooks"
                  checked={settings.enable_webhooks}
                  onCheckedChange={(checked) => updateSetting('enable_webhooks', checked)}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Legal Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Rechtliche Angaben
          </CardTitle>
          <CardDescription>
            Impressum und rechtliche Links für die Hauptdomain
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="company-name">Firmenname</Label>
              <Input
                id="company-name"
                value={settings.company_name}
                onChange={(e) => updateSetting('company_name', e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="company-address">Firmenadresse</Label>
              <Textarea
                id="company-address"
                value={settings.company_address}
                onChange={(e) => updateSetting('company_address', e.target.value)}
                rows={3}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="privacy-policy-url">Datenschutz URL</Label>
              <Input
                id="privacy-policy-url"
                value={settings.privacy_policy_url}
                onChange={(e) => updateSetting('privacy_policy_url', e.target.value)}
                placeholder="https://example.com/privacy"
              />
            </div>
            <div>
              <Label htmlFor="terms-url">AGB URL</Label>
              <Input
                id="terms-url"
                value={settings.terms_of_service_url}
                onChange={(e) => updateSetting('terms_of_service_url', e.target.value)}
                placeholder="https://example.com/terms"
              />
            </div>
            <div>
              <Label htmlFor="imprint-url">Impressum URL</Label>
              <Input
                id="imprint-url"
                value={settings.imprint_url}
                onChange={(e) => updateSetting('imprint_url', e.target.value)}
                placeholder="https://example.com/imprint"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Platform Branding */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5" />
            Plattform-Branding
          </CardTitle>
          <CardDescription>
            Hauptbranding für die Meta-Admin Oberfläche
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="platform-name">Plattform Name</Label>
              <Input
                id="platform-name"
                value={settings.platform_name}
                onChange={(e) => updateSetting('platform_name', e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="platform-logo-url">Logo URL</Label>
              <Input
                id="platform-logo-url"
                value={settings.platform_logo_url}
                onChange={(e) => updateSetting('platform_logo_url', e.target.value)}
                placeholder="https://example.com/logo.png"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="primary-color-global">Primärfarbe</Label>
              <div className="flex gap-2">
                <Input
                  id="primary-color-global"
                  type="color"
                  value={settings.primary_color}
                  onChange={(e) => updateSetting('primary_color', e.target.value)}
                  className="w-16"
                />
                <Input
                  value={settings.primary_color}
                  onChange={(e) => updateSetting('primary_color', e.target.value)}
                  className="flex-1"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="secondary-color-global">Sekundärfarbe</Label>
              <div className="flex gap-2">
                <Input
                  id="secondary-color-global"
                  type="color"
                  value={settings.secondary_color}
                  onChange={(e) => updateSetting('secondary_color', e.target.value)}
                  className="w-16"
                />
                <Input
                  value={settings.secondary_color}
                  onChange={(e) => updateSetting('secondary_color', e.target.value)}
                  className="flex-1"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}