import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useMetaAdminRole, type MetaAdminRole, type MetaAdminPermission } from '@/hooks/useMetaAdminRole';
import { UserPlus, Shield, Trash2, Calendar } from 'lucide-react';

interface User {
  id: string;
  email?: string;
  user_metadata?: {
    full_name?: string;
    first_name?: string;
    last_name?: string;
  };
}

export function MetaAdminRoleManagement() {
  const { user } = useAuth();
  const { hasRole } = useMetaAdminRole();
  const { toast } = useToast();
  const [permissions, setPermissions] = useState<MetaAdminPermission[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedUserId, setSelectedUserId] = useState<string>('');
  const [selectedRole, setSelectedRole] = useState<MetaAdminRole>('READONLY');
  const [expiresInDays, setExpiresInDays] = useState<string>('');

  // Only super admins can manage roles
  const canManage = hasRole('SUPER_ADMIN');

  useEffect(() => {
    loadPermissions();
    loadUsers();
  }, []);

  const loadPermissions = async () => {
    try {
      const { data, error } = await supabase
        .from('meta_admin_permissions')
        .select(`
          *,
          profiles!inner(first_name, last_name, email)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setPermissions(data || []);
    } catch (err) {
      console.error('Error loading permissions:', err);
      toast({
        title: 'Fehler',
        description: 'Fehler beim Laden der Berechtigungen',
        variant: 'destructive',
      });
    }
  };

  const loadUsers = async () => {
    try {
      // Load users from auth.users via profiles table
      const { data, error } = await supabase
        .from('profiles')
        .select('id, first_name, last_name, email');

      if (error) throw error;
      
      const formattedUsers = data?.map(profile => ({
        id: profile.id,
        email: profile.email,
        user_metadata: {
          first_name: profile.first_name,
          last_name: profile.last_name,
          full_name: `${profile.first_name} ${profile.last_name}`,
        }
      })) || [];

      setUsers(formattedUsers);
    } catch (err) {
      console.error('Error loading users:', err);
      toast({
        title: 'Fehler',
        description: 'Fehler beim Laden der Benutzer',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const grantRole = async () => {
    if (!selectedUserId || !selectedRole || !user) return;

    try {
      let expiresAt = null;
      if (expiresInDays) {
        const days = parseInt(expiresInDays);
        if (days > 0) {
          expiresAt = new Date(Date.now() + days * 24 * 60 * 60 * 1000).toISOString();
        }
      }

      const { error } = await supabase
        .from('meta_admin_permissions')
        .insert({
          user_id: selectedUserId,
          role: selectedRole,
          granted_by: user.id,
          expires_at: expiresAt,
        });

      if (error) throw error;

      toast({
        title: 'Erfolg',
        description: 'Rolle erfolgreich zugewiesen',
      });

      setIsDialogOpen(false);
      setSelectedUserId('');
      setSelectedRole('READONLY');
      setExpiresInDays('');
      loadPermissions();
    } catch (err) {
      console.error('Error granting role:', err);
      toast({
        title: 'Fehler',
        description: 'Fehler beim Zuweisen der Rolle',
        variant: 'destructive',
      });
    }
  };

  const revokeRole = async (permissionId: string) => {
    try {
      const { error } = await supabase
        .from('meta_admin_permissions')
        .delete()
        .eq('id', permissionId);

      if (error) throw error;

      toast({
        title: 'Erfolg',
        description: 'Rolle erfolgreich entzogen',
      });

      loadPermissions();
    } catch (err) {
      console.error('Error revoking role:', err);
      toast({
        title: 'Fehler',
        description: 'Fehler beim Entziehen der Rolle',
        variant: 'destructive',
      });
    }
  };

  const getRoleBadgeVariant = (role: MetaAdminRole) => {
    switch (role) {
      case 'SUPER_ADMIN':
        return 'destructive';
      case 'SUPPORT_ADMIN':
        return 'default';
      case 'BILLING_ADMIN':
        return 'secondary';
      case 'READONLY':
        return 'outline';
      default:
        return 'outline';
    }
  };

  const getUserDisplayName = (userId: string) => {
    const permission = permissions.find(p => p.user_id === userId);
    if (permission && 'profiles' in permission) {
      const profile = permission.profiles as any;
      return `${profile.first_name} ${profile.last_name}`;
    }
    
    const user = users.find(u => u.id === userId);
    return user?.user_metadata?.full_name || user?.email || 'Unbekannt';
  };

  const getUserEmail = (userId: string) => {
    const permission = permissions.find(p => p.user_id === userId);
    if (permission && 'profiles' in permission) {
      const profile = permission.profiles as any;
      return profile.email;
    }
    
    const user = users.find(u => u.id === userId);
    return user?.email || '';
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Meta-Admin Rollen</h1>
          <p className="text-muted-foreground">
            Verwalten Sie die Zugriffsrechte für das Meta-Admin Interface
          </p>
        </div>
        {canManage && (
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <UserPlus className="h-4 w-4 mr-2" />
                Rolle zuweisen
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Meta-Admin Rolle zuweisen</DialogTitle>
                <DialogDescription>
                  Weisen Sie einem Benutzer eine Meta-Admin Rolle zu. Diese Berechtigung kann optional zeitlich begrenzt werden.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="user">Benutzer</Label>
                  <Select value={selectedUserId} onValueChange={setSelectedUserId}>
                    <SelectTrigger>
                      <SelectValue placeholder="Benutzer auswählen" />
                    </SelectTrigger>
                    <SelectContent>
                      {users.map(user => (
                        <SelectItem key={user.id} value={user.id}>
                          {user.user_metadata?.full_name || user.email}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="role">Rolle</Label>
                  <Select value={selectedRole} onValueChange={(value) => setSelectedRole(value as MetaAdminRole)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="SUPER_ADMIN">Super Admin (Vollzugriff)</SelectItem>
                      <SelectItem value="SUPPORT_ADMIN">Support Admin (ohne Billing)</SelectItem>
                      <SelectItem value="BILLING_ADMIN">Billing Admin (nur Abrechnung)</SelectItem>
                      <SelectItem value="READONLY">Readonly (nur Lesen)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="expires">Gültig für (Tage, optional)</Label>
                  <Input
                    id="expires"
                    type="number"
                    placeholder="z.B. 30 für 30 Tage"
                    value={expiresInDays}
                    onChange={(e) => setExpiresInDays(e.target.value)}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Abbrechen
                </Button>
                <Button onClick={grantRole} disabled={!selectedUserId || !selectedRole}>
                  Rolle zuweisen
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Aktive Meta-Admin Berechtigungen
          </CardTitle>
          <CardDescription>
            Übersicht aller aktiven Meta-Admin Rollen und deren Gültigkeitsdauer
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Benutzer</TableHead>
                <TableHead>E-Mail</TableHead>
                <TableHead>Rolle</TableHead>
                <TableHead>Zugewiesen am</TableHead>
                <TableHead>Läuft ab</TableHead>
                {canManage && <TableHead>Aktionen</TableHead>}
              </TableRow>
            </TableHeader>
            <TableBody>
              {permissions.map((permission) => (
                <TableRow key={permission.id}>
                  <TableCell className="font-medium">
                    {getUserDisplayName(permission.user_id)}
                  </TableCell>
                  <TableCell>{getUserEmail(permission.user_id)}</TableCell>
                  <TableCell>
                    <Badge variant={getRoleBadgeVariant(permission.role)}>
                      {permission.role}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      {new Date(permission.granted_at).toLocaleDateString('de-DE')}
                    </div>
                  </TableCell>
                  <TableCell>
                    {permission.expires_at ? (
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        {new Date(permission.expires_at).toLocaleDateString('de-DE')}
                      </div>
                    ) : (
                      <Badge variant="outline">Unbegrenzt</Badge>
                    )}
                  </TableCell>
                  {canManage && (
                    <TableCell>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="outline" size="sm">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Rolle entziehen</AlertDialogTitle>
                            <AlertDialogDescription>
                              Sind Sie sicher, dass Sie diese Rolle entziehen möchten? Diese Aktion kann nicht rückgängig gemacht werden.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Abbrechen</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => revokeRole(permission.id)}
                              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                            >
                              Rolle entziehen
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </TableCell>
                  )}
                </TableRow>
              ))}
              {permissions.length === 0 && (
                <TableRow>
                  <TableCell colSpan={canManage ? 6 : 5} className="text-center py-8 text-muted-foreground">
                    Keine Meta-Admin Berechtigungen vorhanden
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}