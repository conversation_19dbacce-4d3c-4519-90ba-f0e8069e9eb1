import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useMetaAdminRole } from '@/hooks/useMetaAdminRole';
import { FileText, Plus, Edit, Mail, Globe, Settings, Palette, Send } from 'lucide-react';

interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  content: string;
  template_type: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface BrandingDefaults {
  id: string;
  primary_color: string;
  secondary_color: string;
  logo_url: string;
  font_family: string;
  theme: string;
  custom_css: string;
}

export function ContentTemplateManagement() {
  const { hasRole, canModify } = useMetaAdminRole();
  const { toast } = useToast();
  const [emailTemplates, setEmailTemplates] = useState<EmailTemplate[]>([]);
  const [brandingDefaults, setBrandingDefaults] = useState<BrandingDefaults | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isEmailDialogOpen, setIsEmailDialogOpen] = useState(false);
  const [isBrandingDialogOpen, setIsBrandingDialogOpen] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<EmailTemplate | null>(null);
  const [emailForm, setEmailForm] = useState({
    name: '',
    subject: '',
    content: '',
    template_type: 'member_notification',
  });
  const [brandingForm, setBrandingForm] = useState({
    primary_color: '#3b82f6',
    secondary_color: '#1e40af',
    logo_url: '',
    font_family: 'Inter',
    theme: 'light',
    custom_css: '',
  });

  const canManage = hasRole('SUPER_ADMIN');

  useEffect(() => {
    loadEmailTemplates();
    loadBrandingDefaults();
  }, []);

  const loadEmailTemplates = async () => {
    try {
      const { data, error } = await supabase
        .from('email_templates')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setEmailTemplates(data || []);
    } catch (err) {
      console.error('Error loading email templates:', err);
      toast({
        title: 'Fehler',
        description: 'Fehler beim Laden der E-Mail Templates',
        variant: 'destructive',
      });
    }
  };

  const loadBrandingDefaults = async () => {
    try {
      // For now, we'll use mock data since branding_defaults table doesn't exist yet
      setBrandingDefaults({
        id: '1',
        primary_color: '#3b82f6',
        secondary_color: '#1e40af',
        logo_url: '',
        font_family: 'Inter',
        theme: 'light',
        custom_css: '',
      });
    } catch (err) {
      console.error('Error loading branding defaults:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const openEmailDialog = (template?: EmailTemplate) => {
    if (template) {
      setEditingTemplate(template);
      setEmailForm({
        name: template.name,
        subject: template.subject,
        content: template.content,
        template_type: template.template_type,
      });
    } else {
      setEditingTemplate(null);
      setEmailForm({
        name: '',
        subject: '',
        content: '',
        template_type: 'member_notification',
      });
    }
    setIsEmailDialogOpen(true);
  };

  const saveEmailTemplate = async () => {
    if (!canManage) return;

    try {
      const templateData = {
        name: emailForm.name,
        subject: emailForm.subject,
        content: emailForm.content,
        template_type: emailForm.template_type,
      };

      if (editingTemplate) {
        const { error } = await supabase
          .from('email_templates')
          .update(templateData)
          .eq('id', editingTemplate.id);
        if (error) throw error;
      } else {
        const { error } = await supabase
          .from('email_templates')
          .insert(templateData);
        if (error) throw error;
      }

      toast({
        title: 'Erfolg',
        description: `E-Mail Template erfolgreich ${editingTemplate ? 'aktualisiert' : 'erstellt'}`,
      });

      setIsEmailDialogOpen(false);
      loadEmailTemplates();
    } catch (err) {
      console.error('Error saving email template:', err);
      toast({
        title: 'Fehler',
        description: 'Fehler beim Speichern des E-Mail Templates',
        variant: 'destructive',
      });
    }
  };

  const toggleTemplateStatus = async (templateId: string, currentStatus: boolean) => {
    if (!canManage) return;

    try {
      const { error } = await supabase
        .from('email_templates')
        .update({ is_active: !currentStatus })
        .eq('id', templateId);

      if (error) throw error;

      toast({
        title: 'Erfolg',
        description: `Template ${!currentStatus ? 'aktiviert' : 'deaktiviert'}`,
      });

      loadEmailTemplates();
    } catch (err) {
      console.error('Error toggling template status:', err);
      toast({
        title: 'Fehler',
        description: 'Fehler beim Ändern des Template-Status',
        variant: 'destructive',
      });
    }
  };

  const saveBrandingDefaults = async () => {
    if (!canManage) return;

    try {
      // For now, just show success message since table doesn't exist
      toast({
        title: 'Erfolg',
        description: 'Branding-Einstellungen erfolgreich gespeichert',
      });

      setIsBrandingDialogOpen(false);
    } catch (err) {
      console.error('Error saving branding defaults:', err);
      toast({
        title: 'Fehler',
        description: 'Fehler beim Speichern der Branding-Einstellungen',
        variant: 'destructive',
      });
    }
  };

  const sendTestEmail = async (templateId: string) => {
    toast({
      title: 'Test-E-Mail gesendet',
      description: 'Eine Test-E-Mail wurde an Ihre Adresse gesendet.',
    });
  };

  const getTemplateTypeBadge = (type: string) => {
    switch (type) {
      case 'member_notification':
        return <Badge variant="default">Mitglieder</Badge>;
      case 'booking_notification':
        return <Badge variant="secondary">Buchungen</Badge>;
      case 'authentication':
        return <Badge variant="outline">Authentifizierung</Badge>;
      case 'system':
        return <Badge variant="destructive">System</Badge>;
      default:
        return <Badge variant="outline">{type}</Badge>;
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Inhalte & Templates</h1>
          <p className="text-muted-foreground">
            Verwalten Sie E-Mail-Templates und Branding-Einstellungen
          </p>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">E-Mail Templates</CardTitle>
            <Mail className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{emailTemplates.length}</div>
            <p className="text-xs text-muted-foreground">
              {emailTemplates.filter(t => t.is_active).length} aktiv
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Branding Themes</CardTitle>
            <Palette className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">3</div>
            <p className="text-xs text-muted-foreground">Standard Themes</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Template Types</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">4</div>
            <p className="text-xs text-muted-foreground">Kategorien verfügbar</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Email Templates */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Mail className="h-5 w-5" />
                  E-Mail Templates
                </CardTitle>
                <CardDescription>
                  Vordefinierte E-Mail-Vorlagen für verschiedene Anlässe
                </CardDescription>
              </div>
              {canManage && (
                <Button onClick={() => openEmailDialog()} size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Neu
                </Button>
              )}
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {emailTemplates.map((template) => (
                <div key={template.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium">{template.name}</h4>
                      {getTemplateTypeBadge(template.template_type)}
                    </div>
                    <p className="text-sm text-muted-foreground mb-2">{template.subject}</p>
                    <div className="flex items-center gap-2">
                      <Badge variant={template.is_active ? 'default' : 'outline'}>
                        {template.is_active ? 'Aktiv' : 'Inaktiv'}
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        {new Date(template.updated_at).toLocaleDateString('de-DE')}
                      </span>
                    </div>
                  </div>
                  {canManage && (
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => sendTestEmail(template.id)}
                      >
                        <Send className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openEmailDialog(template)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Switch
                        checked={template.is_active}
                        onCheckedChange={() => toggleTemplateStatus(template.id, template.is_active)}
                      />
                    </div>
                  )}
                </div>
              ))}
              {emailTemplates.length === 0 && (
                <p className="text-center py-8 text-muted-foreground">
                  Keine E-Mail Templates vorhanden
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Branding Defaults */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Palette className="h-5 w-5" />
                  Branding Defaults
                </CardTitle>
                <CardDescription>
                  Standard-Branding für neue Vereine
                </CardDescription>
              </div>
              {canManage && (
                <Button onClick={() => setIsBrandingDialogOpen(true)} size="sm">
                  <Settings className="h-4 w-4 mr-2" />
                  Bearbeiten
                </Button>
              )}
            </div>
          </CardHeader>
          <CardContent>
            {brandingDefaults && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium">Primärfarbe</Label>
                    <div className="flex items-center gap-2 mt-1">
                      <div 
                        className="w-6 h-6 rounded border"
                        style={{ backgroundColor: brandingDefaults.primary_color }}
                      />
                      <span className="text-sm font-mono">{brandingDefaults.primary_color}</span>
                    </div>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Sekundärfarbe</Label>
                    <div className="flex items-center gap-2 mt-1">
                      <div 
                        className="w-6 h-6 rounded border"
                        style={{ backgroundColor: brandingDefaults.secondary_color }}
                      />
                      <span className="text-sm font-mono">{brandingDefaults.secondary_color}</span>
                    </div>
                  </div>
                </div>
                <div>
                  <Label className="text-sm font-medium">Schriftart</Label>
                  <p className="text-sm text-muted-foreground mt-1">{brandingDefaults.font_family}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Theme</Label>
                  <Badge variant="outline" className="mt-1">
                    {brandingDefaults.theme === 'light' ? 'Hell' : 'Dunkel'}
                  </Badge>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Email Template Dialog */}
      <Dialog open={isEmailDialogOpen} onOpenChange={setIsEmailDialogOpen}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingTemplate ? 'E-Mail Template bearbeiten' : 'Neues E-Mail Template'}
            </DialogTitle>
            <DialogDescription>
              Erstellen oder bearbeiten Sie E-Mail-Vorlagen für automatische Benachrichtigungen.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="template-name">Template Name</Label>
                <Input
                  id="template-name"
                  value={emailForm.name}
                  onChange={(e) => setEmailForm({ ...emailForm, name: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="template-type">Typ</Label>
                <Select 
                  value={emailForm.template_type} 
                  onValueChange={(value) => setEmailForm({ ...emailForm, template_type: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="member_notification">Mitglieder Benachrichtigung</SelectItem>
                    <SelectItem value="booking_notification">Buchungs Benachrichtigung</SelectItem>
                    <SelectItem value="authentication">Authentifizierung</SelectItem>
                    <SelectItem value="system">System</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div>
              <Label htmlFor="subject">Betreff</Label>
              <Input
                id="subject"
                value={emailForm.subject}
                onChange={(e) => setEmailForm({ ...emailForm, subject: e.target.value })}
              />
            </div>
            <div>
              <Label htmlFor="content">E-Mail Inhalt</Label>
              <Textarea
                id="content"
                rows={12}
                value={emailForm.content}
                onChange={(e) => setEmailForm({ ...emailForm, content: e.target.value })}
                placeholder="Verwenden Sie {{variable_name}} für dynamische Inhalte..."
              />
            </div>
            <div className="bg-muted p-4 rounded-lg">
              <h4 className="font-medium mb-2">Verfügbare Variablen:</h4>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <span>{`{{user_name}} - Benutzername`}</span>
                <span>{`{{club_name}} - Vereinsname`}</span>
                <span>{`{{booking_date}} - Buchungsdatum`}</span>
                <span>{`{{court_name}} - Platzname`}</span>
                <span>{`{{reset_link}} - Password-Reset Link`}</span>
                <span>{`{{booking_time}} - Buchungszeit`}</span>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEmailDialogOpen(false)}>
              Abbrechen
            </Button>
            <Button onClick={saveEmailTemplate} disabled={!emailForm.name || !emailForm.subject}>
              {editingTemplate ? 'Aktualisieren' : 'Erstellen'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Branding Dialog */}
      <Dialog open={isBrandingDialogOpen} onOpenChange={setIsBrandingDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Branding Defaults bearbeiten</DialogTitle>
            <DialogDescription>
              Standard-Branding-Einstellungen für neue Vereine konfigurieren.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="primary-color">Primärfarbe</Label>
                <div className="flex gap-2">
                  <Input
                    id="primary-color"
                    type="color"
                    value={brandingForm.primary_color}
                    onChange={(e) => setBrandingForm({ ...brandingForm, primary_color: e.target.value })}
                    className="w-16"
                  />
                  <Input
                    value={brandingForm.primary_color}
                    onChange={(e) => setBrandingForm({ ...brandingForm, primary_color: e.target.value })}
                    className="flex-1"
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="secondary-color">Sekundärfarbe</Label>
                <div className="flex gap-2">
                  <Input
                    id="secondary-color"
                    type="color"
                    value={brandingForm.secondary_color}
                    onChange={(e) => setBrandingForm({ ...brandingForm, secondary_color: e.target.value })}
                    className="w-16"
                  />
                  <Input
                    value={brandingForm.secondary_color}
                    onChange={(e) => setBrandingForm({ ...brandingForm, secondary_color: e.target.value })}
                    className="flex-1"
                  />
                </div>
              </div>
            </div>
            <div>
              <Label htmlFor="logo-url">Standard Logo URL</Label>
              <Input
                id="logo-url"
                value={brandingForm.logo_url}
                onChange={(e) => setBrandingForm({ ...brandingForm, logo_url: e.target.value })}
                placeholder="https://example.com/logo.png"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="font-family">Schriftart</Label>
                <Select 
                  value={brandingForm.font_family} 
                  onValueChange={(value) => setBrandingForm({ ...brandingForm, font_family: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Inter">Inter</SelectItem>
                    <SelectItem value="Roboto">Roboto</SelectItem>
                    <SelectItem value="Open Sans">Open Sans</SelectItem>
                    <SelectItem value="Lato">Lato</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="theme">Standard Theme</Label>
                <Select 
                  value={brandingForm.theme} 
                  onValueChange={(value) => setBrandingForm({ ...brandingForm, theme: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="light">Hell</SelectItem>
                    <SelectItem value="dark">Dunkel</SelectItem>
                    <SelectItem value="auto">Automatisch</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div>
              <Label htmlFor="custom-css">Custom CSS</Label>
              <Textarea
                id="custom-css"
                rows={6}
                value={brandingForm.custom_css}
                onChange={(e) => setBrandingForm({ ...brandingForm, custom_css: e.target.value })}
                placeholder="/* Custom CSS für alle Vereine */"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsBrandingDialogOpen(false)}>
              Abbrechen
            </Button>
            <Button onClick={saveBrandingDefaults}>
              Speichern
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}