import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useMetaAdminRole } from '@/hooks/useMetaAdminRole';
import { 
  Building2, 
  Plus, 
  Edit, 
  Trash2, 
  Users, 
  Globe, 
  Pause, 
  Play, 
  Download,
  Upload,
  Settings,
  Calendar,
  MapPin,
  Mail,
  Phone
} from 'lucide-react';

interface Club {
  id: string;
  name: string;
  description?: string;
  slug: string;
  subdomain?: string;
  custom_domain?: string;
  logo_url?: string;
  is_active: boolean;
  settings: any;
  created_at: string;
  updated_at: string;
}

interface ClubStats {
  members: number;
  bookings: number;
  courts: number;
}

export function ExtendedClubManagement() {
  const { hasRole, canModify } = useMetaAdminRole();
  const { toast } = useToast();
  const [clubs, setClubs] = useState<Club[]>([]);
  const [clubStats, setClubStats] = useState<Record<string, ClubStats>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingClub, setEditingClub] = useState<Club | null>(null);
  const [clubForm, setClubForm] = useState({
    name: '',
    description: '',
    slug: '',
    subdomain: '',
    custom_domain: '',
    logo_url: '',
    settings: {
      timezone: 'Europe/Berlin',
      currency: 'EUR',
      language: 'de',
      theme: 'light',
      contact_email: '',
      contact_phone: '',
      address: '',
      booking_advance_days: 14,
      cancellation_hours: 24,
    }
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  const canManage = hasRole('SUPER_ADMIN');

  useEffect(() => {
    loadClubs();
  }, []);

  const loadClubs = async () => {
    try {
      let query = supabase
        .from('clubs')
        .select('*')
        .order('created_at', { ascending: false });

      if (statusFilter !== 'all') {
        query = query.eq('is_active', statusFilter === 'active');
      }

      const { data, error } = await query;
      if (error) throw error;

      const clubsData = data || [];
      setClubs(clubsData);

      // Load stats for each club
      const statsPromises = clubsData.map(async (club) => {
        const [membersResult, bookingsResult, courtsResult] = await Promise.all([
          supabase.from('profiles').select('*', { count: 'exact', head: true }),
          supabase.from('bookings').select('*', { count: 'exact', head: true }).eq('club_id', club.id),
          supabase.from('courts').select('*', { count: 'exact', head: true }).eq('club_id', club.id),
        ]);

        return {
          clubId: club.id,
          stats: {
            members: membersResult.count || 0,
            bookings: bookingsResult.count || 0,
            courts: courtsResult.count || 0,
          }
        };
      });

      const statsResults = await Promise.all(statsPromises);
      const statsMap = statsResults.reduce((acc, { clubId, stats }) => {
        acc[clubId] = stats;
        return acc;
      }, {} as Record<string, ClubStats>);

      setClubStats(statsMap);
    } catch (err) {
      console.error('Error loading clubs:', err);
      toast({
        title: 'Fehler',
        description: 'Fehler beim Laden der Vereine',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const openClubDialog = (club?: Club) => {
    if (club) {
      setEditingClub(club);
      setClubForm({
        name: club.name,
        description: club.description || '',
        slug: club.slug,
        subdomain: club.subdomain || '',
        custom_domain: club.custom_domain || '',
        logo_url: club.logo_url || '',
        settings: {
          ...clubForm.settings,
          ...club.settings,
        }
      });
    } else {
      setEditingClub(null);
      setClubForm({
        name: '',
        description: '',
        slug: '',
        subdomain: '',
        custom_domain: '',
        logo_url: '',
        settings: {
          timezone: 'Europe/Berlin',
          currency: 'EUR',
          language: 'de',
          theme: 'light',
          contact_email: '',
          contact_phone: '',
          address: '',
          booking_advance_days: 14,
          cancellation_hours: 24,
        }
      });
    }
    setIsDialogOpen(true);
  };

  const saveClub = async () => {
    if (!canManage) return;

    try {
      const clubData = {
        name: clubForm.name,
        description: clubForm.description || null,
        slug: clubForm.slug.toLowerCase(),
        subdomain: clubForm.subdomain || null,
        custom_domain: clubForm.custom_domain || null,
        logo_url: clubForm.logo_url || null,
        settings: clubForm.settings,
      };

      if (editingClub) {
        const { error } = await supabase
          .from('clubs')
          .update(clubData)
          .eq('id', editingClub.id);
        if (error) throw error;
      } else {
        const { error } = await supabase
          .from('clubs')
          .insert(clubData);
        if (error) throw error;
      }

      toast({
        title: 'Erfolg',
        description: `Verein erfolgreich ${editingClub ? 'aktualisiert' : 'erstellt'}`,
      });

      setIsDialogOpen(false);
      loadClubs();
    } catch (err) {
      console.error('Error saving club:', err);
      toast({
        title: 'Fehler',
        description: 'Fehler beim Speichern des Vereins',
        variant: 'destructive',
      });
    }
  };

  const toggleClubStatus = async (clubId: string, currentStatus: boolean) => {
    if (!canManage) return;

    try {
      const { error } = await supabase
        .from('clubs')
        .update({ is_active: !currentStatus })
        .eq('id', clubId);

      if (error) throw error;

      toast({
        title: 'Erfolg',
        description: `Verein ${!currentStatus ? 'aktiviert' : 'deaktiviert'}`,
      });

      loadClubs();
    } catch (err) {
      console.error('Error toggling club status:', err);
      toast({
        title: 'Fehler',
        description: 'Fehler beim Ändern des Vereinsstatus',
        variant: 'destructive',
      });
    }
  };

  const deleteClub = async (clubId: string) => {
    if (!canManage) return;

    try {
      const { error } = await supabase
        .from('clubs')
        .delete()
        .eq('id', clubId);

      if (error) throw error;

      toast({
        title: 'Erfolg',
        description: 'Verein erfolgreich gelöscht',
      });

      loadClubs();
    } catch (err) {
      console.error('Error deleting club:', err);
      toast({
        title: 'Fehler',
        description: 'Fehler beim Löschen des Vereins',
        variant: 'destructive',
      });
    }
  };

  const exportClubData = async (clubId: string) => {
    toast({
      title: 'Export gestartet',
      description: 'Datenexport wird vorbereitet...',
    });
    // Implementation would go here
  };

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  };

  const filteredClubs = clubs.filter(club => {
    const matchesSearch = club.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         club.slug.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || 
                         (statusFilter === 'active' && club.is_active) ||
                         (statusFilter === 'inactive' && !club.is_active);
    return matchesSearch && matchesStatus;
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Vereinsverwaltung</h1>
          <p className="text-muted-foreground">
            Zentrale Verwaltung aller Vereine und deren Einstellungen
          </p>
        </div>
        {canManage && (
          <Button onClick={() => openClubDialog()}>
            <Plus className="h-4 w-4 mr-2" />
            Neuer Verein
          </Button>
        )}
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Gesamt Vereine</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{clubs.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Aktive Vereine</CardTitle>
            <Play className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{clubs.filter(c => c.is_active).length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Custom Domains</CardTitle>
            <Globe className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{clubs.filter(c => c.custom_domain).length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Gesamt Mitglieder</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Object.values(clubStats).reduce((sum, stats) => sum + stats.members, 0)}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex gap-4">
            <div className="flex-1">
              <Input
                placeholder="Verein suchen..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Alle</SelectItem>
                <SelectItem value="active">Aktiv</SelectItem>
                <SelectItem value="inactive">Inaktiv</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Clubs Table */}
      <Card>
        <CardHeader>
          <CardTitle>Vereine</CardTitle>
          <CardDescription>
            Alle registrierten Vereine und deren aktueller Status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Slug/Domain</TableHead>
                <TableHead>Mitglieder</TableHead>
                <TableHead>Plätze</TableHead>
                <TableHead>Buchungen</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Erstellt</TableHead>
                {canManage && <TableHead>Aktionen</TableHead>}
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredClubs.map((club) => {
                const stats = clubStats[club.id] || { members: 0, bookings: 0, courts: 0 };
                return (
                  <TableRow key={club.id}>
                    <TableCell>
                      <div className="font-medium">{club.name}</div>
                      {club.description && (
                        <div className="text-sm text-muted-foreground line-clamp-1">
                          {club.description}
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="font-mono text-sm">{club.slug}</div>
                        {club.custom_domain && (
                          <div className="text-xs text-muted-foreground">{club.custom_domain}</div>
                        )}
                        {club.subdomain && (
                          <div className="text-xs text-muted-foreground">{club.subdomain}.lovable.app</div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>{stats.members}</TableCell>
                    <TableCell>{stats.courts}</TableCell>
                    <TableCell>{stats.bookings}</TableCell>
                    <TableCell>
                      <Badge variant={club.is_active ? 'default' : 'outline'}>
                        {club.is_active ? 'Aktiv' : 'Inaktiv'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {new Date(club.created_at).toLocaleDateString('de-DE')}
                    </TableCell>
                    {canManage && (
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openClubDialog(club)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => toggleClubStatus(club.id, club.is_active)}
                          >
                            {club.is_active ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => exportClubData(club.id)}
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="outline" size="sm">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Verein löschen</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Sind Sie sicher, dass Sie diesen Verein löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Abbrechen</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => deleteClub(club.id)}
                                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                >
                                  Löschen
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </TableCell>
                    )}
                  </TableRow>
                );
              })}
              {filteredClubs.length === 0 && (
                <TableRow>
                  <TableCell colSpan={canManage ? 8 : 7} className="text-center py-8 text-muted-foreground">
                    Keine Vereine gefunden
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Club Creation/Edit Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingClub ? 'Verein bearbeiten' : 'Neuen Verein erstellen'}
            </DialogTitle>
            <DialogDescription>
              Konfigurieren Sie die Grundeinstellungen und Zugänge für diesen Verein.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Grundinformationen</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Vereinsname</Label>
                  <Input
                    id="name"
                    value={clubForm.name}
                    onChange={(e) => {
                      const name = e.target.value;
                      setClubForm({ 
                        ...clubForm, 
                        name,
                        slug: clubForm.slug || generateSlug(name)
                      });
                    }}
                  />
                </div>
                <div>
                  <Label htmlFor="slug">URL Slug</Label>
                  <Input
                    id="slug"
                    value={clubForm.slug}
                    onChange={(e) => setClubForm({ ...clubForm, slug: e.target.value })}
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="description">Beschreibung</Label>
                <Textarea
                  id="description"
                  value={clubForm.description}
                  onChange={(e) => setClubForm({ ...clubForm, description: e.target.value })}
                />
              </div>
            </div>

            {/* Domain Configuration */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Domain Konfiguration</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="subdomain">Subdomain</Label>
                  <div className="flex">
                    <Input
                      id="subdomain"
                      value={clubForm.subdomain}
                      onChange={(e) => setClubForm({ ...clubForm, subdomain: e.target.value })}
                    />
                    <span className="ml-2 self-center text-sm text-muted-foreground">.lovable.app</span>
                  </div>
                </div>
                <div>
                  <Label htmlFor="custom_domain">Custom Domain</Label>
                  <Input
                    id="custom_domain"
                    placeholder="example.com"
                    value={clubForm.custom_domain}
                    onChange={(e) => setClubForm({ ...clubForm, custom_domain: e.target.value })}
                  />
                </div>
              </div>
            </div>

            {/* Contact & Settings */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Kontakt & Einstellungen</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="contact_email">Kontakt E-Mail</Label>
                  <Input
                    id="contact_email"
                    type="email"
                    value={clubForm.settings.contact_email}
                    onChange={(e) => setClubForm({ 
                      ...clubForm, 
                      settings: { ...clubForm.settings, contact_email: e.target.value }
                    })}
                  />
                </div>
                <div>
                  <Label htmlFor="contact_phone">Telefon</Label>
                  <Input
                    id="contact_phone"
                    value={clubForm.settings.contact_phone}
                    onChange={(e) => setClubForm({ 
                      ...clubForm, 
                      settings: { ...clubForm.settings, contact_phone: e.target.value }
                    })}
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="address">Adresse</Label>
                <Textarea
                  id="address"
                  value={clubForm.settings.address}
                  onChange={(e) => setClubForm({ 
                    ...clubForm, 
                    settings: { ...clubForm.settings, address: e.target.value }
                  })}
                />
              </div>
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="timezone">Zeitzone</Label>
                  <Select 
                    value={clubForm.settings.timezone} 
                    onValueChange={(value) => setClubForm({ 
                      ...clubForm, 
                      settings: { ...clubForm.settings, timezone: value }
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Europe/Berlin">Europe/Berlin</SelectItem>
                      <SelectItem value="Europe/Vienna">Europe/Vienna</SelectItem>
                      <SelectItem value="Europe/Zurich">Europe/Zurich</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="currency">Währung</Label>
                  <Select 
                    value={clubForm.settings.currency} 
                    onValueChange={(value) => setClubForm({ 
                      ...clubForm, 
                      settings: { ...clubForm.settings, currency: value }
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="EUR">EUR</SelectItem>
                      <SelectItem value="CHF">CHF</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="language">Sprache</Label>
                  <Select 
                    value={clubForm.settings.language} 
                    onValueChange={(value) => setClubForm({ 
                      ...clubForm, 
                      settings: { ...clubForm.settings, language: value }
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="de">Deutsch</SelectItem>
                      <SelectItem value="en">English</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Booking Settings */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Buchungseinstellungen</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="booking_advance_days">Buchung im Voraus (Tage)</Label>
                  <Input
                    id="booking_advance_days"
                    type="number"
                    value={clubForm.settings.booking_advance_days}
                    onChange={(e) => setClubForm({ 
                      ...clubForm, 
                      settings: { ...clubForm.settings, booking_advance_days: parseInt(e.target.value) || 14 }
                    })}
                  />
                </div>
                <div>
                  <Label htmlFor="cancellation_hours">Stornierung (Stunden vorher)</Label>
                  <Input
                    id="cancellation_hours"
                    type="number"
                    value={clubForm.settings.cancellation_hours}
                    onChange={(e) => setClubForm({ 
                      ...clubForm, 
                      settings: { ...clubForm.settings, cancellation_hours: parseInt(e.target.value) || 24 }
                    })}
                  />
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              Abbrechen
            </Button>
            <Button onClick={saveClub} disabled={!clubForm.name || !clubForm.slug}>
              {editingClub ? 'Aktualisieren' : 'Erstellen'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}