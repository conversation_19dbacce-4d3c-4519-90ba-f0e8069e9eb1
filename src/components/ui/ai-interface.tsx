import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Wand2, <PERSON>rk<PERSON>, Loader2 } from "lucide-react";

interface AIInterfaceProps {
  title?: string;
  description?: string;
  placeholder?: string;
  prompt: string;
  onPromptChange: (value: string) => void;
  onGenerate: () => void;
  isGenerating: boolean;
  generateButtonText?: string;
  generatingText?: string;
  features?: string[];
}

export const AIInterface: React.FC<AIInterfaceProps> = ({
  title = "AI Generator",
  description = "Intelligente Erstellung mit KI-Power",
  placeholder = "Beschreiben Sie was Sie generieren möchten...",
  prompt,
  onPromptChange,
  onGenerate,
  isGenerating,
  generateButtonText = "Generieren",
  generatingText = "Generiert...",
  features = ["AI erkennt automatisch Variablen", "Vereins-optimiert"]
}) => {
  return (
    <div className="relative rounded-xl p-6 border border-border/50">
      <div className="space-y-4">
        <div className="flex items-center gap-3">
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full blur-md opacity-30"></div>
            <div className="relative bg-gradient-to-r from-purple-500 to-blue-500 p-3 rounded-full">
              <Wand2 className="h-6 w-6 text-white" />
            </div>
          </div>
          <div>
            <Label className="text-xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
              {title}
            </Label>
            <p className="text-sm text-muted-foreground mt-1">
              {description}
            </p>
          </div>
        </div>
        
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-blue-500/20 rounded-lg blur-sm"></div>
          <div className="relative flex gap-3 p-1 bg-background/80 backdrop-blur-sm rounded-lg border border-gradient-to-r from-purple-500/30 to-blue-500/30">
            <Input
              placeholder={placeholder}
              value={prompt}
              onChange={(e) => onPromptChange(e.target.value)}
              className="flex-1 border-0 bg-transparent focus:ring-0 focus:ring-offset-0 placeholder:text-muted-foreground/70"
            />
            <Button 
              onClick={onGenerate} 
              disabled={isGenerating}
              className="relative overflow-hidden bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-600/90 hover:to-blue-600/90 text-white border-0 shadow-lg transition-all duration-300 hover:shadow-xl hover:shadow-purple-500/25"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 translate-x-[-100%] hover:translate-x-[100%] transition-transform duration-700 ease-in-out"></div>
              <div className="relative flex items-center gap-2">
                {isGenerating ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Sparkles className="h-4 w-4" />
                )}
                <span className="font-medium">
                  {isGenerating ? generatingText : generateButtonText}
                </span>
              </div>
            </Button>
          </div>
        </div>
        
        <div className="flex items-center gap-2 text-xs">
          {features.map((feature, index) => (
            <React.Fragment key={feature}>
              {index > 0 && <div className="w-1 h-1 bg-muted-foreground/50 rounded-full"></div>}
              <div className="flex items-center gap-1">
                <div className={`w-2 h-2 bg-gradient-to-r ${index % 2 === 0 ? 'from-purple-400 to-purple-500' : 'from-blue-400 to-blue-500'} rounded-full animate-pulse ${index % 2 === 1 ? 'delay-75' : ''}`}></div>
                <span className="text-muted-foreground">{feature}</span>
              </div>
            </React.Fragment>
          ))}
        </div>
      </div>
    </div>
  );
};