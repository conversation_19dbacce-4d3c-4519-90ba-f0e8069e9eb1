import { useState, useEffect } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Calendar } from "@/components/ui/calendar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Clock, Users, Calendar as CalendarIcon, MapPin, Zap } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useWaitlist } from "@/hooks/useWaitlist";
import { supabase } from "@/integrations/supabase/client";
import { format } from "date-fns";
import { de } from "date-fns/locale";

interface Court {
  id: string;
  number: number;
  locked: boolean;
  lock_reason?: string;
}

interface ClubMember {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
}

interface WaitlistDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  initialDate: Date;
  initialTime: string;
  initialCourt: Court;
  clubId: string;
  onWaitlistAdded?: () => void;
}

export const WaitlistDialog = ({
  open,
  onOpenChange,
  initialDate,
  initialTime,
  initialCourt,
  clubId,
  onWaitlistAdded
}: WaitlistDialogProps) => {
  const { toast } = useToast();
  const { addToWaitlist, getCourts, isLoading } = useWaitlist();

  const [selectedDate, setSelectedDate] = useState<Date>(initialDate);
  const [startTime, setStartTime] = useState(initialTime);
  const [endTime, setEndTime] = useState("");
  const [playerName, setPlayerName] = useState("");
  const [partnerName, setPartnerName] = useState("");
  const [autoBookingEnabled, setAutoBookingEnabled] = useState(false);
  const [courts, setCourts] = useState<Court[]>([]);
  const [selectedCourts, setSelectedCourts] = useState<string[]>([initialCourt.id]);
  const [clubMembers, setClubMembers] = useState<ClubMember[]>([]);
  const [selectedPartnerId, setSelectedPartnerId] = useState<string>("");

  useEffect(() => {
    if (open) {
      loadCourts();
      loadUserNameAndMembers();
      // Calculate end time (default to 1 hour later)
      const [hours, minutes] = initialTime.split(':').map(Number);
      const endHour = hours + 1;
      setEndTime(`${endHour.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`);
    }
  }, [open, clubId]);

  const loadUserNameAndMembers = async () => {
    try {
      // Load current user name
      const { data: userDisplayName } = await supabase.rpc('get_current_user_display_name');
      if (userDisplayName) {
        setPlayerName(userDisplayName);
      }

      // Load club members
      const { data: members, error } = await supabase
        .from('club_memberships')
        .select('id, first_name, last_name, email')
        .eq('club_id', clubId)
        .eq('is_active', true)
        .neq('user_id', (await supabase.auth.getUser()).data.user?.id);

      if (error) throw error;
      setClubMembers(members || []);
    } catch (error) {
      console.error('Error loading user data:', error);
      toast({
        title: "Fehler",
        description: "Benutzerdaten konnten nicht geladen werden.",
        variant: "destructive",
      });
    }
  };

  const loadCourts = async () => {
    try {
      const courtsData = await getCourts(clubId);
      setCourts(courtsData);
    } catch (error) {
      console.error('Error loading courts:', error);
      toast({
        title: "Fehler",
        description: "Plätze konnten nicht geladen werden.",
        variant: "destructive",
      });
    }
  };

  const handleCourtToggle = (courtId: string) => {
    setSelectedCourts(prev => 
      prev.includes(courtId) 
        ? prev.filter(id => id !== courtId)
        : [...prev, courtId]
    );
  };

  const handleSubmit = async () => {
    if (selectedCourts.length === 0) {
      toast({
        title: "Fehler", 
        description: "Bitte wählen Sie mindestens einen Platz aus.",
        variant: "destructive",
      });
      return;
    }

    if (!selectedPartnerId) {
      toast({
        title: "Fehler",
        description: "Bitte wählen Sie einen Mitspieler aus.",
        variant: "destructive",
      });
      return;
    }

    if (!startTime || !endTime) {
      toast({
        title: "Fehler",
        description: "Bitte geben Sie Start- und Endzeit ein.",
        variant: "destructive",
      });
      return;
    }

    try {
      const selectedPartner = clubMembers.find(member => member.id === selectedPartnerId);
      const partnerDisplayName = selectedPartner 
        ? `${selectedPartner.first_name} ${selectedPartner.last_name}`.trim()
        : "";

      await addToWaitlist({
        clubId,
        preferredDate: selectedDate,
        startTimeRange: startTime,
        endTimeRange: endTime,
        playerName: playerName,
        partnerName: partnerDisplayName,
        preferredCourts: selectedCourts,
        autoBookingEnabled
      });

      toast({
        title: "Erfolg",
        description: autoBookingEnabled 
          ? "Sie wurden mit Auto-Zusage auf die Warteliste gesetzt! Eine sofortige Prüfung wird durchgeführt."
          : "Sie wurden auf die Warteliste gesetzt!",
      });

      onWaitlistAdded?.();
    } catch (error: any) {
      toast({
        title: "Fehler",
        description: error.message || "Wartelisten-Eintrag konnte nicht erstellt werden.",
        variant: "destructive",
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Auf Warteliste setzen
          </DialogTitle>
          <DialogDescription>
            Setzen Sie sich auf die Warteliste für diesen Zeitraum. Sie werden benachrichtigt, wenn ein Platz frei wird.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Date Selection */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <CalendarIcon className="h-4 w-4" />
                Gewünschtes Datum
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={(date) => date && setSelectedDate(date)}
                disabled={(date) => date < new Date()}
                locale={de}
                className="rounded-md border w-fit"
              />
              <p className="text-sm text-muted-foreground mt-2">
                Gewählt: {format(selectedDate, "EEEE, dd. MMMM yyyy", { locale: de })}
              </p>
            </CardContent>
          </Card>

          {/* Time Selection */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Gewünschte Uhrzeit
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="start-time">Von</Label>
                  <Input
                    id="start-time"
                    type="time"
                    value={startTime}
                    onChange={(e) => setStartTime(e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="end-time">Bis</Label>
                  <Input
                    id="end-time"
                    type="time"
                    value={endTime}
                    onChange={(e) => setEndTime(e.target.value)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Court Selection */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                Gewünschte Plätze
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {courts.map((court) => (
                  <div 
                    key={court.id} 
                    className="flex items-center space-x-2 p-2 rounded border"
                  >
                    <Checkbox
                      id={`court-${court.id}`}
                      checked={selectedCourts.includes(court.id)}
                      onCheckedChange={() => handleCourtToggle(court.id)}
                      disabled={court.locked}
                    />
                    <Label 
                      htmlFor={`court-${court.id}`}
                      className={`text-sm ${court.locked ? 'text-muted-foreground' : ''}`}
                    >
                      Platz {court.number}
                      {court.locked && (
                        <span className="text-xs block text-destructive">
                          ({court.lock_reason})
                        </span>
                      )}
                    </Label>
                  </div>
                ))}
              </div>
              <p className="text-xs text-muted-foreground mt-2">
                Wählen Sie alle Plätze aus, auf denen Sie gerne spielen möchten.
              </p>
            </CardContent>
          </Card>

          {/* Player Information */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <Users className="h-4 w-4" />
                Spieler Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="player-name">Ihr Name *</Label>
                <Input
                  id="player-name"
                  value={playerName}
                  readOnly
                  className="bg-muted"
                />
              </div>
              <div>
                <Label htmlFor="partner-select">Mitspieler *</Label>
                <Select value={selectedPartnerId} onValueChange={setSelectedPartnerId}>
                  <SelectTrigger>
                    <SelectValue placeholder="Mitspieler auswählen..." />
                  </SelectTrigger>
                  <SelectContent>
                    {clubMembers.map((member) => (
                      <SelectItem key={member.id} value={member.id}>
                        {member.first_name} {member.last_name} ({member.email})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Auto-Booking Option */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <Zap className="h-4 w-4" />
                Auto-Zusage
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-start space-x-2">
                <Checkbox
                  id="auto-booking"
                  checked={autoBookingEnabled}
                  onCheckedChange={(checked) => setAutoBookingEnabled(!!checked)}
                />
                <div className="space-y-1">
                  <Label htmlFor="auto-booking" className="text-sm font-medium">
                    Automatische Buchung aktivieren
                  </Label>
                  <p className="text-xs text-muted-foreground">
                    Wenn ein passender Slot frei wird, werden Sie automatisch gebucht. 
                    Sie können nur eine Auto-Zusage gleichzeitig haben.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Separator />

          {/* Action Buttons */}
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Abbrechen
            </Button>
            <Button onClick={handleSubmit} disabled={isLoading}>
              {isLoading ? "Wird hinzugefügt..." : "Auf Warteliste setzen"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};