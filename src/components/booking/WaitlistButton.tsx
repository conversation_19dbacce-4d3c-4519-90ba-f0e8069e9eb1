import { <PERSON><PERSON> } from "@/components/ui/button";
import { Clock, Users } from "lucide-react";
import { useState } from "react";
import { WaitlistDialog } from "./WaitlistDialog";

interface Court {
  id: string;
  number: number;
  locked: boolean;
  lock_reason?: string;
}

interface WaitlistButtonProps {
  date: Date;
  time: string;
  court: Court;
  clubId: string;
  onWaitlistAdded?: () => void;
}

export const WaitlistButton = ({ 
  date, 
  time, 
  court, 
  clubId,
  onWaitlistAdded 
}: WaitlistButtonProps) => {
  const [showDialog, setShowDialog] = useState(false);

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowDialog(true);
  };

  const handleWaitlistAdded = () => {
    setShowDialog(false);
    onWaitlistAdded?.();
  };

  return (
    <>
      <Button
        variant="outline"
        size="sm"
        onClick={handleClick}
        className="text-xs px-2 py-1 h-auto bg-background/80 hover:bg-background border-muted-foreground/20 hover:border-primary/50"
      >
        <Clock className="h-3 w-3 mr-1" />
        Warteliste
      </Button>

      <WaitlistDialog
        open={showDialog}
        onOpenChange={setShowDialog}
        initialDate={date}
        initialTime={time}
        initialCourt={court}
        clubId={clubId}
        onWaitlistAdded={handleWaitlistAdded}
      />
    </>
  );
};