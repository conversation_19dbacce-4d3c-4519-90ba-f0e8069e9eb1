import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";
import { WaitlistButton } from "./WaitlistButton";
import { Lock } from "lucide-react";
import { useState } from "react";
import { format } from "date-fns";
import { de } from "date-fns/locale";

interface Court {
  id: string;
  number: number;
  locked: boolean;
  lock_reason: string | null;
}

interface TimeSlot {
  time: string;
  available: boolean;
  price?: number;
}

interface DayViewProps {
  selectedDate: Date;
  courts: Court[];
  timeSlots: TimeSlot[];
  onSlotSelect: (courtId: string, time: string) => void;
  getSlotStatus: (courtId: string, time: string, date?: Date) => string;
  getBookedPlayerNames: (courtId: string, time: string) => string[];
  getPlayerForSpecificSlot?: (courtId: string, time: string, date?: Date) => string[];
  isPartOfHourBooking?: (courtId: string, time: string, date?: Date) => boolean;
  shouldShowVsBadge?: (courtId: string, time: string, date?: Date) => boolean;
  clubId?: string;
  onWaitlistAdded?: () => void;
}

export const DayView = ({ 
  selectedDate,
  courts, 
  timeSlots, 
  onSlotSelect, 
  getSlotStatus, 
  getBookedPlayerNames,
  getPlayerForSpecificSlot,
  isPartOfHourBooking,
  shouldShowVsBadge,
  clubId,
  onWaitlistAdded
}: DayViewProps) => {
  const [showPlayerDialog, setShowPlayerDialog] = useState(false);
  const [selectedSlotInfo, setSelectedSlotInfo] = useState<{
    courtNumber: number;
    time: string;
    players: string[];
  } | null>(null);

  // Cross-highlight system state for 1-hour highlighting
  const [hoveredTimeSlot, setHoveredTimeSlot] = useState<string | null>(null);
  const [hoveredCourtIndex, setHoveredCourtIndex] = useState<number | null>(null);

  const sortedCourts = [...courts].sort((a, b) => a.number - b.number);
  
  // Debug logging
  console.log('DayView - Courts data:', courts);
  console.log('Locked courts with reasons:', courts.filter(c => c.locked && c.lock_reason));

  return (
    <TooltipProvider>
      <div className="overflow-x-auto">
        <div className="min-w-[400px]">
          {/* Day Header */}
          <div className="grid gap-0.5 mb-2" style={{ gridTemplateColumns: '80px 1fr' }}>
            <div className="p-3 font-semibold text-center bg-muted">Zeit</div>
            <div className="text-center p-3 bg-muted">
              <div className="font-semibold text-sm">Heute</div>
              <div className="text-xs text-muted-foreground">
                {format(selectedDate, 'dd.MM', { locale: de })}
              </div>
            </div>
          </div>

          {/* Court Headers */}
          <div className="grid gap-0.5 mb-2" style={{ gridTemplateColumns: '80px 1fr' }}>
            <div className="bg-muted"></div>
            <div className="grid gap-0.5" style={{ gridTemplateColumns: `repeat(${sortedCourts.length}, 1fr)` }}>
              {sortedCourts.map((court, courtIndex) => (
                <div
                  key={court.id}
                  onMouseEnter={() => setHoveredCourtIndex(courtIndex)}
                  onMouseLeave={() => setHoveredCourtIndex(null)}
                  className={`
                    text-xs font-medium p-2 bg-muted cursor-pointer
                    flex items-center justify-center transition-all duration-200
                    ${hoveredCourtIndex === courtIndex ? 'bg-primary/10 text-primary' : ''}
                  `}
                >
                  Platz {court.number}
                </div>
              ))}
            </div>
          </div>

          {/* Scrollable content area */}
          <div className="max-h-[800px] overflow-y-auto">
            {timeSlots.map((slot) => {
              const isFullHour = slot.time.endsWith(':00');
              
              return (
                <div key={slot.time} className="grid gap-0.5 mb-0.5" style={{ gridTemplateColumns: '80px 1fr' }}>
                  {/* Time column */}
                  <div 
                    className={`
                      flex items-center justify-center p-3 font-medium sticky left-0 z-10 transition-all duration-200
                      ${isFullHour 
                        ? 'bg-background border-l-2 border-l-green-500' 
                        : 'bg-background border-l border-l-border/20'
                      }
                      ${hoveredTimeSlot === slot.time ? 'bg-primary/5' : ''}
                    `}
                    onMouseEnter={() => setHoveredTimeSlot(slot.time)}
                    onMouseLeave={() => setHoveredTimeSlot(null)}
                  >
                    <span className={`
                      transition-all duration-200
                      ${isFullHour 
                        ? 'text-sm font-bold text-foreground' 
                        : 'text-xs font-normal text-muted-foreground'
                      }
                    `}>
                      {slot.time}
                    </span>
                  </div>
                  
                  {/* Day column */}
                  <div className="grid gap-0.5" style={{ gridTemplateColumns: `repeat(${sortedCourts.length}, 1fr)` }}>
                    {sortedCourts.map((court, courtIndex) => {
                      // Calculate hour-partner slot for 1-hour highlight
                      const [hour, minute] = slot.time.split(':').map(Number);
                      const partnerTime = minute === 0 
                        ? `${hour.toString().padStart(2, '0')}:30`  // If :00, partner is :30
                        : `${hour.toString().padStart(2, '0')}:00`; // If :30, partner is :00
                      
                      const isTimeHighlighted = hoveredTimeSlot === slot.time || hoveredTimeSlot === partnerTime;
                      const isCourtHighlighted = hoveredCourtIndex === courtIndex;
                      const isCrossHighlight = isTimeHighlighted && isCourtHighlighted;
                      
                      const slotStatus = court.locked ? 'locked' : getSlotStatus(court.id, slot.time, selectedDate);
                      const isDisabled = court.locked || slotStatus === 'unavailable' || slotStatus === 'past';
                      const bookedPlayers = (slotStatus === 'booked' || slotStatus === 'booked-past') ? getBookedPlayerNames(court.id, slot.time) : [];
                      const specificPlayer = getPlayerForSpecificSlot ? getPlayerForSpecificSlot(court.id, slot.time, selectedDate) : [];
                      const isPartOfHour = isPartOfHourBooking ? isPartOfHourBooking(court.id, slot.time, selectedDate) : false;
                      const showVsBadge = shouldShowVsBadge ? shouldShowVsBadge(court.id, slot.time, selectedDate) : false;
                      
                      console.log('🎾 DayView slot render:', { 
                        courtId: court.id, 
                        time: slot.time, 
                        slotStatus, 
                        specificPlayer, 
                        isPartOfHour, 
                        showVsBadge,
                        hasFunctions: {
                          getPlayerForSpecificSlot: !!getPlayerForSpecificSlot,
                          isPartOfHourBooking: !!isPartOfHourBooking,
                          shouldShowVsBadge: !!shouldShowVsBadge
                        }
                      });
                      
                      // Clean, minimalist button classes with sharp edges
                      let buttonClasses = `
                        h-12 p-3 text-xs font-medium min-w-0 w-full
                        flex flex-col items-center justify-center leading-tight
                        transition-all duration-200 ease-out rounded-none
                        focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-1
                        ${isCrossHighlight ? 'bg-primary/8 z-20' : ''}
                        ${(isTimeHighlighted || isCourtHighlighted) && !isCrossHighlight ? 'bg-primary/4' : ''}
                      `;
                      
                      // Add subtle background hierarchy for full vs half hours
                      const baseHierarchy = isFullHour 
                        ? '' 
                        : 'border-l border-l-border/10';
                      
                      buttonClasses += ` ${baseHierarchy}`;
                      
                      // Sharp edges - no rounded corners
                      buttonClasses += isFullHour ? " border-t border-b" : " border-t border-r border-b";
                      
                      // Clean color system
                      if (court.locked) {
                        buttonClasses += ` 
                          bg-muted text-muted-foreground border-muted
                          cursor-not-allowed opacity-60
                        `;
                      } else if (slotStatus === 'past') {
                        buttonClasses += ` 
                          bg-muted/40 text-muted-foreground border-muted
                          cursor-not-allowed opacity-50
                        `;
                      } else if (slotStatus === 'unavailable') {
                        buttonClasses += ` 
                          bg-muted/60 text-muted-foreground border-muted
                          cursor-not-allowed opacity-70
                        `;
                      } else if (slotStatus === 'booked' || slotStatus === 'booked-past') {
                        if (slotStatus === 'booked-past') {
                          buttonClasses += ` 
                            bg-green-500/40 text-white border-green-500/40
                            opacity-75
                          `;
                        } else {
                          // Clean booking state
                          if (isFullHour) {
                            buttonClasses += ` 
                              bg-green-500 text-white border-green-500
                              hover:bg-green-600 hover:border-green-600
                              shadow-md hover:shadow-lg
                              cursor-pointer
                            `;
                          } else {
                            buttonClasses += ` 
                              bg-green-500 text-white border-green-500
                              hover:bg-green-600 hover:border-green-600
                              shadow-md hover:shadow-lg
                              cursor-pointer
                            `;
                          }
                        }
                      } else {
                        // Available slots - elegant full hour emphasis  
                        if (isFullHour) {
                          buttonClasses += ` 
                            bg-white text-foreground border-border
                            hover:bg-primary/5 hover:border-primary/50 hover:text-primary
                            active:scale-95
                            shadow-sm hover:shadow-md
                            cursor-pointer
                          `;
                        } else {
                          buttonClasses += ` 
                            bg-white/80 text-muted-foreground border-border/30
                            hover:bg-primary/5 hover:border-primary/30 hover:text-foreground
                            active:scale-95
                            shadow-sm hover:shadow-md
                            cursor-pointer opacity-80 hover:opacity-100
                          `;
                        }
                      }

                      const buttonContent = (
                        <Button
                          variant="outline"
                          disabled={isDisabled}
                          size="sm"
                          className={buttonClasses}
                          onClick={() => {
                            if (slotStatus === 'booked' || slotStatus === 'booked-past') {
                              setSelectedSlotInfo({
                                courtNumber: court.number,
                                time: slot.time,
                                players: bookedPlayers
                              });
                              setShowPlayerDialog(true);
                            } else if (!isDisabled && slotStatus !== 'booked' && slotStatus !== 'booked-past') {
                              onSlotSelect(court.id, slot.time);
                            }
                          }}
                        >
                          {court.locked && <Lock className="h-3 w-3 mb-1" />}
                          {slotStatus === 'unavailable' && (
                            <div className="text-xs line-through opacity-60">—</div>
                          )}
                          {(slotStatus === 'booked' || slotStatus === 'booked-past') && (
                            <div className="text-xs font-medium truncate w-full text-center">
                              {isPartOfHour && specificPlayer.length > 0
                                ? specificPlayer[0]?.split(' ')[0]
                                : bookedPlayers.length > 0
                                ? bookedPlayers[0]?.split(' ')[0]
                                : ''
                              }
                            </div>
                          )}
                          {court.locked && (
                            <span className="text-xs text-muted-foreground text-center">Gesperrt</span>
                          )}
                        </Button>
                      );

                      return court.locked && court.lock_reason ? (
                        <Tooltip key={`${court.id}-${slot.time}`}>
                          <TooltipTrigger asChild>
                            <div className="relative">
                              {buttonContent}
                              {showVsBadge && (
                                <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 z-30">
                                  <Badge
                                    variant="secondary"
                                    className="text-[10px] px-1.5 py-0.5 bg-muted text-muted-foreground border-border/50"
                                  >
                                    vs
                                  </Badge>
                                </div>
                              )}
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{court.lock_reason}</p>
                          </TooltipContent>
                        </Tooltip>
                      ) : (
                        <div key={`${court.id}-${slot.time}`} className="relative">
                          {buttonContent}
                          {showVsBadge && (
                            <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 z-30">
                              <Badge
                                variant="secondary"
                                className="text-[10px] px-1.5 py-0.5 bg-muted text-muted-foreground border-border/50"
                              >
                                vs
                              </Badge>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Player Info Dialog */}
        <Dialog open={showPlayerDialog} onOpenChange={setShowPlayerDialog}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>
                Buchungsdetails - Platz {selectedSlotInfo?.courtNumber}
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-3">
              <div className="text-sm text-muted-foreground">
                Heute um {selectedSlotInfo?.time}
              </div>
              <div>
                <h4 className="font-medium mb-2">Gebuchte Spieler:</h4>
                <div className="space-y-1">
                  {selectedSlotInfo?.players.map((player, index) => (
                    <div key={index} className="text-sm bg-muted p-2 rounded">
                      {player}
                    </div>
                  ))}
                </div>
              </div>
              
              {/* Warteliste Button */}
              {clubId && selectedSlotInfo && (
                <div className="border-t pt-3">
                  <h4 className="font-medium mb-2">Auf Warteliste setzen:</h4>
                  <WaitlistButton
                    date={selectedDate}
                    time={selectedSlotInfo.time}
                    court={courts.find(c => c.number === selectedSlotInfo.courtNumber)!}
                    clubId={clubId}
                    onWaitlistAdded={() => {
                      onWaitlistAdded?.();
                      setShowPlayerDialog(false);
                    }}
                  />
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </TooltipProvider>
  );
};