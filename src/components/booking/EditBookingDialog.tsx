import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Title } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from "@/components/ui/command";
import { Check, ChevronsUpDown, Trash2, Plus, X } from "lucide-react";
import { format } from "date-fns";
import { de } from "date-fns/locale";
import { useState, useEffect } from "react";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { useClub } from "@/contexts/ClubContext";
import { cn } from "@/lib/utils";

interface Booking {
  id: string;
  court_id: string;
  booking_date: string;
  start_time: string;
  end_time: string;
  player_name: string;
  partner_name?: string;
}

type SpielTyp = "einzel" | "doppel";

interface PlayerOption {
  id: string;
  label: string;
  source: 'member' | 'guest';
}

interface EditBookingDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  booking: Booking | null;
  courtNumber: number;
  onBookingUpdated: () => void;
}

// Hook für Spielersuche 
const usePlayerSearch = () => {
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<PlayerOption[]>([]);
  const { currentClubId } = useClub();

  const loadPlayers = async (query = '') => {
    if (!currentClubId) {
      setResults([]);
      return;
    }
    
    setLoading(true);
    try {
      let clubMembersQuery = supabase
        .from("club_memberships")
        .select("id, first_name, last_name, user_id")
        .eq("club_id", currentClubId)
        .eq("is_active", true);

      let accountsQuery = supabase
        .from("accounts")
        .select("id, first_name, last_name, account_type")
        .eq("club_id", currentClubId);

      // Wenn eine Suchanfrage vorhanden ist, filtern
      if (query.trim()) {
        const q = query.trim();
        clubMembersQuery = clubMembersQuery.or(`first_name.ilike.%${q}%,last_name.ilike.%${q}%`);
        accountsQuery = accountsQuery.or(`first_name.ilike.%${q}%,last_name.ilike.%${q}%`);
      }

      // Limit für Performance
      clubMembersQuery = clubMembersQuery.limit(50);
      accountsQuery = accountsQuery.limit(50);

      const [{ data: clubMembers }, { data: accounts }] = await Promise.all([
        clubMembersQuery,
        accountsQuery
      ]);

      console.log('🔍 EditDialog Player search results:', { 
        query, 
        clubId: currentClubId, 
        clubMembers: clubMembers?.length || 0, 
        accounts: accounts?.length || 0 
      });

      const mapped: PlayerOption[] = [];

      // Club-Mitglieder hinzufügen
      (clubMembers || []).forEach((m) =>
        mapped.push({ 
          id: m.id, 
          label: `${m.first_name || ''} ${m.last_name || ''}`.trim(), 
          source: "member" 
        })
      );

      // Accounts (Gäste, Trainer) hinzufügen
      (accounts || []).forEach((a) =>
        mapped.push({
          id: a.id,
          label: `${a.first_name || ''} ${a.last_name || ''}`.trim(),
          source: (String(a.account_type || "guest").toLowerCase() as PlayerOption["source"]) || "guest",
        })
      );

      // Deduplizieren nach id + label
      const uniq = new Map<string, PlayerOption>();
      for (const r of mapped) {
        const key = `${r.id}-${r.label}`;
        if (!uniq.has(key) && r.label.trim()) uniq.set(key, r);
      }
      
      const finalResults = Array.from(uniq.values()).slice(0, 50);
      console.log('🎾 EditDialog Final player results:', finalResults);
      setResults(finalResults);
    } catch (error) {
      console.error('Error searching players:', error);
      setResults([]);
    } finally {
      setLoading(false);
    }
  };

  // Beim ersten Laden alle Spieler laden
  useEffect(() => {
    if (currentClubId) {
      loadPlayers();
    }
  }, [currentClubId]);

  const search = (query: string) => {
    loadPlayers(query);
  };

  return { loading, results, search };
};

// Spieler-Combobox Komponente
const PlayerCombobox = ({ 
  onSelect, 
  placeholder = "Spieler auswählen...",
  value,
  className
}: {
  onSelect: (player: PlayerOption | null) => void;
  placeholder?: string;
  value?: PlayerOption | null;
  className?: string;
}) => {
  const [open, setOpen] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const { loading, results, search } = usePlayerSearch();

  useEffect(() => {
    search(searchValue);
  }, [searchValue]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn("justify-between", className)}
        >
          {value?.label || placeholder}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[300px] p-0 bg-background border shadow-md z-50">
        <Command>
          <CommandInput 
            placeholder="Nach Spieler suchen..." 
            value={searchValue}
            onValueChange={setSearchValue}
          />
          <CommandEmpty>
            {loading ? "Suche..." : "Keine Spieler gefunden."}
          </CommandEmpty>
          <CommandGroup className="max-h-48 overflow-y-auto">
            {results.map((player) => (
              <CommandItem
                key={player.id}
                value={player.label}
                onSelect={() => {
                  onSelect(player);
                  setOpen(false);
                  setSearchValue("");
                }}
                className="hover:bg-muted cursor-pointer"
              >
                <Check
                  className={cn(
                    "mr-2 h-4 w-4",
                    value?.id === player.id ? "opacity-100" : "opacity-0"
                  )}
                />
                <div className="flex flex-col">
                  <span>{player.label}</span>
                  <span className="text-xs text-muted-foreground">
                    {player.source === 'member' ? 'Mitglied' : 'Gast'}
                  </span>
                </div>
              </CommandItem>
            ))}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
};

export const EditBookingDialog = ({ 
  open, 
  onOpenChange, 
  booking, 
  courtNumber,
  onBookingUpdated 
}: EditBookingDialogProps) => {
  const [startTime, setStartTime] = useState("");
  const [endTime, setEndTime] = useState("");
  const [spielTyp, setSpielTyp] = useState<SpielTyp>("einzel");
  const [selectedPlayers, setSelectedPlayers] = useState<(PlayerOption | null)[]>([null]);
  const [isLoading, setIsLoading] = useState(false);
  const { currentClubId } = useClub();

  // Update state when booking changes
  useEffect(() => {
    if (booking) {
      setStartTime(booking.start_time.slice(0, 5));
      setEndTime(booking.end_time.slice(0, 5));
      
      // Parse existing players
      const players = [booking.player_name];
      if (booking.partner_name) {
        players.push(booking.partner_name);
      }
      
      // Set game type based on number of players
      if (players.length > 1 && booking.partner_name) {
        setSpielTyp("doppel");
        setSelectedPlayers([
          { id: "player1", label: booking.player_name, source: "member" },
          { id: "player2", label: booking.partner_name, source: "member" }
        ]);
      } else {
        setSpielTyp("einzel");
        setSelectedPlayers([
          { id: "player1", label: booking.player_name, source: "member" }
        ]);
      }
    }
  }, [booking]);

  // Update players array when game type changes
  useEffect(() => {
    if (spielTyp === "einzel") {
      setSelectedPlayers([selectedPlayers[0] || null]);
    } else {
      setSelectedPlayers([
        selectedPlayers[0] || null,
        selectedPlayers[1] || null
      ]);
    }
  }, [spielTyp]);

  const updatePlayer = (index: number, player: PlayerOption | null) => {
    const newPlayers = [...selectedPlayers];
    newPlayers[index] = player;
    setSelectedPlayers(newPlayers);
  };

  const removePlayer = (index: number) => {
    if (selectedPlayers.length > 1) {
      const newPlayers = selectedPlayers.filter((_, i) => i !== index);
      setSelectedPlayers(newPlayers);
      if (newPlayers.length === 1) {
        setSpielTyp("einzel");
      }
    }
  };

  const addPlayer = () => {
    if (selectedPlayers.length < 2) {
      setSelectedPlayers([...selectedPlayers, null]);
      if (selectedPlayers.length === 1) {
        setSpielTyp("doppel");
      }
    }
  };

  const canConfirm = () => {
    const requiredPlayers = spielTyp === "einzel" ? 2 : 4; // Einzel = 2 Spieler, Doppel = 4 Spieler
    const filledPlayers = selectedPlayers.filter(p => p !== null).length;
    return filledPlayers >= requiredPlayers;
  };

  const handleSave = async () => {
    if (!booking || !canConfirm()) return;
    
    setIsLoading(true);
    try {
      const playerNames = selectedPlayers.filter(p => p !== null).map(p => p!.label);
      
      const { error } = await supabase
        .from("bookings")
        .update({
          player_name: playerNames[0] || "",
          partner_name: playerNames[1] || null,
          start_time: `${startTime}:00`,
          end_time: `${endTime}:00`,
        })
        .eq("id", booking.id);

      if (error) throw error;

      toast.success("Buchung erfolgreich aktualisiert");
      onBookingUpdated();
      onOpenChange(false);
    } catch (error: any) {
      console.error("Error updating booking:", error);
      toast.error(error?.message || "Fehler beim Aktualisieren der Buchung");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!booking) return;
    
    if (!confirm("Möchten Sie diese Buchung wirklich löschen?")) return;

    setIsLoading(true);
    try {
      // Store booking details before deletion for waitlist processing
      const bookingDetails = {
        club_id: currentClubId,
        court_id: booking.court_id,
        booking_date: booking.booking_date,
        start_time: booking.start_time,
        end_time: booking.end_time
      };

      const { error } = await supabase
        .from("bookings")
        .delete()
        .eq("id", booking.id);

      if (error) throw error;

      // Process waitlist for auto-booking after successful deletion
      try {
        await supabase.functions.invoke('waitlist-manager', {
          body: {
            action: 'process_booking_cancellation',
            ...bookingDetails
          }
        });
      } catch (waitlistError) {
        console.error('Waitlist processing error:', waitlistError);
        // Don't show error to user as booking deletion was successful
      }

      toast.success("Buchung erfolgreich gelöscht");
      onBookingUpdated();
      onOpenChange(false);
    } catch (error: any) {
      console.error("Error deleting booking:", error);
      toast.error(error?.message || "Fehler beim Löschen der Buchung");
    } finally {
      setIsLoading(false);
    }
  };

  if (!booking) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>
            Buchung bearbeiten - Platz {courtNumber}
          </DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div className="text-sm text-muted-foreground">
            {format(new Date(booking.booking_date), 'EEEE, dd.MM.yyyy', { locale: de })}
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="startTime">Startzeit</Label>
              <Input
                id="startTime"
                type="time"
                value={startTime}
                onChange={(e) => setStartTime(e.target.value)}
                disabled={isLoading}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="endTime">Endzeit</Label>
              <Input
                id="endTime"
                type="time"
                value={endTime}
                onChange={(e) => setEndTime(e.target.value)}
                disabled={isLoading}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="spielTyp">Spiel-Typ</Label>
            <Select 
              value={spielTyp} 
              onValueChange={(value: SpielTyp) => setSpielTyp(value)}
              disabled={isLoading}
            >
              <SelectTrigger id="spielTyp">
                <SelectValue placeholder="Spiel-Typ auswählen" />
              </SelectTrigger>
              <SelectContent className="bg-background border shadow-md z-50">
                <SelectItem value="einzel">Einzel</SelectItem>
                <SelectItem value="doppel">Doppel</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label>Spieler</Label>
              {spielTyp === "doppel" && selectedPlayers.length < 2 && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addPlayer}
                  disabled={isLoading}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Spieler hinzufügen
                </Button>
              )}
            </div>

            {selectedPlayers.map((player, index) => (
              <div key={index} className="flex gap-2">
                <PlayerCombobox
                  value={player}
                  onSelect={(selected) => updatePlayer(index, selected)}
                  placeholder={`Spieler ${index + 1} auswählen...`}
                  className="flex-1"
                />
                {selectedPlayers.length > 1 && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => removePlayer(index)}
                    disabled={isLoading}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>
            ))}
          </div>

          <div className="flex justify-between gap-2">
            <Button
              variant="destructive"
              size="sm"
              onClick={handleDelete}
              disabled={isLoading}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Löschen
            </Button>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                Abbrechen
              </Button>
              <Button 
                onClick={handleSave} 
                disabled={isLoading || !canConfirm()}
              >
                {isLoading ? "Speichert..." : "Speichern"}
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};