import { Button } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { Calendar, CalendarDays, ChevronLeft, ChevronRight } from "lucide-react";
import { format } from "date-fns";
import { de } from "date-fns/locale";

interface CompactHeaderProps {
  view: 'day' | 'week' | 'compact';
  onViewChange: (view: 'day' | 'week' | 'compact') => void;
  selectedDate: Date;
  onDateChange: (date: Date) => void;
  onNavigate: (direction: 'prev' | 'next') => void;
}

export const CompactHeader = ({ 
  view, 
  onViewChange, 
  selectedDate, 
  onDateChange, 
  onNavigate 
}: CompactHeaderProps) => {
  const formatDate = (date: Date) => {
    return format(date, "EEEE, dd. MMMM yyyy", { locale: de });
  };

  const getDateLabel = () => {
    if (view === 'week' || view === 'compact') {
      return `Woche vom ${format(selectedDate, "dd.MM.yyyy", { locale: de })}`;
    }
    return format(selectedDate, "dd.MM.yyyy", { locale: de });
  };

  return (
    <div className="bg-card border rounded-lg p-3">
      <div className="flex items-center justify-between">
        {/* Date Navigation */}
        <div className="flex items-center gap-2">
          <Button 
            variant="ghost" 
            size="sm"
            onClick={() => onNavigate('prev')}
            className="h-8 w-8 p-0"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="ghost" size="sm" className="font-normal h-8">
                <Calendar className="mr-2 h-3 w-3" />
                <span className="text-sm font-medium">
                  {getDateLabel()}
                </span>
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <CalendarComponent
                mode="single"
                selected={selectedDate}
                onSelect={(date) => date && onDateChange(date)}
                initialFocus
                className="p-3"
              />
            </PopoverContent>
          </Popover>
          
          <Button 
            variant="ghost" 
            size="sm"
            onClick={() => onNavigate('next')}
            className="h-8 w-8 p-0"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>

        {/* View Selector */}
        <ToggleGroup
          type="single"
          value={view}
          onValueChange={(value) => value && onViewChange(value as 'day' | 'week' | 'compact')}
          className="bg-muted rounded-md p-1"
        >
          <ToggleGroupItem
            value="compact"
            size="sm"
            className="h-6 px-2 text-xs data-[state=on]:bg-background data-[state=on]:shadow-sm data-[state=on]:text-primary"
          >
            <CalendarDays className="h-3 w-3 mr-1" />
            Woche
          </ToggleGroupItem>
          <ToggleGroupItem
            value="day"
            size="sm"
            className="h-6 px-2 text-xs data-[state=on]:bg-background data-[state=on]:shadow-sm data-[state=on]:text-primary"
          >
            <Calendar className="h-3 w-3 mr-1" />
            Tag
          </ToggleGroupItem>
        </ToggleGroup>
      </div>
    </div>
  );
};