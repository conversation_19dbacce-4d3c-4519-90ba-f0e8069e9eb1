import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Calendar, CalendarDays } from "lucide-react";

interface ViewSelectorProps {
  view: 'day' | 'week' | 'compact';
  onViewChange: (view: 'day' | 'week' | 'compact') => void;
}

export const ViewSelector = ({ view, onViewChange }: ViewSelectorProps) => {
  return (
    <Tabs value={view} onValueChange={(value) => onViewChange(value as 'day' | 'week' | 'compact')}>
      <TabsList className="grid w-full grid-cols-2 max-w-lg mx-auto bg-muted">
        <TabsTrigger 
          value="compact" 
          className="flex items-center gap-2 data-[state=active]:bg-tennis-green-light data-[state=active]:text-white data-[state=inactive]:bg-transparent data-[state=inactive]:text-muted-foreground"
        >
          <CalendarDays className="h-4 w-4" />
          Woche
        </TabsTrigger>
        <TabsTrigger 
          value="day" 
          className="flex items-center gap-2 data-[state=active]:bg-tennis-green-light data-[state=active]:text-white data-[state=inactive]:bg-transparent data-[state=inactive]:text-muted-foreground"
        >
          <Calendar className="h-4 w-4" />
          Tag
        </TabsTrigger>
      </TabsList>
    </Tabs>
  );
};