import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Tooltip, TooltipContent, Toolt<PERSON>Provider, TooltipTrigger } from "@/components/ui/tooltip";
import { WaitlistButton } from "./WaitlistButton";
import { Lock, Edit } from "lucide-react";
import { addDays, format } from "date-fns";
import { de } from "date-fns/locale";
import { useState, useEffect } from "react";
import { EditBookingDialog } from "./EditBookingDialog";
import { supabase } from "@/integrations/supabase/client";
import { isPastSlotInClubTZ } from "@/lib/timezone-utils";
import { useClub } from "@/contexts/ClubContext";

interface Court {
  id: string;
  number: number;
  locked: boolean;
  lock_reason: string | null;
}

interface TimeSlot {
  time: string;
  available: boolean;
  price?: number;
}

interface Booking {
  id: string;
  court_id: string;
  booking_date: string;
  start_time: string;
  end_time: string;
  player_name: string;
  partner_name?: string;
}

interface CompactWeekViewProps {
  selectedDate: Date;
  courts: Court[];
  timeSlots: TimeSlot[];
  onSlotSelect: (courtId: string, time: string, date: Date) => void;
  getSlotStatus: (courtId: string, time: string, date?: Date) => string;
  getBookedPlayerNames: (courtId: string, time: string, date?: Date) => string[];
  getBooking?: (courtId: string, time: string, date?: Date) => Booking | null;
  onBookingUpdated?: () => void;
  clubId?: string;
  onWaitlistAdded?: () => void;
}

export const CompactWeekView = ({ 
  selectedDate, 
  courts, 
  timeSlots, 
  onSlotSelect, 
  getSlotStatus, 
  getBookedPlayerNames,
  getBooking,
  onBookingUpdated,
  clubId,
  onWaitlistAdded
}: CompactWeekViewProps) => {
  const [showPlayerDialog, setShowPlayerDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [currentUserName, setCurrentUserName] = useState<string | null>(null);
  const [selectedSlotInfo, setSelectedSlotInfo] = useState<{
    courtNumber: number;
    time: string;
    date: Date;
    players: string[];
  } | null>(null);
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null);
  
  // Cross-highlight system state - only highlight the specific day-court combination
  const [hoveredTimeSlot, setHoveredTimeSlot] = useState<string | null>(null);
  const [hoveredCourtKey, setHoveredCourtKey] = useState<string | null>(null);
  
  // Get club timezone
  const { currentClub } = useClub();
  const clubTimezone = currentClub?.timezone || 'Europe/Berlin';

  // Get current user's display name
  useEffect(() => {
    const getCurrentUserName = async () => {
      try {
        const { data: auth } = await supabase.auth.getUser();
        if (!auth?.user?.id) return;

        const { data: profile } = await supabase
          .from("profiles")
          .select("first_name,last_name")
          .eq("id", auth.user.id)
          .maybeSingle();

        if (profile?.first_name || profile?.last_name) {
          const name = `${profile.first_name ?? ""} ${profile.last_name ?? ""}`.trim();
          setCurrentUserName(name);
        } else if (auth.user.email) {
          setCurrentUserName(auth.user.email);
        }
      } catch (error) {
        console.error("Error fetching user name:", error);
      }
    };

    getCurrentUserName();
  }, []);

  const canEditBooking = (booking: Booking | null) => {
    if (!booking || !currentUserName) return false;
    return booking.player_name === currentUserName || booking.partner_name === currentUserName;
  };

  // Generate week days starting from Monday
  const getWeekDays = (date: Date) => {
    const startOfWeek = new Date(date);
    const day = startOfWeek.getDay();
    const diff = startOfWeek.getDate() - day + (day === 0 ? -6 : 1);
    startOfWeek.setDate(diff);
    
    const weekDays = Array.from({ length: 7 }, (_, i) => addDays(startOfWeek, i));
    
    console.log('🔍 WEEK DEBUG - Generated week days:', {
      inputDate: date.toISOString(),
      startOfWeek: startOfWeek.toISOString(),
      weekDays: weekDays.map(d => ({ date: d.toISOString(), dayName: format(d, 'EEEE') }))
    });
    
    return weekDays;
  };

  const weekDays = getWeekDays(selectedDate);
  const sortedCourts = [...courts].sort((a, b) => a.number - b.number);

  return (
    <TooltipProvider>
      <div className="overflow-x-auto w-full max-w-full">
        <div className="min-w-[1000px]">
          <Card className="w-full">
            <CardContent className="p-6">
              {/* Fixed Header with days */}
              <div className="sticky top-0 z-[5] bg-background">
                <div className="grid gap-1 mb-4" style={{ gridTemplateColumns: '80px repeat(7, 1fr)' }}>
                  <div className="p-2 font-semibold text-center bg-background">Zeit</div>
                  {weekDays.map((day) => (
                    <div key={day.toISOString()} className="text-center p-2 bg-muted rounded">
                      <div className="flex items-center justify-center gap-2">
                        <div className="font-semibold text-sm">
                          {format(day, 'EEEE', { locale: de })}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {format(day, 'dd.MM', { locale: de })}
                        </div>
                      </div>
                      <div className="grid gap-1 mt-1" style={{ gridTemplateColumns: `repeat(${sortedCourts.length}, 1fr)` }}>
                        {sortedCourts.map((court, courtIndex) => {
                          const courtKey = `${day.toISOString()}-${courtIndex}`;
                          return (
                            <div 
                              key={court.id} 
                              className={`
                                text-xs px-1 h-6 bg-background text-foreground font-medium 
                                flex items-center justify-center transition-all duration-200
                                ${hoveredCourtKey === courtKey ? 'bg-primary/10 text-primary' : ''}
                              `}
                            >
                              {court.number}
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Scrollable content area */}
              <div className="max-h-[600px] overflow-y-auto">
                {timeSlots.map((slot, slotIndex) => {
                  const isFullHour = slot.time.endsWith(':00');
                  
                  return (
                  <div key={slot.time} className="grid gap-1" style={{ gridTemplateColumns: '80px repeat(7, 1fr)' }}>
                     {/* Time column - subtle but clear hierarchy */}
                     <div 
                       className={`
                         flex items-center justify-center p-3 font-medium sticky left-0 z-[5] transition-all duration-200
                         ${isFullHour 
                           ? 'bg-background border-l-2 border-l-primary/40' 
                           : 'bg-background border-l border-l-border/20'
                         }
                         ${hoveredTimeSlot === slot.time ? 'bg-primary/5' : ''}
                       `}
                       onMouseEnter={() => setHoveredTimeSlot(slot.time)}
                       onMouseLeave={() => setHoveredTimeSlot(null)}
                     >
                      <span className={`
                        transition-all duration-200
                        ${isFullHour 
                          ? 'text-sm font-bold text-foreground' 
                          : 'text-xs font-normal text-muted-foreground'
                        }
                      `}>
                        {slot.time}
                      </span>
                    </div>
                    
                       {/* Day columns */}
                        {weekDays.map((day, dayIndex) => {
                          const isToday = format(day, "yyyy-MM-dd") === format(new Date(), "yyyy-MM-dd");
                          const isPastDay = day < new Date() && !isToday;
                          
                          return (
                         <div 
                           key={`${slot.time}-${day.toISOString()}`} 
                           className="grid gap-0 transition-all duration-200" 
                           style={{ gridTemplateColumns: `repeat(${sortedCourts.length}, 1fr)` }}
                         >
                         {sortedCourts.map((court, courtIndex) => {
                              const slotStatus = court.locked ? 'locked' : getSlotStatus(court.id, slot.time, day);
                              const isDisabled = court.locked || slotStatus === 'unavailable' || slotStatus === 'past';
                              const bookedPlayers = (slotStatus === 'booked' || slotStatus === 'booked-past') ? getBookedPlayerNames(court.id, slot.time, day) : [];
                              
                              // Check if this slot is part of a longer booking
                              const booking = getBooking ? getBooking(court.id, slot.time, day) : null;
                              const isPartOfLongerBooking = booking && booking.start_time !== booking.end_time;
                              
                              // Determine position in multi-slot booking
                              let slotPosition = 'single';
                              let showTopBorder = true;
                              let showBottomBorder = true;
                              
                              if (isPartOfLongerBooking && booking) {
                                const [slotHour, slotMinute] = slot.time.split(':').map(Number);
                                const slotStartMinutes = slotHour * 60 + slotMinute;
                                
                                const [bookingStartHour, bookingStartMinute] = booking.start_time.split(':').map(Number);
                                const bookingStartMinutes = bookingStartHour * 60 + bookingStartMinute;
                                
                                const [bookingEndHour, bookingEndMinute] = booking.end_time.split(':').map(Number);
                                const bookingEndMinutes = bookingEndHour * 60 + bookingEndMinute;
                                
                                if (slotStartMinutes === bookingStartMinutes) {
                                  slotPosition = 'first';
                                  showBottomBorder = false;
                                } else if (slotStartMinutes + 30 >= bookingEndMinutes) {
                                  slotPosition = 'last';
                                  showTopBorder = false;
                                } else {
                                  slotPosition = 'middle';
                                  showTopBorder = false;
                                  showBottomBorder = false;
                                }
                              }
                            
                             // Cross-highlight effects - unique court key for each day-court combination
                             const courtKey = `${day.toISOString()}-${courtIndex}`;
                             
                             // Calculate hour-partner slot for 1-hour highlight
                             const [hour, minute] = slot.time.split(':').map(Number);
                             const partnerTime = minute === 0 
                               ? `${hour.toString().padStart(2, '0')}:30`  // If :00, partner is :30
                               : `${hour.toString().padStart(2, '0')}:00`; // If :30, partner is :00
                             
                             const isTimeHighlighted = hoveredTimeSlot === slot.time || hoveredTimeSlot === partnerTime;
                             const isCourtHighlighted = hoveredCourtKey === courtKey;
                             const isCrossHighlight = isTimeHighlighted && isCourtHighlighted;
                             
                             // Debug logging
                             if (hoveredTimeSlot && (slot.time === '09:00' || slot.time === '09:30') && courtIndex === 0 && dayIndex === 0) {
                               console.log('🎯 Cross-Highlight Debug:', {
                                 slotTime: slot.time,
                                 partnerTime,
                                 hoveredTimeSlot,
                                 courtKey,
                                 hoveredCourtKey,
                                 isTimeHighlighted,
                                 isCourtHighlighted,
                                 isCrossHighlight
                               });
                             }
                             
                             // Steve Jobs-approved: Clean, consistent, intuitive with subtle hierarchy
                             let buttonClasses = `
                               group relative h-14 p-2 text-xs font-medium min-w-0 
                               flex flex-col items-center justify-center leading-tight
                               transition-all duration-200 ease-out rounded-none
                               focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-1
                               ${isCrossHighlight ? 'bg-primary/8 z-[3]' : ''}
                               ${(isTimeHighlighted || isCourtHighlighted) && !isCrossHighlight ? 'bg-primary/4' : ''}
                             `;
                            
                             // Add subtle background hierarchy for full vs half hours
                             const baseHierarchy = isFullHour 
                               ? '' 
                               : 'border-l border-l-border/10';
                            
                            buttonClasses += ` ${baseHierarchy}`;
                            
                            // Perfect sharp edges and connection for multi-slot bookings
                            if (isPartOfLongerBooking) {
                              // No rounded corners for clean connected look
                              if (slotPosition === 'first') {
                                // First slot of booking
                              } else if (slotPosition === 'last') {
                                // Last slot of booking
                              } else {
                                // Middle slot of booking
                              }
                              
                              // Seamless borders for connected slots
                              if (showTopBorder) {
                                buttonClasses += " border-t-2 border-r-2";
                              } else {
                                buttonClasses += " border-r-2";
                              }
                              if (showBottomBorder) {
                                buttonClasses += " border-b-2";
                              }
                            } else {
                              buttonClasses += isFullHour ? " border-t border-b" : " border-t border-r border-b";
                            }
                            
                            // Crystal clear color system - no confusion
                            if (court.locked) {
                               buttonClasses += ` 
                                 bg-muted text-muted-foreground border-muted
                                 cursor-not-allowed opacity-60
                               `;
                             } else if (slotStatus === 'past') {
                               buttonClasses += ` 
                                 bg-muted/40 text-muted-foreground border-muted
                                 cursor-not-allowed opacity-50
                               `;
                             } else if (slotStatus === 'unavailable') {
                               buttonClasses += ` 
                                 bg-muted/60 text-muted-foreground border-muted
                                 cursor-not-allowed opacity-70
                               `;
                                } else if (slotStatus === 'booked' || slotStatus === 'booked-past') {
                                 const isUserPartOfBooking = booking && currentUserName && 
                                   (booking.player_name?.trim() === currentUserName?.trim() || 
                                    booking.partner_name?.trim() === currentUserName?.trim());
                                 
                                 if (slotStatus === 'booked-past') {
                                   if (isUserPartOfBooking) {
                                      buttonClasses += ` 
                                        bg-primary/70 text-primary-foreground border-primary/70
                                        hover:bg-primary/80 hover:border-primary/80
                                        opacity-75 cursor-pointer
                                      `;
                                   } else {
                                     buttonClasses += ` 
                                       bg-muted text-muted-foreground border-muted
                                       opacity-75
                                     `;
                                   }
                                  } else if (isUserPartOfBooking) {
                                    // Your booking - unmistakable green with full hour emphasis
                                     if (isFullHour) {
                                       buttonClasses += ` 
                                         bg-primary text-primary-foreground border-primary
                                         hover:bg-primary/90 hover:border-primary/90
                                         shadow-md hover:shadow-lg
                                         cursor-pointer
                                       `;
                                     } else {
                                       buttonClasses += ` 
                                         bg-primary text-primary-foreground border-primary
                                         hover:bg-primary/90 hover:border-primary/90
                                         shadow-md hover:shadow-lg
                                         cursor-pointer
                                      `;
                                   }
                                 } else {
                                   // Others' booking - clear blue with full hour emphasis
                                    if (isFullHour) {
                                      buttonClasses += ` 
                                        bg-secondary text-secondary-foreground border-secondary
                                        hover:bg-secondary/90 hover:border-secondary/90
                                        shadow-md hover:shadow-lg
                                        cursor-pointer
                                      `;
                                    } else {
                                      buttonClasses += ` 
                                        bg-secondary text-secondary-foreground border-secondary
                                        hover:bg-secondary/90 hover:border-secondary/90
                                        shadow-md hover:shadow-lg
                                        cursor-pointer
                                      `;
                                   }
                                 }
                                } else {
                                // Available slots with full hour emphasis
                                const isPastDay = day < new Date() && !format(day, "yyyy-MM-dd").includes(format(new Date(), "yyyy-MM-dd"));
                                if (isPastDay) {
                                  buttonClasses += ` 
                                    bg-muted/30 text-muted-foreground border-muted
                                    cursor-not-allowed opacity-60
                                  `;
                                  } else {
                                     // Available slots - elegant full hour emphasis
                                     if (isFullHour) {
                                       buttonClasses += ` 
                                         bg-white text-foreground border-border
                                         hover:bg-primary/5 hover:border-primary/50 hover:text-primary
                                         active:scale-95
                                         shadow-sm hover:shadow-md
                                         cursor-pointer
                                       `;
                                    } else {
                                      buttonClasses += ` 
                                        bg-white/80 text-muted-foreground border-border/30
                                        hover:bg-primary/5 hover:border-primary/30 hover:text-foreground
                                        active:scale-95
                                        shadow-sm hover:shadow-md
                                        cursor-pointer opacity-80 hover:opacity-100
                                      `;
                                    }
                                 }
                             }
                          
                          const buttonContent = (
                              <Button
                                key={`${court.id}-${slot.time}-${day.toISOString()}`}
                                variant="outline"
                                disabled={isDisabled}
                                size="sm"
                                 className={buttonClasses}
                                 title={slotStatus === 'booked' ? bookedPlayers.join(', ') : (court.locked && court.lock_reason ? court.lock_reason : undefined)}
                                 onMouseEnter={() => {
                                   console.log('🎯 Hover Enter:', { time: slot.time, courtKey, dayIndex, courtIndex });
                                   setHoveredTimeSlot(slot.time);
                                   setHoveredCourtKey(courtKey);
                                 }}
                                 onMouseLeave={() => {
                                   console.log('🎯 Hover Leave:', { time: slot.time, courtKey });
                                   setHoveredTimeSlot(null);
                                   setHoveredCourtKey(null);
                                 }}
                                   onClick={(e) => {
                                    console.log(`🎯 CLICK HANDLER TRIGGERED - Court: ${court.number}, Time: ${slot.time}, Status: ${slotStatus}, Disabled: ${isDisabled}`);
                                    console.log(`🎯 CLICK EVENT:`, e);
                                    
                                    if (slotStatus === 'booked' || slotStatus === 'booked-past') {
                                      const booking = getBooking ? getBooking(court.id, slot.time, day) : null;
                                      
                                      const isUserPartOfBooking = booking && currentUserName && 
                                        (booking.player_name?.trim() === currentUserName?.trim() || 
                                         booking.partner_name?.trim() === currentUserName?.trim());
                                      
                                      if (isUserPartOfBooking && slotStatus !== 'booked-past') {
                                        // User can edit current booking - show edit dialog
                                        setSelectedBooking(booking);
                                        setShowEditDialog(true);
                                      } else {
                                        // Show info dialog for past bookings or others' bookings
                                        setSelectedSlotInfo({
                                          courtNumber: court.number,
                                          time: slot.time,
                                          date: day,
                                          players: bookedPlayers
                                        });
                                        setShowPlayerDialog(true);
                                      }
                                    } else if (!isDisabled) {
                                      console.log(`🎯 CALLING onSlotSelect for available slot`);
                                      onSlotSelect(court.id, slot.time, day);
                                    } else {
                                      console.log(`🎯 SLOT DISABLED - cannot click`);
                                    }
                                  }}
                              >
                                {court.locked && <Lock className="h-2 w-2" />}
                                {slotStatus === 'unavailable' && (
                                  <div className="text-xs line-through opacity-60">—</div>
                                )}
                                  {(slotStatus === 'booked' || slotStatus === 'booked-past') && (
                                   <div className="w-full h-full flex flex-col justify-between relative">
                                     {(() => {
                                       const booking = getBooking ? getBooking(court.id, slot.time, day) : null;
                                       const canEdit = canEditBooking(booking);
                                       return (
                                         <>
                                           <div className="px-0.5">
                                             <span className="block text-[11px] font-semibold overflow-hidden text-clip whitespace-nowrap leading-none text-left">
                                               {(bookedPlayers[0] || '').split(' ').filter(Boolean)[0] || ''}
                                             </span>
                                             {(() => { const parts = (bookedPlayers[0] || '').trim().split(' ').filter(Boolean); const ln = parts.length > 1 ? parts[parts.length - 1] : ''; return ln ? (
                                               <span className="block text-[11px] font-medium overflow-hidden text-clip whitespace-nowrap leading-none text-left">{ln}</span>
                                             ) : null; })()}
                                           </div>
                                           {bookedPlayers[1] && (
                                             <>
                                               <div className="h-px bg-border mx-0.5" />
                                               <span className="text-[11px] font-medium overflow-hidden text-clip whitespace-nowrap leading-none px-0.5 text-right">{bookedPlayers[1].split(' ')[0]}</span>
                                             </>
                                           )}
                                         </>
                                       );
                                     })()}
                                   </div>
                                 )}
                             </Button>
                          );

                          return court.locked && court.lock_reason ? (
                            <Tooltip key={`${court.id}-${slot.time}-${day.toISOString()}`}>
                              <TooltipTrigger asChild>
                                {buttonContent}
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{court.lock_reason}</p>
                              </TooltipContent>
                            </Tooltip>
                           ) : buttonContent;
                         })}
                       </div>
                     );
                      })}
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Player Info Dialog */}
        <Dialog open={showPlayerDialog} onOpenChange={setShowPlayerDialog}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>
                Buchungsdetails - Platz {selectedSlotInfo?.courtNumber}
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-3">
              <div className="text-sm text-muted-foreground">
                {selectedSlotInfo && format(selectedSlotInfo.date, 'EEEE, dd.MM.yyyy', { locale: de })} um {selectedSlotInfo?.time}
              </div>
              <div>
                <h4 className="font-medium mb-2">Gebuchte Spieler:</h4>
                <div className="space-y-1">
                  {selectedSlotInfo?.players.map((player, index) => (
                    <div key={index} className="text-sm bg-muted p-2 rounded">
                      {player}
                    </div>
                  ))}
                </div>
              </div>
              
              {/* Warteliste Button - nur für zukünftige Slots */}
              {clubId && selectedSlotInfo && !isPastSlotInClubTZ(selectedSlotInfo.date, selectedSlotInfo.time, clubTimezone) && (
                <div className="border-t pt-3">
                  <h4 className="font-medium mb-2">Auf Warteliste setzen:</h4>
                  <WaitlistButton
                    date={selectedSlotInfo.date}
                    time={selectedSlotInfo.time}
                    court={courts.find(c => c.number === selectedSlotInfo.courtNumber)!}
                    clubId={clubId}
                    onWaitlistAdded={() => {
                      onWaitlistAdded?.();
                      setShowPlayerDialog(false);
                    }}
                  />
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>

        {/* Edit Booking Dialog */}
        <EditBookingDialog
          open={showEditDialog}
          onOpenChange={setShowEditDialog}
          booking={selectedBooking}
          courtNumber={courts.find(c => c.id === selectedBooking?.court_id)?.number || 0}
          onBookingUpdated={() => {
            onBookingUpdated?.();
            setSelectedBooking(null);
          }}
        />
      </div>
    </TooltipProvider>
  );
};