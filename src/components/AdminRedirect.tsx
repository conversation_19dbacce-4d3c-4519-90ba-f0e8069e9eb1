import { Navigate } from "react-router-dom";
import { useIsMetaAdmin } from "@/contexts/TenantContext";
import { useAdminRole } from "@/hooks/useAdminRole";

export const AdminRedirect = () => {
  const isMetaAdmin = useIsMetaAdmin();
  const { hasAnyAdminRole } = useAdminRole();
  
  // Meta-admins can access any club admin area
  if (isMetaAdmin) {
    return <Navigate to="dashboard" replace />;
  }
  
  // Regular users need club admin rights
  if (hasAnyAdminRole) {
    return <Navigate to="dashboard" replace />;
  }
  
  // If no admin rights, show NotFound (404)
  return <Navigate to="/not-found" replace />;
};