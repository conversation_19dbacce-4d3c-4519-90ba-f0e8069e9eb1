import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import tennisHero from "@/assets/tennis-hero.jpg";
import { useAuth } from "@/contexts/AuthContext";
import { useClub } from "@/contexts/ClubContext";
import { supabase } from "@/integrations/supabase/client";

const Hero = () => {
  const { user } = useAuth();
  const { currentClub } = useClub();
  const navigate = useNavigate();

  // Redirect authenticated users based on their role
  useEffect(() => {
    if (user) {
      const checkUserRole = async () => {
        try {
          const { data: isAdminUser, error } = await supabase
            .rpc('has_any_admin_role', { _user_id: user.id });
          
          if (error) {
            console.error('Error checking admin role:', error);
            // Redirect to first club member area if available
            if (currentClub) {
              navigate(`/c/${currentClub.slug}/member`);
            }
            return;
          }
          
          if (isAdminUser) {
            // Check if user has a club context first
            if (currentClub) {
              navigate(`/c/${currentClub.slug}/admin`);
            } else {
              // Check if they have meta-admin permissions
              const { data: metaAdminData } = await supabase
                .from('meta_admin_permissions')
                .select('role')
                .eq('user_id', user.id)
                .or('expires_at.is.null,expires_at.gt.now()');
              
              if (metaAdminData && metaAdminData.length > 0) {
                navigate('/meta-admin');
              } else {
                // Fallback: redirect to first available club
                if (currentClub) {
                  navigate(`/c/${currentClub.slug}/member`);
                }
              }
            }
          } else {
            // Regular member - redirect to club-specific area
            if (currentClub) {
              navigate(`/c/${currentClub.slug}/member`);
            }
          }
        } catch (error) {
          console.error('Error checking role:', error);
          if (currentClub) {
            navigate(`/c/${currentClub.slug}/member`);
          }
        }
      };
      
      checkUserRole();
    }
  }, [user, currentClub, navigate]);

  return (
    <div className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background Image */}
      <div 
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{ backgroundImage: `url(${tennisHero})` }}
      >
        <div className="absolute inset-0 bg-black/40"></div>
      </div>
      
      {/* Content */}
      <div className="relative z-10 px-4 w-full flex justify-center">
        <div className="text-center text-white">
          <h1 className="text-4xl md:text-6xl font-bold mb-4">
            Tennis Club Management
          </h1>
          <p className="text-lg md:text-xl mb-8 max-w-2xl mx-auto">
            Verwalten Sie Ihren Tennisverein professionell und effizient
          </p>
        </div>
      </div>
    </div>
  );
};

export default Hero;