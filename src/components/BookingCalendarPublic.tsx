import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ChevronLeft, ChevronRight, Calendar } from "lucide-react";
import { useState, useEffect, useRef } from "react";
import { format } from "date-fns";
import { de } from "date-fns/locale";
import { supabase } from "@/integrations/supabase/client";
import { useTenant } from "@/contexts/TenantContext";
import { useClubTimezone } from "@/hooks/useClubTimezone";
import { isPastSlotInClubTZ } from "@/lib/timezone-utils";
import { ViewSelector } from "./booking/ViewSelector";
import { CompactHeader } from "./booking/CompactHeader";
import { DayView } from "./booking/DayView";

interface Court {
  id: string;
  number: number;
  locked: boolean;
  lock_reason: string | null;
}

interface TimeSlot {
  time: string;
  available: boolean;
  price?: number;
}

const BookingCalendarPublic = () => {
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [selectedCourt, setSelectedCourt] = useState<string | null>(null);
  const [courts, setCourts] = useState<Court[]>([]);
  const [courtAvailability, setCourtAvailability] = useState<any[]>([]);
  const [bookings, setBookings] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [view, setView] = useState<'day' | 'week' | 'compact'>('day'); // Default to day view
  
  console.log('🔍 PUBLIC BOOKING DEBUG - Current View State:', view);
  const viewAutoSet = useRef(false);
  const { club } = useTenant();
  const currentClubId = club?.id;
  const { timezone } = useClubTimezone();

  // 🔍 SHERLOCK: Tracking the evidence
  console.log('🕵️ SHERLOCK - Club Investigation:', { 
    club: club?.name, 
    clubId: currentClubId, 
    timezone 
  });

  useEffect(() => {
    console.log('🕵️ SHERLOCK - Effect Investigation:', {
      currentClubId,
      selectedDate: selectedDate.toISOString(),
      hasCurrentClubId: !!currentClubId
    });
    
    if (currentClubId) {
      console.log('🔍 SHERLOCK - Starting data loading for club:', currentClubId);
      loadCourts();
      loadCourtAvailability();
      loadBookings();
    } else {
      console.log('❌ SHERLOCK - No club ID found, investigation halted!');
    }
  }, [currentClubId, selectedDate]);

  const loadCourts = async () => {
    if (!currentClubId) {
      console.log('❌ SHERLOCK - Cannot load courts: No club ID');
      return;
    }
    
    console.log('🔍 SHERLOCK - Loading courts for club:', currentClubId);
    
    try {
      // For public view, we need to ensure proper access to courts data
      // The RLS policies should allow public/guest access to courts
      const { data, error } = await supabase
        .from("courts")
        .select("id,number,locked,lock_reason")
        .eq("club_id", currentClubId)
        .order("number");

      if (error) {
        console.log('❌ SHERLOCK - Court loading error:', error);
        console.log('🔍 SHERLOCK - Error details:', error.message, error.details, error.hint);
        throw error;
      }

      console.log('✅ SHERLOCK - Courts loaded successfully:', data);
      console.log('🔍 SHERLOCK - Court count:', data?.length || 0);
      
      if (!data || data.length === 0) {
        console.log('⚠️ SHERLOCK - No courts found! This could be an RLS policy issue for public access.');
      }
      
      setCourts(data || []);

      // Auto-set view based on number of courts
      if (!viewAutoSet.current && data && data.length > 0) {
        const optimalView = 'day'; // Always use day view for public
        console.log('🔍 SHERLOCK - Setting view to:', optimalView, 'for', data.length, 'courts');
        setView(optimalView);
        viewAutoSet.current = true;
      }
    } catch (error) {
      console.error('❌ SHERLOCK - Court loading failed:', error);
      // For debugging: Try to understand what's happening
      console.log('🔍 SHERLOCK - Auth state check:', { 
        hasAuthUser: !!supabase.auth.getUser(),
        currentClubId 
      });
    }
  };

  const loadBookings = async () => {
    if (!currentClubId) return;
    
    try {
      // Get start and end of the week for the selected date
      const startOfWeek = new Date(selectedDate);
      startOfWeek.setDate(selectedDate.getDate() - selectedDate.getDay());
      const endOfWeek = new Date(startOfWeek);
      endOfWeek.setDate(startOfWeek.getDate() + 6);

      const { data, error } = await supabase
        .from("bookings")
        .select("*")
        .eq("club_id", currentClubId)
        .gte("booking_date", startOfWeek.toISOString().split('T')[0])
        .lte("booking_date", endOfWeek.toISOString().split('T')[0]);

      if (error) throw error;

      console.log('📅 Loaded bookings for week:', data);
      // Anonymize player names for public view
      const anonymizedBookings = (data || []).map(booking => ({
        ...booking,
        player_name: "Belegt",
        partner_name: booking.partner_name ? "Belegt" : null
      }));
      setBookings(anonymizedBookings);
    } catch (error) {
      console.error('Error loading bookings:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadCourtAvailability = async () => {
    if (!currentClubId) return;
    
    try {
      const { data, error } = await supabase
        .from("court_availability")
        .select("*");

      if (error) throw error;

      console.log('📅 Loaded court availability:', data);
      setCourtAvailability(data || []);
    } catch (error) {
      console.error('Error loading court availability:', error);
    }
  };

  const generateTimeSlots = (): TimeSlot[] => {
    const slots: TimeSlot[] = [];
    for (let hour = 8; hour < 22; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
        slots.push({
          time: timeString,
          available: true, // Will be determined by booking data
        });
      }
    }
    return slots;
  };

  const getSlotStatus = (courtId: string, date: Date, time: string) => {
    // Check if slot is in the past
    if (isPastSlotInClubTZ(date, time, timezone)) {
      return 'past';
    }

    // Check for existing bookings
    const dateStr = format(date, 'yyyy-MM-dd');
    const booking = bookings.find(b => 
      b.court_id === courtId && 
      b.booking_date === dateStr && 
      b.start_time <= time && 
      b.end_time > time
    );

    if (booking) {
      return 'booked';
    }

    // Check court availability rules
    const courtAvail = courtAvailability.find(ca => ca.court_id === courtId);
    if (courtAvail) {
      const dayOfWeek = date.getDay();
      if (!courtAvail.days_of_week.includes(dayOfWeek)) {
        return 'unavailable';
      }
      
      if (time < courtAvail.start_time || time >= courtAvail.end_time) {
        return 'unavailable';
      }
    }

    return 'available';
  };

  const getBookedPlayerNames = (courtId: string, date: Date, time: string) => {
    const dateStr = format(date, 'yyyy-MM-dd');
    const booking = bookings.find(b => 
      b.court_id === courtId && 
      b.booking_date === dateStr && 
      b.start_time <= time && 
      b.end_time > time
    );

    if (booking) {
      return {
        player: booking.player_name,
        partner: booking.partner_name
      };
    }

    return null;
  };

  const navigateDate = (direction: 'prev' | 'next') => {
    const newDate = new Date(selectedDate);
    
    if (view === 'day') {
      newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1));
    } else if (view === 'week' || view === 'compact') {
      newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7));
    }
    
    setSelectedDate(newDate);
  };

  const handleSlotSelect = (courtId: string, time: string) => {
    // For public view, just show that login is required
    console.log('Slot selected, but login required for booking');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Lade Kalender...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with navigation and court selector */}
      <div className="flex flex-col gap-4">
        <CompactHeader
          selectedDate={selectedDate}
          onDateChange={setSelectedDate}
          onNavigate={navigateDate}
          view={view}
          onViewChange={setView}
        />
        
        <div className="flex flex-wrap items-center gap-4">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Platz:</span>
            <Select value={selectedCourt || "all"} onValueChange={(value) => setSelectedCourt(value === "all" ? null : value)}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Alle Plätze" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Alle Plätze</SelectItem>
                {courts.map((court) => (
                  <SelectItem key={court.id} value={court.id}>
                    Platz {court.number}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <ViewSelector view={view} onViewChange={setView} />
        </div>
      </div>

      {/* Calendar Views */}
      {view === 'day' && (
        <>
          {console.log('🔍 SHERLOCK - Rendering DayView with courts:', courts.length)}
          {console.log('🔍 SHERLOCK - Selected court filter:', selectedCourt)}
          {console.log('🔍 SHERLOCK - Filtered courts:', selectedCourt && selectedCourt !== "all" ? courts.filter(c => c.id === selectedCourt) : courts)}
          <DayView
            selectedDate={selectedDate}
            courts={selectedCourt && selectedCourt !== "all" ? courts.filter(c => c.id === selectedCourt) : courts}
            timeSlots={generateTimeSlots()}
            getSlotStatus={(courtId: string, time: string) => getSlotStatus(courtId, selectedDate, time)}
            getBookedPlayerNames={(courtId: string, time: string) => {
              const players = getBookedPlayerNames(courtId, selectedDate, time);
              return players ? [players.player, players.partner].filter(Boolean) : [];
            }}
            onSlotSelect={handleSlotSelect}
          />
        </>
      )}
    </div>
  );
};

export default BookingCalendarPublic;