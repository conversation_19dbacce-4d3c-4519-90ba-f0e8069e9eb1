import { ChevronDown, User, Users } from 'lucide-react';
import { useAccount } from '@/contexts/AccountContext';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

const AccountSwitcher = () => {
  const { accounts, activeAccount, switchAccount, isLoading } = useAccount();

  if (isLoading || !activeAccount || accounts.length <= 1) {
    return null;
  }

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  const getDisplayName = (account: typeof activeAccount) => {
    if (!account) return '';
    const parts = [];
    if (account.title) parts.push(account.title);
    if (account.first_name) parts.push(account.first_name);
    if (account.last_name) parts.push(account.last_name);
    return parts.join(' ') || account.email;
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 gap-2 px-3">
          <Avatar className="h-6 w-6">
            <AvatarImage src="" alt={getDisplayName(activeAccount)} />
            <AvatarFallback className="bg-primary text-primary-foreground text-xs">
              {getInitials(activeAccount.first_name, activeAccount.last_name)}
            </AvatarFallback>
          </Avatar>
          <span className="text-sm font-medium max-w-32 truncate">
            {getDisplayName(activeAccount)}
          </span>
          <ChevronDown className="h-3 w-3 opacity-50" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-64" align="end">
        <div className="px-2 py-1.5">
          <p className="text-xs text-muted-foreground">Profil wechseln</p>
        </div>
        <DropdownMenuSeparator />
        {accounts.map((account) => (
          <DropdownMenuItem
            key={account.id}
            onClick={() => switchAccount(account.id)}
            className={`flex items-center gap-3 p-3 ${
              activeAccount.id === account.id ? 'bg-accent' : ''
            }`}
          >
            <Avatar className="h-8 w-8">
              <AvatarImage src="" alt={getDisplayName(account)} />
              <AvatarFallback className="bg-primary text-primary-foreground">
                {getInitials(account.first_name, account.last_name)}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium truncate">
                {getDisplayName(account)}
              </p>
              <p className="text-xs text-muted-foreground truncate">
                {account.account_type}
              </p>
            </div>
            {account.is_primary && (
              <div className="text-xs text-primary">Haupt</div>
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default AccountSwitcher;