
import { User } from "@supabase/supabase-js";
import { Button } from "@/components/ui/button";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { LogOut, Bell, Settings } from "lucide-react";
import { Link } from "react-router-dom";
import { useClubUrl, useTenant } from "@/contexts/TenantContext";
import { useAdminRole } from "@/hooks/useAdminRole";
import { useAccount } from "@/contexts/AccountContext";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { SidebarTrigger } from "@/components/ui/sidebar";
import AccountSwitcher from "./AccountSwitcher";

interface MemberHeaderProps {
  user: User;
}

const MemberHeader = ({ user }: MemberHeaderProps) => {
  const buildClubUrl = useClubUrl();
  const { isClubAdmin } = useAdminRole();
  const { club, isLoading: clubLoading } = useTenant();
  const { activeAccount } = useAccount();

  const handleSignOut = async () => {
    try {
      await supabase.auth.signOut();
      toast.success("Erfolgreich abgemeldet");
    } catch (error) {
      toast.error("Fehler beim Abmelden");
    }
  };

  const getInitials = (firstName?: string, lastName?: string, email?: string) => {
    if (firstName && lastName) {
      return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
    }
    return email?.substring(0, 2).toUpperCase() || "U";
  };

  const getDisplayName = () => {
    console.log('Active account:', activeAccount); // Debug log
    
    if (activeAccount) {
      const parts = [];
      if (activeAccount.title) parts.push(activeAccount.title);
      if (activeAccount.first_name) parts.push(activeAccount.first_name);
      if (activeAccount.last_name) parts.push(activeAccount.last_name);
      
      const fullName = parts.join(' ');
      if (fullName) {
        return fullName;
      }
      
      // Fallback to email if no name parts available
      return activeAccount.email || "Unbekannt";
    }
    
    // Fallback to user email if no active account
    return user.email || "Benutzer";
  };

  return (
    <header className="h-16 border-b border-border bg-background">
      <div className="h-full px-6 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <SidebarTrigger className="mr-2" />
          <h1 className="text-lg font-semibold">Mitgliederbereich</h1>
        </div>

        <div className="flex items-center gap-4">
          <AccountSwitcher />
          
          <Button variant="ghost" size="icon">
            <Bell className="h-4 w-4" />
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                <Avatar className="h-8 w-8">
                  <AvatarImage src="" alt={getDisplayName()} />
                  <AvatarFallback className="bg-primary text-primary-foreground">
                    {getInitials(activeAccount?.first_name, activeAccount?.last_name, user.email || undefined)}
                  </AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-72" align="end" forceMount>
              <div className="flex items-center space-x-3 p-4">
                <Avatar className="h-12 w-12">
                  <AvatarImage src="" alt={getDisplayName()} />
                  <AvatarFallback className="bg-primary text-primary-foreground text-lg">
                    {getInitials(activeAccount?.first_name, activeAccount?.last_name, user.email || undefined)}
                  </AvatarFallback>
                </Avatar>
                <div className="flex flex-col space-y-1 min-w-0">
                  <p className="text-base font-semibold leading-none truncate">{getDisplayName()}</p>
                  <p className="text-sm text-muted-foreground truncate">
                    {activeAccount?.email || user.email}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {activeAccount?.account_type || "Mitglied"}
                  </p>
                </div>
              </div>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                Profil bearbeiten
              </DropdownMenuItem>
              {isClubAdmin && club && !clubLoading && (
                <>
                  <DropdownMenuItem asChild>
                    <Link to={buildClubUrl("/admin")}>
                      <Settings className="mr-2 h-4 w-4" />
                      Zur Verwaltung
                    </Link>
                  </DropdownMenuItem>
                </>
              )}
              <DropdownMenuItem onClick={handleSignOut}>
                <LogOut className="mr-2 h-4 w-4" />
                <span>Abmelden</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
};

export default MemberHeader;
