import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Clock, MapPin, Calendar, X, Zap, Users } from "lucide-react";
import { useWaitlist } from "@/hooks/useWaitlist";
import { useToast } from "@/hooks/use-toast";
import { useNameMapping } from "@/hooks/useNameMapping";
import { format } from "date-fns";
import { de } from "date-fns/locale";

interface WaitlistEntry {
  id: string;
  club_id: string;
  user_id: string;
  player_name: string;
  partner_name?: string;
  preferred_date: string;
  start_time_range: string;
  end_time_range: string;
  preferred_courts: string[];
  auto_booking_enabled: boolean;
  position: number;
  status: 'waiting' | 'converted_to_booking' | 'cancelled' | 'expired';
  created_at: string;
  updated_at: string;
}

export const WaitlistSection = () => {
  const [waitlistEntries, setWaitlistEntries] = useState<WaitlistEntry[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { getUserWaitlistEntries, cancelWaitlistEntry } = useWaitlist();
  const { toast } = useToast();
  const { mapPlayerAndPartner } = useNameMapping();

  useEffect(() => {
    loadWaitlistEntries();
  }, []);

  const loadWaitlistEntries = async () => {
    try {
      setIsLoading(true);
      const entries = await getUserWaitlistEntries();
      // Filter out expired entries (past dates) and cancelled/expired status
      const activeEntries = entries.filter(entry => {
        const isNotExpiredStatus = entry.status === 'waiting';
        const isNotPastDate = new Date(entry.preferred_date) >= new Date(new Date().toDateString());
        return isNotExpiredStatus && isNotPastDate;
      });
      setWaitlistEntries(activeEntries);
    } catch (error: any) {
      console.error('Error loading waitlist entries:', error);
      toast({
        title: "Fehler",
        description: "Wartelisten-Einträge konnten nicht geladen werden.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = async (entryId: string) => {
    try {
      await cancelWaitlistEntry(entryId);
      toast({
        title: "Erfolgreich",
        description: "Wartelisten-Eintrag wurde storniert.",
      });
      await loadWaitlistEntries();
    } catch (error: any) {
      toast({
        title: "Fehler", 
        description: error.message || "Stornierung fehlgeschlagen.",
        variant: "destructive",
      });
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader className="pb-1 pt-2">
          <CardTitle className="text-xs font-medium flex items-center gap-1">
            <Clock className="h-3 w-3" />
            Meine Wartelisten
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0 pb-2">
          <p className="text-xs text-muted-foreground">Laden...</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-1 pt-2">
        <CardTitle className="text-xs font-medium flex items-center gap-1">
          <Clock className="h-3 w-3" />
          Meine Wartelisten
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0 pb-2">
        {waitlistEntries.length === 0 ? (
          <p className="text-xs text-muted-foreground text-center py-2">
            Sie haben aktuell keine aktiven Wartelisten-Einträge.
          </p>
        ) : (
          <div className="space-y-2">
            {waitlistEntries.map((entry) => (
              <div
                key={entry.id}
                className="border rounded-lg p-2 space-y-1"
              >
                <div className="flex items-start justify-between">
                  <div className="space-y-2 flex-1">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">
                        {format(new Date(entry.preferred_date), "EEEE, dd. MMMM yyyy", { locale: de })}
                      </span>
                      {entry.auto_booking_enabled ? (
                        <Badge variant="secondary" className="bg-primary/10 text-primary">
                          <Zap className="h-3 w-3 mr-1" />
                          Auto-Zusage aktiv
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="text-muted-foreground">
                          Auto-Zusage inaktiv
                        </Badge>
                      )}
                    </div>

                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        {entry.start_time_range} - {entry.end_time_range} Uhr
                      </span>
                    </div>

                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        {(() => {
                          const mappedNames = mapPlayerAndPartner(entry.player_name, entry.partner_name);
                          console.log('🔍 WaitlistSection: Mapping names for entry:', {
                            original: { player: entry.player_name, partner: entry.partner_name },
                            mapped: mappedNames
                          });
                          return `${mappedNames.playerName}${mappedNames.partnerName ? ` & ${mappedNames.partnerName}` : ''}`;
                        })()}
                      </span>
                    </div>

                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        {entry.preferred_courts.length === 0 
                          ? "Alle Plätze" 
                          : `${entry.preferred_courts.length} Plätze ausgewählt`}
                      </span>
                    </div>

                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">
                        Position #{entry.position}
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        Erstellt am {format(new Date(entry.created_at), "dd.MM.yyyy HH:mm", { locale: de })}
                      </span>
                    </div>
                  </div>

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleCancel(entry.id)}
                    className="text-destructive hover:text-destructive hover:bg-destructive/10"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};