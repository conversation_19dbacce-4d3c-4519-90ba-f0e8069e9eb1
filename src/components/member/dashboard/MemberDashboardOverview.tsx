
import { useEffect, useMemo, useState } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { supabase } from "@/integrations/supabase/client";
import { Calendar, Megaphone } from "lucide-react";
import { format, parse } from "date-fns";
import { de } from "date-fns/locale";

interface Booking {
  id: string;
  booking_date: string; // yyyy-MM-dd
  start_time: string;   // HH:mm:ss
  end_time: string;     // HH:mm:ss
  court_id: string;
  player_name: string;
  partner_name?: string | null;
  created_by?: string | null;
}

interface Court { id: string; number: number }

interface ClubNewsItem { title: string; date?: string; link?: string }

interface Team {
  id: string;
  name: string;
  description?: string;
  league?: string;
  captain_account_id?: string;
}

interface UserNameMapping {
  [oldName: string]: string; // oldName -> currentName
}

const MemberDashboardOverview = () => {
  const [userId, setUserId] = useState<string | null>(null);
  const [fullName, setFullName] = useState<string>("");
  const [upcoming, setUpcoming] = useState<Booking[]>([]);
  const [recent, setRecent] = useState<Booking[]>([]);
  const [courtsMap, setCourtsMap] = useState<Record<string, Court>>({});
  const [news, setNews] = useState<ClubNewsItem[]>([]);
  const [roles, setRoles] = useState<string[]>([]);
  const [teams, setTeams] = useState<Team[]>([]);
  const [nameMapping, setNameMapping] = useState<UserNameMapping>({});
  const [loading, setLoading] = useState(true);

  const getCurrentName = (oldName: string) => {
    return nameMapping[oldName] || oldName;
  };

  useEffect(() => {
    const load = async () => {
      try {
        const { data: userRes } = await supabase.auth.getUser();
        const uid = userRes.user?.id || null;
        setUserId(uid);

        // load profile to get the full name
        let name = "";
        if (uid) {
          const { data: profile } = await supabase
            .from("profiles")
            .select("first_name, last_name")
            .eq("id", uid)
            .maybeSingle();
          if (profile?.first_name || profile?.last_name) {
            name = `${profile.first_name ?? ""} ${profile.last_name ?? ""}`.trim();
          }
          const { data: rolesData } = await supabase.rpc('get_user_roles', { _user_id: uid });
          const globalRoles = (rolesData || []).map((r: { role: string }) => r.role);
          
          // Also check club membership roles
          const { data: clubMemberships } = await supabase
            .from('club_memberships')
            .select('role')
            .eq('user_id', uid)
            .eq('is_active', true);
          
          const clubRoles = (clubMemberships || []).map(m => m.role).filter(r => r !== 'member');
          
          // Combine all roles, removing duplicates
          const allRoles = [...new Set([...globalRoles, ...clubRoles])];
          setRoles(allRoles);
        }
        setFullName(name);

        // Load courts for number mapping
        const { data: courts } = await supabase
          .from("courts")
          .select("id, number")
          .order("number");
        const cMap: Record<string, Court> = {};
        (courts || []).forEach(c => { cMap[c.id] = c; });
        setCourtsMap(cMap);

        // Load all active club members to create name mapping
        const { data: clubMembersData } = await supabase
          .from("club_memberships")
          .select("first_name, last_name")
          .eq("is_active", true);

        const { data: profilesData } = await supabase
          .from("profiles")
          .select("first_name, last_name");

        // Create name mapping from all possible name combinations
        const mapping: UserNameMapping = {};
        
        // Add club memberships names
        (clubMembersData || []).forEach(member => {
          if (member.first_name || member.last_name) {
            const currentName = `${member.first_name ?? ""} ${member.last_name ?? ""}`.trim();
            if (currentName) {
              // Map various possible old name formats to current name
              mapping[currentName] = currentName;
              mapping[member.first_name || ""] = currentName;
              mapping[member.last_name || ""] = currentName;
            }
          }
        });

        // Add profiles names
        (profilesData || []).forEach(profile => {
          if (profile.first_name || profile.last_name) {
            const currentName = `${profile.first_name ?? ""} ${profile.last_name ?? ""}`.trim();
            if (currentName) {
              mapping[currentName] = currentName;
              mapping[profile.first_name || ""] = currentName;
              mapping[profile.last_name || ""] = currentName;
            }
          }
        });

        setNameMapping(mapping);

        // Date boundaries
        const todayStr = format(new Date(), "yyyy-MM-dd");
        const pastSince = new Date();
        pastSince.setDate(pastSince.getDate() - 90);
        const pastStr = format(pastSince, "yyyy-MM-dd");

        // Upcoming (>= today)
        const { data: upcomingData } = await supabase
          .from("bookings")
          .select("*")
          .gte("booking_date", todayStr)
          .order("booking_date", { ascending: true });

        // Recent past (last 90 days)
        const { data: recentData } = await supabase
          .from("bookings")
          .select("*")
          .gte("booking_date", pastStr)
          .lte("booking_date", todayStr)
          .order("booking_date", { ascending: false });

        const belongsToUser = (b: Booking) => {
          if (!name) return false;
          const currentPlayerName = getCurrentName(b.player_name);
          const currentPartnerName = b.partner_name ? getCurrentName(b.partner_name) : null;
          return currentPlayerName === name || currentPartnerName === name;
        };

        setUpcoming((upcomingData || []).filter(belongsToUser).slice(0, 5));
        setRecent((recentData || []).filter(belongsToUser));

        // News (optional via system_settings -> key = 'club_news', value: [{title, date, link}])
        const { data: settings } = await supabase
          .from("system_settings")
          .select("value")
          .eq("key", "club_news")
          .maybeSingle();
        if (settings?.value && Array.isArray(settings.value)) {
          setNews(settings.value as unknown as ClubNewsItem[]);
        }
      } finally {
        setLoading(false);
      }
    };

    load();
  }, []);

  // Realtime-Updates: Buchungen aktuell halten
  useEffect(() => {
    if (!userId && !fullName) return;

    const refresh = async () => {
      const todayStr = format(new Date(), "yyyy-MM-dd");
      const pastSince = new Date();
      pastSince.setDate(pastSince.getDate() - 90);
      const pastStr = format(pastSince, "yyyy-MM-dd");

      const { data: upcomingData } = await supabase
        .from("bookings")
        .select("*")
        .gte("booking_date", todayStr)
        .order("booking_date", { ascending: true });

      const { data: recentData } = await supabase
        .from("bookings")
        .select("*")
        .gte("booking_date", pastStr)
        .lte("booking_date", todayStr)
        .order("booking_date", { ascending: false });

      const belongs = (b: Booking) => {
        if (!fullName) return false;
        const currentPlayerName = getCurrentName(b.player_name);
        const currentPartnerName = b.partner_name ? getCurrentName(b.partner_name) : null;
        return currentPlayerName === fullName || currentPartnerName === fullName;
      };

      setUpcoming((upcomingData || []).filter(belongs).slice(0, 5));
      setRecent((recentData || []).filter(belongs));
    };

    const channel = supabase
      .channel('bookings-dashboard')
      .on('postgres_changes', { event: '*', schema: 'public', table: 'bookings' }, () => {
        refresh();
      })
      .subscribe();

    refresh();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [userId, fullName, nameMapping]);

  const frequentPartners = useMemo(() => {
    if (!fullName) return [] as { name: string; count: number }[];
    const counts = new Map<string, number>();
    for (const b of recent) {
      const currentPlayerName = getCurrentName(b.player_name);
      const currentPartnerName = b.partner_name ? getCurrentName(b.partner_name) : "";
      const other = currentPlayerName === fullName ? currentPartnerName : (currentPartnerName === fullName ? currentPlayerName : "");
      if (!other) continue;
      counts.set(other, (counts.get(other) || 0) + 1);
    }
    return Array.from(counts.entries())
      .map(([name, count]) => ({ name, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);
  }, [recent, fullName, nameMapping]);

  const toDateTime = (b: Booking) => {
    // Combine booking_date and start_time to a JS Date
    // start_time format HH:mm:ss
    return parse(`${b.booking_date} ${b.start_time}`, "yyyy-MM-dd HH:mm:ss", new Date());
  };

  const nextMatches = useMemo(() => {
    const now = new Date();
    // Filter out expired matches (past date/time)
    const activeMatches = upcoming.filter(match => {
      const matchDateTime = toDateTime(match);
      return matchDateTime > now;
    });
    return activeMatches.sort((a, b) => toDateTime(a).getTime() - toDateTime(b).getTime());
  }, [upcoming]);

  return (
    <>
      {/* Nächste Matches */}
      <Card className="md:col-span-1">
        <CardHeader className="pb-1 pt-2">
          <CardTitle className="text-xs font-medium flex items-center gap-1">
            <Calendar className="h-3 w-3" /> Nächste Matches
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0 pb-2">
          {loading ? (
            <p className="text-xs text-muted-foreground">Laden…</p>
          ) : nextMatches.length === 0 ? (
            <p className="text-xs text-muted-foreground">Keine kommenden Matches</p>
          ) : (
            <ul className="space-y-1">
              {nextMatches.slice(0, 3).map((m) => {
                const currentPlayerName = getCurrentName(m.player_name);
                const currentPartnerName = m.partner_name ? getCurrentName(m.partner_name) : null;
                const other = currentPlayerName === fullName ? (currentPartnerName || "-") : currentPlayerName;
                const court = courtsMap[m.court_id]?.number;
                return (
                  <li key={m.id} className="text-xs">
                    <div className="flex flex-col gap-0.5">
                      <span className="truncate font-medium">{other}</span>
                      <span className="text-muted-foreground text-xs">
                        {format(toDateTime(m), "dd.MM HH:mm", { locale: de })}
                        {typeof court === 'number' ? ` · P${court}` : ""}
                      </span>
                    </div>
                  </li>
                );
              })}
            </ul>
          )}
        </CardContent>
      </Card>

      {/* Vereinsnews */}
      <Card className="md:col-span-1">
        <CardHeader className="pb-1 pt-2">
          <CardTitle className="text-xs font-medium flex items-center gap-1">
            <Megaphone className="h-3 w-3" /> Vereinsnews
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0 pb-2">
          {loading ? (
            <p className="text-xs text-muted-foreground">Laden…</p>
          ) : news.length === 0 ? (
            <p className="text-xs text-muted-foreground">Keine News vorhanden</p>
          ) : (
            <ul className="space-y-1">
              {news.slice(0, 3).map((n, idx) => (
                <li key={idx} className="text-xs">
                  <div className="flex flex-col gap-0.5">
                    <span className="truncate font-medium">{n.title}</span>
                    {n.date && (
                      <span className="text-muted-foreground text-xs">{format(new Date(n.date), "dd.MM.")}</span>
                    )}
                  </div>
                </li>
              ))}
            </ul>
          )}
        </CardContent>
      </Card>

    </>
  );
};

export default MemberDashboardOverview;
