
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Clock, CheckCircle, AlertCircle } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { useClub } from "@/contexts/ClubContext";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

interface WorkTarget {
  target_hours: number;
  completed_hours: number;
  is_manually_exempt: boolean;
  is_auto_exempt: boolean;
  exemption_reason?: string;
  auto_exemption_reason?: string;
}

export const WorkServiceProgress = () => {
  const { user } = useAuth();
  const { currentClubId } = useClub();
  const { toast } = useToast();
  const [workTarget, setWorkTarget] = useState<WorkTarget | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user && currentClubId) {
      loadWorkTarget();
    }
  }, [user, currentClubId]);

  const loadWorkTarget = async () => {
    try {
      const currentYear = new Date().getFullYear();
      
      const { data, error } = await supabase
        .from('member_work_service_targets')
        .select('*')
        .eq('user_id', user!.id)
        .eq('club_id', currentClubId)
        .eq('year', currentYear)
        .maybeSingle();

      if (error) {
        console.error('Error loading work target:', error);
        toast({
          title: "Fehler",
          description: "Arbeitsstunden konnten nicht geladen werden.",
          variant: "destructive",
        });
        return;
      }

      if (data) {
        setWorkTarget(data);
      } else {
        // No record exists yet, show default state
        setWorkTarget({
          target_hours: 20, // Default value
          completed_hours: 0,
          is_manually_exempt: false,
          is_auto_exempt: false,
        });
      }
    } catch (error) {
      console.error('Error loading work target:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Card className="md:col-span-1">
        <CardHeader className="pb-1 pt-2">
          <CardTitle className="text-xs font-medium flex items-center gap-1">
            <Clock className="h-3 w-3" />
            Arbeitsstunden
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0 pb-2">
          <div className="animate-pulse space-y-2">
            <div className="h-3 bg-muted rounded w-3/4"></div>
            <div className="h-2 bg-muted rounded"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!workTarget) {
    return null;
  }

  const isExempt = workTarget.is_manually_exempt || workTarget.is_auto_exempt;
  const progressPercentage = isExempt ? 100 : Math.min((workTarget.completed_hours / workTarget.target_hours) * 100, 100);
  const remainingHours = Math.max(workTarget.target_hours - workTarget.completed_hours, 0);
  const isCompleted = workTarget.completed_hours >= workTarget.target_hours;

  const getStatusBadge = () => {
    if (isExempt) {
      return (
        <Badge variant="secondary" className="bg-blue-100 text-blue-800 hover:bg-blue-100 text-xs">
          <CheckCircle className="h-2 w-2 mr-1" />
          Befreit
        </Badge>
      );
    }
    
    if (isCompleted) {
      return (
        <Badge variant="secondary" className="bg-green-100 text-green-800 hover:bg-green-100 text-xs">
          <CheckCircle className="h-2 w-2 mr-1" />
          Erfüllt
        </Badge>
      );
    }
    
    if (remainingHours > 0) {
      return (
        <Badge variant="secondary" className="bg-orange-100 text-orange-800 hover:bg-orange-100 text-xs">
          <AlertCircle className="h-2 w-2 mr-1" />
          Offen
        </Badge>
      );
    }
    
    return null;
  };

  return (
    <Card className="md:col-span-1">
      <CardHeader className="pb-1 pt-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-xs font-medium flex items-center gap-1">
            <Clock className="h-3 w-3" />
            Arbeitsstunden {new Date().getFullYear()}
          </CardTitle>
          {getStatusBadge()}
        </div>
      </CardHeader>
      <CardContent className="pt-0 pb-2 space-y-1">
        {isExempt ? (
          <div className="space-y-1">
            <p className="text-xs font-medium text-foreground">Befreiung erhalten</p>
            <p className="text-xs text-muted-foreground">
              {workTarget.exemption_reason || workTarget.auto_exemption_reason || "Automatische Befreiung"}
            </p>
          </div>
        ) : (
          <>
            <div className="space-y-2">
              <div className="flex justify-between text-xs">
                <span className="text-muted-foreground">Fortschritt</span>
                <span className="font-medium">
                  {workTarget.completed_hours} / {workTarget.target_hours} h
                </span>
              </div>
              <Progress value={progressPercentage} className="h-2" />
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>{Math.round(progressPercentage)}%</span>
                {!isCompleted && <span>{remainingHours}h verbleibend</span>}
              </div>
            </div>

            {isCompleted && (
              <div className="p-2 bg-green-50 border border-green-200 rounded-md">
                <p className="text-xs text-green-800 font-medium">
                  🎉 Erfolgreich erfüllt!
                </p>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
};
