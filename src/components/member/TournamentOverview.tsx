
import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Trophy, Calendar, Users, UserPlus, Eye, CheckCircle, Clock, MapPin, Info, Edit } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';
import { useClub } from '@/contexts/ClubContext';
import { useClubTimezone } from '@/hooks/useClubTimezone';
import { formatDateInClubTZ, formatDateTimeInClubTZ, toClubTimezone } from '@/lib/timezone-utils';
import { getTodayInTimezone } from '@/lib/utils';
import { useTournamentManagement } from '@/hooks/useTournamentManagement';
import { useTournamentFormatConfig } from '@/hooks/useTournamentFormatConfig';
import { MatchResultEntry } from '@/components/tournaments/MatchResultEntry';

interface Tournament {
  id: string;
  club_id: string;
  name: string;
  description?: string;
  status: 'registration' | 'active' | 'completed' | 'cancelled';
  format_type?: string;
  format_config?: any;
  registration_start?: string;
  registration_end?: string;
  tournament_start?: string;
  tournament_end?: string;
  max_participants?: number;
  allow_guests: boolean;
  created_at: string;
  updated_at: string;
}

interface TournamentParticipant {
  id: string;
  tournament_id: string;
  club_id: string;
  user_id?: string;
  guest_name?: string;
  guest_email?: string;
  phone?: string;
  is_guest: boolean;
  seeding?: number;
  status: 'registered' | 'confirmed' | 'withdrawn' | 'disqualified';
  registration_date: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

interface TournamentMatch {
  id: string;
  tournament_id: string;
  club_id: string;
  round_number: number;
  match_number: number;
  participant1_id?: string;
  participant2_id?: string;
  court_id?: string;
  scheduled_at?: string;
  scheduled_date?: string;
  scheduled_time?: string;
  actual_start?: string;
  actual_end?: string;
  score_json?: any;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled' | 'walkover';
  winner_id?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
  booking_id?: string;
  estimated_duration_minutes?: number;
  tournaments?: {
    id: string;
    name: string;
    status: string;
    format_type?: string;
  };
  courts?: {
    id: string;
    number: number;
  };
  participant1?: {
    id: string;
    user_id?: string;
    guest_name?: string;
    is_guest: boolean;
    profiles?: {
      first_name: string;
      last_name: string;
    } | null;
  } | null;
  participant2?: {
    id: string;
    user_id?: string;
    guest_name?: string;
    is_guest: boolean;
    profiles?: {
      first_name: string;
      last_name: string;
    } | null;
  } | null;
}

// Tournament format descriptions mapping
const TOURNAMENT_FORMAT_NAMES = {
  'knockout': 'K.o.-System (Single Elimination)',
  'round_robin': 'Round Robin (Jeder gegen jeden)',
  'compass': 'Compass Draw (E/W/N/S)',
  'groups_to_ko': 'Gruppen → K.o.',
  'double_elimination': 'Doppel-K.o. (Double Elimination)',
  'swiss': 'Schweizer System',
  'qualifying_main_draw': 'Qualifying + Main Draw',
  'uts': 'UTS (Universal Tennis Scoring)',
  'team_tie': 'Team Tie (Davis Cup Style)',
  'team_challenge': 'Team Challenge',
  'timeboxed': 'Timeboxed Tournament',
  'timeboxed_doubles': 'Rotationsdoppel',
  'social_mixer': 'Social Mixer (Partner Rotation)',
  'pro_set': 'Pro Set Event',
  'king_of_court': 'King/Queen of the Court',
  'fast4_short_sets': 'Fast4 / Short Sets',
  'tiebreak_series': 'Tiebreak-Series',
  'match_tiebreak_ko': 'Match-Tiebreak K.O.',
  'challenge_ladder': 'Challenge Ladder',
  'ladder_box_league': 'Ladder Box Liga',
  'league': 'Liga (Medenspiel Style)',
  'cup_competition': 'Pokal-Wettbewerb'
};

// Tournament format detailed descriptions
const TOURNAMENT_FORMAT_DESCRIPTIONS = {
  'knockout': 'Bei diesem klassischen K.o.-System scheidet jeder Spieler nach einer Niederlage aus. Der Gewinner steht fest, sobald alle Runden gespielt sind.',
  'round_robin': 'Jeder Spieler spielt gegen jeden anderen Teilnehmer. Der Gewinner wird durch die meisten Siege oder das beste Satzverhältnis ermittelt.',
  'compass': 'Spieler werden nach der ersten Runde in vier Gruppen (Nord, Süd, Ost, West) aufgeteilt und spielen separate K.o.-Turniere.',
  'groups_to_ko': 'Zunächst spielen die Teilnehmer in Gruppen (Round Robin), dann qualifizieren sich die Besten für ein K.o.-System.',
  'double_elimination': 'Jeder Spieler muss zwei Mal verlieren, um ausgeschieden zu sein. Es gibt eine Gewinner- und eine Verlierer-Klammer.',
  'swiss': 'Teilnehmer mit ähnlichen Ergebnissen werden in jeder Runde gegeneinander gelost. Keine Ausscheidung, feste Rundenanzahl.',
  'qualifying_main_draw': 'Schwächere Spieler spielen zunächst eine Qualifikation, um sich für das Hauptfeld zu qualifizieren.',
  'uts': 'Revolutionäres Tennis-Format ohne traditionelle Sätze! Gespielt wird nur mit Punkten bis 4 Spiele pro Satz. Bei 3:3 entscheidet ein Sudden Death Punkt.',
  'team_tie': 'Teams spielen gegeneinander mit mehreren Einzeln und Doppeln pro Begegnung (wie Davis Cup).',
  'team_challenge': 'Team-Wettkampf mit verschiedenen Spielformaten und Punktesystem.',
  'timeboxed': 'Matches haben eine feste Zeitbegrenzung. Bei Zeitablauf entscheidet der aktuelle Spielstand.',
  'timeboxed_doubles': 'Doppel-Turnier mit rotierenden Partnern und fester Spielzeit pro Runde.',
  'social_mixer': 'Geselliges Format mit wechselnden Partnern. Jeder spielt mit und gegen verschiedene Spieler.',
  'pro_set': 'Kurzes Format: Ein Satz bis 8 Spiele (mit Tiebreak bei 8:8).',
  'king_of_court': 'Der Gewinner bleibt auf dem Platz, der Verlierer wird durch einen neuen Herausforderer ersetzt.',
  'fast4_short_sets': 'Schnelles Tennis mit verkürzten Sätzen (bis 4 Spiele) und speziellen Regeln.',
  'tiebreak_series': 'Turnier besteht ausschließlich aus Tiebreaks (bis 7 oder 10 Punkte).',
  'match_tiebreak_ko': 'K.o.-System, aber jedes Match besteht nur aus einem Match-Tiebreak (bis 10 Punkte).',
  'challenge_ladder': 'Dauerturnier: Spieler können andere herausfordern und in der Rangliste aufsteigen.',
  'ladder_box_league': 'Liga-System mit verschiedenen Leistungsklassen, Auf- und Abstieg möglich.',
  'league': 'Liga-Format wie im Vereinstennis mit Hin- und Rückrunde.',
  'cup_competition': 'Pokal-Wettbewerb mit verschiedenen Runden und möglicherweise Heim- und Auswärtsspielen.'
};

// Detailed format rules and scoring information
const TOURNAMENT_FORMAT_RULES = {
  'uts': {
    title: 'UTS Spielregeln im Detail',
    rules: [
      '🎯 Keine traditionellen Sätze - nur Punkte!',
      '⚡ Gespielt wird "First to 4 Games" pro Satz',
      '🔥 Bei 3:3 entscheidet ein Sudden Death Punkt',
      '⏱️ No-Ad Scoring: Bei Einstand entscheidet der nächste Punkt',
      '🚫 Keine zweiten Aufschläge - nur ein Aufschlag pro Punkt',
      '⏰ Shot Clock: 15 Sekunden zwischen den Punkten',
      '🎪 Timeouts: Jeder Spieler hat pro Satz ein Timeout (90 Sek)',
      '🔄 Seitenwechsel nur nach ungeraden Spielständen (1, 3)',
      '⚖️ Best of 3 oder Best of 5 Format möglich',
      '📊 Punkte werden live auf dem Court angezeigt'
    ],
    scoring: 'First-to-4 Games, Sudden Death bei 3:3, No-Ad Scoring'
  },
  'knockout': {
    title: 'K.o.-System Spielregeln',
    rules: [
      '🎯 Klassisches Ausscheidungsturnier',
      '❌ Wer ein Match verliert, scheidet aus',
      '🏆 Direkter Weg zum Finale',
      '⚡ Schnelle Entscheidungen',
      '🎾 Traditionelles Tennis-Scoring',
      '🗓️ Weniger Matches pro Spieler',
      '🏅 Keine zweite Chance'
    ],
    scoring: 'Standard Tennis-Scoring (6 Spiele = 1 Satz)'
  },
  'round_robin': {
    title: 'Round Robin Spielregeln',
    rules: [
      '🔄 Jeder spielt gegen jeden',
      '📊 Tabellen-Modus wie in der Liga',
      '🏆 Gewinner nach Punkten/Satzverhältnis',
      '⚖️ Faire Chancen für alle',
      '🎾 Viele Matches pro Spieler',
      '📈 Kontinuierliche Leistungsmessung',
      '🎯 Keine Ausscheidung'
    ],
    scoring: 'Standard Tennis-Scoring, Tabellenwertung'
  },
  'pro_set': {
    title: 'Pro Set Spielregeln',
    rules: [
      '🎯 Ein einziger langer Satz',
      '🏁 First to 8 Games (statt 6)',
      '⚡ Tiebreak bei 8:8',
      '⏰ Schnelleres Format',
      '🎾 Traditionelles Scoring innerhalb der Spiele',
      '💪 Mehr Ausdauer gefordert'
    ],
    scoring: 'First to 8 Games, Tiebreak bei 8:8'
  },
  'timeboxed': {
    title: 'Timeboxed Tournament Regeln',
    rules: [
      '⏰ Feste Zeitbegrenzung pro Match',
      '🏁 Bei Zeitablauf entscheidet der aktuelle Stand',
      '🎯 Flexibles Scoring-System',
      '⚡ Planbare Turnierdauer',
      '📊 Kann nach Spielen oder Punkten gewertet werden',
      '🔔 Warnung 5 Minuten vor Zeitablauf'
    ],
    scoring: 'Zeit-basiert, flexibles Wertungssystem'
  },
  'social_mixer': {
    title: 'Social Mixer Regeln',
    rules: [
      '👥 Wechselnde Partner und Gegner',
      '🎪 Geselliges Turnier-Format',
      '🔄 Jeder spielt mit/gegen verschiedene Spieler',
      '🎾 Kurze Matches oder Sets',
      '🏆 Gesamtwertung über alle Spiele',
      '🤝 Fokus auf Spaß und Gemeinschaft'
    ],
    scoring: 'Kurze Sets, Gesamtpunktwertung'
  }
};

export function TournamentOverview() {
  const { getFormatConfig } = useTournamentFormatConfig();
  const [activeTab, setActiveTab] = useState('available');
  const [selectedTournament, setSelectedTournament] = useState<Tournament | null>(null);
  const [resultEntryMatch, setResultEntryMatch] = useState<any>(null);
  const [radiusKm, setRadiusKm] = useState(25);
  const queryClient = useQueryClient();
  const { user } = useAuth();
  const { currentClubId, currentClub } = useClub();
  const { timezone } = useClubTimezone();

  // Fetch tournaments for the current club
  const { data: tournaments, isLoading: tournamentsLoading } = useQuery({
    queryKey: ['tournaments', currentClubId, 'member'],
    queryFn: async () => {
      if (!currentClubId) return [];
      
      const { data, error } = await supabase
        .from('tournaments')
        .select('*')
        .eq('club_id', currentClubId)
        .in('status', ['registration', 'active', 'completed'])
        .order('created_at', { ascending: false });
      
      if (error) {
        console.error('Error fetching tournaments:', error);
        throw error;
      }
      
      // Debug: Log tournament data to see what we're getting
      console.log('Tournaments loaded:', data?.map(t => ({
        id: t.id,
        name: t.name,
        registration_start: t.registration_start,
        registration_end: t.registration_end,
        tournament_start: t.tournament_start,
        tournament_end: t.tournament_end
      })));
      
      return data as Tournament[];
    },
    enabled: !!currentClubId
  });

  // Fetch user's tournament participations
  const { data: myParticipations, isLoading: participationsLoading } = useQuery({
    queryKey: ['my-tournament-participations', user?.id, currentClubId],
    queryFn: async () => {
      if (!user?.id || !currentClubId) return [];
      
      const { data, error } = await supabase
        .from('tournament_participants')
        .select(`
          *,
          tournaments (
            id, name, status, tournament_start, tournament_end, format_type
          )
        `)
        .eq('user_id', user.id)
        .eq('club_id', currentClubId);
      
      if (error) {
        console.error('Error fetching participations:', error);
        throw error;
      }
      return data;
    },
    enabled: !!user?.id && !!currentClubId
  });

  // Fetch user's tournament matches - SIMPLIFIED for better reliability
  const { data: myMatches, isLoading: matchesLoading } = useQuery({
    queryKey: ['my-tournament-matches', user?.id, currentClubId],
    queryFn: async () => {
      if (!user?.id || !currentClubId) return [];
      
      console.log('🔍 Fetching matches for user:', user.id, 'in club:', currentClubId);
      
      // First get participant IDs for the current user
      const { data: userParticipants, error: participantsError } = await supabase
        .from('tournament_participants')
        .select('id, tournament_id, guest_name, is_guest, user_id')
        .eq('user_id', user.id)
        .eq('club_id', currentClubId);

      console.log('🔍 User participants found:', userParticipants?.length || 0);

      if (participantsError) {
        console.error('Error fetching user participants:', participantsError);
        throw participantsError;
      }

      if (!userParticipants || userParticipants.length === 0) {
        console.log('🔍 No participants found for user');
        return [];
      }

      const participantIds = userParticipants.map(p => p.id);
      console.log('🔍 Participant IDs:', participantIds);

      // Get matches where user is participant1 or participant2
      const { data: matches, error: matchesError } = await supabase
        .from('tournament_matches')
        .select(`
          *,
          tournaments!inner(id, name, status, format_type)
        `)
        .or(`participant1_id.in.(${participantIds.join(',')}),participant2_id.in.(${participantIds.join(',')})`)
        .order('scheduled_at', { ascending: true, nullsFirst: false });
      
      console.log('🔍 Matches found:', matches?.length || 0);
      
      if (matchesError) {
        console.error('Error fetching matches:', matchesError);
        throw matchesError;
      }
      
      if (!matches || matches.length === 0) {
        console.log('🔍 No matches found');
        return [];
      }

      // Get all participants in one query for efficiency
      const allParticipantIds = [...new Set([
        ...matches.map(m => m.participant1_id).filter(Boolean),
        ...matches.map(m => m.participant2_id).filter(Boolean)
      ])];

      const { data: allParticipants } = await supabase
        .from('tournament_participants')
        .select('id, user_id, guest_name, is_guest')
        .in('id', allParticipantIds);

      // Get all club memberships for name lookup
      const allUserIds = allParticipants?.filter(p => !p.is_guest && p.user_id).map(p => p.user_id) || [];
      const { data: clubMemberships } = await supabase
        .from('club_memberships')
        .select('user_id, first_name, last_name')
        .in('user_id', allUserIds)
        .eq('club_id', currentClubId);

      // Get court info for all matches in one query
      const courtIds = matches.map(m => m.court_id).filter(Boolean);
      const { data: courts } = courtIds.length > 0 ? await supabase
        .from('courts')
        .select('id, number')
        .in('id', courtIds) : { data: [] };

      // Enrich matches with participant and court information
      const enrichedMatches = matches.map(match => {
        const participant1 = allParticipants?.find(p => p.id === match.participant1_id);
        const participant2 = allParticipants?.find(p => p.id === match.participant2_id);
        const court = courts?.find(c => c.id === match.court_id);

        // Get names for participants
        let participant1Profile = null;
        let participant2Profile = null;

        if (participant1 && !participant1.is_guest && participant1.user_id) {
          const membership = clubMemberships?.find(m => m.user_id === participant1.user_id);
          if (membership) {
            participant1Profile = {
              first_name: membership.first_name,
              last_name: membership.last_name
            };
          }
        }

        if (participant2 && !participant2.is_guest && participant2.user_id) {
          const membership = clubMemberships?.find(m => m.user_id === participant2.user_id);
          if (membership) {
            participant2Profile = {
              first_name: membership.first_name,
              last_name: membership.last_name
            };
          }
        }

        return {
          ...match,
          participant1: participant1 ? { ...participant1, profiles: participant1Profile } : null,
          participant2: participant2 ? { ...participant2, profiles: participant2Profile } : null,
          courts: court
        };
      });
      
      console.log('🔍 Enriched matches:', enrichedMatches.length);
      return enrichedMatches;
    },
    enabled: !!user?.id && !!currentClubId
  });

  // Registration mutation
  const registerMutation = useMutation({
    mutationFn: async (tournamentId: string) => {
      if (!user?.id || !currentClubId) {
        throw new Error('Nicht angemeldet oder kein Club ausgewählt');
      }

      // Check if already registered
      const { data: existingRegistration } = await supabase
        .from('tournament_participants')
        .select('id')
        .eq('tournament_id', tournamentId)
        .eq('user_id', user.id)
        .eq('club_id', currentClubId)
        .maybeSingle();

      if (existingRegistration) {
        throw new Error('Sie sind bereits für dieses Turnier angemeldet');
      }

      const { data, error } = await supabase
        .from('tournament_participants')
        .insert([{
          tournament_id: tournamentId,
          club_id: currentClubId,
          user_id: user.id,
          is_guest: false,
          status: 'registered'
        }])
        .select()
        .single();
      
      if (error) {
        console.error('Registration error:', error);
        throw error;
      }
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['my-tournament-participations'] });
      queryClient.invalidateQueries({ queryKey: ['tournament-participants'] });
      toast.success('Erfolgreich für Turnier angemeldet');
    },
    onError: (error) => {
      console.error('Registration mutation error:', error);
      toast.error('Fehler bei der Anmeldung: ' + error.message);
    }
  });

  // Withdrawal mutation
  const withdrawMutation = useMutation({
    mutationFn: async (participationId: string) => {
      const { error } = await supabase
        .from('tournament_participants')
        .update({ status: 'withdrawn' })
        .eq('id', participationId);
      
      if (error) {
        console.error('Withdrawal error:', error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['my-tournament-participations'] });
      toast.success('Erfolgreich vom Turnier abgemeldet');
    },
    onError: (error) => {
      console.error('Withdrawal mutation error:', error);
      toast.error('Fehler bei der Abmeldung: ' + error.message);
    }
  });

  // Helper functions
  const isRegistered = (tournamentId: string) => {
    return myParticipations?.some(p => 
      p.tournament_id === tournamentId && 
      (p.status === 'registered' || p.status === 'confirmed')
    ) || false;
  };

  const getParticipation = (tournamentId: string) => {
    return myParticipations?.find(p => 
      p.tournament_id === tournamentId && 
      (p.status === 'registered' || p.status === 'confirmed')
    );
  };

  const canRegister = (tournament: Tournament) => {
    if (tournament.status !== 'registration') return false;
    if (isRegistered(tournament.id)) return false;
    
    const now = new Date();
    if (tournament.registration_end && new Date(tournament.registration_end) < now) return false;
    if (tournament.registration_start && new Date(tournament.registration_start) > now) return false;
    
    return true;
  };

  const getRegistrationStatus = (tournament: Tournament) => {
    const now = new Date();
    
    if (tournament.status !== 'registration') {
      return { status: tournament.status, label: tournament.status === 'active' ? 'Läuft' : 'Abgeschlossen', variant: 'default' as const };
    }
    
    // Convert times to local timezone for comparison
    const nowLocal = now.getTime();
    const registrationStartLocal = tournament.registration_start ? new Date(tournament.registration_start).getTime() : 0;
    const registrationEndLocal = tournament.registration_end ? new Date(tournament.registration_end).getTime() : Infinity;
    
    if (registrationStartLocal > nowLocal) {
      const minutesUntilStart = Math.ceil((registrationStartLocal - nowLocal) / (1000 * 60));
      return { 
        status: 'not_started', 
        label: `Anmeldung in ${minutesUntilStart} Min.`, 
        variant: 'secondary' as const 
      };
    }
    
    if (registrationEndLocal < nowLocal) {
      return { status: 'closed', label: 'Anmeldung geschlossen', variant: 'destructive' as const };
    }
    
    return { status: 'open', label: 'Anmeldung offen', variant: 'secondary' as const };
  };

  const getFormatName = (formatType?: string) => {
    if (!formatType) return 'Standard';
    return TOURNAMENT_FORMAT_NAMES[formatType as keyof typeof TOURNAMENT_FORMAT_NAMES] || formatType;
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Nicht festgelegt';
    return formatDateInClubTZ(dateString, timezone, {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const formatDateTime = (dateString?: string) => {
    if (!dateString) return 'Nicht geplant';
    return formatDateTimeInClubTZ(dateString, timezone, {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      'registration': 'secondary' as const,
      'active': 'default' as const,
      'completed': 'outline' as const,
      'cancelled': 'destructive' as const,
      'registered': 'secondary' as const,
      'confirmed': 'default' as const,
      'withdrawn': 'destructive' as const,
      'disqualified': 'destructive' as const,
      'pending': 'secondary' as const,
      'in_progress': 'default' as const,
      'walkover': 'outline' as const
    };
    
    const labels = {
      'registration': 'Anmeldung',
      'active': 'Aktiv',
      'completed': 'Abgeschlossen',
      'cancelled': 'Abgesagt',
      'registered': 'Angemeldet',
      'confirmed': 'Bestätigt',
      'withdrawn': 'Abgemeldet',
      'disqualified': 'Disqualifiziert',
      'pending': 'Geplant',
      'in_progress': 'Läuft',
      'walkover': 'Walkover'
    };

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'secondary'}>
        {labels[status as keyof typeof labels] || status}
      </Badge>
    );
  };

  // Tournament Details Component
  const TournamentDetails = ({ tournament }: { tournament: Tournament }) => {
    // Get custom format configuration if available (only call if we have format_type)
    const customConfig = tournament.format_type ? getFormatConfig(tournament.format_type, tournament.id) : null;
    
    const formatInfo = customConfig?.custom_name || TOURNAMENT_FORMAT_NAMES[tournament.format_type as keyof typeof TOURNAMENT_FORMAT_NAMES] || tournament.format_type;
    const formatDescription = customConfig?.custom_description || TOURNAMENT_FORMAT_DESCRIPTIONS[tournament.format_type as keyof typeof TOURNAMENT_FORMAT_DESCRIPTIONS] || '';
    const formatRules = TOURNAMENT_FORMAT_RULES[tournament.format_type as keyof typeof TOURNAMENT_FORMAT_RULES] || null;
    
    // Extract time configuration from format_config
    const timeConfig = tournament.format_config?.time_config;

    const formatTimeMinutes = (minutes: number) => {
      if (minutes >= 60) {
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;
        return mins > 0 ? `${hours}h ${mins}min` : `${hours}h`;
      }
      return `${minutes} Min.`;
    };

    // Calculate total match time including all components
    const getTotalMatchTime = () => {
      if (!timeConfig) return null;
      
      // The total time includes ALL components in sequence
      const total = (timeConfig.warmup_minutes || 0) + 
                   (timeConfig.match_duration_minutes || 0) + 
                   (timeConfig.changeover_minutes || 0) + 
                   (timeConfig.set_break_minutes || 0) + 
                   (timeConfig.between_match_buffer_minutes || 0) + 
                   (timeConfig.court_preparation_minutes || 0);
      
      return total;
    };
    
    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold">{tournament.name}</h3>
          {tournament.description && (
            <p className="text-muted-foreground mt-1">{tournament.description}</p>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-3 flex items-center gap-2">
                <Trophy className="h-4 w-4" />
                Turnier-Format
              </h4>
              <div className="space-y-2 text-sm">
                <div className="font-medium">{formatInfo}</div>
                {formatDescription && (
                  <div className="text-muted-foreground bg-muted/50 p-3 rounded-md">
                    {formatDescription}
                  </div>
                )}
                
                {/* Enhanced Format Configuration with detailed rules */}
                {tournament.format_config && (
                  <div className="mt-3 p-4 bg-blue-50 rounded-md border border-blue-200">
                    <div className="flex items-center gap-2 text-blue-900 mb-3">
                      <Info className="h-4 w-4" />
                      <span className="font-medium">Spielmodus-Details:</span>
                    </div>
                    
                    {/* Format-specific rules */}
                    {formatRules && (
                      <div className="mb-4">
                        <div className="font-medium text-blue-900 mb-2">{formatRules.title}</div>
                        <div className="space-y-1">
                          {formatRules.rules.map((rule, index) => (
                            <div key={index} className="text-xs text-blue-800 flex items-start gap-1">
                              <span className="inline-block w-2 text-center">•</span>
                              <span>{rule}</span>
                            </div>
                          ))}
                        </div>
                        <div className="mt-3 p-2 bg-blue-100 rounded border border-blue-300">
                          <div className="text-xs font-medium text-blue-900">Wertungssystem:</div>
                          <div className="text-xs text-blue-800">{formatRules.scoring}</div>
                        </div>
                      </div>
                    )}
                    
                    {/* Technical configuration */}
                    <div className="text-xs text-blue-800 space-y-1 border-t border-blue-300 pt-3">
                      <div className="font-medium text-blue-900 mb-2">Technische Einstellungen:</div>
                      {tournament.format_config.set_count && (
                        <div>Anzahl Sätze: {tournament.format_config.set_count}</div>
                      )}
                      {tournament.format_config.scoring_system && (
                        <div>Wertungssystem: {tournament.format_config.scoring_system}</div>
                      )}
                      {tournament.format_config.seeding && (
                        <div>Setzung: {tournament.format_config.seeding === 'random' ? 'Zufällig' : 
                          tournament.format_config.seeding === 'rating' ? 'Nach Rating' : 'Manuell'}</div>
                      )}
                      {tournament.format_config.groups && (
                        <div>Anzahl Gruppen: {tournament.format_config.groups}</div>
                      )}
                      {tournament.format_config.group_size && (
                        <div>Gruppengröße: max. {tournament.format_config.group_size} Spieler</div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-3 flex items-center gap-2">
                <Users className="h-4 w-4" />
                Teilnahme & Kosten
              </h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Teilnahmegebühr:</span>
                  <span className="font-medium text-green-600">Kostenlos</span>
                </div>
                {tournament.max_participants && (
                  <div className="flex justify-between">
                    <span>Max. Teilnehmer:</span>
                    <span>{tournament.max_participants}</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span>Gäste erlaubt:</span>
                  <span>{tournament.allow_guests ? 'Ja' : 'Nein'}</span>
                </div>
                <div className="flex justify-between">
                  <span>Status:</span>
                  <span>{getStatusBadge(tournament.status)}</span>
                </div>
              </div>
            </div>

            {/* Time Configuration Section */}
            {timeConfig && (
              <div>
                <h4 className="font-medium mb-3 flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  Zeit & Spielablauf
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="p-3 bg-green-50 rounded-md border border-green-200">
                    <div className="grid grid-cols-2 gap-3 text-xs">
                      <div>
                        <span className="font-medium text-green-900">Spieldauer:</span>
                        <div className="text-green-800">{formatTimeMinutes(timeConfig.match_duration_minutes || 0)}</div>
                      </div>
                      <div>
                        <span className="font-medium text-green-900">Einspielzeit:</span>
                        <div className="text-green-800">{formatTimeMinutes(timeConfig.warmup_minutes || 0)}</div>
                      </div>
                      <div>
                        <span className="font-medium text-green-900">Seitenwechsel:</span>
                        <div className="text-green-800">{formatTimeMinutes(timeConfig.changeover_minutes || 0)}</div>
                      </div>
                      <div>
                        <span className="font-medium text-green-900">Satzpause:</span>
                        <div className="text-green-800">{formatTimeMinutes(timeConfig.set_break_minutes || 0)}</div>
                      </div>
                      <div>
                        <span className="font-medium text-green-900">Platzwechsel:</span>
                        <div className="text-green-800">{formatTimeMinutes(timeConfig.court_preparation_minutes || 0)}</div>
                      </div>
                      <div>
                        <span className="font-medium text-green-900">Puffer:</span>
                        <div className="text-green-800">{formatTimeMinutes(timeConfig.between_match_buffer_minutes || 0)}</div>
                      </div>
                    </div>
                    {getTotalMatchTime() && (
                      <div className="mt-3 pt-2 border-t border-green-300">
                        <div className="flex justify-between items-center">
                          <span className="font-medium text-green-900">Gesamtzeit pro Spiel:</span>
                          <span className="font-bold text-green-800">{formatTimeMinutes(getTotalMatchTime()!)}</span>
                        </div>
                        <div className="text-xs text-green-700 mt-1">
                          (inkl. aller Pausen und Wechselzeiten)
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>

          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-3 flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Termine & Zeitplan
              </h4>
              <div className="space-y-2 text-sm">
                {tournament.registration_start && tournament.registration_end && (
                  <div>
                    <span className="font-medium">Anmeldezeitraum:</span><br />
                    <span className="text-muted-foreground">
                      {formatDate(tournament.registration_start)} - {formatDate(tournament.registration_end)}
                    </span>
                  </div>
                )}
                {tournament.tournament_start && tournament.tournament_end && (
                  <div>
                    <span className="font-medium">Turnier-Zeitraum:</span><br />
                    <span className="text-muted-foreground">
                      {formatDate(tournament.tournament_start)} - {formatDate(tournament.tournament_end)}
                    </span>
                  </div>
                )}
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-3 flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                Spielplan & Plätze
              </h4>
              <div className="text-sm bg-muted/50 p-3 rounded-md">
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Clock className="h-4 w-4" />
                  <span>Der detaillierte Zeitplan für die Spiele wird noch bekannt gegeben</span>
                </div>
                <div className="mt-2 text-xs text-muted-foreground">
                  Nach Anmeldeschluss werden die Spiele automatisch generiert und den Plätzen der Anlage zugeordnet.
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="flex gap-2 pt-2">
          {(() => {
            const participation = getParticipation(tournament.id);
            
            if (!participation) {
              // Nicht angemeldet - zeige Anmelden Button falls möglich
              if (canRegister(tournament)) {
                return (
                  <Button 
                    onClick={() => registerMutation.mutate(tournament.id)}
                    disabled={registerMutation.isPending}
                    className="flex items-center gap-2"
                  >
                    <UserPlus className="h-4 w-4" />
                    {registerMutation.isPending ? 'Anmeldung läuft...' : 'Anmelden'}
                  </Button>
                );
              } else {
                return (
                  <Badge variant="outline" className="px-3 py-1">
                    <Clock className="h-3 w-3 mr-1" />
                    Anmeldung geschlossen
                  </Badge>
                );
              }
            } else {
              // Angemeldet - zeige Status und Absage-Button
              return (
                <div className="flex gap-2 items-center">
                  <Badge 
                    variant={participation.status === 'confirmed' ? 'default' : 'secondary'} 
                    className="flex items-center gap-1 px-3 py-1"
                  >
                    <CheckCircle className="h-3 w-3" />
                    {participation.status === 'confirmed' ? 'Bestätigt' : 'Angemeldet'}
                  </Badge>
                  {tournament.status === 'registration' && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => withdrawMutation.mutate(participation.id)}
                      disabled={withdrawMutation.isPending}
                      className="flex items-center gap-1"
                    >
                      {withdrawMutation.isPending ? 'Abmeldung...' : 'Absagen'}
                    </Button>
                  )}
                </div>
              );
            }
          })()}
        </div>
      </div>
    );
  };

  // Loading state
  if (tournamentsLoading || participationsLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-muted rounded w-1/3"></div>
          <div className="h-64 bg-muted rounded"></div>
        </div>
      </div>
    );
  }

  // No club selected
  if (!currentClub) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Turniere</h1>
          <p className="text-muted-foreground">
            Bitte wählen Sie einen Club aus, um Turniere anzuzeigen.
          </p>
        </div>
      </div>
    );
  }

  const availableTournaments = tournaments?.filter(t => 
    t.status === 'registration' || t.status === 'active'
  ) || [];
  const activeTournaments = tournaments?.filter(t => t.status === 'active') || [];
  const completedTournaments = tournaments?.filter(t => t.status === 'completed') || [];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-foreground">Turniere</h1>
        <p className="text-muted-foreground">
          Melden Sie sich für Turniere an und verfolgen Sie Ihre Teilnahmen im {currentClub.name}
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="available">Verfügbare Turniere</TabsTrigger>
          <TabsTrigger value="nearby-lk">LK-Spiele in der Nähe</TabsTrigger>
          <TabsTrigger value="my-tournaments">Meine Turniere</TabsTrigger>
          <TabsTrigger value="my-matches">Meine Spiele</TabsTrigger>
          <TabsTrigger value="results">Ergebnisse</TabsTrigger>
        </TabsList>

        <TabsContent value="available">
          <div className="space-y-4">
            {availableTournaments.length === 0 ? (
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center text-muted-foreground py-8">
                    <Trophy className="h-16 w-16 mx-auto mb-4 opacity-50" />
                    <h3 className="text-lg font-semibold mb-2">Keine Turniere verfügbar</h3>
                    <p>Derzeit sind keine Turniere zur Anmeldung verfügbar.</p>
                  </div>
                </CardContent>
              </Card>
            ) : (
              availableTournaments.map((tournament) => (
                <Card key={tournament.id} className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Trophy className="h-5 w-5" />
                        {tournament.name}
                      </div>
                      {(() => {
                        const regStatus = getRegistrationStatus(tournament);
                        return <Badge variant={regStatus.variant}>{regStatus.label}</Badge>;
                      })()}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <TournamentDetails tournament={tournament} />
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </TabsContent>

        <TabsContent value="nearby-lk">
          <div className="space-y-6">
            {/* Radius Selection */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5" />
                  LK-Spiele in der Nähe
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-4 mb-6">
                  <label htmlFor="radius" className="text-sm font-medium">
                    Umkreis:
                  </label>
                  <select
                    id="radius"
                    value={radiusKm}
                    onChange={(e) => setRadiusKm(Number(e.target.value))}
                    className="px-3 py-1 border border-input rounded-md bg-background text-sm"
                  >
                    <option value={10}>10 km</option>
                    <option value={25}>25 km</option>
                    <option value={50}>50 km</option>
                    <option value={100}>100 km</option>
                    <option value={200}>200 km</option>
                  </select>
                  <span className="text-sm text-muted-foreground">
                    von {currentClub?.name}
                  </span>
                </div>
                
                {/* LK Tournament List */}
                <div className="space-y-4">
                  {/* Placeholder for nearby LK tournaments */}
                  <div className="text-center text-muted-foreground py-12">
                    <Trophy className="h-16 w-16 mx-auto mb-4 opacity-50" />
                    <h3 className="text-lg font-semibold mb-2">LK-Turniere werden geladen...</h3>
                    <p className="mb-4">Suche nach LK-Turnieren im Umkreis von {radiusKm} km</p>
                    <p className="text-xs text-muted-foreground">
                      Feature wird demnächst verfügbar sein
                    </p>
                  </div>
                  
                  {/* Example LK Tournament Cards - Remove when real data is available */}
                  <Card className="border-dashed border-2 opacity-50">
                    <CardHeader>
                      <CardTitle className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Trophy className="h-5 w-5" />
                          LK Turnier - TC Musterstadt
                        </div>
                        <Badge variant="outline">LK 15-20</Badge>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="font-medium mb-1">📍 Standort</p>
                          <p className="text-muted-foreground">TC Musterstadt e.V.</p>
                          <p className="text-muted-foreground">Musterstraße 123, 12345 Musterstadt</p>
                          <p className="text-muted-foreground">Entfernung: ~15 km</p>
                        </div>
                        <div>
                          <p className="font-medium mb-1">📅 Termine</p>
                          <p className="text-muted-foreground">Anmeldung bis: 15.09.2025</p>
                          <p className="text-muted-foreground">Turnier: 20.-22.09.2025</p>
                          <p className="text-muted-foreground">Format: Einzel LK 15-20</p>
                        </div>
                      </div>
                      <div className="mt-4 pt-4 border-t">
                        <div className="flex items-center justify-between">
                          <div className="text-sm text-muted-foreground">
                            Teilnahmegebühr: 35,00 €
                          </div>
                          <Button variant="outline" size="sm" disabled>
                            <Eye className="h-4 w-4 mr-1" />
                            Details anzeigen
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                  
                  <Card className="border-dashed border-2 opacity-50">
                    <CardHeader>
                      <CardTitle className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Trophy className="h-5 w-5" />
                          Bezirksmeisterschaften - TC Nachbarort
                        </div>
                        <Badge variant="outline">LK 10-15</Badge>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="font-medium mb-1">📍 Standort</p>
                          <p className="text-muted-foreground">TC Nachbarort 1985</p>
                          <p className="text-muted-foreground">Sportplatz 7, 12346 Nachbarort</p>
                          <p className="text-muted-foreground">Entfernung: ~22 km</p>
                        </div>
                        <div>
                          <p className="font-medium mb-1">📅 Termine</p>
                          <p className="text-muted-foreground">Anmeldung bis: 01.10.2025</p>
                          <p className="text-muted-foreground">Turnier: 05.-06.10.2025</p>
                          <p className="text-muted-foreground">Format: Einzel + Doppel</p>
                        </div>
                      </div>
                      <div className="mt-4 pt-4 border-t">
                        <div className="flex items-center justify-between">
                          <div className="text-sm text-muted-foreground">
                            Teilnahmegebühr: 45,00 €
                          </div>
                          <Button variant="outline" size="sm" disabled>
                            <Eye className="h-4 w-4 mr-1" />
                            Details anzeigen
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="my-tournaments">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Trophy className="h-5 w-5" />
                Meine Turnier-Anmeldungen
              </CardTitle>
            </CardHeader>
            <CardContent>
              {!myParticipations || myParticipations.length === 0 ? (
                <div className="text-center text-muted-foreground py-8">
                  <Users className="h-16 w-16 mx-auto mb-4 opacity-50" />
                  <h3 className="text-lg font-semibold mb-2">Keine Anmeldungen</h3>
                  <p>Sie sind derzeit für keine Turniere angemeldet.</p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Turnier</TableHead>
                      <TableHead>Format</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Anmeldedatum</TableHead>
                      <TableHead>Aktionen</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {myParticipations.map((participation) => (
                      <TableRow key={participation.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{participation.tournaments?.name}</div>
                            <div className="text-sm text-muted-foreground">
                              Turnier-Status: {getStatusBadge(participation.tournaments?.status || 'unknown')}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <span className="text-sm">
                            {getFormatName(participation.tournaments?.format_type)}
                          </span>
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(participation.status)}
                        </TableCell>
                        <TableCell>
                          {formatDate(participation.registration_date)}
                        </TableCell>
                        <TableCell>
                          {participation.status === 'registered' && 
                           participation.tournaments?.status === 'registration' && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => withdrawMutation.mutate(participation.id)}
                              disabled={withdrawMutation.isPending}
                            >
                              {withdrawMutation.isPending ? 'Abmeldung...' : 'Abmelden'}
                            </Button>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="my-matches">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Meine Spiele
              </CardTitle>
            </CardHeader>
            <CardContent>
              {!myMatches || myMatches.length === 0 ? (
                <div className="text-center text-muted-foreground py-8">
                  <Calendar className="h-16 w-16 mx-auto mb-4 opacity-50" />
                  <h3 className="text-lg font-semibold mb-2">Keine Spiele geplant</h3>
                  <p>Sie haben derzeit keine geplanten Spiele.</p>
                  <p className="text-sm mt-2">
                    Debug: Matches: {myMatches ? myMatches.length : 'null'}, 
                    User: {user?.id?.slice(0, 8)}..., 
                    Club: {currentClubId?.slice(0, 8)}...
                  </p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Turnier</TableHead>
                      <TableHead>Runde</TableHead>
                      <TableHead>Gegner</TableHead>
                      <TableHead>Geplant</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Ergebnis</TableHead>
                      <TableHead>Aktionen</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {myMatches.map((match) => (
                      <TableRow key={match.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{match.tournaments?.name}</div>
                            <div className="text-sm text-muted-foreground">
                              {getFormatName(match.tournaments?.format_type)}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>Runde {match.round_number}</TableCell>
                        <TableCell>
                          {(() => {
                            // Helper function to get player name
                            const getPlayerName = (participant: any) => {
                              if (participant?.is_guest) {
                                return participant.guest_name || 'Gast';
                              } else if (participant?.profiles) {
                                return `${participant.profiles.first_name} ${participant.profiles.last_name}`;
                              } else if (participant?.user_id) {
                                return 'Gegner'; // Fallback for users without profile
                              }
                              return 'Unbekannt';
                            };

                            // Get all participant IDs for the current user from the participations
                            const userParticipantIds = myParticipations?.map(p => p.id) || [];

                            const participant1Name = getPlayerName(match.participant1);
                            const participant2Name = getPlayerName(match.participant2);
                            
                            // Check if current user is participant1 or participant2
                            if (userParticipantIds.includes(match.participant1_id)) {
                              // Current user is participant1, show opponent
                              return (
                                <div className="space-y-1 min-w-0">
                                  <div className="text-sm font-medium truncate">Du</div>
                                  <div className="h-px bg-border"></div>
                                  <div className="text-sm truncate">{participant2Name}</div>
                                </div>
                              );
                            } else if (userParticipantIds.includes(match.participant2_id)) {
                              // Current user is participant2, show opponent
                              return (
                                <div className="space-y-1 min-w-0">
                                  <div className="text-sm truncate">{participant1Name}</div>
                                  <div className="h-px bg-border"></div>
                                  <div className="text-sm font-medium truncate">Du</div>
                                </div>
                              );
                            } else {
                              // Not user's match, show both players
                              return (
                                <div className="space-y-1 min-w-0">
                                  <div className="text-sm truncate">{participant1Name}</div>
                                  <div className="h-px bg-border"></div>
                                  <div className="text-sm truncate">{participant2Name}</div>
                                </div>
                              );
                            }
                          })()}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1 text-sm">
                            <Clock className="h-3 w-3" />
                            {match.scheduled_date && match.scheduled_time ? (
                              <span>
                                {new Date(match.scheduled_date).toLocaleDateString('de-DE')} um {match.scheduled_time}
                              </span>
                            ) : match.scheduled_at ? (
                              formatDateTime(match.scheduled_at)
                            ) : (
                              'Noch nicht geplant'
                            )}
                            {match.courts && (match.courts as any)?.number && (
                              <span className="ml-2 text-muted-foreground">
                                Platz {(match.courts as any).number}
                              </span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(match.status)}
                        </TableCell>
                        <TableCell>
                          {match.score_json ? (
                            <div className="space-y-2">
                              {(() => {
                                try {
                                  const score = typeof match.score_json === 'string' 
                                    ? JSON.parse(match.score_json) 
                                    : match.score_json;
                                  
                                  // Get participant names
                                  const getPlayerName = (participant: any) => {
                                    if (participant?.is_guest) {
                                      return participant.guest_name || 'Gast';
                                    } else if (participant?.profiles) {
                                      return `${participant.profiles.first_name} ${participant.profiles.last_name}`;
                                    } else if (participant?.user_id) {
                                      return 'Gegner';
                                    }
                                    return 'Unbekannt';
                                  };

                                  const participant1Name = getPlayerName(match.participant1);
                                  const participant2Name = getPlayerName(match.participant2);
                                  const isPlayer1Winner = match.winner_id === match.participant1_id;
                                  const isPlayer2Winner = match.winner_id === match.participant2_id;
                                  
                                  if (score?.sets && score.sets.length > 0) {
                                    // Set-based format - show sets vertically aligned
                                    const validSets = score.sets.filter((set: any) => set && (set.player1 !== undefined && set.player2 !== undefined));
                                    const hasWinner = isPlayer1Winner || isPlayer2Winner;
                                    
                                    return (
                                      <div className={`px-3 py-2 rounded border space-y-1 ${hasWinner ? 'bg-green-50 border-green-200' : 'bg-muted/30'}`}>
                                        <div className={`flex items-center justify-between text-xs font-medium ${isPlayer1Winner ? 'text-green-700' : ''}`}>
                                          <span className="truncate max-w-[80px] flex items-center gap-1 min-w-0">
                                            <span className="w-4 flex-shrink-0">
                                              {isPlayer1Winner && '🏆'}
                                            </span>
                                            <span className="truncate">{participant1Name}</span>
                                          </span>
                                          <div className="flex gap-1">
                                            {validSets.map((set: any, i: number) => (
                                              <span key={i} className={`px-1 ${isPlayer1Winner ? 'font-bold' : ''}`}>
                                                {set.player1}
                                              </span>
                                            ))}
                                          </div>
                                        </div>
                                        <div className="h-px bg-border"></div>
                                        <div className={`flex items-center justify-between text-xs font-medium ${isPlayer2Winner ? 'text-green-700' : ''}`}>
                                          <span className="truncate max-w-[80px] flex items-center gap-1 min-w-0">
                                            <span className="w-4 flex-shrink-0">
                                              {isPlayer2Winner && '🏆'}
                                            </span>
                                            <span className="truncate">{participant2Name}</span>
                                          </span>
                                          <div className="flex gap-1">
                                            {validSets.map((set: any, i: number) => (
                                              <span key={i} className={`px-1 ${isPlayer2Winner ? 'font-bold' : ''}`}>
                                                {set.player2}
                                              </span>
                                            ))}
                                          </div>
                                        </div>
                                      </div>
                                    );
                                  } else if (score?.player1_games !== undefined && score?.player2_games !== undefined) {
                                    // Game-based format - show games vertically aligned
                                    const hasWinner = isPlayer1Winner || isPlayer2Winner;
                                    
                                    return (
                                      <div className={`px-3 py-2 rounded border space-y-1 ${hasWinner ? 'bg-green-50 border-green-200' : 'bg-muted/30'}`}>
                                        <div className={`flex items-center justify-between text-xs font-medium ${isPlayer1Winner ? 'text-green-700' : ''}`}>
                                          <span className="truncate max-w-[80px] flex items-center gap-1 min-w-0">
                                            <span className="w-4 flex-shrink-0">
                                              {isPlayer1Winner && '🏆'}
                                            </span>
                                            <span className="truncate">{participant1Name}</span>
                                          </span>
                                          <span className={isPlayer1Winner ? 'font-bold' : ''}>
                                            {score.player1_games}
                                          </span>
                                        </div>
                                        <div className="h-px bg-border"></div>
                                        <div className={`flex items-center justify-between text-xs font-medium ${isPlayer2Winner ? 'text-green-700' : ''}`}>
                                          <span className="truncate max-w-[80px] flex items-center gap-1 min-w-0">
                                            <span className="w-4 flex-shrink-0">
                                              {isPlayer2Winner && '🏆'}
                                            </span>
                                            <span className="truncate">{participant2Name}</span>
                                          </span>
                                          <span className={isPlayer2Winner ? 'font-bold' : ''}>
                                            {score.player2_games}
                                          </span>
                                        </div>
                                      </div>
                                    );
                                  } else if (score?.custom_score) {
                                    return (
                                      <div className="text-sm bg-muted/30 px-2 py-1 rounded border">
                                        {score.custom_score}
                                      </div>
                                    );
                                  } else {
                                    return (
                                      <div className="text-sm bg-muted/30 px-2 py-1 rounded border">
                                        Ergebnis verfügbar
                                      </div>
                                    );
                                  }
                                } catch {
                                  return (
                                    <div className="text-sm bg-muted/30 px-2 py-1 rounded border">
                                      Ergebnis verfügbar
                                    </div>
                                  );
                                }
                              })()}
                            </div>
                          ) : (
                            <div className="text-sm text-muted-foreground bg-muted/20 px-3 py-2 rounded border border-dashed">
                              Noch kein Ergebnis
                            </div>
                          )}
                        </TableCell>
                        <TableCell>
                          {(() => {
                            const matchStatus = match.status;
                            const hasScore = match.score_json;
                            
                              if (matchStatus === 'completed' && hasScore) {
                                return (
                                  <div className="flex items-center gap-2">
                                    <Button 
                                      size="sm"
                                      variant="secondary"
                                      disabled
                                      className="text-muted-foreground"
                                    >
                                      Ergebnis übermittelt
                                    </Button>
                                    <Button 
                                      size="sm"
                                      variant="outline"
                                      onClick={() => {
                                        // Get participant names using the same logic as the display
                                        const getPlayerName = (participant: any) => {
                                          if (participant?.is_guest) {
                                            return participant.guest_name || 'Gast';
                                          } else if (participant?.profiles) {
                                            return `${participant.profiles.first_name} ${participant.profiles.last_name}`;
                                          } else if (participant?.user_id) {
                                            return 'Gegner';
                                          }
                                          return 'Unbekannt';
                                        };
                                        
                                        const participant1Name = getPlayerName(match.participant1);
                                        const participant2Name = getPlayerName(match.participant2);
                                        
                                        setResultEntryMatch({
                                          id: match.id,
                                          tournament_id: match.tournament_id,
                                          participant1_id: match.participant1_id,
                                          participant2_id: match.participant2_id,
                                          participant1_name: participant1Name,
                                          participant2_name: participant2Name,
                                          format_type: match.tournaments?.format_type || 'standard_tennis',
                                          court_name: match.courts?.number ? `Platz ${match.courts.number}` : undefined,
                                          scheduled_start: match.scheduled_at
                                        });
                                      }}
                                    >
                                      <Edit className="h-3 w-3" />
                                    </Button>
                                  </div>
                                );
                              } else if (matchStatus === 'scheduled' || matchStatus === 'pending') {
                                return (
                                  <Button 
                                    size="sm"
                                    onClick={() => {
                                      // Get participant names using the same logic as the display
                                      const getPlayerName = (participant: any) => {
                                        if (participant?.is_guest) {
                                          return participant.guest_name || 'Gast';
                                        } else if (participant?.profiles) {
                                          return `${participant.profiles.first_name} ${participant.profiles.last_name}`;
                                        } else if (participant?.user_id) {
                                          return 'Gegner';
                                        }
                                        return 'Unbekannt';
                                      };
                                      
                                      const participant1Name = getPlayerName(match.participant1);
                                      const participant2Name = getPlayerName(match.participant2);
                                      
                                      setResultEntryMatch({
                                        id: match.id,
                                        tournament_id: match.tournament_id,
                                        participant1_id: match.participant1_id,
                                        participant2_id: match.participant2_id,
                                        participant1_name: participant1Name,
                                        participant2_name: participant2Name,
                                        format_type: match.tournaments?.format_type || 'standard_tennis',
                                        court_name: match.courts?.number ? `Platz ${match.courts.number}` : undefined,
                                        scheduled_start: match.scheduled_at
                                      });
                                    }}
                                  >
                                    Ergebnis eintragen
                                  </Button>
                                );
                              } else {
                                return (
                                  <div className="text-sm text-muted-foreground">
                                    {matchStatus === 'cancelled' ? 'Abgesagt' : 
                                     matchStatus === 'in_progress' ? 'Läuft gerade' : 
                                     'Noch nicht verfügbar'}
                                  </div>
                                );
                              }
                            })()}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="results">
          <div className="space-y-4">
            {completedTournaments.length === 0 ? (
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center text-muted-foreground py-8">
                    <Trophy className="h-16 w-16 mx-auto mb-4 opacity-50" />
                    <h3 className="text-lg font-semibold mb-2">Keine abgeschlossenen Turniere</h3>
                    <p>Es sind noch keine Turniere abgeschlossen.</p>
                  </div>
                </CardContent>
              </Card>
            ) : (
              completedTournaments.map((tournament) => (
                <Card key={tournament.id}>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Trophy className="h-5 w-5" />
                        {tournament.name}
                      </div>
                      <Badge variant="outline">Abgeschlossen</Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <h4 className="font-medium mb-2">Format</h4>
                        <p className="text-sm text-muted-foreground">
                          {getFormatName(tournament.format_type)}
                        </p>
                      </div>
                      <div>
                        <h4 className="font-medium mb-2">Zeitraum</h4>
                        <p className="text-sm text-muted-foreground">
                          {formatDate(tournament.tournament_start)} - {formatDate(tournament.tournament_end)}
                        </p>
                      </div>
                      <div>
                        <Button variant="outline" size="sm" className="w-full">
                          <Eye className="h-4 w-4 mr-2" />
                          Ergebnisse ansehen
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </TabsContent>
      </Tabs>

      {/* Match Result Entry Dialog */}
      <MatchResultEntry
        open={!!resultEntryMatch}
        onOpenChange={(open) => {
          if (!open) setResultEntryMatch(null);
        }}
        match={resultEntryMatch || {}}
        onResultSubmitted={() => {
          // Refresh matches data immediately
          queryClient.invalidateQueries({ queryKey: ['user-matches'] });
          // Force refetch to ensure immediate display
          queryClient.refetchQueries({ queryKey: ['user-matches'] });
          setResultEntryMatch(null);
        }}
      />
    </div>
  );
}
