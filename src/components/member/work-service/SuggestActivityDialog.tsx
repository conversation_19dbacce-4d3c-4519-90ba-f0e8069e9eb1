import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { de } from "date-fns/locale";
import { cn } from "@/lib/utils";
import { supabase } from "@/integrations/supabase/client";
import { useClub } from "@/contexts/ClubContext";
import { useToast } from "@/components/ui/use-toast";

interface SuggestActivityDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onActivitySuggested: () => void;
}

export default function SuggestActivityDialog({ 
  isOpen, 
  onOpenChange, 
  onActivitySuggested 
}: SuggestActivityDialogProps) {
  const { currentClubId } = useClub();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [date, setDate] = useState<Date>();
  
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    hourly_rate: "",
    notes: ""
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentClubId) return;

    setIsSubmitting(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error("User not authenticated");

      const activityData = {
        name: formData.name.trim(),
        description: formData.description.trim() || null,
        hourly_rate: formData.hourly_rate ? parseFloat(formData.hourly_rate) : null,
        club_id: currentClubId,
        status: 'pending',
        requested_by: user.id,
        created_by: user.id
      };

      const { error } = await supabase
        .from('activities')
        .insert([activityData]);

      if (error) throw error;

      // If there are notes or a preferred completion date, we could store those in a separate table
      // For now, we'll include notes in the description
      if (formData.notes || date) {
        let enhancedDescription = formData.description.trim();
        if (date) {
          enhancedDescription += `\n\nGewünschtes Erledigungsdatum: ${format(date, "dd.MM.yyyy", { locale: de })}`;
        }
        if (formData.notes) {
          enhancedDescription += `\n\nZusätzliche Notizen: ${formData.notes}`;
        }
        
        // Update the activity with enhanced description
        await supabase
          .from('activities')
          .update({ description: enhancedDescription })
          .eq('requested_by', user.id)
          .eq('name', formData.name.trim())
          .eq('status', 'pending');
      }

      toast({
        title: "Tätigkeit vorgeschlagen",
        description: "Ihre Tätigkeitsvorschlag wurde erfolgreich eingereicht und wartet auf Genehmigung.",
      });

      // Reset form
      setFormData({
        name: "",
        description: "",
        hourly_rate: "",
        notes: ""
      });
      setDate(undefined);
      
      onOpenChange(false);
      onActivitySuggested();
    } catch (error) {
      console.error('Error suggesting activity:', error);
      toast({
        title: "Fehler",
        description: "Tätigkeitsvorschlag konnte nicht eingereicht werden.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setFormData({
      name: "",
      description: "",
      hourly_rate: "",
      notes: ""
    });
    setDate(undefined);
    onOpenChange(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Tätigkeit vorschlagen</DialogTitle>
          <DialogDescription>
            Schlagen Sie eine neue Tätigkeit vor, die Sie gerne für den Verein erledigen möchten. 
            Der Vorschlag wird von einem Administrator geprüft und bei Genehmigung automatisch Ihnen zugeordnet.
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="name">Tätigkeitsname *</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder="z.B. Platzpflege, Eventorganisation, ..."
              required
            />
          </div>

          <div>
            <Label htmlFor="description">Beschreibung *</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              placeholder="Beschreiben Sie die Tätigkeit genauer..."
              rows={3}
              required
            />
          </div>

          <div>
            <Label htmlFor="hourly_rate">Geschätzte Stundenzahl *</Label>
            <Input
              id="hourly_rate"
              type="number"
              step="0.25"
              min="0.25"
              value={formData.hourly_rate}
              onChange={(e) => setFormData({ ...formData, hourly_rate: e.target.value })}
              placeholder="z.B. 2.5"
              required
            />
            <p className="text-sm text-muted-foreground mt-1">
              Wie viele Stunden schätzen Sie für diese Tätigkeit?
            </p>
          </div>

          <div>
            <Label>Gewünschtes Erledigungsdatum (optional)</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !date && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {date ? format(date, "dd.MM.yyyy", { locale: de }) : "Datum auswählen"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={date}
                  onSelect={setDate}
                  initialFocus
                  locale={de}
                />
              </PopoverContent>
            </Popover>
          </div>

          <div>
            <Label htmlFor="notes">Zusätzliche Notizen (optional)</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
              placeholder="Zusätzliche Informationen oder besondere Anforderungen..."
              rows={2}
            />
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              Abbrechen
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Wird eingereicht..." : "Vorschlag einreichen"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}