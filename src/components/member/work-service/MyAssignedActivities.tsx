import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useTenant } from "@/contexts/TenantContext";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Calendar, CheckCircle, XCircle, Clock } from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

interface AssignedActivity {
  id: string;
  activity_id: string;
  status: string;
  assigned_at: string;
  completed_at: string | null;
  hours_credited: number;
  activities: {
    name: string;
    description: string | null;
    hourly_rate: number | null;
  };
}

interface MyAssignedActivitiesProps {
  onActivityStatusChanged: () => void;
}

export default function MyAssignedActivities({ onActivityStatusChanged }: MyAssignedActivitiesProps) {
  const { user } = useAuth();
  const { club } = useTenant();
  const { toast } = useToast();
  const [assignments, setAssignments] = useState<AssignedActivity[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [updatingAssignment, setUpdatingAssignment] = useState<string | null>(null);

  useEffect(() => {
    fetchMyAssignments();
  }, [user, club]);

  const fetchMyAssignments = async () => {
    if (!user || !club) return;

    try {
      const { data, error } = await supabase
        .from('activity_assignments')
        .select(`
          id,
          activity_id,
          status,
          assigned_at,
          completed_at,
          hours_credited,
          activities (
            name,
            description,
            hourly_rate
          )
        `)
        .eq('member_id', user.id)
        .eq('club_id', club.id)
        .order('assigned_at', { ascending: false });

      if (error) throw error;
      setAssignments(data || []);
    } catch (error) {
      console.error('Error fetching assignments:', error);
      toast({
        title: "Fehler",
        description: "Ihre übernommenen Tätigkeiten konnten nicht geladen werden.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleStatusUpdate = async (assignmentId: string, newStatus: string) => {
    setUpdatingAssignment(assignmentId);

    try {
      const updateData: any = { status: newStatus };
      if (newStatus === 'completed') {
        updateData.completed_at = new Date().toISOString();
      }

      const { error } = await supabase
        .from('activity_assignments')
        .update(updateData)
        .eq('id', assignmentId);

      if (error) throw error;

      toast({
        title: newStatus === 'completed' ? "Tätigkeit abgeschlossen" : "Tätigkeit storniert",
        description: newStatus === 'completed' 
          ? "Die Tätigkeit wurde als abgeschlossen markiert."
          : "Die Tätigkeit wurde storniert und steht wieder zur Verfügung.",
      });

      fetchMyAssignments();
      onActivityStatusChanged();
    } catch (error) {
      console.error('Error updating assignment:', error);
      toast({
        title: "Fehler",
        description: "Der Status konnte nicht aktualisiert werden.",
        variant: "destructive",
      });
    } finally {
      setUpdatingAssignment(null);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'assigned':
        return <Badge variant="outline" className="text-blue-600 border-blue-600"><Clock className="w-3 h-3 mr-1" />Übernommen</Badge>;
      case 'completed':
        return <Badge variant="default" className="bg-green-600"><CheckCircle className="w-3 h-3 mr-1" />Abgeschlossen</Badge>;
      case 'cancelled':
        return <Badge variant="destructive"><XCircle className="w-3 h-3 mr-1" />Storniert</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-3">
        {[...Array(2)].map((_, i) => (
          <div key={i} className="h-12 bg-muted animate-pulse rounded" />
        ))}
      </div>
    );
  }

  if (assignments.length === 0) {
    return (
      <div className="text-center py-6">
        <Clock className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
        <p className="text-muted-foreground">
          Sie haben noch keine Tätigkeiten übernommen.
        </p>
      </div>
    );
  }

  return (
    <div className="border rounded-lg">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Tätigkeit</TableHead>
            <TableHead>Übernommen am</TableHead>
            <TableHead>Stunden</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right">Aktionen</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {assignments.map((assignment) => (
            <TableRow key={assignment.id}>
              <TableCell className="font-medium">
                <div>
                  <div>{assignment.activities.name}</div>
                  {assignment.activities.description && (
                    <div className="text-sm text-muted-foreground">
                      {assignment.activities.description}
                    </div>
                  )}
                </div>
              </TableCell>
              <TableCell>
                <div className="flex items-center">
                  <Calendar className="mr-1 h-4 w-4" />
                  {new Date(assignment.assigned_at).toLocaleDateString('de-DE')}
                </div>
              </TableCell>
              <TableCell>
                {assignment.hours_credited}h
              </TableCell>
              <TableCell>
                {getStatusBadge(assignment.status)}
              </TableCell>
              <TableCell className="text-right">
                <div className="flex gap-2 justify-end">
                  {assignment.status === 'assigned' && (
                    <>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            size="sm"
                            variant="default"
                            disabled={updatingAssignment === assignment.id}
                          >
                            Abschließen
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Tätigkeit abschließen</AlertDialogTitle>
                            <AlertDialogDescription>
                              Möchten Sie die Tätigkeit "{assignment.activities.name}" als abgeschlossen markieren?
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Abbrechen</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => handleStatusUpdate(assignment.id, 'completed')}
                            >
                              Abschließen
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>

                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            size="sm"
                            variant="outline"
                            disabled={updatingAssignment === assignment.id}
                          >
                            Stornieren
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Tätigkeit stornieren</AlertDialogTitle>
                            <AlertDialogDescription>
                              Möchten Sie die Tätigkeit "{assignment.activities.name}" stornieren? 
                              Die Tätigkeit wird wieder für andere Mitglieder verfügbar und die gutgeschriebenen Stunden werden abgezogen.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Abbrechen</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => handleStatusUpdate(assignment.id, 'cancelled')}
                              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                            >
                              Stornieren
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </>
                  )}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}