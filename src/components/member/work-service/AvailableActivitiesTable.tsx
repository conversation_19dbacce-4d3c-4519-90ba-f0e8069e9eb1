import { useState, useMemo } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useTenant } from "@/contexts/TenantContext";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowUpDown, Calendar, Users } from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

interface Activity {
  id: string;
  name: string;
  description?: string | null;
  hourly_rate: number | null;
  status: string;
}

interface AvailableActivitiesTableProps {
  activities: Activity[];
  isLoading: boolean;
  onActivityAssigned: () => void;
}

type SortField = 'name' | 'hourly_rate';
type SortDirection = 'asc' | 'desc';

export default function AvailableActivitiesTable({
  activities,
  isLoading,
  onActivityAssigned,
}: AvailableActivitiesTableProps) {
  const { user } = useAuth();
  const { club } = useTenant();
  const { toast } = useToast();
  const [assigningActivity, setAssigningActivity] = useState<string | null>(null);
  const [sortField, setSortField] = useState<SortField>('name');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');

  const sortedActivities = useMemo(() => {
    return [...activities].sort((a, b) => {
      let aValue, bValue;
      
      if (sortField === 'name') {
        aValue = a.name.toLowerCase();
        bValue = b.name.toLowerCase();
      } else {
        aValue = a.hourly_rate || 0;
        bValue = b.hourly_rate || 0;
      }
      
      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });
  }, [activities, sortField, sortDirection]);

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const handleAssignActivity = async (activityId: string) => {
    if (!user || !club) {
      toast({
        title: "Fehler",
        description: "Benutzer oder Club nicht gefunden",
        variant: "destructive",
      });
      return;
    }

    setAssigningActivity(activityId);

    try {
      const { error } = await supabase
        .from('activity_assignments')
        .insert({
          activity_id: activityId,
          member_id: user.id,
          club_id: club.id,
          status: 'assigned'
        });

      if (error) throw error;

      toast({
        title: "Tätigkeit übernommen",
        description: "Die Tätigkeit wurde erfolgreich übernommen und Ihren Arbeitsstunden gutgeschrieben.",
      });

      onActivityAssigned();
    } catch (error) {
      console.error('Error assigning activity:', error);
      toast({
        title: "Fehler",
        description: "Die Tätigkeit konnte nicht übernommen werden.",
        variant: "destructive",
      });
    } finally {
      setAssigningActivity(null);
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-3">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="h-12 bg-muted animate-pulse rounded" />
        ))}
      </div>
    );
  }

  if (activities.length === 0) {
    return (
      <div className="text-center py-8">
        <Users className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium">Keine verfügbaren Tätigkeiten</h3>
        <p className="text-muted-foreground">
          Aktuell stehen keine Tätigkeiten zur Übernahme zur Verfügung.
        </p>
      </div>
    );
  }

  return (
    <div className="border rounded-lg">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>
              <Button
                variant="ghost"
                onClick={() => handleSort('name')}
                className="h-auto p-0 font-medium hover:bg-transparent"
              >
                Tätigkeit
                <ArrowUpDown className="ml-2 h-4 w-4" />
              </Button>
            </TableHead>
            <TableHead>Beschreibung</TableHead>
            <TableHead>
              <Button
                variant="ghost"
                onClick={() => handleSort('hourly_rate')}
                className="h-auto p-0 font-medium hover:bg-transparent"
              >
                Stunden
                <ArrowUpDown className="ml-2 h-4 w-4" />
              </Button>
            </TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right">Aktion</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {sortedActivities.map((activity) => (
            <TableRow key={activity.id}>
              <TableCell className="font-medium">{activity.name}</TableCell>
              <TableCell>
                {activity.description || (
                  <span className="text-muted-foreground italic">
                    Keine Beschreibung
                  </span>
                )}
              </TableCell>
              <TableCell>
                {activity.hourly_rate ? (
                  <div className="flex items-center">
                    <Calendar className="mr-1 h-4 w-4" />
                    {activity.hourly_rate}h
                  </div>
                ) : (
                  <span className="text-muted-foreground">-</span>
                )}
              </TableCell>
              <TableCell>
                <Badge variant={activity.status === 'approved' ? 'default' : 'secondary'}>
                  {activity.status === 'approved' ? 'Verfügbar' : activity.status}
                </Badge>
              </TableCell>
              <TableCell className="text-right">
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button
                      size="sm"
                      disabled={assigningActivity === activity.id}
                    >
                      {assigningActivity === activity.id ? 'Wird übernommen...' : 'Übernehmen'}
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Tätigkeit übernehmen</AlertDialogTitle>
                      <AlertDialogDescription>
                        Möchten Sie die Tätigkeit "{activity.name}" übernehmen?
                        {activity.hourly_rate && (
                          <> Diese wird mit {activity.hourly_rate} Stunden Ihren Arbeitsstunden gutgeschrieben.</>
                        )}
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Abbrechen</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={() => handleAssignActivity(activity.id)}
                      >
                        Übernehmen
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}