
import { useEffect, useMemo, useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import CancellationForm from "../cancellations/CancellationForm";

interface Group {
  id: string;
  type: string;
  total_fee: number;
  billing_member_id: string | null;
}

interface GroupMember {
  id: string; // group_members.id
  account_id: string;
  role: string;
}

interface Account {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
}

export default function GroupManager() {
  const { toast } = useToast();
  const [groups, setGroups] = useState<Group[]>([]);
  const [activeGroupId, setActiveGroupId] = useState<string | null>(null);

  const [members, setMembers] = useState<GroupMember[]>([]);
  const [accountsMap, setAccountsMap] = useState<Record<string, Account>>({});
  const [loading, setLoading] = useState(true);

  const [inviteEmail, setInviteEmail] = useState("");

  // Load groups where the current user is billing member (RLS filters automatically)
  const loadGroups = async () => {
    const { data, error } = await supabase
      .from("groups")
      .select("*")
      .order("created_at", { ascending: false });
    if (error) {
      console.error(error);
      return;
    }
    const rows = (data as Group[]) || [];
    setGroups(rows);
    if (rows.length > 0 && !activeGroupId) {
      setActiveGroupId(rows[0].id);
    }
  };

  const loadMembers = async (groupId: string) => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from("group_members")
        .select("*")
        .eq("group_id", groupId);

      if (error) throw error;
      const gm = (data as GroupMember[]) || [];
      setMembers(gm);

      const accountIds = Array.from(new Set(gm.map((m) => m.account_id)));
      if (accountIds.length === 0) {
        setAccountsMap({});
        return;
      }

      const { data: accs, error: accErr } = await supabase
        .from("accounts")
        .select("id, first_name, last_name, email")
        .in("id", accountIds);

      if (accErr) throw accErr;

      const map: Record<string, Account> = {};
      (accs as Account[]).forEach((a) => {
        map[a.id] = a;
      });
      setAccountsMap(map);
    } catch (err: any) {
      console.error(err);
      toast({
        title: "Fehler",
        description: err.message || "Gruppenmitglieder konnten nicht geladen werden.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadGroups();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (activeGroupId) {
      loadMembers(activeGroupId);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeGroupId]);

  const activeGroup = useMemo(
    () => groups.find((g) => g.id === activeGroupId) || null,
    [groups, activeGroupId]
  );

  const addMemberByEmail = async () => {
    if (!activeGroupId || !inviteEmail) return;

    try {
      // Find account by email
      const { data: account, error: accErr } = await supabase
        .from("accounts")
        .select("id")
        .eq("email", inviteEmail)
        .maybeSingle();

      if (accErr) throw accErr;
      if (!account?.id) {
        toast({
          title: "Nicht gefunden",
          description: "Kein Account mit dieser E-Mail vorhanden.",
          variant: "destructive",
        });
        return;
      }

      // Prevent duplicates
      const exists = members.some((m) => m.account_id === account.id);
      if (exists) {
        toast({
          title: "Hinweis",
          description: "Dieser Account ist bereits Mitglied der Gruppe.",
        });
        return;
      }

      const { error } = await supabase.from("group_members").insert({
        group_id: activeGroupId,
        account_id: account.id,
      });

      if (error) throw error;

      toast({ title: "Hinzugefügt", description: "Mitglied wurde zur Gruppe hinzugefügt." });
      setInviteEmail("");
      await loadMembers(activeGroupId);
    } catch (err: any) {
      toast({
        title: "Fehler",
        description: err.message || "Mitglied konnte nicht hinzugefügt werden.",
        variant: "destructive",
      });
    }
  };

  const removeMember = async (groupMemberId: string) => {
    if (!activeGroupId) return;
    if (!confirm("Mitglied wirklich aus der Gruppe entfernen?")) return;

    try {
      const { error } = await supabase
        .from("group_members")
        .delete()
        .eq("id", groupMemberId);

      if (error) throw error;

      toast({ title: "Entfernt", description: "Mitglied wurde entfernt." });
      await loadMembers(activeGroupId);
    } catch (err: any) {
      toast({
        title: "Fehler",
        description: err.message || "Mitglied konnte nicht entfernt werden.",
        variant: "destructive",
      });
    }
  };

  if (groups.length === 0) {
    return null; // Not a billing member; hide section
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Meine Gruppe</CardTitle>
          <CardDescription>
            Verwalten Sie Mitglieder Ihrer Gruppe. Diese Ansicht ist nur für Rechnungsverantwortliche sichtbar.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {groups.length > 1 && (
            <div className="space-y-2">
              <Label htmlFor="group">Gruppe</Label>
              <select
                id="group"
                className="w-full rounded-md border bg-background px-3 py-2 text-sm"
                value={activeGroupId || ""}
                onChange={(e) => setActiveGroupId(e.target.value)}
              >
                {groups.map((g) => (
                  <option key={g.id} value={g.id}>
                    {g.type || "Gruppe"} • {g.id.slice(0, 6)}
                  </option>
                ))}
              </select>
            </div>
          )}

          {activeGroup && (
            <>
              <div className="flex flex-wrap items-center gap-3">
                <Badge variant="secondary">
                  Typ: {activeGroup.type || "—"}
                </Badge>
                <Badge variant="outline">
                  Gesamtbetrag: {typeof activeGroup.total_fee === "number" ? `${activeGroup.total_fee} €` : "—"}
                </Badge>
              </div>

              <div className="space-y-3">
                <h4 className="text-sm font-medium text-muted-foreground">Mitglieder</h4>

                <div className="flex flex-col md:flex-row gap-2 md:items-end">
                  <div className="flex-1 space-y-2">
                    <Label htmlFor="inviteEmail">Mitglied per E-Mail hinzufügen</Label>
                    <Input
                      id="inviteEmail"
                      placeholder="<EMAIL>"
                      value={inviteEmail}
                      onChange={(e) => setInviteEmail(e.target.value)}
                    />
                  </div>
                  <Button onClick={addMemberByEmail} className="md:mb-[2px]">
                    Hinzufügen
                  </Button>
                </div>

                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>E-Mail</TableHead>
                      <TableHead>Rolle</TableHead>
                      <TableHead className="text-right">Aktionen</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {members.map((m) => {
                      const acc = accountsMap[m.account_id];
                      return (
                        <TableRow key={m.id}>
                          <TableCell>
                            {acc ? `${acc.first_name} ${acc.last_name}` : "—"}
                          </TableCell>
                          <TableCell>{acc?.email || "—"}</TableCell>
                          <TableCell>
                            <Badge variant="outline">{m.role || "member"}</Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <Button variant="ghost" size="sm" onClick={() => removeMember(m.id)}>
                              Entfernen
                            </Button>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                    {members.length === 0 && (
                      <TableRow>
                        <TableCell colSpan={4} className="text-center text-muted-foreground">
                          Keine Mitglieder vorhanden.
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>

                {loading && (
                  <p className="text-sm text-muted-foreground">Laden...</p>
                )}
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {activeGroup && (
        <CancellationForm mode="group" groupId={activeGroup.id} />
      )}
    </div>
  );
}
