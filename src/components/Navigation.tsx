import { But<PERSON> } from "@/components/ui/button";
import { Menu, X, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, ArrowLeft } from "lucide-react";
import { useState, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { useClub } from "@/contexts/ClubContext";
import { toast } from "sonner";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from "@/components/ui/dropdown-menu";
const Navigation = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [currentRole, setCurrentRole] = useState<string | null>(null);
  const {
    currentClub
  } = useClub();
  const location = useLocation();
  useEffect(() => {
    const getCurrentUser = async () => {
      const {
        data: {
          user
        }
      } = await supabase.auth.getUser();
      if (user) {
        setCurrentUser(user);
        // Get current role
        const {
          data: roles
        } = await supabase.rpc('get_user_roles', {
          _user_id: user.id
        });
        if (roles && roles.length > 0) {
          setCurrentRole(roles[0].role);
        }
      }
    };
    getCurrentUser();

    // Listen for auth changes
    const {
      data: {
        subscription
      }
    } = supabase.auth.onAuthStateChange((event, session) => {
      if (session?.user) {
        setCurrentUser(session.user);
        // Defer role fetch to avoid supabase calls inside auth callback
        setTimeout(async () => {
          try {
            const {
              data: roles
            } = await supabase.rpc('get_user_roles', {
              _user_id: session.user!.id
            });
            if (roles && roles.length > 0) {
              setCurrentRole(roles[0].role);
            }
          } catch {}
        }, 0);
      } else {
        setCurrentUser(null);
        setCurrentRole(null);
      }
    });
    return () => subscription.unsubscribe();
  }, []);
  const switchRole = async (newRole: 'admin' | 'trainer' | 'mitglied') => {
    if (!currentUser) {
      console.error('No current user found');
      toast.error('Benutzer nicht angemeldet');
      return;
    }
    try {
      console.log('Switching to role:', newRole);
      console.log('Current user:', currentUser.id);

      // Delete existing role
      const {
        error: deleteError,
        data: deleteData
      } = await supabase.from('user_roles').delete().eq('user_id', currentUser.id);
      console.log('Delete result:', {
        deleteError,
        deleteData
      });
      if (deleteError) {
        console.error('Error deleting role:', deleteError);
        throw deleteError;
      }

      // Insert new role
      const {
        error: insertError,
        data: insertData
      } = await supabase.from('user_roles').insert({
        user_id: currentUser.id,
        role: newRole
      });
      console.log('Insert result:', {
        insertError,
        insertData
      });
      if (insertError) {
        console.error('Error inserting role:', insertError);
        throw insertError;
      }
      setCurrentRole(newRole);
      console.log('Role updated successfully, navigating to:', newRole);
      toast.success(`Rolle zu ${newRole} gewechselt - Weiterleitung...`);

      // Navigate to the appropriate area based on role - club-specific only
      let targetUrl: string;
      if (newRole === 'admin') {
        if (currentClub) {
          targetUrl = `/c/${currentClub.slug}/admin`;
        } else {
          // Check if they have meta-admin permissions
          const {
            data: metaAdminData
          } = await supabase.from('meta_admin_permissions').select('role').eq('user_id', currentUser.id).or('expires_at.is.null,expires_at.gt.now()');
          if (metaAdminData && metaAdminData.length > 0) {
            targetUrl = '/meta-admin';
          } else {
            // No club context and no meta-admin - redirect to first available club
            toast.error('Bitte wählen Sie zunächst einen Verein aus');
            return;
          }
        }
      } else if (newRole === 'trainer') {
        if (currentClub) {
          targetUrl = `/c/${currentClub.slug}/trainer`;
        } else {
          toast.error('Bitte wählen Sie zunächst einen Verein aus');
          return;
        }
      } else if (newRole === 'mitglied') {
        if (currentClub) {
          targetUrl = `/c/${currentClub.slug}/member`;
        } else {
          toast.error('Bitte wählen Sie zunächst einen Verein aus');
          return;
        }
      } else {
        targetUrl = '/meta-admin';
      }
      console.log('Target URL:', targetUrl);
      setTimeout(() => {
        console.log('Navigating now...');
        window.location.href = targetUrl;
      }, 1500);
    } catch (error) {
      console.error('Complete error switching role:', error);
      toast.error('Fehler beim Wechseln der Rolle: ' + (error as Error).message);
    }
  };
  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case 'admin':
        return 'Admin';
      case 'trainer':
        return 'Trainer';
      case 'mitglied':
        return 'Mitglied';
      default:
        return 'Unbekannt';
    }
  };
  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'text-red-600';
      case 'trainer':
        return 'text-blue-600';
      case 'mitglied':
        return 'text-green-600';
      default:
        return 'text-gray-600';
    }
  };
  const getCurrentView = () => {
    const path = location.pathname;
    if (path.includes('/admin')) return 'Admin-Bereich';
    if (path.includes('/trainer')) return 'Trainer-Bereich';
    if (path.includes('/member')) return 'Mitglieder-Bereich';
    if (path.startsWith('/meta-admin')) return 'Meta-Admin';
    if (path === '/auth') return 'Anmeldung';
    return 'Vereinsbereich';
  };
  return <nav className="fixed top-0 left-0 right-0 z-50 bg-background border-b border-border">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Development Switcher - Left Side */}
          <div className="flex items-center gap-4">
            {currentUser ? <div className="flex items-center gap-3">
                <div className="flex flex-col">
                  <div className="text-sm font-semibold text-tennis-green">
                    {getCurrentView()}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    DEV: <span className={getRoleColor(currentRole || '')}>{getRoleDisplayName(currentRole || '')}</span>
                  </div>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm" className="gap-2 h-8">
                      <Wrench className="h-3 w-3" />
                      Rolle wechseln
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="start">
                    <DropdownMenuItem onClick={() => switchRole('admin')}>
                      <span className="text-red-600 font-medium">Admin</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => switchRole('trainer')}>
                      <span className="text-blue-600 font-medium">Trainer</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => switchRole('mitglied')}>
                      <span className="text-green-600 font-medium">Mitglied</span>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem asChild>
                      <Link to="/meta-admin" className="text-purple-600 font-medium w-full">
                        🔗 Meta-Admin
                      </Link>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div> : <div className="flex-shrink-0">
                <h1 className="text-2xl font-bold text-fg-strong">
                  Courtw<span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">ai</span>ve
                </h1>
              </div>}
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:block">
            <div className="ml-10 flex items-baseline space-x-4">
              <Link to="/" className="text-foreground hover:text-tennis-green px-3 py-2 text-sm font-medium transition-smooth">
                Startseite
              </Link>
            </div>
          </div>

          {/* Development Role Switcher */}
          {currentUser && <div className="flex items-center gap-2">
            </div>}

          {/* Desktop Auth Buttons */}
          <div className="flex items-center space-x-4">
            {!currentUser ? <>
              </> : <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">
                  {currentUser.email}
                </span>
                <Button variant="ghost" size="sm" onClick={() => supabase.auth.signOut()}>
                  Abmelden
                </Button>
              </div>}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <Button variant="ghost" size="icon" onClick={() => setIsOpen(!isOpen)}>
              {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </Button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation Menu */}
      {isOpen && <div className="md:hidden bg-white border-t border-net-gray">
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
            <Link to="/" className="block px-3 py-2 text-base font-medium text-foreground hover:text-tennis-green hover:bg-court-white transition-smooth">
              Startseite
            </Link>
          </div>
          <div className="pt-4 pb-3 border-t border-net-gray">
            {/* Mobile Development Role Switcher */}
            {currentUser && <div className="px-5 mb-3">
                <div className="text-sm font-semibold text-tennis-green mb-1">
                  {getCurrentView()}
                </div>
                <div className="text-xs text-muted-foreground mb-2">
                  DEV - Aktuelle Rolle: <span className={getRoleColor(currentRole || '')}>{getRoleDisplayName(currentRole || '')}</span>
                </div>
                <div className="grid grid-cols-3 gap-2">
                  <Button variant="outline" size="sm" onClick={() => switchRole('admin')} className="text-red-600 border-red-200">
                    Admin
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => switchRole('trainer')} className="text-blue-600 border-blue-200">
                    Trainer
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => switchRole('mitglied')} className="text-green-600 border-green-200">
                    Mitglied
                  </Button>
                </div>
              </div>}
            
            <div className="flex items-center px-5 space-x-3">
              {!currentUser ? <>
                </> : <Button variant="ghost" className="w-full justify-start" onClick={() => supabase.auth.signOut()}>
                  Abmelden
                </Button>}
            </div>
          </div>
        </div>}
    </nav>;
};
export default Navigation;