import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Clock, Zap, Activity, CheckCircle, AlertCircle, RefreshCw } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useClub } from "@/contexts/ClubContext";
import { formatTimeInClubTZ } from "@/lib/timezone-utils";

interface AutoBookingResult {
  processed_entries: number;
  successful_bookings: number;
  failed_attempts: number;
  club_timezone: string;
}

export const WaitlistAutoBookingPanel = () => {
  const { toast } = useToast();
  const { currentClub } = useClub();
  const [isChecking, setIsChecking] = useState(false);
  const [lastCheckResult, setLastCheckResult] = useState<AutoBookingResult | null>(null);
  const [lastCheckTime, setLastCheckTime] = useState<Date | null>(null);

  const handleManualAutoBookingCheck = async () => {
    if (!currentClub) {
      toast({
        title: "Fehler",
        description: "Kein Club ausgewählt",
        variant: "destructive",
      });
      return;
    }

    setIsChecking(true);
    try {
      console.log('Triggering manual auto-booking check for club:', currentClub.id);
      
      const { data, error } = await supabase.functions.invoke('waitlist-auto-checker', {
        body: {
          action: 'check_all_pending_entries',
          club_id: currentClub.id
        }
      });

      if (error) {
        throw error;
      }

      console.log('Auto-booking check response:', data);
      
      setLastCheckResult(data.results);
      setLastCheckTime(new Date());

      if (data.results.successful_bookings > 0) {
        toast({
          title: "Auto-Buchungen erfolgreich!",
          description: `${data.results.successful_bookings} Buchung(en) wurden automatisch erstellt.`,
        });
      } else if (data.results.processed_entries > 0) {
        toast({
          title: "Prüfung abgeschlossen",
          description: `${data.results.processed_entries} Warteliste-Einträge geprüft, keine Auto-Buchungen möglich.`,
        });
      } else {
        toast({
          title: "Keine Einträge",
          description: "Keine wartenden Auto-Buchungs-Einträge gefunden.",
        });
      }

    } catch (error: any) {
      console.error('Auto-booking check failed:', error);
      toast({
        title: "Fehler bei Auto-Buchung",
        description: error.message || "Auto-Buchungs-Prüfung fehlgeschlagen",
        variant: "destructive",
      });
    } finally {
      setIsChecking(false);
    }
  };

  const formatLastCheckTime = () => {
    if (!lastCheckTime || !currentClub?.timezone) return "Nie";
    
    try {
      return formatTimeInClubTZ(lastCheckTime, currentClub.timezone, {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch (error) {
      console.error('Error formatting check time:', error);
      return lastCheckTime.toLocaleTimeString();
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Zap className="h-5 w-5" />
          Auto-Buchungs-Verwaltung
        </CardTitle>
        <CardDescription>
          Überprüfen und verwalten Sie automatische Buchungen aus der Warteliste
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        
        {/* Manual Check Button */}
        <div className="flex items-center justify-between">
          <div>
            <h4 className="font-medium flex items-center gap-2">
              <RefreshCw className="h-4 w-4" />
              Manuelle Prüfung
            </h4>
            <p className="text-sm text-muted-foreground">
              Sofortige Überprüfung aller wartenden Auto-Buchungs-Einträge
            </p>
          </div>
          <Button 
            onClick={handleManualAutoBookingCheck}
            disabled={isChecking || !currentClub}
            className="flex items-center gap-2"
          >
            {isChecking ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              <Activity className="h-4 w-4" />
            )}
            {isChecking ? "Prüfung läuft..." : "Jetzt prüfen"}
          </Button>
        </div>

        <Separator />

        {/* Last Check Results */}
        {lastCheckResult && (
          <div className="space-y-3">
            <h4 className="font-medium flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Letzte Prüfung
            </h4>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                <div>
                  <p className="text-sm font-medium">Geprüfte Einträge</p>
                  <p className="text-2xl font-bold">{lastCheckResult.processed_entries}</p>
                </div>
                <Activity className="h-8 w-8 text-muted-foreground" />
              </div>
              
              <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200">
                <div>
                  <p className="text-sm font-medium text-green-700">Erfolgreich</p>
                  <p className="text-2xl font-bold text-green-800">{lastCheckResult.successful_bookings}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-500" />
              </div>
              
              <div className="flex items-center justify-between p-3 bg-orange-50 rounded-lg border border-orange-200">
                <div>
                  <p className="text-sm font-medium text-orange-700">Fehlgeschlagen</p>
                  <p className="text-2xl font-bold text-orange-800">{lastCheckResult.failed_attempts}</p>
                </div>
                <AlertCircle className="h-8 w-8 text-orange-500" />
              </div>
            </div>

            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <span>Letzte Prüfung: {formatLastCheckTime()}</span>
              <Badge variant="outline">
                Zeitzone: {lastCheckResult.club_timezone || currentClub?.timezone || 'Europe/Berlin'}
              </Badge>
            </div>
          </div>
        )}

        <Separator />

        {/* Information */}
        <div className="space-y-2">
          <h4 className="font-medium">Automatische Prüfungen</h4>
          <div className="text-sm text-muted-foreground space-y-1">
            <p>• Auto-Buchungen werden automatisch ausgelöst, wenn Buchungen storniert werden</p>
            <p>• Diese manuelle Prüfung ist nützlich für bereits freie Plätze oder bei Problemen</p>
            <p>• Alle Zeiten werden in der Club-Zeitzone ({currentClub?.timezone || 'Europe/Berlin'}) verarbeitet</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};