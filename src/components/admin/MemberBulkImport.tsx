import { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Upload, Download, FileSpreadsheet, CheckCircle, XCircle, AlertCircle, Users } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useTenant } from "@/contexts/TenantContext";
import { toast } from "sonner";

interface ImportResult {
  success: number;
  errors: Array<{ row: number; field: string; message: string; data: any }>;
  warnings: Array<{ row: number; field: string; message: string; data: any }>;
}

const MemberBulkImport = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [importing, setImporting] = useState(false);
  const [progress, setProgress] = useState(0);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);
  const [csvData, setCsvData] = useState("");
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { club } = useTenant();

  const downloadTemplate = () => {
    const template = `first_name,last_name,email,phone,birth_date,address_street,address_postal_code,address_city,membership_type,account_type,title
Max,Mustermann,<EMAIL>,+49 123 456789,1990-01-15,Musterstraße 1,12345,Musterstadt,Erwachsener,member,Herr
Anna,Beispiel,<EMAIL>,+49 987 654321,1985-05-22,Beispielweg 2,54321,Beispielstadt,Erwachsener,member,Frau`;
    
    const blob = new Blob([template], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'mitglieder_vorlage.csv';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    toast.success("CSV-Vorlage heruntergeladen");
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.name.endsWith('.csv')) {
      toast.error("Bitte wählen Sie eine CSV-Datei aus");
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      setCsvData(content);
    };
    reader.readAsText(file);
  };

  const parseCSV = (csv: string) => {
    const lines = csv.trim().split('\n');
    const headers = lines[0].split(',').map(h => h.trim());
    const rows = lines.slice(1).map(line => {
      const values = line.split(',').map(v => v.trim().replace(/^"(.*)"$/, '$1'));
      const row: any = {};
      headers.forEach((header, index) => {
        row[header] = values[index] || '';
      });
      return row;
    });
    return { headers, rows };
  };

  const validateRow = (row: any, rowIndex: number) => {
    const errors: Array<{ row: number; field: string; message: string; data: any }> = [];
    const warnings: Array<{ row: number; field: string; message: string; data: any }> = [];

    // Required fields
    if (!row.first_name) errors.push({ row: rowIndex, field: 'first_name', message: 'Vorname ist erforderlich', data: row });
    if (!row.last_name) errors.push({ row: rowIndex, field: 'last_name', message: 'Nachname ist erforderlich', data: row });
    if (!row.email) errors.push({ row: rowIndex, field: 'email', message: 'E-Mail ist erforderlich', data: row });

    // Email validation
    if (row.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(row.email)) {
      errors.push({ row: rowIndex, field: 'email', message: 'Ungültige E-Mail-Adresse', data: row });
    }

    // Date validation
    if (row.birth_date && !/^\d{4}-\d{2}-\d{2}$/.test(row.birth_date)) {
      warnings.push({ row: rowIndex, field: 'birth_date', message: 'Geburtsdatum sollte im Format YYYY-MM-DD sein', data: row });
    }

    // Account type validation
    if (row.account_type && !['member', 'guest', 'trainer', 'admin'].includes(row.account_type.toLowerCase())) {
      warnings.push({ row: rowIndex, field: 'account_type', message: 'Unbekannter Kontotyp, wird auf "member" gesetzt', data: row });
    }

    return { errors, warnings };
  };

  const importMembers = async () => {
    if (!csvData || !club) return;

    setImporting(true);
    setProgress(0);
    
    try {
      const { rows } = parseCSV(csvData);
      const totalRows = rows.length;
      const errors: Array<{ row: number; field: string; message: string; data: any }> = [];
      const warnings: Array<{ row: number; field: string; message: string; data: any }> = [];
      let successCount = 0;

      for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        const rowIndex = i + 2; // +2 because CSV has header and is 1-indexed
        
        // Validate row
        const validation = validateRow(row, rowIndex);
        errors.push(...validation.errors);
        warnings.push(...validation.warnings);

        if (validation.errors.length > 0) {
          setProgress(((i + 1) / totalRows) * 100);
          continue;
        }

        try {
          // Create club membership (without user_id for now - just importing member data)
          const membershipData = {
            club_id: club.id,
            user_id: '00000000-0000-0000-0000-*********000', // Placeholder - will be updated when user registers
            first_name: row.first_name,
            last_name: row.last_name,
            email: row.email,
            phone: row.phone || null,
            birth_date: row.birth_date || null,
            address_street: row.address_street || null,
            address_postal_code: row.address_postal_code || null,
            address_city: row.address_city || null,
            membership_type: row.membership_type || 'Erwachsener',
            account_type: (row.account_type || 'member').toLowerCase(),
            title: row.title || null,
            role: (row.account_type || 'member').toLowerCase() === 'admin' ? 'admin' : 'member',
            is_active: true
          };

          const { error: membershipError } = await supabase
            .from('club_memberships')
            .insert(membershipData);

          if (membershipError) {
            errors.push({ 
              row: rowIndex, 
              field: 'database', 
              message: `Datenbankfehler: ${membershipError.message}`, 
              data: row 
            });
          } else {
            successCount++;
          }
        } catch (error: any) {
          errors.push({ 
            row: rowIndex, 
            field: 'system', 
            message: `Systemfehler: ${error.message}`, 
            data: row 
          });
        }

        setProgress(((i + 1) / totalRows) * 100);
      }

      setImportResult({
        success: successCount,
        errors,
        warnings
      });

      if (successCount > 0) {
        toast.success(`${successCount} Mitglied(er) erfolgreich importiert`);
      }
      if (errors.length > 0) {
        toast.error(`${errors.length} Fehler beim Import`);
      }
      if (warnings.length > 0) {
        toast.warning(`${warnings.length} Warnungen beim Import`);
      }

    } catch (error: any) {
      toast.error(`Import fehlgeschlagen: ${error.message}`);
    } finally {
      setImporting(false);
    }
  };

  const resetImport = () => {
    setCsvData("");
    setImportResult(null);
    setProgress(0);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="gap-2">
          <Upload className="h-4 w-4" />
          Mitglieder importieren
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Mitglieder-Massenimport
          </DialogTitle>
          <DialogDescription>
            Importieren Sie mehrere Mitglieder gleichzeitig über eine CSV-Datei
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Template Download */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">1. CSV-Vorlage herunterladen</CardTitle>
              <CardDescription>
                Laden Sie die Vorlage herunter und füllen Sie Ihre Mitgliederdaten ein
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button onClick={downloadTemplate} variant="outline" className="gap-2">
                <Download className="h-4 w-4" />
                CSV-Vorlage herunterladen
              </Button>
            </CardContent>
          </Card>

          {/* File Upload */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">2. CSV-Datei hochladen</CardTitle>
              <CardDescription>
                Wählen Sie Ihre ausgefüllte CSV-Datei aus
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <input
                ref={fileInputRef}
                type="file"
                accept=".csv"
                onChange={handleFileUpload}
                className="block w-full text-sm text-muted-foreground file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-primary-foreground hover:file:bg-primary/80"
              />
              
              {csvData && (
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm text-green-600">
                    <CheckCircle className="h-4 w-4" />
                    CSV-Datei geladen ({parseCSV(csvData).rows.length} Zeilen)
                  </div>
                  <Textarea
                    value={csvData}
                    onChange={(e) => setCsvData(e.target.value)}
                    placeholder="CSV-Daten..."
                    className="h-32 text-xs font-mono"
                  />
                </div>
              )}
            </CardContent>
          </Card>

          {/* Import Action */}
          {csvData && !importResult && (
            <Card>
              <CardHeader>
                <CardTitle className="text-base">3. Import starten</CardTitle>
                <CardDescription>
                  Starten Sie den Import-Prozess
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {importing && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span>Importiere Mitglieder...</span>
                      <span>{Math.round(progress)}%</span>
                    </div>
                    <Progress value={progress} />
                  </div>
                )}
                
                <div className="flex gap-2">
                  <Button 
                    onClick={importMembers} 
                    disabled={importing}
                    className="gap-2"
                  >
                    <FileSpreadsheet className="h-4 w-4" />
                    {importing ? 'Importiere...' : 'Import starten'}
                  </Button>
                  <Button variant="outline" onClick={resetImport}>
                    Zurücksetzen
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Results */}
          {importResult && (
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Import-Ergebnisse</CardTitle>
                <CardDescription>
                  Zusammenfassung des Import-Prozesses
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="flex items-center justify-center gap-2 text-green-600">
                      <CheckCircle className="h-5 w-5" />
                      <span className="font-semibold">{importResult.success}</span>
                    </div>
                    <p className="text-sm text-muted-foreground">Erfolgreich</p>
                  </div>
                  <div className="text-center">
                    <div className="flex items-center justify-center gap-2 text-yellow-600">
                      <AlertCircle className="h-5 w-5" />
                      <span className="font-semibold">{importResult.warnings.length}</span>
                    </div>
                    <p className="text-sm text-muted-foreground">Warnungen</p>
                  </div>
                  <div className="text-center">
                    <div className="flex items-center justify-center gap-2 text-red-600">
                      <XCircle className="h-5 w-5" />
                      <span className="font-semibold">{importResult.errors.length}</span>
                    </div>
                    <p className="text-sm text-muted-foreground">Fehler</p>
                  </div>
                </div>

                {importResult.errors.length > 0 && (
                  <div>
                    <h4 className="font-medium text-red-600 mb-2">Fehler:</h4>
                    <div className="space-y-1 max-h-32 overflow-y-auto">
                      {importResult.errors.map((error, index) => (
                        <div key={index} className="text-sm bg-red-50 p-2 rounded">
                          <Badge variant="destructive" className="mr-2">Zeile {error.row}</Badge>
                          <span className="font-medium">{error.field}:</span> {error.message}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {importResult.warnings.length > 0 && (
                  <div>
                    <h4 className="font-medium text-yellow-600 mb-2">Warnungen:</h4>
                    <div className="space-y-1 max-h-32 overflow-y-auto">
                      {importResult.warnings.map((warning, index) => (
                        <div key={index} className="text-sm bg-yellow-50 p-2 rounded">
                          <Badge variant="secondary" className="mr-2">Zeile {warning.row}</Badge>
                          <span className="font-medium">{warning.field}:</span> {warning.message}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <Button onClick={resetImport} variant="outline" className="w-full">
                  Neuen Import starten
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default MemberBulkImport;