import { useState, useEffect, useMemo } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Plus, Edit, Trash2, Calendar, Filter, Search, Users, User } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { useClub } from "@/contexts/ClubContext";
import { useClubTimezone } from "@/hooks/useClubTimezone";
import { getTodayInTimezone, formatDate } from "@/lib/utils";

interface Booking {
  id: string;
  court_id: string;
  booking_date: string;
  start_time: string;
  end_time: string;
  player_name: string;
  partner_name: string | null;
  created_at: string;
  updated_at: string;
  courts?: {
    number: number;
  };
}

interface Court {
  id: string;
  number: number;
}

const BookingsList = () => {
  const { currentClubId, isLoading: clubLoading } = useClub();
  const { timezone } = useClubTimezone();
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [courts, setCourts] = useState<Court[]>([]);
  const [loading, setLoading] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingBooking, setEditingBooking] = useState<Booking | null>(null);
  
  // Filter states
  const [searchTerm, setSearchTerm] = useState("");
  const [courtFilter, setCourtFilter] = useState("all");
  const [gameTypeFilter, setGameTypeFilter] = useState("all"); // all, single, double
  const [timeFilter, setTimeFilter] = useState("all"); // all, past, current, future
  
  const [formData, setFormData] = useState({
    court_id: "",
    booking_date: "",
    start_time: "",
    end_time: "",
    player_name: "",
    partner_name: ""
  });
  const { toast } = useToast();

  useEffect(() => {
    if (currentClubId) {
      loadData();
    }
  }, [currentClubId]);

  const loadData = async () => {
    if (!currentClubId) return;

    setLoading(true);
    try {
      console.log('BookingsList: Loading bookings for club', currentClubId);
      // Fetch bookings with court information for current club
      const { data: bookingsData, error: bookingsError } = await supabase
        .from("bookings")
        .select(`
          *,
          courts (
            number
          )
        `)
        .eq("club_id", currentClubId)  // ⭐ CLUB FILTER HINZUGEFÜGT
        .order("booking_date", { ascending: false })
        .order("start_time", { ascending: true });

      console.log('BookingsList: bookings result:', { bookingsData, bookingsError });
      if (bookingsError) throw bookingsError;

      // Fetch courts for current club
      const { data: courtsData, error: courtsError } = await supabase
        .from("courts")
        .select("id, number")
        .eq("club_id", currentClubId)  // ⭐ CLUB FILTER HINZUGEFÜGT
        .order("number");

      if (courtsError) throw courtsError;

      setBookings(bookingsData || []);
      setCourts(courtsData || []);
      console.log('BookingsList: Data loaded successfully, bookings:', bookingsData?.length, 'courts:', courtsData?.length, 'for club', currentClubId);
    } catch (error) {
      console.error('BookingsList: Error loading data:', error);
      toast({
        title: "Fehler",
        description: "Fehler beim Laden der Daten",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
      console.log('BookingsList: Loading finished');
    }
  };

  // Filter bookings based on search and filter criteria
  const filteredBookings = useMemo(() => {
    // CRITICAL: Use club timezone for "today" calculation
    const now = getTodayInTimezone(timezone);
    const today = now.toISOString().split('T')[0];
    const currentTime = formatDate(now, { hour: '2-digit', minute: '2-digit', hour12: false }, timezone).slice(-5);

    return bookings.filter((booking) => {
      // Search filter
      const matchesSearch = searchTerm === "" || 
        booking.player_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        booking.partner_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        booking.courts?.number.toString().includes(searchTerm);

      // Court filter
      const matchesCourt = courtFilter === "all" || booking.court_id === courtFilter;

      // Game type filter (single/double)
      const matchesGameType = gameTypeFilter === "all" || 
        (gameTypeFilter === "single" && booking.partner_name) ||
        (gameTypeFilter === "double" && false); // Double not supported yet (needs 4 players)

      // Time filter (past/current/future)
      let matchesTime = true;
      if (timeFilter === "past") {
        matchesTime = booking.booking_date < today || 
          (booking.booking_date === today && booking.end_time < currentTime);
      } else if (timeFilter === "current") {
        matchesTime = booking.booking_date === today && 
          booking.start_time <= currentTime && booking.end_time >= currentTime;
      } else if (timeFilter === "future") {
        matchesTime = booking.booking_date > today || 
          (booking.booking_date === today && booking.start_time > currentTime);
      }

      return matchesSearch && matchesCourt && matchesGameType && matchesTime;
    });
  }, [bookings, searchTerm, courtFilter, gameTypeFilter, timeFilter]);

  const resetForm = () => {
    setFormData({
      court_id: "",
      booking_date: "",
      start_time: "",
      end_time: "",
      player_name: "",
      partner_name: ""
    });
    setEditingBooking(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.court_id || !formData.booking_date || !formData.start_time || !formData.end_time || !formData.player_name) {
      toast({
        title: "Fehler",
        description: "Bitte füllen Sie alle Pflichtfelder aus",
        variant: "destructive"
      });
      return;
    }

    try {
      if (editingBooking) {
        const { error } = await supabase
          .from("bookings")
          .update({
            court_id: formData.court_id,
            booking_date: formData.booking_date,
            start_time: formData.start_time,
            end_time: formData.end_time,
            player_name: formData.player_name,
            partner_name: formData.partner_name || null
          })
          .eq("id", editingBooking.id);

        if (error) throw error;

        toast({
          title: "Erfolg",
          description: "Buchung wurde aktualisiert"
        });
      } else {
        const { error } = await supabase
          .from("bookings")
          .insert({
            court_id: formData.court_id,
            booking_date: formData.booking_date,
            start_time: formData.start_time,
            end_time: formData.end_time,
            player_name: formData.player_name,
            partner_name: formData.partner_name || null,
            club_id: currentClubId  // ⭐ CLUB ID HINZUGEFÜGT
          });

        if (error) throw error;

        toast({
          title: "Erfolg",
          description: "Buchung wurde erstellt"
        });
      }

      setDialogOpen(false);
      resetForm();
      loadData();
    } catch (error) {
      toast({
        title: "Fehler",
        description: `Fehler beim ${editingBooking ? 'Aktualisieren' : 'Erstellen'} der Buchung`,
        variant: "destructive"
      });
    }
  };

  const handleEdit = (booking: Booking) => {
    setEditingBooking(booking);
    setFormData({
      court_id: booking.court_id,
      booking_date: booking.booking_date,
      start_time: booking.start_time,
      end_time: booking.end_time,
      player_name: booking.player_name,
      partner_name: booking.partner_name || ""
    });
    setDialogOpen(true);
  };

  const handleDelete = async (booking: Booking) => {
    if (!confirm("Sind Sie sicher, dass Sie diese Buchung löschen möchten?")) {
      return;
    }

    try {
      // Store booking details before deletion for waitlist processing
      const bookingDetails = {
        club_id: currentClubId,
        court_id: booking.court_id,
        booking_date: booking.booking_date,
        start_time: booking.start_time,
        end_time: booking.end_time
      };

      const { error } = await supabase
        .from("bookings")
        .delete()
        .eq("id", booking.id);

      if (error) throw error;

      // Process waitlist for auto-booking after successful deletion
      try {
        await supabase.functions.invoke('waitlist-manager', {
          body: {
            action: 'process_booking_cancellation',
            ...bookingDetails
          }
        });
      } catch (waitlistError) {
        console.error('Waitlist processing error:', waitlistError);
        // Don't show error to user as booking deletion was successful
      }

      toast({
        title: "Erfolg",
        description: "Buchung wurde gelöscht"
      });
      loadData();
    } catch (error) {
      toast({
        title: "Fehler",
        description: "Fehler beim Löschen der Buchung",
        variant: "destructive"
      });
    }
  };

  const getBookingStatus = (booking: Booking) => {
    // CRITICAL: Use club timezone for status determination
    const now = getTodayInTimezone(timezone);
    const today = now.toISOString().split('T')[0];
    const currentTime = formatDate(now, { hour: '2-digit', minute: '2-digit', hour12: false }, timezone).slice(-5);

    if (booking.booking_date < today || 
        (booking.booking_date === today && booking.end_time < currentTime)) {
      return { label: "Vergangen", variant: "secondary" as const };
    } else if (booking.booking_date === today && 
               booking.start_time <= currentTime && booking.end_time >= currentTime) {
      return { label: "Aktiv", variant: "default" as const };
    } else {
      return { label: "Geplant", variant: "outline" as const };
    }
  };

  if (clubLoading || loading) {
    return <div>Laden...</div>;
  }

  if (!currentClubId) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">Kein Club gefunden. Bitte stellen Sie sicher, dass Sie einer aktiven Club-Mitgliedschaft zugeordnet sind.</p>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Buchungsübersicht
            </CardTitle>
            <CardDescription>
              Alle Buchungen mit erweiterten Filterfunktionen
            </CardDescription>
          </div>
          <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={resetForm} className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                Neue Buchung
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>
                  {editingBooking ? "Buchung bearbeiten" : "Neue Buchung"}
                </DialogTitle>
                <DialogDescription>
                  {editingBooking ? "Bearbeiten Sie die Buchungs-Details" : "Erstellen Sie eine neue Platzbuchung"}
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="court_id">Platz *</Label>
                  <Select
                    value={formData.court_id}
                    onValueChange={(value) => setFormData({ ...formData, court_id: value })}
                    required
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Platz auswählen" />
                    </SelectTrigger>
                    <SelectContent>
                      {courts.map((court) => (
                        <SelectItem key={court.id} value={court.id}>
                          Platz {court.number}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="booking_date">Datum *</Label>
                  <Input
                    id="booking_date"
                    type="date"
                    value={formData.booking_date}
                    onChange={(e) => setFormData({ ...formData, booking_date: e.target.value })}
                    required
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="start_time">Startzeit *</Label>
                    <Input
                      id="start_time"
                      type="time"
                      value={formData.start_time}
                      onChange={(e) => setFormData({ ...formData, start_time: e.target.value })}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="end_time">Endzeit *</Label>
                    <Input
                      id="end_time"
                      type="time"
                      value={formData.end_time}
                      onChange={(e) => setFormData({ ...formData, end_time: e.target.value })}
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="player_name">Spielername *</Label>
                  <Input
                    id="player_name"
                    value={formData.player_name}
                    onChange={(e) => setFormData({ ...formData, player_name: e.target.value })}
                    placeholder="Name des Hauptspielers"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="partner_name">Partner (optional)</Label>
                  <Input
                    id="partner_name"
                    value={formData.partner_name}
                    onChange={(e) => setFormData({ ...formData, partner_name: e.target.value })}
                    placeholder="Name des Partners"
                  />
                </div>

                <div className="flex justify-end gap-2">
                  <Button type="button" variant="outline" onClick={() => setDialogOpen(false)}>
                    Abbrechen
                  </Button>
                  <Button type="submit">
                    {editingBooking ? "Aktualisieren" : "Erstellen"}
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {/* Filter Section */}
        <div className="mb-6 space-y-4">
          <div className="flex items-center gap-2 text-sm font-medium">
            <Filter className="h-4 w-4" />
            Filter & Suche
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="search">Suche</Label>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Spielername oder Platz..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Platz</Label>
              <Select value={courtFilter} onValueChange={setCourtFilter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Alle Plätze</SelectItem>
                  {courts.map((court) => (
                    <SelectItem key={court.id} value={court.id}>
                      Platz {court.number}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Spieltyp</Label>
              <Select value={gameTypeFilter} onValueChange={setGameTypeFilter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Alle</SelectItem>
                  <SelectItem value="single">Einzel</SelectItem>
                  <SelectItem value="double">Doppel</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Status</Label>
              <Select value={timeFilter} onValueChange={setTimeFilter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Alle</SelectItem>
                  <SelectItem value="past">Vergangen</SelectItem>
                  <SelectItem value="current">Aktiv</SelectItem>
                  <SelectItem value="future">Geplant</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Results Summary */}
        <div className="mb-4 text-sm text-muted-foreground">
          {filteredBookings.length} von {bookings.length} Buchungen angezeigt
        </div>
        
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Status</TableHead>
              <TableHead>Platz</TableHead>
              <TableHead>Datum</TableHead>
              <TableHead>Zeit</TableHead>
              <TableHead>Spieler</TableHead>
              <TableHead>Partner</TableHead>
              <TableHead>Typ</TableHead>
              <TableHead className="text-right">Aktionen</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredBookings.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center text-muted-foreground py-8">
                  Keine Buchungen gefunden
                </TableCell>
              </TableRow>
            ) : (
              filteredBookings.map((booking) => {
                const status = getBookingStatus(booking);
                return (
                  <TableRow key={booking.id}>
                    <TableCell>
                      <Badge variant={status.variant}>
                        {status.label}
                      </Badge>
                    </TableCell>
                    <TableCell className="font-medium">
                      Platz {booking.courts?.number || booking.court_id}
                    </TableCell>
                    <TableCell>{booking.booking_date}</TableCell>
                    <TableCell>{booking.start_time} - {booking.end_time}</TableCell>
                    <TableCell>{booking.player_name}</TableCell>
                    <TableCell>{booking.partner_name || "-"}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        {booking.partner_name ? (
                          <>
                            <Users className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm">Einzel</span>
                          </>
                        ) : (
                          <>
                            <User className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm">Unvollständig</span>
                          </>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEdit(booking)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDelete(booking)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })
            )}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};

export default BookingsList;