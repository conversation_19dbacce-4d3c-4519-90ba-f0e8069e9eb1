import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Users, Clock } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { useClub } from "@/contexts/ClubContext";

interface ActivityRegistration {
  id: string;
  registered_at: string;
  status: string;
  notes?: string;
  activities: {
    name: string;
    description?: string;
  };
  members: {
    first_name: string;
    last_name: string;
    email: string;
  };
}

const ActivityRegistrationManagement = () => {
  const [registrations, setRegistrations] = useState<ActivityRegistration[]>([]);
  const [selectedActivity, setSelectedActivity] = useState<string>("all");
  const [activities, setActivities] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();
  const { currentClubId } = useClub();

  useEffect(() => {
    if (currentClubId) {
      fetchActivities();
      fetchRegistrations();
    }
  }, [currentClubId]);

  useEffect(() => {
    if (currentClubId) {
      fetchRegistrations();
    }
  }, [selectedActivity, currentClubId]);

  const fetchActivities = async () => {
    if (!currentClubId) return;
    
    try {
      const { data, error } = await supabase
        .from("activities")
        .select("id, name")
        .eq("club_id", currentClubId)
        .eq("status", "approved")
        .order("name");

      if (error) throw error;
      setActivities(data || []);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch activities",
        variant: "destructive",
      });
    }
  };

  const fetchRegistrations = async () => {
    if (!currentClubId) return;
    
    setLoading(true);
    try {
      let query = supabase
        .from("activity_registrations")
        .select(`
          id,
          registered_at,
          status,
          notes,
          activities!inner (name, description, club_id),
          members (first_name, last_name, email)
        `)
        .eq('activities.club_id', currentClubId)
        .order("registered_at", { ascending: false });

      if (selectedActivity !== "all") {
        query = query.eq("activity_id", selectedActivity);
      }

      const { data, error } = await query;

      if (error) throw error;
      setRegistrations(data || []);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch registrations",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const getRegistrationStats = () => {
    const total = registrations.length;
    const active = registrations.filter(r => r.status === "registered").length;
    const completed = registrations.filter(r => r.status === "completed").length;
    
    return { total, active, completed };
  };

  const stats = getRegistrationStats();

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Gesamt Anmeldungen</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Aktive Teilnahmen</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.active}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Abgeschlossen</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.completed}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filter */}
      <div className="flex items-center gap-4">
        <Select value={selectedActivity} onValueChange={setSelectedActivity}>
          <SelectTrigger className="w-[280px]">
            <SelectValue placeholder="Tätigkeit auswählen" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Alle Tätigkeiten</SelectItem>
            {activities.map((activity) => (
              <SelectItem key={activity.id} value={activity.id}>
                {activity.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Registrations Table */}
      <Card>
        <CardHeader>
          <CardTitle>Mitgliederverzeichnis für Arbeitsdienste</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Mitglied</TableHead>
                <TableHead>Tätigkeit</TableHead>
                <TableHead>Anmeldedatum</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Notizen</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-8">
                    Loading...
                  </TableCell>
                </TableRow>
              ) : registrations.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                    Keine Anmeldungen gefunden
                  </TableCell>
                </TableRow>
              ) : (
                registrations.map((registration) => (
                  <TableRow key={registration.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">
                          {registration.members.first_name} {registration.members.last_name}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {registration.members.email}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{registration.activities.name}</div>
                        {registration.activities.description && (
                          <div className="text-sm text-muted-foreground">
                            {registration.activities.description}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      {new Date(registration.registered_at).toLocaleDateString("de-DE")}
                    </TableCell>
                    <TableCell>
                      <Badge 
                        variant={
                          registration.status === "completed" 
                            ? "default" 
                            : registration.status === "registered"
                            ? "secondary"
                            : "outline"
                        }
                      >
                        {registration.status === "registered" && "Angemeldet"}
                        {registration.status === "completed" && "Abgeschlossen"}
                        {registration.status === "cancelled" && "Abgebrochen"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {registration.notes || "—"}
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default ActivityRegistrationManagement;