import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, XCircle, AlertTriangle, Users, GitMerge, UserX, Play, RotateCcw } from "lucide-react";
import { ImportStats } from "@/hooks/useAdvancedBulkImport";

interface ImportExecutionStepProps {
  isImporting: boolean;
  importProgress: number;
  importStats: ImportStats | null;
  onExecute: () => void;
  onReset: () => void;
}

export const ImportExecutionStep = ({ 
  isImporting, 
  importProgress, 
  importStats, 
  onExecute, 
  onReset 
}: ImportExecutionStepProps) => {
  const isComplete = importStats !== null;

  return (
    <div className="space-y-6">
      {!isComplete && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Play className="h-5 w-5" />
              Import ausführen
            </CardTitle>
            <CardDescription>
              Der Import-Prozess wird alle verarbeiteten Mitgliederdaten in die Datenbank übertragen
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {isImporting && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Importiere Mitglieder...</span>
                  <span>{Math.round(importProgress)}%</span>
                </div>
                <Progress value={importProgress} className="h-2" />
              </div>
            )}
            
            <Button 
              onClick={onExecute} 
              disabled={isImporting}
              size="lg"
              className="w-full gap-2"
            >
              <Users className="h-4 w-4" />
              {isImporting ? 'Import läuft...' : 'Import starten'}
            </Button>
          </CardContent>
        </Card>
      )}

      {isComplete && importStats && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              Import abgeschlossen
            </CardTitle>
            <CardDescription>
              Der Import wurde erfolgreich durchgeführt. Hier ist die Zusammenfassung:
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Summary Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-gray-900">{importStats.total}</div>
                <div className="text-sm text-muted-foreground">Verarbeitet</div>
              </div>
              <div className="text-center p-4 border rounded-lg bg-green-50">
                <div className="flex items-center justify-center gap-1 text-green-600">
                  <Users className="h-4 w-4" />
                  <span className="text-2xl font-bold">{importStats.imported}</span>
                </div>
                <div className="text-sm text-muted-foreground">Importiert</div>
              </div>
              <div className="text-center p-4 border rounded-lg bg-purple-50">
                <div className="flex items-center justify-center gap-1 text-purple-600">
                  <GitMerge className="h-4 w-4" />
                  <span className="text-2xl font-bold">{importStats.merged}</span>
                </div>
                <div className="text-sm text-muted-foreground">Zusammengeführt</div>
              </div>
              <div className="text-center p-4 border rounded-lg bg-gray-50">
                <div className="flex items-center justify-center gap-1 text-gray-600">
                  <UserX className="h-4 w-4" />
                  <span className="text-2xl font-bold">{importStats.skipped}</span>
                </div>
                <div className="text-sm text-muted-foreground">Übersprungen</div>
              </div>
            </div>

            {/* Error Summary */}
            {importStats.errors > 0 && (
              <div className="p-4 border border-red-200 rounded-lg bg-red-50">
                <div className="flex items-center gap-2 text-red-600 mb-2">
                  <XCircle className="h-4 w-4" />
                  <span className="font-medium">{importStats.errors} Fehler aufgetreten</span>
                </div>
                <p className="text-sm text-red-700">
                  Einige Datensätze konnten nicht importiert werden. Überprüfen Sie die Daten und versuchen Sie es erneut.
                </p>
              </div>
            )}

            {/* Success Summary */}
            {importStats.errors === 0 && (
              <div className="p-4 border border-green-200 rounded-lg bg-green-50">
                <div className="flex items-center gap-2 text-green-600 mb-2">
                  <CheckCircle className="h-4 w-4" />
                  <span className="font-medium">Import erfolgreich abgeschlossen</span>
                </div>
                <p className="text-sm text-green-700">
                  Alle Mitgliederdaten wurden erfolgreich verarbeitet und in die Datenbank übertragen.
                </p>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex justify-center">
              <Button 
                onClick={onReset} 
                variant="outline" 
                size="lg"
                className="gap-2"
              >
                <RotateCcw className="h-4 w-4" />
                Neuen Import starten
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};