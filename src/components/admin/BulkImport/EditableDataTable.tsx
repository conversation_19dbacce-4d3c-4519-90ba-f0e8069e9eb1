import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Edit, Save, X, Check } from "lucide-react";
import { ParsedMember } from "@/hooks/useAdvancedBulkImport";

interface EditableDataTableProps {
  parsedMembers: ParsedMember[];
  onMembersChange: (members: ParsedMember[]) => void;
}

export const EditableDataTable = ({ parsedMembers, onMembersChange }: EditableDataTableProps) => {
  const [editingCell, setEditingCell] = useState<{ memberId: string; field: string } | null>(null);
  const [editValue, setEditValue] = useState("");
  const [filter, setFilter] = useState<'all' | 'errors' | 'warnings' | 'problems'>('all');

  const startEdit = (memberId: string, field: string, currentValue: any) => {
    setEditingCell({ memberId, field });
    setEditValue(currentValue || "");
  };

  const cancelEdit = () => {
    setEditingCell(null);
    setEditValue("");
  };

  const saveEdit = () => {
    if (!editingCell) return;

    const updatedMembers = parsedMembers.map(member => {
      if (member.id === editingCell.memberId) {
        const updatedMappedData = {
          ...member.mappedData,
          [editingCell.field]: editValue
        };

        // Re-validate after edit
        // This is a simplified validation - you might want to use the full validation function
        const hasRequiredFields = updatedMappedData.first_name && updatedMappedData.last_name && updatedMappedData.email;
        const isEmailValid = !updatedMappedData.email || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(updatedMappedData.email);
        const isDateValid = !updatedMappedData.birth_date || /^\d{4}-\d{2}-\d{2}$/.test(updatedMappedData.birth_date);

        return {
          ...member,
          mappedData: updatedMappedData,
          // Update validation status (simplified)
          validationErrors: hasRequiredFields && isEmailValid ? [] : member.validationErrors,
          validationWarnings: isDateValid ? member.validationWarnings.filter(w => w.field !== 'birth_date') : member.validationWarnings
        };
      }
      return member;
    });

    onMembersChange(updatedMembers);
    setEditingCell(null);
    setEditValue("");
  };

  const getDisplayFields = () => {
    if (parsedMembers.length === 0) return [];
    
    const sampleMember = parsedMembers[0];
    return Object.keys(sampleMember.mappedData).filter(key => 
      !['club_id', 'role', 'is_active'].includes(key)
    );
  };

  const formatFieldLabel = (field: string) => {
    const labels: Record<string, string> = {
      first_name: 'Vorname',
      last_name: 'Nachname',
      email: 'E-Mail',
      gender: 'Geschlecht',
      phone: 'Telefon',
      birth_date: 'Geburtsdatum',
      address_street: 'Straße',
      address_postal_code: 'PLZ',
      address_city: 'Stadt',
      membership_type: 'Mitgliedschaft',
      account_type: 'Kontotyp',
      title: 'Titel',
      role: 'Rolle'
    };
    return labels[field] || field;
  };

  const getCellStatus = (member: ParsedMember, field: string) => {
    const hasError = member.validationErrors.some(e => e.field === field);
    const hasWarning = member.validationWarnings.some(w => w.field === field);
    
    if (hasError) return 'error';
    if (hasWarning) return 'warning';
    return 'ok';
  };

  const getCellClassName = (status: string) => {
    switch (status) {
      case 'error':
        return 'bg-red-50 border-red-200';
      case 'warning':
        return 'bg-yellow-50 border-yellow-200';
      default:
        return 'bg-white';
    }
  };

  const fields = getDisplayFields();

  // Filter members based on selected filter
  const filteredMembers = parsedMembers.filter(member => {
    switch (filter) {
      case 'errors':
        return member.validationErrors.length > 0;
      case 'warnings':
        return member.validationWarnings.length > 0 && member.validationErrors.length === 0;
      case 'problems':
        return member.validationErrors.length > 0 || member.validationWarnings.length > 0;
      default:
        return true;
    }
  });

  const stats = {
    total: parsedMembers.length,
    errors: parsedMembers.filter(m => m.validationErrors.length > 0).length,
    warnings: parsedMembers.filter(m => m.validationWarnings.length > 0).length,
    problems: parsedMembers.filter(m => m.validationErrors.length > 0 || m.validationWarnings.length > 0).length
  };

  if (parsedMembers.length === 0) {
    return <div>Keine Daten zum Bearbeiten</div>;
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Edit className="h-5 w-5" />
            Daten bearbeiten
          </CardTitle>
          <div className="flex items-center gap-4">
            <div className="text-sm text-muted-foreground">
              {filteredMembers.length} von {parsedMembers.length} Einträgen
            </div>
            <Select value={filter} onValueChange={(value: any) => setFilter(value)}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Alle anzeigen ({stats.total})</SelectItem>
                <SelectItem value="problems">Nur Probleme ({stats.problems})</SelectItem>
                <SelectItem value="errors">Nur Fehler ({stats.errors})</SelectItem>
                <SelectItem value="warnings">Nur Warnungen ({stats.warnings})</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="border rounded-lg">
          <ScrollArea className="h-96 w-full">
            <div className="min-w-max">
              <Table>
                <TableHeader>
                  <TableRow className="bg-gray-50">
                    <TableHead className="w-12 sticky left-0 bg-gray-50 border-r">#</TableHead>
                    {fields.map(field => (
                      <TableHead key={field} className="min-w-40 px-4">
                        {formatFieldLabel(field)}
                      </TableHead>
                    ))}
                    <TableHead className="w-20 sticky right-0 bg-gray-50 border-l">Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredMembers.map((member, index) => (
                    <TableRow key={member.id} className="hover:bg-gray-50">
                      <TableCell className="font-medium sticky left-0 bg-white border-r">
                        {parsedMembers.indexOf(member) + 1}
                      </TableCell>
                      {fields.map(field => {
                        const isEditing = editingCell?.memberId === member.id && editingCell?.field === field;
                        const status = getCellStatus(member, field);
                        const value = member.mappedData[field] || '';

                        return (
                          <TableCell 
                            key={field} 
                            className={`relative px-4 ${getCellClassName(status)}`}
                          >
                            {isEditing ? (
                              <div className="flex items-center gap-1 min-w-36">
                                <Input
                                  value={editValue}
                                  onChange={(e) => setEditValue(e.target.value)}
                                  className="h-8 text-xs"
                                  placeholder={field === 'birth_date' ? 'YYYY-MM-DD' : ''}
                                  onKeyDown={(e) => {
                                    if (e.key === 'Enter') saveEdit();
                                    if (e.key === 'Escape') cancelEdit();
                                  }}
                                  autoFocus
                                />
                                <Button size="sm" variant="ghost" onClick={saveEdit} className="h-8 w-8 p-0 flex-shrink-0">
                                  <Check className="h-3 w-3" />
                                </Button>
                                <Button size="sm" variant="ghost" onClick={cancelEdit} className="h-8 w-8 p-0 flex-shrink-0">
                                  <X className="h-3 w-3" />
                                </Button>
                              </div>
                            ) : (
                              <div 
                                className="cursor-pointer hover:bg-gray-100 p-2 rounded min-h-8 flex items-center justify-between min-w-36"
                                onClick={() => startEdit(member.id, field, value)}
                              >
                                <span className="text-xs flex-grow">{value || <span className="text-gray-400 italic">leer</span>}</span>
                                <div className="flex gap-1 ml-2 flex-shrink-0">
                                  {status === 'error' && (
                                    <Badge variant="destructive" className="text-xs py-0 px-1">!</Badge>
                                  )}
                                  {status === 'warning' && (
                                    <Badge variant="secondary" className="text-xs py-0 px-1">⚠</Badge>
                                  )}
                                </div>
                              </div>
                            )}
                          </TableCell>
                        );
                      })}
                      <TableCell className="sticky right-0 bg-white border-l">
                        <div className="flex flex-col gap-1">
                          {member.validationErrors.length > 0 && (
                            <Badge variant="destructive" className="text-xs">
                              {member.validationErrors.length}
                            </Badge>
                          )}
                          {member.validationWarnings.length > 0 && (
                            <Badge variant="secondary" className="text-xs">
                              {member.validationWarnings.length}
                            </Badge>
                          )}
                          {member.validationErrors.length === 0 && member.validationWarnings.length === 0 && (
                            <Badge variant="default" className="text-xs bg-green-100 text-green-700">
                              OK
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </ScrollArea>
        </div>
        
        <div className="mt-4 p-4 bg-blue-50 rounded-lg">
          <div className="text-sm space-y-2">
            <div className="font-medium">💡 Tipps zur Bearbeitung:</div>
            <div>• <strong>Klicken</strong> Sie auf eine Zelle, um sie zu bearbeiten</div>
            <div>• <strong>Enter</strong> = Speichern | <strong>Escape</strong> = Abbrechen</div>
            <div>• <strong>Horizontales Scrollen</strong> mit Mausrad oder Scrollbalken</div>
            <div>• <strong>Filter "Nur Probleme"</strong> zeigt nur Zeilen mit Fehlern/Warnungen</div>
            <div>• <strong>Rote Badges (!)</strong> = Fehler | <strong>Gelbe Badges (⚠)</strong> = Warnungen</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};