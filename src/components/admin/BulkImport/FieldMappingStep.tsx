import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight, CheckCircle, AlertTriangle } from "lucide-react";
import { FieldMapping } from "@/hooks/useAdvancedBulkImport";
import { useCustomFields } from "@/hooks/useCustomFields";
import { useTenant } from "@/contexts/TenantContext";

interface FieldMappingStepProps {
  headers: string[];
  fieldMappings: FieldMapping[];
  onMappingChange: (mappings: FieldMapping[]) => void;
  onNext: () => void;
  onBack: () => void;
}

const STANDARD_FIELDS = [
  { key: 'first_name', label: 'Vorname', required: true },
  { key: 'last_name', label: 'Nachname', required: true },
  { key: 'email', label: 'E-Mail', required: true },
  { key: 'gender', label: 'Geschlecht', required: true },
  { key: 'phone', label: 'Telefon', required: false },
  { key: 'birth_date', label: 'Geburtsdatum', required: false },
  { key: 'address_street', label: 'Straße', required: false },
  { key: 'address_postal_code', label: 'PLZ', required: false },
  { key: 'address_city', label: 'Stadt', required: false },
  { key: 'membership_type', label: 'Mitgliedschaftstyp', required: false },
  { key: 'account_type', label: 'Kontotyp', required: false },
  { key: 'title', label: 'Titel', required: false },
  { key: 'role', label: 'Rolle', required: false },
];

export const FieldMappingStep = ({ headers, fieldMappings, onMappingChange, onNext, onBack }: FieldMappingStepProps) => {
  const { club } = useTenant();
  const { customFields } = useCustomFields(club?.id);

  const allTargetFields = [
    ...STANDARD_FIELDS,
    ...customFields.map(cf => ({
      key: cf.field_key,
      label: cf.field_name,
      required: cf.is_required,
      isCustom: true
    }))
  ];

  const updateMapping = (csvColumn: string, targetField: string) => {
    const existingIndex = fieldMappings.findIndex(m => m.csvColumn === csvColumn);
    const targetFieldInfo = allTargetFields.find(f => f.key === targetField);
    
    if (!targetFieldInfo) return;

    const newMapping: FieldMapping = {
      csvColumn,
      targetField,
      confidence: 1.0,
      isCustomField: 'isCustom' in targetFieldInfo,
      required: targetFieldInfo.required
    };

    if (existingIndex >= 0) {
      const updated = [...fieldMappings];
      updated[existingIndex] = newMapping;
      onMappingChange(updated);
    } else {
      onMappingChange([...fieldMappings, newMapping]);
    }
  };

  const removeMapping = (csvColumn: string) => {
    onMappingChange(fieldMappings.filter(m => m.csvColumn !== csvColumn));
  };

  const getMappingForColumn = (csvColumn: string) => {
    return fieldMappings.find(m => m.csvColumn === csvColumn);
  };

  const getUsedTargetFields = () => {
    return fieldMappings.map(m => m.targetField);
  };

  const requiredFieldsMapped = () => {
    const mappedFields = fieldMappings.map(m => m.targetField);
    const requiredFields = STANDARD_FIELDS.filter(f => f.required).map(f => f.key);
    return requiredFields.every(field => mappedFields.includes(field));
  };

  const usedTargetFields = getUsedTargetFields();

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ArrowRight className="h-5 w-5" />
          Felderzuordnung
        </CardTitle>
        <CardDescription>
          Ordnen Sie die CSV-Spalten den entsprechenden Datenfeldern zu
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid gap-4">
          {headers.map((header, index) => {
            const mapping = getMappingForColumn(header);
            const isAutoMapped = mapping && mapping.confidence < 1.0;
            
            return (
              <div key={index} className="flex items-center gap-4 p-4 border rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">{header}</span>
                    {isAutoMapped && (
                      <Badge variant="secondary" className="text-xs">
                        Auto ({Math.round(mapping.confidence * 100)}%)
                      </Badge>
                    )}
                  </div>
                </div>
                
                <ArrowRight className="h-4 w-4 text-muted-foreground" />
                
                <div className="flex-1">
                  <Select
                    value={mapping?.targetField || ''}
                    onValueChange={(value) => {
                      if (value === 'none') {
                        removeMapping(header);
                      } else {
                        updateMapping(header, value);
                      }
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Feld auswählen..." />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">Nicht zuordnen</SelectItem>
                      
                      <div className="px-2 py-1 text-xs font-medium text-muted-foreground">
                        Standard-Felder
                      </div>
                      {STANDARD_FIELDS.map(field => (
                        <SelectItem 
                          key={field.key} 
                          value={field.key}
                          disabled={usedTargetFields.includes(field.key) && mapping?.targetField !== field.key}
                        >
                          <div className="flex items-center gap-2">
                            {field.label}
                            {field.required && <Badge variant="destructive" className="text-xs">Pflicht</Badge>}
                            {usedTargetFields.includes(field.key) && mapping?.targetField !== field.key && (
                              <Badge variant="secondary" className="text-xs">Verwendet</Badge>
                            )}
                          </div>
                        </SelectItem>
                      ))}
                      
                      {customFields.length > 0 && (
                        <>
                          <div className="px-2 py-1 text-xs font-medium text-muted-foreground border-t mt-1 pt-2">
                            Benutzerdefinierte Felder
                          </div>
                          {customFields.map(field => (
                            <SelectItem 
                              key={field.field_key} 
                              value={field.field_key}
                              disabled={usedTargetFields.includes(field.field_key) && mapping?.targetField !== field.field_key}
                            >
                              <div className="flex items-center gap-2">
                                {field.field_name}
                                {field.is_required && <Badge variant="destructive" className="text-xs">Pflicht</Badge>}
                                <Badge variant="outline" className="text-xs">Custom</Badge>
                                {usedTargetFields.includes(field.field_key) && mapping?.targetField !== field.field_key && (
                                  <Badge variant="secondary" className="text-xs">Verwendet</Badge>
                                )}
                              </div>
                            </SelectItem>
                          ))}
                        </>
                      )}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            );
          })}
        </div>

        <div className="flex items-center justify-between pt-4 border-t">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={onBack}>
              Zurück zum Upload
            </Button>
            
            {requiredFieldsMapped() ? (
              <div className="flex items-center gap-2 text-green-600">
                <CheckCircle className="h-4 w-4" />
                <span className="text-sm">Alle Pflichtfelder zugeordnet</span>
              </div>
            ) : (
              <div className="flex items-center gap-2 text-amber-600">
                <AlertTriangle className="h-4 w-4" />
                <span className="text-sm">Nicht alle Pflichtfelder zugeordnet</span>
              </div>
            )}
          </div>
          
          <Button 
            onClick={onNext}
            disabled={!requiredFieldsMapped()}
            className="gap-2"
          >
            Weiter zur Vorschau
            <ArrowRight className="h-4 w-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};