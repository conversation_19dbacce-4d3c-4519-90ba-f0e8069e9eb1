import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ArrowRight, CheckCircle, XCircle, AlertTriangle, Users, GitMerge, UserX } from "lucide-react";
import { ParsedMember } from "@/hooks/useAdvancedBulkImport";
import { EditableDataTable } from "./EditableDataTable";

interface ImportPreviewStepProps {
  parsedMembers: ParsedMember[];
  onMembersChange: (members: ParsedMember[]) => void;
  onNext: () => void;
  onBack: () => void;
}

export const ImportPreviewStep = ({ parsedMembers, onMembersChange, onNext, onBack }: ImportPreviewStepProps) => {
  const [filter, setFilter] = useState<'all' | 'errors' | 'warnings' | 'duplicates' | 'ready'>('all');

  const filteredMembers = parsedMembers.filter(member => {
    switch (filter) {
      case 'errors':
        return member.validationErrors.length > 0;
      case 'warnings':
        return member.validationWarnings.length > 0 && member.validationErrors.length === 0;
      case 'duplicates':
        return member.duplicateMatches.length > 0;
      case 'ready':
        return member.validationErrors.length === 0 && member.duplicateMatches.length === 0;
      default:
        return true;
    }
  });

  const updateMemberAction = (memberId: string, action: 'import' | 'merge' | 'skip', mergeTarget?: string) => {
    const updated = parsedMembers.map(member => 
      member.id === memberId 
        ? { ...member, action, mergeTarget, resolved: true }
        : member
    );
    onMembersChange(updated);
  };

  const stats = {
    total: parsedMembers.length,
    errors: parsedMembers.filter(m => m.validationErrors.length > 0).length,
    warnings: parsedMembers.filter(m => m.validationWarnings.length > 0).length,
    duplicates: parsedMembers.filter(m => m.duplicateMatches.length > 0).length,
    ready: parsedMembers.filter(m => m.validationErrors.length === 0 && m.duplicateMatches.length === 0).length,
    toImport: parsedMembers.filter(m => m.action === 'import').length,
    toMerge: parsedMembers.filter(m => m.action === 'merge').length,
    toSkip: parsedMembers.filter(m => m.action === 'skip').length,
  };

  const canProceed = parsedMembers.every(m => m.validationErrors.length === 0);

  return (
    <div className="space-y-6">
      {/* Stats Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Import-Vorschau
          </CardTitle>
          <CardDescription>
            Überprüfen Sie die Daten vor dem Import und lösen Sie eventuelle Konflikte
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold">{stats.total}</div>
              <div className="text-sm text-muted-foreground">Gesamt</div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center gap-1 text-green-600">
                <CheckCircle className="h-4 w-4" />
                <span className="text-2xl font-bold">{stats.ready}</span>
              </div>
              <div className="text-sm text-muted-foreground">Bereit</div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center gap-1 text-amber-600">
                <AlertTriangle className="h-4 w-4" />
                <span className="text-2xl font-bold">{stats.warnings}</span>
              </div>
              <div className="text-sm text-muted-foreground">Warnungen</div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center gap-1 text-red-600">
                <XCircle className="h-4 w-4" />
                <span className="text-2xl font-bold">{stats.errors}</span>
              </div>
              <div className="text-sm text-muted-foreground">Fehler</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Actions Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Import-Aktionen</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="flex items-center justify-center gap-1 text-blue-600">
                <Users className="h-4 w-4" />
                <span className="text-xl font-bold">{stats.toImport}</span>
              </div>
              <div className="text-sm text-muted-foreground">Neue Importe</div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center gap-1 text-purple-600">
                <GitMerge className="h-4 w-4" />
                <span className="text-xl font-bold">{stats.toMerge}</span>
              </div>
              <div className="text-sm text-muted-foreground">Zusammenführungen</div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center gap-1 text-gray-600">
                <UserX className="h-4 w-4" />
                <span className="text-xl font-bold">{stats.toSkip}</span>
              </div>
              <div className="text-sm text-muted-foreground">Übersprungen</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Filter and Table */}
      <Tabs defaultValue="summary" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="summary">Zusammenfassung</TabsTrigger>
          <TabsTrigger value="edit">Daten bearbeiten</TabsTrigger>
        </TabsList>
        
        <TabsContent value="summary">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-base">Detailansicht</CardTitle>
                <Select value={filter} onValueChange={(value: any) => setFilter(value)}>
                  <SelectTrigger className="w-48">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Alle anzeigen ({stats.total})</SelectItem>
                    <SelectItem value="ready">Bereit ({stats.ready})</SelectItem>
                    <SelectItem value="duplicates">Duplikate ({stats.duplicates})</SelectItem>
                    <SelectItem value="warnings">Warnungen ({stats.warnings})</SelectItem>
                    <SelectItem value="errors">Fehler ({stats.errors})</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardHeader>
        <CardContent>
          <ScrollArea className="h-96">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>E-Mail</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Aktion</TableHead>
                  <TableHead>Konfidenz</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredMembers.map((member) => (
                  <TableRow key={member.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">
                          {member.mappedData.first_name} {member.mappedData.last_name}
                        </div>
                        {member.duplicateMatches.length > 0 && (
                          <div className="text-xs text-muted-foreground">
                            Ähnlich: {member.duplicateMatches[0].existingMember.first_name} {member.duplicateMatches[0].existingMember.last_name}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    
                    <TableCell>{member.mappedData.email}</TableCell>
                    
                    <TableCell>
                      <div className="flex flex-col gap-1">
                        {member.validationErrors.length > 0 && (
                          <div className="space-y-1">
                            <Badge variant="destructive" className="text-xs">
                              {member.validationErrors.length} Fehler
                            </Badge>
                            <div className="text-xs text-red-600 space-y-1">
                              {member.validationErrors.slice(0, 2).map((error, idx) => (
                                <div key={idx} className="break-words">
                                  <span className="font-medium">{error.field}:</span> {error.message}
                                </div>
                              ))}
                              {member.validationErrors.length > 2 && (
                                <div className="text-xs text-muted-foreground">
                                  +{member.validationErrors.length - 2} weitere...
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                        {member.validationWarnings.length > 0 && (
                          <div className="space-y-1">
                            <Badge variant="secondary" className="text-xs">
                              {member.validationWarnings.length} Warnungen
                            </Badge>
                            <div className="text-xs text-amber-600 space-y-1">
                              {member.validationWarnings.slice(0, 2).map((warning, idx) => (
                                <div key={idx} className="break-words">
                                  <span className="font-medium">{warning.field}:</span> {warning.message}
                                </div>
                              ))}
                              {member.validationWarnings.length > 2 && (
                                <div className="text-xs text-muted-foreground">
                                  +{member.validationWarnings.length - 2} weitere...
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                        {member.duplicateMatches.length > 0 && (
                          <div className="space-y-1">
                            <Badge variant="outline" className="text-xs">
                              {member.duplicateMatches.length} Duplikat(e)
                            </Badge>
                            <div className="text-xs text-blue-600 space-y-1">
                              {member.duplicateMatches.slice(0, 1).map((duplicate, idx) => (
                                <div key={idx} className="break-words">
                                  <span className="font-medium">{duplicate.field}:</span> {duplicate.confidence.toFixed(0)}% Ähnlichkeit mit "{duplicate.existingMember.first_name} {duplicate.existingMember.last_name}"
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                        {member.validationErrors.length === 0 && member.validationWarnings.length === 0 && member.duplicateMatches.length === 0 && (
                          <Badge variant="default" className="text-xs text-green-600">
                            Bereit
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <Select
                        value={member.action}
                        onValueChange={(value: any) => updateMemberAction(member.id, value, member.duplicateMatches[0]?.id)}
                        disabled={member.validationErrors.length > 0}
                      >
                        <SelectTrigger className="w-32">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="import">Importieren</SelectItem>
                          {member.duplicateMatches.length > 0 && (
                            <SelectItem value="merge">Zusammenführen</SelectItem>
                          )}
                          <SelectItem value="skip">Überspringen</SelectItem>
                        </SelectContent>
                      </Select>
                    </TableCell>
                    
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div className="w-12 bg-gray-200 rounded-full h-2">
                          <div 
                            className="h-2 rounded-full bg-gradient-to-r from-red-500 via-yellow-500 to-green-500"
                            style={{ width: `${member.confidenceScore * 100}%` }}
                          />
                        </div>
                        <span className="text-xs text-muted-foreground">
                          {Math.round(member.confidenceScore * 100)}%
                        </span>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </ScrollArea>
      </CardContent>
    </Card>
  </TabsContent>
  
  <TabsContent value="edit">
    <EditableDataTable 
      parsedMembers={parsedMembers}
      onMembersChange={onMembersChange}
    />
  </TabsContent>
</Tabs>

      {/* Actions */}
      <div className="flex items-center justify-between">
        <Button variant="outline" onClick={onBack}>
          Zurück zur Zuordnung
        </Button>
        
        <div className="flex items-center gap-2">
          {!canProceed && (
            <div className="flex items-center gap-2 text-red-600 text-sm">
              <XCircle className="h-4 w-4" />
              Lösen Sie alle Fehler vor dem Import
            </div>
          )}
          <Button 
            onClick={onNext}
            disabled={!canProceed}
            className="gap-2"
          >
            Import starten
            <ArrowRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};