import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { AlertTriangle, CheckCircle, Play, RefreshCw, Settings } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { useClub } from '@/contexts/ClubContext';
import { useClubTimezone } from '@/hooks/useClubTimezone';
import { getTodayInTimezone } from '@/lib/utils';

interface ValidationResult {
  type: 'error' | 'warning' | 'info' | 'success';
  category: string;
  message: string;
  details?: string;
  matchId?: string;
}

interface TournamentValidationPanelProps {
  tournamentId: string;
  onMatchSelect?: (matchId: string) => void;
}

export function TournamentValidationPanel({ tournamentId, onMatchSelect }: TournamentValidationPanelProps) {
  const [isValidating, setIsValidating] = useState(false);
  const [validationResults, setValidationResults] = useState<ValidationResult[]>([]);
  const { timezone } = useClubTimezone();

  // Fetch tournament and match data
  const { data: tournament } = useQuery({
    queryKey: ['tournament', tournamentId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('tournaments')
        .select('*')
        .eq('id', tournamentId)
        .single();
      if (error) throw error;
      return data;
    }
  });

  const { data: matches } = useQuery({
    queryKey: ['tournament-matches', tournamentId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('tournament_matches')
        .select(`
          *,
          participant1:tournament_participants!participant1_id(id, user_name),
          participant2:tournament_participants!participant2_id(id, user_name)
        `)
        .eq('tournament_id', tournamentId)
        .order('round_number', { ascending: true })
        .order('match_number', { ascending: true });
      if (error) throw error;
      return data;
    }
  });

  // Comprehensive validation function
  const runValidation = useCallback(async () => {
    if (!tournament || !matches) return;
    
    setIsValidating(true);
    const results: ValidationResult[] = [];
    
    try {
      // 1. Schedule completeness check
      const unscheduledMatches = matches.filter(match => 
        !match.scheduled_date || !match.scheduled_time || !match.court_id
      );
      
      if (unscheduledMatches.length > 0) {
        results.push({
          type: 'warning',
          category: 'Terminplanung',
          message: `${unscheduledMatches.length} Spiele sind noch nicht terminiert`,
          details: `Spiele ohne vollständige Terminierung: ${unscheduledMatches.map(m => `R${m.round_number}-${m.match_number}`).join(', ')}`
        });
      } else {
        results.push({
          type: 'success',
          category: 'Terminplanung',
          message: 'Alle Spiele sind vollständig terminiert'
        });
      }

      // 2. Conflict detection
      const scheduledMatches = matches.filter(match => 
        match.scheduled_date && match.scheduled_time && match.court_id
      );
      
      const conflicts = [];
      for (let i = 0; i < scheduledMatches.length; i++) {
        for (let j = i + 1; j < scheduledMatches.length; j++) {
          const match1 = scheduledMatches[i];
          const match2 = scheduledMatches[j];
          
          if (match1.scheduled_date === match2.scheduled_date && 
              match1.court_id === match2.court_id) {
            
            const time1 = match1.scheduled_time!.split(':').map(Number);
            const time2 = match2.scheduled_time!.split(':').map(Number);
            const start1 = time1[0] * 60 + time1[1];
            const start2 = time2[0] * 60 + time2[1];
            const duration1 = match1.estimated_duration_minutes || 90;
            const duration2 = match2.estimated_duration_minutes || 90;
            
            // Check for overlap
            if (!(start1 + duration1 <= start2 || start2 + duration2 <= start1)) {
              conflicts.push({ match1, match2 });
            }
          }
        }
      }
      
      if (conflicts.length > 0) {
        conflicts.forEach(({ match1, match2 }) => {
          results.push({
            type: 'error',
            category: 'Zeitkonflikte',
            message: `Konflikt zwischen R${match1.round_number}-${match1.match_number} und R${match2.round_number}-${match2.match_number}`,
            details: `Beide Spiele am ${match1.scheduled_date} auf Platz ${match1.court_id}`,
            matchId: match1.id
          });
        });
      }

      // 3. Format compliance check
      if (tournament.format_type) {
        const formatIssues = scheduledMatches.filter(match => {
          const duration = match.estimated_duration_minutes || 90;
          
          switch (tournament.format_type.toLowerCase()) {
            case 'social_mixer':
              return duration > 35;
            case 'timeboxed':
              return duration !== 65;
            default:
              return false;
          }
        });
        
        if (formatIssues.length > 0) {
          results.push({
            type: 'warning',
            category: 'Format-Validierung',
            message: `${formatIssues.length} Spiele entsprechen nicht den Format-Vorgaben`,
            details: `Format: ${tournament.format_type}`
          });
        }
      }

      // 4. Booking system sync status
      const syncedMatches = scheduledMatches.filter(match => match.booking_id);
      const syncPercentage = scheduledMatches.length > 0 
        ? Math.round((syncedMatches.length / scheduledMatches.length) * 100)
        : 0;
      
      if (syncPercentage < 100) {
        results.push({
          type: 'info',
          category: 'Buchungssystem',
          message: `${syncPercentage}% der Spiele sind mit dem Buchungssystem synchronisiert`,
          details: `${syncedMatches.length}/${scheduledMatches.length} Spiele synchronisiert`
        });
      } else if (scheduledMatches.length > 0) {
        results.push({
          type: 'success',
          category: 'Buchungssystem',
          message: 'Alle terminierten Spiele sind synchronisiert'
        });
      }

      // 5. Tournament timeline check
      const today = getTodayInTimezone(timezone).toISOString().split('T')[0];
      const pastMatches = scheduledMatches.filter(match => 
        match.scheduled_date && match.scheduled_date < today && match.status === 'scheduled'
      );
      
      if (pastMatches.length > 0) {
        results.push({
          type: 'warning',
          category: 'Zeitplan',
          message: `${pastMatches.length} Spiele sind überfällig`,
          details: 'Spiele in der Vergangenheit sollten aktualisiert werden'
        });
      }

    } catch (error) {
      console.error('Validation error:', error);
      results.push({
        type: 'error',
        category: 'System',
        message: 'Fehler bei der Validierung',
        details: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
    
    setValidationResults(results);
    setIsValidating(false);
    
    // Show summary toast
    const errors = results.filter(r => r.type === 'error').length;
    const warnings = results.filter(r => r.type === 'warning').length;
    
    if (errors > 0) {
      toast.error(`Validierung abgeschlossen: ${errors} Fehler, ${warnings} Warnungen`);
    } else if (warnings > 0) {
      toast.warning(`Validierung abgeschlossen: ${warnings} Warnungen`);
    } else {
      toast.success('Validierung erfolgreich - keine Probleme gefunden');
    }
  }, [tournament, matches]);

  // Auto-run validation when data changes
  React.useEffect(() => {
    if (tournament && matches) {
      runValidation();
    }
  }, [tournament, matches, runValidation]);

  const getResultIcon = (type: ValidationResult['type']) => {
    switch (type) {
      case 'error': return <AlertTriangle className="h-4 w-4 text-destructive" />;
      case 'warning': return <AlertTriangle className="h-4 w-4 text-warning" />;
      case 'success': return <CheckCircle className="h-4 w-4 text-success" />;
      case 'info': return <Settings className="h-4 w-4 text-info" />;
    }
  };

  const getResultBadgeVariant = (type: ValidationResult['type']) => {
    switch (type) {
      case 'error': return 'destructive';
      case 'warning': return 'secondary';
      case 'success': return 'default';
      case 'info': return 'outline';
    }
  };

  const resultsByCategory = validationResults.reduce((acc, result) => {
    if (!acc[result.category]) acc[result.category] = [];
    acc[result.category].push(result);
    return acc;
  }, {} as Record<string, ValidationResult[]>);

  const totalIssues = validationResults.filter(r => r.type === 'error' || r.type === 'warning').length;
  const progress = validationResults.length > 0 
    ? Math.round(((validationResults.length - totalIssues) / validationResults.length) * 100)
    : 0;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Turnier-Validierung
            </CardTitle>
            <CardDescription>
              Automatische Überprüfung der Turnier-Konfiguration
            </CardDescription>
          </div>
          <Button 
            onClick={runValidation} 
            disabled={isValidating}
            size="sm"
          >
            {isValidating ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              <Play className="h-4 w-4" />
            )}
            {isValidating ? 'Validiere...' : 'Validieren'}
          </Button>
        </div>
        
        {validationResults.length > 0 && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>Turnier-Status</span>
              <span className="font-medium">{progress}%</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>
        )}
      </CardHeader>
      
      <CardContent>
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Übersicht</TabsTrigger>
            <TabsTrigger value="details">Details</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview" className="space-y-4">
            <div className="grid gap-4">
              {Object.entries(resultsByCategory).map(([category, results]) => {
                const errors = results.filter(r => r.type === 'error').length;
                const warnings = results.filter(r => r.type === 'warning').length;
                const success = results.filter(r => r.type === 'success').length;
                
                return (
                  <div key={category} className="flex items-center justify-between p-3 rounded-lg border">
                    <div>
                      <div className="font-medium">{category}</div>
                      <div className="text-sm text-muted-foreground">
                        {results.length} Überprüfung{results.length !== 1 ? 'en' : ''}
                      </div>
                    </div>
                    <div className="flex gap-2">
                      {errors > 0 && (
                        <Badge variant="destructive" className="text-xs">
                          {errors} Fehler
                        </Badge>
                      )}
                      {warnings > 0 && (
                        <Badge variant="secondary" className="text-xs">
                          {warnings} Warnung{warnings !== 1 ? 'en' : ''}
                        </Badge>
                      )}
                      {success > 0 && (
                        <Badge variant="default" className="text-xs">
                          {success} OK
                        </Badge>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </TabsContent>
          
          <TabsContent value="details" className="space-y-4">
            <div className="space-y-3">
              {validationResults.map((result, index) => (
                <div 
                  key={index}
                  className={`p-3 rounded-lg border ${
                    result.matchId ? 'cursor-pointer hover:bg-muted/50' : ''
                  }`}
                  onClick={() => result.matchId && onMatchSelect?.(result.matchId)}
                >
                  <div className="flex items-start gap-3">
                    {getResultIcon(result.type)}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <Badge 
                          variant={getResultBadgeVariant(result.type)}
                          className="text-xs"
                        >
                          {result.category}
                        </Badge>
                      </div>
                      <div className="font-medium text-sm">{result.message}</div>
                      {result.details && (
                        <div className="text-xs text-muted-foreground mt-1">
                          {result.details}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
              
              {validationResults.length === 0 && !isValidating && (
                <div className="text-center py-8 text-muted-foreground">
                  <Settings className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <div>Noch keine Validierung durchgeführt</div>
                  <div className="text-xs">Klicken Sie auf "Validieren" um zu beginnen</div>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}