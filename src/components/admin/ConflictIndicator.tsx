import React, { useState, useEffect } from 'react';
import { AlertTriangle, CheckCircle } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface ConflictIndicatorProps {
  date: string;
  time: string;
  courtId: string;
  matchId: string;
  checkConflicts: (date: string, time: string, courtId: string, excludeMatchId?: string) => Promise<{
    hasConflict: boolean;
    conflicts: { type: string; details: string }[];
  }>;
}

export function ConflictIndicator({ date, time, courtId, matchId, checkConflicts }: ConflictIndicatorProps) {
  const [conflictStatus, setConflictStatus] = useState<{
    checking: boolean;
    hasConflict: boolean;
    conflicts: { type: string; details: string }[];
  }>({ checking: true, hasConflict: false, conflicts: [] });

  useEffect(() => {
    const checkForConflicts = async () => {
      try {
        setConflictStatus(prev => ({ ...prev, checking: true }));
        const result = await checkConflicts(date, time, courtId, matchId);
        setConflictStatus({
          checking: false,
          hasConflict: result.hasConflict,
          conflicts: result.conflicts
        });
      } catch (error) {
        console.error('Error checking conflicts:', error);
        setConflictStatus({
          checking: false,
          hasConflict: false,
          conflicts: []
        });
      }
    };

    checkForConflicts();
  }, [date, time, courtId, matchId, checkConflicts]);

  if (conflictStatus.checking) {
    return <div className="h-4 w-4 animate-pulse bg-muted rounded" />;
  }

  if (conflictStatus.hasConflict) {
    return (
      <Badge variant="destructive" className="ml-1 h-5 w-5 p-0 rounded-full">
        <AlertTriangle className="h-3 w-3" />
      </Badge>
    );
  }

  return (
    <Badge variant="secondary" className="ml-1 h-5 w-5 p-0 rounded-full bg-green-100 text-green-700">
      <CheckCircle className="h-3 w-3" />
    </Badge>
  );
}