import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { CalendarDays, Users, MapPin, Clock, Plus, Edit, Trash2, PartyPopper, Utensils, Monitor, Calendar, Trophy } from "lucide-react";
import { toast } from "sonner";

interface Event {
  id: string;
  title: string;
  description: string;
  type: 'jubilee' | 'grill' | 'public_viewing' | 'social' | 'workshop' | 'meeting' | 'other';
  date: string;
  startTime: string;
  endTime: string;
  location: string;
  maxParticipants?: number;
  currentParticipants: number;
  status: 'draft' | 'published' | 'cancelled' | 'completed';
  registrationRequired: boolean;
  fee?: number;
  organizer: string;
}

const eventTypes = [
  { value: 'jubilee', label: 'Jubiläumsfeier', icon: PartyPopper, color: 'bg-purple-100 text-purple-800' },
  { value: 'grill', label: 'Grillabend', icon: Utensils, color: 'bg-orange-100 text-orange-800' },
  { value: 'public_viewing', label: 'Public Viewing', icon: Monitor, color: 'bg-blue-100 text-blue-800' },
  { value: 'social', label: 'Gesellschaftsabend', icon: Users, color: 'bg-green-100 text-green-800' },
  { value: 'workshop', label: 'Workshop', icon: Trophy, color: 'bg-indigo-100 text-indigo-800' },
  { value: 'meeting', label: 'Mitgliederversammlung', icon: Calendar, color: 'bg-gray-100 text-gray-800' },
  { value: 'other', label: 'Sonstiges', icon: Calendar, color: 'bg-slate-100 text-slate-800' }
];

const mockEvents: Event[] = [
  {
    id: '1',
    title: '25-jähriges Vereinsjubiläum',
    description: 'Große Feier zum 25-jährigen Bestehen unseres Tennisvereins mit Live-Musik, Buffet und Ehrungen.',
    type: 'jubilee',
    date: '2024-06-15',
    startTime: '18:00',
    endTime: '23:00',
    location: 'Clubhaus & Terrasse',
    maxParticipants: 150,
    currentParticipants: 87,
    status: 'published',
    registrationRequired: true,
    fee: 35,
    organizer: 'Vorstand'
  },
  {
    id: '2',
    title: 'Grillabend für Mitglieder',
    description: 'Gemütlicher Grillabend mit kühlen Getränken und gegrillten Leckereien für alle Vereinsmitglieder.',
    type: 'grill',
    date: '2024-07-20',
    startTime: '19:00',
    endTime: '22:00',
    location: 'Clubhaus Terrasse',
    maxParticipants: 80,
    currentParticipants: 23,
    status: 'published',
    registrationRequired: true,
    fee: 15,
    organizer: 'Sozialausschuss'
  },
  {
    id: '3',
    title: 'Wimbledon Finale Public Viewing',
    description: 'Schauen Sie das Wimbledon Finale gemeinsam auf unserer großen Leinwand im Clubhaus.',
    type: 'public_viewing',
    date: '2024-07-14',
    startTime: '15:00',
    endTime: '18:00',
    location: 'Clubhaus Hauptraum',
    maxParticipants: 60,
    currentParticipants: 45,
    status: 'published',
    registrationRequired: false,
    organizer: 'Sportausschuss'
  }
];

export function EventManagement() {
  const [events, setEvents] = useState<Event[]>(mockEvents);
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [filter, setFilter] = useState<string>('all');

  const [formData, setFormData] = useState<Partial<Event>>({
    title: '',
    description: '',
    type: 'social',
    date: '',
    startTime: '',
    endTime: '',
    location: '',
    maxParticipants: undefined,
    registrationRequired: false,
    fee: undefined,
    organizer: ''
  });

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      type: 'social',
      date: '',
      startTime: '',
      endTime: '',
      location: '',
      maxParticipants: undefined,
      registrationRequired: false,
      fee: undefined,
      organizer: ''
    });
  };

  const handleCreateEvent = () => {
    if (!formData.title || !formData.date || !formData.startTime || !formData.organizer) {
      toast.error("Bitte füllen Sie alle Pflichtfelder aus");
      return;
    }

    const newEvent: Event = {
      id: Date.now().toString(),
      title: formData.title!,
      description: formData.description || '',
      type: formData.type as Event['type'],
      date: formData.date!,
      startTime: formData.startTime!,
      endTime: formData.endTime || '23:59',
      location: formData.location || '',
      maxParticipants: formData.maxParticipants,
      currentParticipants: 0,
      status: 'draft',
      registrationRequired: formData.registrationRequired || false,
      fee: formData.fee,
      organizer: formData.organizer!
    };

    setEvents([...events, newEvent]);
    setIsCreateDialogOpen(false);
    resetForm();
    toast.success("Event erfolgreich erstellt");
  };

  const handleEditEvent = () => {
    if (!selectedEvent || !formData.title || !formData.date || !formData.startTime || !formData.organizer) {
      toast.error("Bitte füllen Sie alle Pflichtfelder aus");
      return;
    }

    const updatedEvent: Event = {
      ...selectedEvent,
      title: formData.title!,
      description: formData.description || '',
      type: formData.type as Event['type'],
      date: formData.date!,
      startTime: formData.startTime!,
      endTime: formData.endTime || '23:59',
      location: formData.location || '',
      maxParticipants: formData.maxParticipants,
      registrationRequired: formData.registrationRequired || false,
      fee: formData.fee,
      organizer: formData.organizer!
    };

    setEvents(events.map(event => event.id === selectedEvent.id ? updatedEvent : event));
    setIsEditDialogOpen(false);
    setSelectedEvent(null);
    resetForm();
    toast.success("Event erfolgreich aktualisiert");
  };

  const handleDeleteEvent = (eventId: string) => {
    setEvents(events.filter(event => event.id !== eventId));
    toast.success("Event erfolgreich gelöscht");
  };

  const toggleEventStatus = (eventId: string) => {
    setEvents(events.map(event => 
      event.id === eventId 
        ? { ...event, status: event.status === 'published' ? 'draft' : 'published' as Event['status'] }
        : event
    ));
    toast.success("Event-Status aktualisiert");
  };

  const openEditDialog = (event: Event) => {
    setSelectedEvent(event);
    setFormData({
      title: event.title,
      description: event.description,
      type: event.type,
      date: event.date,
      startTime: event.startTime,
      endTime: event.endTime,
      location: event.location,
      maxParticipants: event.maxParticipants,
      registrationRequired: event.registrationRequired,
      fee: event.fee,
      organizer: event.organizer
    });
    setIsEditDialogOpen(true);
  };

  const getEventTypeInfo = (type: string) => {
    return eventTypes.find(t => t.value === type) || eventTypes[eventTypes.length - 1];
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'bg-green-100 text-green-800';
      case 'draft': return 'bg-yellow-100 text-yellow-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      case 'completed': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredEvents = filter === 'all' ? events : events.filter(event => event.type === filter);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h2 className="text-2xl font-bold">Event-Verwaltung</h2>
          <p className="text-muted-foreground">
            Verwalten Sie Vereinsevents wie Jubiläumsfeiern, Grillabende und Public-Viewing Events
          </p>
        </div>
        
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={resetForm}>
              <Plus className="h-4 w-4 mr-2" />
              Neues Event
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Neues Event erstellen</DialogTitle>
              <DialogDescription>
                Erstellen Sie ein neues Event für Ihren Verein
              </DialogDescription>
            </DialogHeader>

            <div className="grid grid-cols-2 gap-4">
              <div className="col-span-2">
                <Label htmlFor="title">Titel *</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  placeholder="z.B. Vereinsjubiläum, Grillabend..."
                />
              </div>

              <div className="col-span-2">
                <Label htmlFor="description">Beschreibung</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  placeholder="Detaillierte Beschreibung des Events..."
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="type">Event-Typ *</Label>
                <Select value={formData.type} onValueChange={(value) => setFormData({ ...formData, type: value as Event['type'] })}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {eventTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        <div className="flex items-center">
                          <type.icon className="h-4 w-4 mr-2" />
                          {type.label}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="organizer">Veranstalter *</Label>
                <Input
                  id="organizer"
                  value={formData.organizer}
                  onChange={(e) => setFormData({ ...formData, organizer: e.target.value })}
                  placeholder="z.B. Vorstand, Sozialausschuss..."
                />
              </div>

              <div>
                <Label htmlFor="date">Datum *</Label>
                <Input
                  id="date"
                  type="date"
                  value={formData.date}
                  onChange={(e) => setFormData({ ...formData, date: e.target.value })}
                />
              </div>

              <div>
                <Label htmlFor="startTime">Startzeit *</Label>
                <Input
                  id="startTime"
                  type="time"
                  value={formData.startTime}
                  onChange={(e) => setFormData({ ...formData, startTime: e.target.value })}
                />
              </div>

              <div>
                <Label htmlFor="endTime">Endzeit</Label>
                <Input
                  id="endTime"
                  type="time"
                  value={formData.endTime}
                  onChange={(e) => setFormData({ ...formData, endTime: e.target.value })}
                />
              </div>

              <div>
                <Label htmlFor="location">Ort</Label>
                <Input
                  id="location"
                  value={formData.location}
                  onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                  placeholder="z.B. Clubhaus, Terrasse..."
                />
              </div>

              <div>
                <Label htmlFor="maxParticipants">Max. Teilnehmer</Label>
                <Input
                  id="maxParticipants"
                  type="number"
                  value={formData.maxParticipants || ''}
                  onChange={(e) => setFormData({ ...formData, maxParticipants: e.target.value ? parseInt(e.target.value) : undefined })}
                  placeholder="Unbegrenzt wenn leer"
                />
              </div>

              <div>
                <Label htmlFor="fee">Teilnahmegebühr (€)</Label>
                <Input
                  id="fee"
                  type="number"
                  step="0.01"
                  value={formData.fee || ''}
                  onChange={(e) => setFormData({ ...formData, fee: e.target.value ? parseFloat(e.target.value) : undefined })}
                  placeholder="Kostenlos wenn leer"
                />
              </div>

              <div className="col-span-2">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="registrationRequired"
                    checked={formData.registrationRequired}
                    onChange={(e) => setFormData({ ...formData, registrationRequired: e.target.checked })}
                  />
                  <Label htmlFor="registrationRequired">Anmeldung erforderlich</Label>
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                Abbrechen
              </Button>
              <Button onClick={handleCreateEvent}>
                Event erstellen
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Filter */}
      <div className="flex flex-wrap gap-2">
        <Button
          variant={filter === 'all' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setFilter('all')}
        >
          Alle Events
        </Button>
        {eventTypes.map((type) => (
          <Button
            key={type.value}
            variant={filter === type.value ? 'default' : 'outline'}
            size="sm"
            onClick={() => setFilter(type.value)}
          >
            <type.icon className="h-4 w-4 mr-1" />
            {type.label}
          </Button>
        ))}
      </div>

      {/* Events Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredEvents.map((event) => {
          const typeInfo = getEventTypeInfo(event.type);
          const TypeIcon = typeInfo.icon;

          return (
            <Card key={event.id} className="relative">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-2">
                    <TypeIcon className="h-5 w-5 mt-1 text-muted-foreground" />
                    <div>
                      <CardTitle className="text-lg">{event.title}</CardTitle>
                      <CardDescription className="line-clamp-2">
                        {event.description}
                      </CardDescription>
                    </div>
                  </div>
                  <div className="flex space-x-1">
                    <Button variant="ghost" size="sm" onClick={() => openEditDialog(event)}>
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => handleDeleteEvent(event.id)}>
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>

              <CardContent>
                <div className="space-y-3">
                  {/* Badges */}
                  <div className="flex flex-wrap gap-2">
                    <Badge className={typeInfo.color}>
                      {typeInfo.label}
                    </Badge>
                    <Badge className={getStatusColor(event.status)}>
                      {event.status === 'published' ? 'Veröffentlicht' : 
                       event.status === 'draft' ? 'Entwurf' :
                       event.status === 'cancelled' ? 'Abgesagt' : 'Abgeschlossen'}
                    </Badge>
                  </div>

                  {/* Event Details */}
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center text-muted-foreground">
                      <CalendarDays className="h-4 w-4 mr-2" />
                      {new Date(event.date).toLocaleDateString('de-DE')}
                    </div>
                    
                    <div className="flex items-center text-muted-foreground">
                      <Clock className="h-4 w-4 mr-2" />
                      {event.startTime} - {event.endTime}
                    </div>

                    {event.location && (
                      <div className="flex items-center text-muted-foreground">
                        <MapPin className="h-4 w-4 mr-2" />
                        {event.location}
                      </div>
                    )}

                    {event.maxParticipants && (
                      <div className="flex items-center text-muted-foreground">
                        <Users className="h-4 w-4 mr-2" />
                        {event.currentParticipants}/{event.maxParticipants} Teilnehmer
                      </div>
                    )}

                    {event.fee && (
                      <div className="text-muted-foreground">
                        <strong>Gebühr:</strong> {event.fee}€
                      </div>
                    )}

                    <div className="text-muted-foreground">
                      <strong>Veranstalter:</strong> {event.organizer}
                    </div>
                  </div>

                  {/* Action Button */}
                  <Button
                    variant={event.status === 'published' ? 'secondary' : 'default'}
                    size="sm"
                    className="w-full"
                    onClick={() => toggleEventStatus(event.id)}
                  >
                    {event.status === 'published' ? 'Als Entwurf markieren' : 'Veröffentlichen'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {filteredEvents.length === 0 && (
        <Card className="text-center py-12">
          <CardContent>
            <PartyPopper className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-medium mb-2">Keine Events gefunden</h3>
            <p className="text-muted-foreground mb-4">
              {filter === 'all' 
                ? 'Es wurden noch keine Events erstellt.'
                : `Keine Events vom Typ "${getEventTypeInfo(filter).label}" gefunden.`
              }
            </p>
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Erstes Event erstellen
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Event bearbeiten</DialogTitle>
            <DialogDescription>
              Bearbeiten Sie die Event-Details
            </DialogDescription>
          </DialogHeader>

          <div className="grid grid-cols-2 gap-4">
            <div className="col-span-2">
              <Label htmlFor="edit-title">Titel *</Label>
              <Input
                id="edit-title"
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                placeholder="z.B. Vereinsjubiläum, Grillabend..."
              />
            </div>

            <div className="col-span-2">
              <Label htmlFor="edit-description">Beschreibung</Label>
              <Textarea
                id="edit-description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Detaillierte Beschreibung des Events..."
                rows={3}
              />
            </div>

            <div>
              <Label htmlFor="edit-type">Event-Typ *</Label>
              <Select value={formData.type} onValueChange={(value) => setFormData({ ...formData, type: value as Event['type'] })}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {eventTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      <div className="flex items-center">
                        <type.icon className="h-4 w-4 mr-2" />
                        {type.label}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="edit-organizer">Veranstalter *</Label>
              <Input
                id="edit-organizer"
                value={formData.organizer}
                onChange={(e) => setFormData({ ...formData, organizer: e.target.value })}
                placeholder="z.B. Vorstand, Sozialausschuss..."
              />
            </div>

            <div>
              <Label htmlFor="edit-date">Datum *</Label>
              <Input
                id="edit-date"
                type="date"
                value={formData.date}
                onChange={(e) => setFormData({ ...formData, date: e.target.value })}
              />
            </div>

            <div>
              <Label htmlFor="edit-startTime">Startzeit *</Label>
              <Input
                id="edit-startTime"
                type="time"
                value={formData.startTime}
                onChange={(e) => setFormData({ ...formData, startTime: e.target.value })}
              />
            </div>

            <div>
              <Label htmlFor="edit-endTime">Endzeit</Label>
              <Input
                id="edit-endTime"
                type="time"
                value={formData.endTime}
                onChange={(e) => setFormData({ ...formData, endTime: e.target.value })}
              />
            </div>

            <div>
              <Label htmlFor="edit-location">Ort</Label>
              <Input
                id="edit-location"
                value={formData.location}
                onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                placeholder="z.B. Clubhaus, Terrasse..."
              />
            </div>

            <div>
              <Label htmlFor="edit-maxParticipants">Max. Teilnehmer</Label>
              <Input
                id="edit-maxParticipants"
                type="number"
                value={formData.maxParticipants || ''}
                onChange={(e) => setFormData({ ...formData, maxParticipants: e.target.value ? parseInt(e.target.value) : undefined })}
                placeholder="Unbegrenzt wenn leer"
              />
            </div>

            <div>
              <Label htmlFor="edit-fee">Teilnahmegebühr (€)</Label>
              <Input
                id="edit-fee"
                type="number"
                step="0.01"
                value={formData.fee || ''}
                onChange={(e) => setFormData({ ...formData, fee: e.target.value ? parseFloat(e.target.value) : undefined })}
                placeholder="Kostenlos wenn leer"
              />
            </div>

            <div className="col-span-2">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="edit-registrationRequired"
                  checked={formData.registrationRequired}
                  onChange={(e) => setFormData({ ...formData, registrationRequired: e.target.checked })}
                />
                <Label htmlFor="edit-registrationRequired">Anmeldung erforderlich</Label>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Abbrechen
            </Button>
            <Button onClick={handleEditEvent}>
              Änderungen speichern
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}