import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useClub } from "@/contexts/ClubContext";
import { getTimezoneOptions, timezoneService } from '@/services/TimezoneService';
import { Loader2, Clock } from "lucide-react";

// 🎯 REPLACED WITH CENTRALIZED SERVICE: TIMEZONE_OPTIONS now comes from TimezoneService

export const TimezoneSettings = () => {
  const [timezone, setTimezone] = useState<string>("");
  const [currentTime, setCurrentTime] = useState<string>("");
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const { currentClubId } = useClub();
  const { toast } = useToast();

  useEffect(() => {
    if (currentClubId) {
      loadTimezone();
    }
  }, [currentClubId]);

  // Update current time every second using centralized service
  useEffect(() => {
    const updateTime = () => {
      if (timezone) {
        const timeString = timezoneService.formatCurrentTimeInTimezone(timezone);
        setCurrentTime(timeString);
      }
    };

    updateTime();
    const interval = setInterval(updateTime, 1000);
    return () => clearInterval(interval);
  }, [timezone]);

  const loadTimezone = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from("clubs")
        .select("timezone")
        .eq("id", currentClubId)
        .single();

      if (error) throw error;

      setTimezone(data?.timezone || timezoneService.getSystemDefaultTimezone());
    } catch (error) {
      console.error("Error loading timezone:", error);
      toast({
        title: "Fehler",
        description: "Zeitzone konnte nicht geladen werden",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const saveTimezone = async () => {
    if (!currentClubId || !timezone) return;

    try {
      setSaving(true);
      const { error } = await supabase
        .from("clubs")
        .update({ timezone })
        .eq("id", currentClubId);

      if (error) throw error;

      toast({
        title: "Zeitzone gespeichert",
        description: `Vereinszeitzone wurde auf ${timezone} gesetzt`
      });
    } catch (error) {
      console.error("Error saving timezone:", error);
      toast({
        title: "Fehler",
        description: "Zeitzone konnte nicht gespeichert werden",
        variant: "destructive"
      });
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Zeitzone
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5" />
          Zeitzone
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="timezone">Vereinszeitzone</Label>
          <Select value={timezone} onValueChange={setTimezone}>
            <SelectTrigger>
              <SelectValue placeholder="Zeitzone auswählen" />
            </SelectTrigger>
            <SelectContent>
              {getTimezoneOptions().map((tz) => (
                <SelectItem key={tz.value} value={tz.value}>
                  {tz.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <p className="text-sm text-muted-foreground">
            Diese Zeitzone wird für alle Buchungen und Termine im Verein verwendet.
          </p>
        </div>

        {timezone && (
          <div className="p-4 bg-muted rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Clock className="h-4 w-4" />
              <span className="font-medium">Aktuelle Zeit in {timezone}:</span>
            </div>
            <div className="text-lg font-mono">{currentTime}</div>
          </div>
        )}

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="font-medium text-blue-900 mb-2">Wichtiger Hinweis</h4>
          <p className="text-sm text-blue-800">
            Die Vereinszeitzone bestimmt, wie alle Zeiten im System interpretiert werden. 
            Buchungen, Turniere und andere zeitbezogene Aktivitäten verwenden diese Zeitzone 
            als Referenz. Eine Änderung wirkt sich auf alle zukünftigen Zeitberechnungen aus.
          </p>
        </div>

        <Button 
          onClick={saveTimezone} 
          disabled={saving || !timezone}
          className="w-full"
        >
          {saving ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Speichern...
            </>
          ) : (
            "Zeitzone speichern"
          )}
        </Button>
      </CardContent>
    </Card>
  );
};