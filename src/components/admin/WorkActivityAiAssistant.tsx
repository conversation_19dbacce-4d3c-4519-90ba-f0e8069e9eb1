import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Sparkles, Loader2, Plus, Trash2, Wand2 } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";

interface Activity {
  name: string;
  description?: string;
  hourly_rate: number;
}

interface WorkActivityAiAssistantProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onActivitiesCreated: () => void;
}

const WorkActivityAiAssistant = ({ isOpen, onOpenChange, onActivitiesCreated }: WorkActivityAiAssistantProps) => {
  const [mode, setMode] = useState<string>("");
  const [input, setInput] = useState("");
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState(1);
  const [suggestedActivities, setSuggestedActivities] = useState<Activity[]>([]);
  const { toast } = useToast();

  const modes = [
    {
      value: "suggest",
      label: "Tätigkeiten vorschlagen",
      description: "Lasse dir typische Arbeitsdienste für Tennisvereine vorschlagen",
      placeholder: "z.B. Platzpflege, Turnierorganisation, Winterarbeiten..."
    },
    {
      value: "import",
      label: "Liste importieren",
      description: "Füge eine Liste aus Excel/Word ein und wandle sie in Tätigkeiten um",
      placeholder: "Füge hier deine Liste ein:\n1. Platz harken - 2h\n2. Linien erneuern - 3h\n..."
    },
    {
      value: "analyze",
      label: "Beschreibung analysieren",
      description: "Analysiere eine einzelne Tätigkeitsbeschreibung",
      placeholder: "z.B. Jeden Samstag die Tennisplätze harken und Linien kontrollieren"
    },
    {
      value: "seasonal",
      label: "Saisonale Empfehlungen",
      description: "Erhalte Vorschläge für saisonale Arbeitsdienste",
      placeholder: "z.B. Frühjahr, Sommer, Herbst oder Winter"
    }
  ];

  const currentModeInfo = modes.find(m => m.value === mode);

  const handleAnalyze = async () => {
    if (!mode || !input.trim()) {
      toast({
        title: "Eingabe fehlt",
        description: "Bitte wähle einen Modus und gib eine Beschreibung ein.",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      const { data, error } = await supabase.functions.invoke('parse-work-activities', {
        body: { mode, input: input.trim() }
      });

      if (error) throw error;

      if (!data.success) {
        throw new Error(data.error || 'KI-Analyse fehlgeschlagen');
      }

      setSuggestedActivities(data.activities);
      setStep(2);

      toast({
        title: "Analyse erfolgreich",
        description: `${data.activities.length} Tätigkeiten wurden erkannt.`,
      });
    } catch (error) {
      console.error('Error analyzing activities:', error);
      toast({
        title: "Analyse fehlgeschlagen",
        description: error instanceof Error ? error.message : 'Unbekannter Fehler',
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSaveActivities = async () => {
    if (suggestedActivities.length === 0) return;

    setLoading(true);
    try {
      // Get current user's club_id
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Nicht angemeldet');

      const { data: userMembership } = await supabase
        .from("club_memberships")
        .select("club_id")
        .eq("user_id", user.id)
        .eq("is_active", true)
        .single();

      if (!userMembership?.club_id) throw new Error('Kein aktives Vereinsmitglied');

      // Save activities
      const activitiesToSave = suggestedActivities.map(activity => ({
        name: activity.name,
        description: activity.description || null,
        hourly_rate: activity.hourly_rate,
        club_id: userMembership.club_id,
        created_by: user.id,
        status: 'approved'
      }));

      const { error } = await supabase
        .from('activities')
        .insert(activitiesToSave);

      if (error) throw error;

      toast({
        title: "Tätigkeiten erstellt",
        description: `${suggestedActivities.length} Tätigkeiten wurden erfolgreich erstellt.`,
      });

      onActivitiesCreated();
      handleClose();
    } catch (error) {
      console.error('Error saving activities:', error);
      toast({
        title: "Speichern fehlgeschlagen",
        description: error instanceof Error ? error.message : 'Unbekannter Fehler',
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEditActivity = (index: number, field: keyof Activity, value: string | number) => {
    const updated = [...suggestedActivities];
    updated[index] = { ...updated[index], [field]: value };
    setSuggestedActivities(updated);
  };

  const handleRemoveActivity = (index: number) => {
    setSuggestedActivities(prev => prev.filter((_, i) => i !== index));
  };

  const handleClose = () => {
    setMode("");
    setInput("");
    setStep(1);
    setSuggestedActivities([]);
    onOpenChange(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto border border-purple-500/20 bg-gradient-to-br from-background via-background to-purple-50/50 dark:to-purple-950/20">
        <DialogHeader className="space-y-4">
          <div className="flex items-center gap-3">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full blur-md opacity-30"></div>
              <div className="relative bg-gradient-to-r from-purple-500 to-blue-500 p-3 rounded-full">
                <Wand2 className="h-6 w-6 text-white" />
              </div>
            </div>
            <div>
              <DialogTitle className="text-xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                KI-Assistent für Arbeitsdienste
              </DialogTitle>
              <DialogDescription className="text-sm text-muted-foreground mt-1">
                {step === 1 
                  ? "Lasse dir dabei helfen, Arbeitsdienst-Tätigkeiten für deinen Tennisverein zu erstellen."
                  : "Überprüfe und bearbeite die vorgeschlagenen Tätigkeiten vor dem Speichern."
                }
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        {step === 1 && (
          <div className="space-y-6">
            <div className="space-y-3">
              <Label htmlFor="mode" className="text-sm font-medium">Modus wählen</Label>
              <Select value={mode} onValueChange={setMode}>
                <SelectTrigger className="border-purple-500/20 focus:border-purple-500/40 focus:ring-purple-500/20 data-[state=open]:border-purple-500/40">
                  <SelectValue placeholder="Wähle einen Modus..." />
                </SelectTrigger>
                <SelectContent className="bg-background border-purple-500/20 shadow-xl shadow-purple-500/10">
                  {modes.map((modeOption) => (
                    <SelectItem 
                      key={modeOption.value} 
                      value={modeOption.value}
                      className="focus:bg-purple-500/10 focus:text-purple-700 dark:focus:text-purple-300"
                    >
                      {modeOption.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {currentModeInfo && (
                <p className="text-sm text-muted-foreground">
                  {currentModeInfo.description}
                </p>
              )}
            </div>

            {mode && (
              <div className="space-y-3">
                <Label htmlFor="input" className="text-sm font-medium">Eingabe</Label>
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-blue-500/10 rounded-lg blur-sm"></div>
                  <Textarea
                    id="input"
                    placeholder={currentModeInfo?.placeholder}
                    value={input}
                    onChange={(e) => setInput(e.target.value)}
                    rows={mode === "import" ? 8 : 4}
                    className="relative bg-background/80 backdrop-blur-sm border-purple-500/20 focus:border-purple-500/40 focus:ring-purple-500/20"
                  />
                </div>
              </div>
            )}
          </div>
        )}

        {step === 2 && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Vorgeschlagene Tätigkeiten</h3>
              <Badge variant="secondary">
                {suggestedActivities.length} Tätigkeiten
              </Badge>
            </div>
            
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {suggestedActivities.map((activity, index) => (
                <Card key={index}>
                  <CardContent className="pt-4">
                    <div className="grid gap-3">
                      <div className="flex items-center justify-between">
                        <div className="grid gap-2 flex-1">
                          <div className="grid grid-cols-3 gap-2">
                            <div>
                              <Label className="text-xs">Name</Label>
                              <Input
                                value={activity.name}
                                onChange={(e) => handleEditActivity(index, 'name', e.target.value)}
                                className="h-8 border-purple-500/20 focus:border-purple-500/40 focus:ring-purple-500/20"
                              />
                            </div>
                            <div>
                              <Label className="text-xs">Stunden</Label>
                              <Input
                                type="number"
                                step="0.5"
                                min="0"
                                value={activity.hourly_rate}
                                onChange={(e) => handleEditActivity(index, 'hourly_rate', parseFloat(e.target.value) || 0)}
                                className="h-8 border-purple-500/20 focus:border-purple-500/40 focus:ring-purple-500/20"
                              />
                            </div>
                            <div className="flex items-end">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleRemoveActivity(index)}
                                className="h-8"
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                          {activity.description && (
                            <div>
                              <Label className="text-xs">Beschreibung</Label>
                              <Textarea
                                value={activity.description}
                                onChange={(e) => handleEditActivity(index, 'description', e.target.value)}
                                rows={2}
                                className="text-sm border-purple-500/20 focus:border-purple-500/40 focus:ring-purple-500/20"
                              />
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        <DialogFooter>
          {step === 1 && (
            <>
              <Button variant="outline" onClick={handleClose}>
                Abbrechen
              </Button>
              <Button 
                onClick={handleAnalyze} 
                disabled={loading || !mode || !input.trim()}
                className="relative overflow-hidden bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-600/90 hover:to-blue-600/90 text-white border-0 shadow-lg transition-all duration-300 hover:shadow-xl hover:shadow-purple-500/25"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 translate-x-[-100%] hover:translate-x-[100%] transition-transform duration-700 ease-in-out"></div>
                <div className="relative flex items-center gap-2">
                  {loading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Sparkles className="h-4 w-4" />
                  )}
                  <span className="font-medium">
                    {loading ? "Analysiert..." : "Analysieren"}
                  </span>
                </div>
              </Button>
            </>
          )}
          
          {step === 2 && (
            <>
              <Button variant="outline" onClick={() => setStep(1)}>
                Zurück
              </Button>
              <Button onClick={handleSaveActivities} disabled={loading || suggestedActivities.length === 0}>
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                <Plus className="mr-2 h-4 w-4" />
                {suggestedActivities.length} Tätigkeiten erstellen
              </Button>
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default WorkActivityAiAssistant;