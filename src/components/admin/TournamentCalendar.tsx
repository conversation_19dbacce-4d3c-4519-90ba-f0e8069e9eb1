import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Calendar, Clock, MapPin, Users, Play, RotateCcw, Settings, Info, RefreshCw, X, CheckCircle, ChevronLeft, ChevronRight } from 'lucide-react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useTournamentFormatConfig } from '@/hooks/useTournamentFormatConfig';
import { tournamentFormatEngine } from '@/lib/tournament/TournamentFormatEngine';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface TournamentMatch {
  id: string;
  tournament_id: string;
  round_number: number;
  match_number: number;
  participant1_id: string;
  participant2_id: string;
  status: 'scheduled' | 'in_progress' | 'completed' | 'postponed';
  scheduled_date?: string;
  scheduled_time?: string;
  court_id?: string;
  booking_id?: string;
  estimated_duration_minutes?: number;
  participant1?: { id: string; user_name: string };
  participant2?: { id: string; user_name: string };
}

interface Court {
  id: string;
  number: number;
  surface_type?: string;
}

interface TournamentCalendarProps {
  tournamentId: string;
  tournamentName: string;
}

interface Tournament {
  id: string;
  name: string;
  format_type: string;
  tournament_start: string;
  tournament_end: string;
}

interface TimelineMatch {
  match: TournamentMatch;
  startMinutes: number;
  durationMinutes: number;
  endMinutes: number;
}

export function TournamentCalendar({ tournamentId, tournamentName }: TournamentCalendarProps) {
  const [selectedMatch, setSelectedMatch] = useState<TournamentMatch | null>(null);
  const [isScheduleDialogOpen, setIsScheduleDialogOpen] = useState(false);
  const [scheduleForm, setScheduleForm] = useState({
    date: '',
    time: '',
    courtId: ''
  });
  const [selectedDate, setSelectedDate] = useState(new Date());
  const queryClient = useQueryClient();

  // Timeline constants
  const START_HOUR = 6;
  const END_HOUR = 22;
  const PIXELS_PER_MINUTE = 2;
  const TIMELINE_HEIGHT = (END_HOUR - START_HOUR) * 60 * PIXELS_PER_MINUTE;

  // Helper function to parse player names
  const parsePlayerName = (fullName: string) => {
    if (!fullName || fullName === 'TBD') return { firstName: fullName || 'TBD', lastName: '' };
    
    const parts = fullName.trim().split(' ');
    if (parts.length === 1) {
      return { firstName: parts[0], lastName: '' };
    }
    
    const firstName = parts[0];
    const lastName = parts.slice(1).join(' ');
    return { firstName, lastName };
  };

  // Helper function to convert time string to minutes from START_HOUR
  const timeToMinutes = (timeString: string): number => {
    const [hours, minutes] = timeString.split(':').map(Number);
    const totalMinutes = hours * 60 + minutes;
    return totalMinutes - (START_HOUR * 60);
  };

  // Helper function to convert minutes to time string
  const minutesToTime = (minutes: number): string => {
    const totalMinutes = minutes + (START_HOUR * 60);
    const hours = Math.floor(totalMinutes / 60);
    const mins = totalMinutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  };

  // Fetch tournament data
  const { data: tournament } = useQuery({
    queryKey: ['tournament', tournamentId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('tournaments')
        .select('id, name, format_type, tournament_start, tournament_end')
        .eq('id', tournamentId)
        .single();
      
      if (error) throw error;
      return data as Tournament;
    }
  });

  // Update selected date when tournament loads
  useEffect(() => {
    if (tournament?.tournament_start) {
      setSelectedDate(new Date(tournament.tournament_start));
    }
  }, [tournament?.tournament_start]);

  // Generate tournament days for tabs
  const tournamentDays = useMemo(() => {
    if (!tournament) return [];
    
    const days = [];
    const startDate = new Date(tournament.tournament_start);
    const endDate = new Date(tournament.tournament_end);
    
    for (let date = new Date(startDate); date <= endDate; date.setDate(date.getDate() + 1)) {
      days.push(new Date(date));
    }
    
    return days;
  }, [tournament?.tournament_start, tournament?.tournament_end]);

  // Get format configuration for dynamic time slots
  const { getFormatConfig } = useTournamentFormatConfig(tournamentId, tournament?.format_type);
  
  // Calculate dynamic match duration based on format config
  const getMatchDuration = useMemo(() => {
    if (!tournament?.format_type) return 90; // Default fallback
    
    // Get format-specific config (tournament > club > default)
    const formatConfig = getFormatConfig(tournament.format_type, tournamentId);
    
    if (formatConfig?.time_config) {
      const timeConfig = formatConfig.time_config;
      return (timeConfig.match_duration_minutes || 90) + 
             (timeConfig.between_match_buffer_minutes || 10);
    }
    
    // Fallback to engine defaults
    const availableFormats = tournamentFormatEngine.getAvailableFormats();
    const defaultFormat = availableFormats.find(f => f.type === tournament.format_type);
    if (defaultFormat?.default_time_config) {
      const timeConfig = defaultFormat.default_time_config;
      return timeConfig.match_duration_minutes + timeConfig.between_match_buffer_minutes;
    }
    
    return 90; // Ultimate fallback
  }, [tournament?.format_type, getFormatConfig, tournamentId]);

  // Fetch tournament matches with participant data
  const { data: rawMatches, isLoading: matchesLoading } = useQuery({
    queryKey: ['tournament-matches', tournamentId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('tournament_matches')
        .select('*')
        .eq('tournament_id', tournamentId)
        .order('round_number', { ascending: true })
        .order('match_number', { ascending: true });
      
      if (error) throw error;
      return data;
    }
  });

  // Fetch participant data with proper names
  const { data: participants } = useQuery({
    queryKey: ['tournament-participants', tournamentId],
    queryFn: async () => {
      // First get tournament participants
      const { data: participantData, error: participantError } = await supabase
        .from('tournament_participants')
        .select('id, user_id, guest_name, is_guest')
        .eq('tournament_id', tournamentId);
      
      if (participantError) throw participantError;
      if (!participantData) return [];
      
      // Get user names from club_memberships for non-guests
      const userIds = participantData.filter(p => !p.is_guest && p.user_id).map(p => p.user_id);
      let userNames: Record<string, string> = {};
      
      if (userIds.length > 0) {
        const { data: memberData, error: memberError } = await supabase
          .from('club_memberships')
          .select('user_id, first_name, last_name')
          .in('user_id', userIds)
          .eq('is_active', true);
        
        if (!memberError && memberData) {
          userNames = memberData.reduce((acc, member) => {
            acc[member.user_id] = `${member.first_name || ''} ${member.last_name || ''}`.trim() || 'Unbekannt';
            return acc;
          }, {} as Record<string, string>);
        }
      }
      
      // Map to proper user names
      return participantData.map(p => ({
        id: p.id,
        user_name: p.is_guest 
          ? (p.guest_name || 'Gast')
          : (userNames[p.user_id] || 'Unbekannt')
      }));
    }
  });

  // Combine matches with participant data
  const matches = rawMatches?.map(match => ({
    ...match,
    participant1: participants?.find(p => p.id === match.participant1_id),
    participant2: participants?.find(p => p.id === match.participant2_id)
  })) as TournamentMatch[];

  // Fetch tournament courts only
  const { data: courts } = useQuery({
    queryKey: ['tournament-courts', tournamentId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('tournament_courts')
        .select(`
          court:courts(id, number, surface_type, locked, lock_reason)
        `)
        .eq('tournament_id', tournamentId);
      
      if (error) throw error;
      
      // Filter out locked courts and flatten structure
      const availableCourts = data
        ?.map(tc => tc.court)
        .filter(court => court && !court.locked)
        .sort((a, b) => a.number - b.number) || [];
      
      return availableCourts as Court[];
    }
  });

  // Process matches for timeline display
  const timelineMatches = useMemo(() => {
    if (!matches) return [];
    
    const selectedDateString = selectedDate.toISOString().split('T')[0];
    
    return matches
      .filter(match => 
        match.scheduled_date === selectedDateString && 
        match.scheduled_time && 
        match.court_id
      )
      .map(match => {
        const timeString = match.scheduled_time!.substring(0, 5);
        const startMinutes = timeToMinutes(timeString);
        
        // ALWAYS use the calculated duration (100 min) to ensure correct display
        const durationMinutes = getMatchDuration;
        
        // Debug log to verify values
        console.log(`Match ${match.id}: stored=${match.estimated_duration_minutes}, calculated=${getMatchDuration}, using=${durationMinutes}`);
        console.log(`Timeline: start=${timeString} (${startMinutes}min), duration=${durationMinutes}min, end=${minutesToTime(startMinutes + durationMinutes)}`);
        
        const endMinutes = startMinutes + durationMinutes;
        
        return {
          match,
          startMinutes,
          durationMinutes,
          endMinutes
        } as TimelineMatch;
      });
  }, [matches, selectedDate, getMatchDuration]);

  // Check for conflicts between matches
  const checkMatchConflicts = useCallback(
    (courtId: string, startMinutes: number, endMinutes: number, excludeMatchId?: string): TournamentMatch[] => {
      const conflicts = timelineMatches
        .filter(tm => {
          if (tm.match.court_id !== courtId || tm.match.id === excludeMatchId) {
            return false;
          }
          
          // CRITICAL FIX: Proper overlap detection
          const hasOverlap = !(endMinutes <= tm.startMinutes || startMinutes >= tm.endMinutes);
          
          if (hasOverlap) {
            console.warn(`OVERLAP DETECTED: Match ${tm.match.id} (${tm.startMinutes}-${tm.endMinutes}) conflicts with new slot (${startMinutes}-${endMinutes}) on court ${courtId}`);
          }
          
          return hasOverlap;
        })
        .map(tm => tm.match);
        
      return conflicts;
    },
    [timelineMatches]
  );

  // Generate available time slots for scheduling
  const availableTimeSlots = useMemo(() => {
    const slots = [];
    
    // Generate slots every 5 minutes from 6:00 to 22:00
    for (let hour = START_HOUR; hour < END_HOUR; hour++) {
      for (let minute = 0; minute < 60; minute += 5) {
        const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
        const startMinutes = timeToMinutes(timeString);
        const endMinutes = startMinutes + getMatchDuration;
        
        // Check if match would end before 22:00
        if (endMinutes <= timeToMinutes('22:00')) {
          const endHour = Math.floor((endMinutes + START_HOUR * 60) / 60);
          const endMin = (endMinutes + START_HOUR * 60) % 60;
          const endTimeString = `${endHour.toString().padStart(2, '0')}:${endMin.toString().padStart(2, '0')}`;
          
          slots.push({
            value: timeString,
            label: `${timeString} - ${endTimeString} (${getMatchDuration} Min)`
          });
        }
      }
    }
    
    return slots;
  }, [getMatchDuration]);

  // Update match schedule mutation
  const updateScheduleMutation = useMutation({
    mutationFn: async ({ matchId, updates }: { matchId: string; updates: Partial<TournamentMatch> }) => {
      const { data, error } = await supabase
        .from('tournament_matches')
        .update(updates)
        .eq('id', matchId)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tournament-matches', tournamentId] });
      toast.success('Spiel erfolgreich geplant');
      setIsScheduleDialogOpen(false);
      setSelectedMatch(null);
      setScheduleForm({ date: '', time: '', courtId: '' });
    },
    onError: (error) => {
      toast.error(`Fehler beim Planen: ${error.message}`);
    }
  });

  // Sync match to booking system mutation
  const syncMatchMutation = useMutation({
    mutationFn: async ({ matchId, action }: { matchId: string; action: 'sync_match' | 'unsync_match' }) => {
      const { data, error } = await supabase.functions.invoke('tournament-sync', {
        body: { action, match_id: matchId }
      });

      if (error) throw error;
      return data;
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['tournament-matches', tournamentId] });
      const message = variables.action === 'sync_match' 
        ? 'Spiel erfolgreich mit Buchungssystem synchronisiert'
        : 'Synchronisation mit Buchungssystem aufgehoben';
      toast.success(message);
    },
    onError: (error) => {
      toast.error(`Sync-Fehler: ${error.message}`);
    }
  });

  const handleScheduleMatch = async () => {
    if (!selectedMatch || !scheduleForm.date || !scheduleForm.time || !scheduleForm.courtId) {
      toast.error('Bitte alle Felder ausfüllen');
      return;
    }

    // Validate date is within tournament bounds
    if (tournament && scheduleForm.date) {
      const selectedDate = new Date(scheduleForm.date);
      const startDate = new Date(tournament.tournament_start);
      const endDate = new Date(tournament.tournament_end);
      
      if (selectedDate < startDate || selectedDate > endDate) {
        toast.error(`Datum muss zwischen ${startDate.toLocaleDateString('de-DE')} und ${endDate.toLocaleDateString('de-DE')} liegen`);
        return;
      }
    }

    // Check for scheduling conflicts
    const startMinutes = timeToMinutes(scheduleForm.time);
    const endMinutes = startMinutes + getMatchDuration;
    const conflicts = checkMatchConflicts(scheduleForm.courtId, startMinutes, endMinutes, selectedMatch.id);
    
    if (conflicts.length > 0) {
      const conflictDetails = conflicts.map(c => 
        `Runde ${c.round_number}, Spiel ${c.match_number} (${c.scheduled_time})`
      ).join('\n');
      toast.error(`Zeitkonflikt erkannt:\n${conflictDetails}`);
      return;
    }

    // Check against regular bookings
    try {
      const { data: bookings } = await supabase
        .from('bookings')
        .select('id, player_name, start_time, end_time')
        .eq('court_id', scheduleForm.courtId)
        .eq('booking_date', scheduleForm.date);
      
      const bookingConflicts = bookings?.filter(booking => {
        const [bookingStartHours, bookingStartMins] = booking.start_time.split(':').map(Number);
        const [bookingEndHours, bookingEndMins] = booking.end_time.split(':').map(Number);
        const bookingStartMinutes = bookingStartHours * 60 + bookingStartMins;
        const bookingEndMinutes = bookingEndHours * 60 + bookingEndMins;
        
        const matchStartMinutes = startMinutes + START_HOUR * 60;
        const matchEndMinutes = endMinutes + START_HOUR * 60;
        
        // Check for overlap
        return !(matchEndMinutes <= bookingStartMinutes || matchStartMinutes >= bookingEndMinutes);
      }) || [];
      
      if (bookingConflicts.length > 0) {
        const conflictDetails = bookingConflicts.map(b => 
          `${b.player_name} (${b.start_time} - ${b.end_time})`
        ).join('\n');
        toast.error(`Buchungskonflikt erkannt:\n${conflictDetails}`);
        return;
      }
    } catch (error) {
      console.error('Error checking booking conflicts:', error);
    }

    updateScheduleMutation.mutate({
      matchId: selectedMatch.id,
      updates: {
        scheduled_date: scheduleForm.date,
        scheduled_time: scheduleForm.time,
        court_id: scheduleForm.courtId,
        estimated_duration_minutes: getMatchDuration
      }
    });
  };

  const openScheduleDialog = (match: TournamentMatch) => {
    setSelectedMatch(match);
    setScheduleForm({
      date: match.scheduled_date || selectedDate.toISOString().split('T')[0],
      time: match.scheduled_time || '',
      courtId: match.court_id || ''
    });
    setIsScheduleDialogOpen(true);
  };

  const handleSyncMatch = (matchId: string, isSynced: boolean) => {
    const action = isSynced ? 'unsync_match' : 'sync_match';
    syncMatchMutation.mutate({ matchId, action });
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      'scheduled': 'secondary' as const,
      'in_progress': 'default' as const,
      'completed': 'outline' as const,
      'postponed': 'destructive' as const
    };
    
    const labels = {
      'scheduled': 'Geplant',
      'in_progress': 'Läuft',
      'completed': 'Beendet',
      'postponed': 'Verschoben'
    };

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'secondary'}>
        {labels[status as keyof typeof labels] || status}
      </Badge>
    );
  };

  const getMatchTitle = (match: TournamentMatch) => {
    const p1 = match.participant1?.user_name || 'TBD';
    const p2 = match.participant2?.user_name || 'TBD';
    return `${p1} vs ${p2}`;
  };

  // Handle empty area clicks for scheduling
  const handleTimelineClick = (courtId: string, clickY: number) => {
    const clickMinutes = Math.floor(clickY / PIXELS_PER_MINUTE);
    const clickTime = minutesToTime(clickMinutes);
    
    // Find the next unscheduled match
    const unscheduledMatch = matches?.find(m => !m.scheduled_date || !m.scheduled_time);
    
    if (unscheduledMatch) {
      setSelectedMatch(unscheduledMatch);
      setScheduleForm({
        date: selectedDate.toISOString().split('T')[0],
        time: clickTime,
        courtId
      });
      setIsScheduleDialogOpen(true);
    } else {
      toast.info('Alle Spiele sind bereits geplant');
    }
  };

  if (matchesLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-6 bg-muted rounded w-1/3"></div>
            <div className="space-y-2">
              {[1, 2, 3].map(i => (
                <div key={i} className="h-16 bg-muted rounded"></div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!courts || courts.length === 0) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <MapPin className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">Keine Turnierplätze</h3>
          <p className="text-muted-foreground">
            Bitte zuerst Plätze für das Turnier konfigurieren.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <TooltipProvider>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold tracking-tight">{tournamentName}</h2>
            <p className="text-muted-foreground">
              Turnierkalender - Spiele planen und verwalten
            </p>
          </div>
          
          {/* Tournament Days Tabs */}
          {tournamentDays.length > 0 && (
            <Tabs 
              value={selectedDate.toISOString().split('T')[0]} 
              onValueChange={(dateString) => setSelectedDate(new Date(dateString))}
              className="w-full max-w-md"
            >
              <TabsList className="grid w-full" style={{ gridTemplateColumns: `repeat(${tournamentDays.length}, 1fr)` }}>
                {tournamentDays.map((day, index) => (
                  <TabsTrigger 
                    key={day.toISOString().split('T')[0]} 
                    value={day.toISOString().split('T')[0]}
                    className="text-xs px-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                  >
                    <div className="text-center">
                      <div className="font-medium">Tag {index + 1}</div>
                      <div className="text-xs opacity-80">
                        {day.toLocaleDateString('de-DE', { day: '2-digit', month: '2-digit' })}
                      </div>
                    </div>
                  </TabsTrigger>
                ))}
              </TabsList>
            </Tabs>
          )}
        </div>

        {/* Selected Date Display */}
        <div className="text-center mb-4">
          <div className="font-semibold text-lg">
            {selectedDate.toLocaleDateString('de-DE', { 
              weekday: 'long', 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric' 
            })}
          </div>
          {tournament && (
            <div className="text-sm text-muted-foreground">
              Turnier: {new Date(tournament.tournament_start).toLocaleDateString('de-DE')} - {new Date(tournament.tournament_end).toLocaleDateString('de-DE')}
            </div>
          )}
        </div>

        {/* Timeline Calendar */}
        <Card>
          <CardContent className="p-6">
            <div className="overflow-x-auto">
              <div className="min-w-[800px]">
                {/* Timeline Header */}
                <div className="flex sticky top-0 z-10 bg-background mb-4">
                  <div className="w-16 text-center font-semibold bg-background border rounded p-2">Zeit</div>
                  <div className="flex-1 text-center bg-muted rounded p-2 ml-2">
                    <div className="font-semibold text-sm mb-2">Turnierplätze</div>
                    <div className="grid gap-2" style={{ gridTemplateColumns: `repeat(${courts.length}, 1fr)` }}>
                      {courts.map((court) => (
                        <div key={court.id} className="text-sm px-3 py-2 bg-background rounded font-medium border">
                          <div className="text-center">
                            <div>Platz {court.number}</div>
                            {court.surface_type && (
                              <div className="text-xs text-muted-foreground">{court.surface_type}</div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Timeline Content */}
                <div className="flex max-h-[600px] overflow-y-auto">
                  {/* Time axis */}
                  <div className="w-16 relative">
                    {Array.from({ length: END_HOUR - START_HOUR + 1 }, (_, i) => {
                      const hour = START_HOUR + i;
                      return (
                        <div
                          key={hour}
                          className="absolute text-xs font-medium text-muted-foreground"
                          style={{
                            top: `${i * 60 * PIXELS_PER_MINUTE}px`,
                            transform: 'translateY(-50%)'
                          }}
                        >
                          {hour.toString().padStart(2, '0')}:00
                        </div>
                      );
                    })}
                  </div>

                  {/* Courts timeline */}
                  <div className="flex-1 ml-2 relative">
                    <div className="grid gap-2 h-full" style={{ gridTemplateColumns: `repeat(${courts.length}, 1fr)` }}>
                      {courts.map((court) => (
                        <div
                          key={court.id}
                          className="relative bg-muted/30 border rounded cursor-pointer hover:bg-muted/50 transition-colors"
                          style={{ height: `${TIMELINE_HEIGHT}px` }}
                          onClick={(e) => {
                            const rect = e.currentTarget.getBoundingClientRect();
                            const clickY = e.clientY - rect.top;
                            handleTimelineClick(court.id, clickY);
                          }}
                        >
                          {/* Hour grid lines */}
                          {Array.from({ length: END_HOUR - START_HOUR }, (_, i) => (
                            <div
                              key={i}
                              className="absolute w-full border-t border-border/30"
                              style={{ top: `${(i + 1) * 60 * PIXELS_PER_MINUTE}px` }}
                            />
                          ))}

                          {/* Matches for this court */}
                          {timelineMatches
                            .filter(tm => tm.match.court_id === court.id)
                            .map(({ match, startMinutes, durationMinutes }) => {
                              const top = startMinutes * PIXELS_PER_MINUTE;
                              const height = durationMinutes * PIXELS_PER_MINUTE;
                              
                              let bgColor = 'bg-blue-100 border-blue-300 text-blue-900';
                              
                              switch (match.status) {
                                case 'in_progress':
                                  bgColor = 'bg-green-100 border-green-300 text-green-900';
                                  break;
                                case 'completed':
                                  bgColor = 'bg-gray-100 border-gray-300 text-gray-700';
                                  break;
                                case 'postponed':
                                  bgColor = 'bg-red-100 border-red-300 text-red-900';
                                  break;
                              }

                              return (
                                <Tooltip key={match.id}>
                                  <TooltipTrigger asChild>
                                     <div
                                       className={`absolute inset-x-0 rounded-lg border-l-4 shadow-sm cursor-pointer hover:shadow-md transition-all duration-200 p-3 ${bgColor}`}
                                       style={{
                                         top: `${top}px`,
                                         height: `${Math.max(height, 60)}px`
                                       }}
                                       onClick={(e) => {
                                         e.stopPropagation();
                                         openScheduleDialog(match);
                                       }}
                                     >
                                       {/* Header with round info and time */}
                                       <div className="flex justify-between items-start mb-2">
                                         <div className="flex items-center gap-2">
                                           <Badge variant="secondary" className="text-xs px-2 py-0.5">
                                             Runde {match.round_number}
                                           </Badge>
                                           <span className="text-xs font-medium opacity-80">
                                             Spiel {match.match_number}
                                           </span>
                                         </div>
                                         <div className="text-right">
                                           <div className="text-xs font-bold">
                                             {match.scheduled_time?.substring(0, 5)}
                                           </div>
                                           {match.booking_id && (
                                             <CheckCircle className="h-3 w-3 text-green-600 ml-auto mt-0.5" />
                                           )}
                                         </div>
                                       </div>
                                       
                                       {/* Players with better typography */}
                                       <div className="flex-1 space-y-1">
                                         <div className="flex items-center justify-between">
                                           <div className="flex-1">
                                             <div className="font-semibold text-sm truncate">
                                               {parsePlayerName(match.participant1?.user_name || 'TBD').firstName}
                                             </div>
                                             <div className="text-xs opacity-70 truncate">
                                               {parsePlayerName(match.participant1?.user_name || 'TBD').lastName}
                                             </div>
                                           </div>
                                           <div className="mx-2 text-xs font-bold opacity-60">vs</div>
                                           <div className="flex-1 text-right">
                                             <div className="font-semibold text-sm truncate">
                                               {parsePlayerName(match.participant2?.user_name || 'TBD').firstName}
                                             </div>
                                             <div className="text-xs opacity-70 truncate">
                                               {parsePlayerName(match.participant2?.user_name || 'TBD').lastName}
                                             </div>
                                           </div>
                                         </div>
                                       </div>
                                       
                                       {/* Duration and end time */}
                                       <div className="flex justify-between items-center text-xs opacity-75 mt-2 pt-2 border-t border-current border-opacity-20">
                                         <span>{durationMinutes}min (angesetzt)</span>
                                         <span>bis {minutesToTime(startMinutes + durationMinutes)}</span>
                                       </div>
                                    </div>
                                  </TooltipTrigger>
                                   <TooltipContent side="right" className="max-w-xs">
                                     <div className="space-y-2 text-sm">
                                       <div className="font-semibold text-base">{getMatchTitle(match)}</div>
                                       
                                       <div className="grid grid-cols-2 gap-2 text-xs">
                                         <div>
                                           <span className="font-medium">Runde:</span> {match.round_number}
                                         </div>
                                          <div>
                                            <span className="font-medium">Spiel:</span> {match.match_number}
                                          </div>
                                          <div>
                                            <span className="font-medium">Status:</span> {match.status}
                                          </div>
                                          <div>
                                            <span className="font-medium">Dauer:</span> {durationMinutes} Min
                                          </div>
                                        </div>
                                        
                                        <div className="pt-2 border-t">
                                          <div className="text-xs">
                                            <span className="font-medium">Zeit:</span> {match.scheduled_time} - {minutesToTime(startMinutes + durationMinutes)}
                                          </div>
                                         {match.booking_id && (
                                           <div className="text-xs text-green-600 flex items-center gap-1 mt-1">
                                             <CheckCircle className="h-3 w-3" />
                                             Mit Buchungssystem synchronisiert
                                           </div>
                                         )}
                                       </div>
                                     </div>
                                  </TooltipContent>
                                </Tooltip>
                              );
                            })}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Schedule Dialog */}
        <Dialog open={isScheduleDialogOpen} onOpenChange={setIsScheduleDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Spiel planen</DialogTitle>
            </DialogHeader>
            
            {selectedMatch && (
              <div className="space-y-4">
                <div className="text-center p-4 bg-muted rounded-lg">
                  <h3 className="font-medium">{getMatchTitle(selectedMatch)}</h3>
                  <p className="text-sm text-muted-foreground">
                    Runde {selectedMatch.round_number}, Spiel #{selectedMatch.match_number}
                  </p>
                </div>

                <div className="grid gap-4">
                   <div>
                     <Label htmlFor="date">Datum</Label>
                     <Input
                       id="date"
                       type="date"
                       value={scheduleForm.date}
                       min={tournament?.tournament_start}
                       max={tournament?.tournament_end}
                       onChange={(e) => setScheduleForm(prev => ({ ...prev, date: e.target.value }))}
                     />
                     {tournament && (
                         <p className="text-xs text-muted-foreground mt-1">
                           Turnier-Zeitraum: {new Date(tournament.tournament_start).toLocaleDateString('de-DE')} - {new Date(tournament.tournament_end).toLocaleDateString('de-DE')}
                         </p>
                     )}
                   </div>

                   <div>
                     <Label htmlFor="time">Uhrzeit ({getMatchDuration} Min)</Label>
                     <Select 
                       value={scheduleForm.time} 
                       onValueChange={(value) => setScheduleForm(prev => ({ ...prev, time: value }))}
                     >
                       <SelectTrigger>
                         <SelectValue placeholder="Zeitslot auswählen" />
                       </SelectTrigger>
                       <SelectContent className="max-h-[200px]">
                         {availableTimeSlots.map((slot) => (
                           <SelectItem key={slot.value} value={slot.value}>
                             {slot.label}
                           </SelectItem>
                         ))}
                       </SelectContent>
                     </Select>
                     <p className="text-xs text-muted-foreground mt-1">
                       Verfügbare Zeiten: 6:00 - 22:00 (alle 5 Minuten)
                     </p>
                   </div>

                   <div>
                     <Label htmlFor="court">Turnierplatz</Label>
                     <Select 
                       value={scheduleForm.courtId} 
                       onValueChange={(value) => setScheduleForm(prev => ({ ...prev, courtId: value }))}
                     >
                       <SelectTrigger>
                         <SelectValue placeholder="Turnierplatz auswählen" />
                       </SelectTrigger>
                       <SelectContent>
                         {courts?.map((court) => (
                           <SelectItem key={court.id} value={court.id}>
                             Platz {court.number}
                             {court.surface_type && ` (${court.surface_type})`}
                           </SelectItem>
                         ))}
                       </SelectContent>
                     </Select>
                     <p className="text-xs text-muted-foreground mt-1">
                       Nur freigegebene Turnierplätze verfügbar ({courts?.length || 0} Plätze)
                     </p>
                   </div>
                </div>

                <div className="flex gap-2 pt-4">
                  <Button
                    variant="outline"
                    className="flex-1"
                    onClick={() => setIsScheduleDialogOpen(false)}
                  >
                    Abbrechen
                  </Button>
                  <Button
                    className="flex-1"
                    onClick={handleScheduleMatch}
                    disabled={updateScheduleMutation.isPending}
                  >
                    {updateScheduleMutation.isPending ? 'Speichern...' : 'Speichern'}
                  </Button>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </TooltipProvider>
  );
}