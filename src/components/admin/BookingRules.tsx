
import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Settings, Plus, Edit, Trash2, Clock, Users, Calendar, Sparkles, Loader2 } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { AIInterface } from "@/components/ui/ai-interface";

interface BookingRule {
  id: string;
  name: string;
  description: string;
  rule_type: string;
  conditions: any;
  actions: any;
  is_active: boolean;
  created_at: string;
  club_id: string;
}

type RuleType = 'advance_booking_limit' | 'max_duration' | 'min_duration' | 'max_bookings_per_user' | 'booking_window' | 'custom';

const BookingRules = () => {
  const [rules, setRules] = useState<BookingRule[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingRule, setEditingRule] = useState<BookingRule | null>(null);
  const [currentClubId, setCurrentClubId] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    rule_type: "advance_booking_limit" as RuleType,
    max_advance_days: 7,
    max_duration_hours: 2,
    min_duration_hours: 1,
    max_bookings_per_user: 3,
    booking_start_hour: 8,
    booking_end_hour: 22,
    custom_logic: "",
    is_active: true
  });
  const [aiDescription, setAiDescription] = useState("");
  const [isParsingRule, setIsParsingRule] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    getCurrentClub();
  }, []);

  useEffect(() => {
    if (currentClubId) {
      loadRules();
    }
  }, [currentClubId]);

  const getCurrentClub = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // Aktuellen Club des Admins ermitteln
      const { data: membership, error } = await supabase
        .from("club_memberships")
        .select("club_id")
        .eq("user_id", user.id)
        .eq("is_active", true)
        .in("role", ["admin", "super_admin"])
        .single();

      if (error) {
        console.error("Error getting current club:", error);
        return;
      }

      setCurrentClubId(membership?.club_id || null);
    } catch (error) {
      console.error("Error in getCurrentClub:", error);
    }
  };

  const loadRules = async () => {
    if (!currentClubId) return;

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from("booking_rules")
        .select("*")
        .eq("club_id", currentClubId)
        .order("created_at", { ascending: false });

      if (error) throw error;

      setRules(data || []);
    } catch (error) {
      console.error("Error loading booking rules:", error);
      toast({
        title: "Fehler",
        description: "Fehler beim Laden der Buchungsregeln",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSaveRule = async () => {
    if (!currentClubId) {
      toast({
        title: "Fehler",
        description: "Kein Club ausgewählt",
        variant: "destructive"
      });
      return;
    }

    try {
      const ruleData = {
        club_id: currentClubId,
        name: formData.name,
        description: formData.description,
        rule_type: formData.rule_type,
        conditions: getRuleConditions(),
        actions: getRuleActions(),
        is_active: formData.is_active,
        created_by: (await supabase.auth.getUser()).data.user?.id
      };

      if (editingRule) {
        // Update existing rule
        const { error } = await supabase
          .from("booking_rules")
          .update({
            name: formData.name,
            description: formData.description,
            rule_type: formData.rule_type,
            conditions: getRuleConditions(),
            actions: getRuleActions(),
            is_active: formData.is_active
          })
          .eq("id", editingRule.id);

        if (error) throw error;

        toast({
          title: "Erfolg",
          description: "Buchungsregel wurde aktualisiert"
        });
      } else {
        // Create new rule
        const { error } = await supabase
          .from("booking_rules")
          .insert(ruleData);

        if (error) throw error;

        toast({
          title: "Erfolg", 
          description: "Neue Buchungsregel wurde erstellt"
        });
      }

      resetForm();
      loadRules();
    } catch (error) {
      console.error("Error saving rule:", error);
      toast({
        title: "Fehler",
        description: "Fehler beim Speichern der Regel",
        variant: "destructive"
      });
    }
  };

  const getRuleConditions = () => {
    switch (formData.rule_type) {
      case "advance_booking_limit":
        return { max_days: formData.max_advance_days };
      case "max_duration":
        return { max_hours: formData.max_duration_hours };
      case "min_duration":
        return { min_hours: formData.min_duration_hours };
      case "max_bookings_per_user":
        return { max_count: formData.max_bookings_per_user };
      case "booking_window":
        return { 
          start_hour: formData.booking_start_hour,
          end_hour: formData.booking_end_hour
        };
      default:
        return {};
    }
  };

  const getRuleActions = () => {
    switch (formData.rule_type) {
      case "advance_booking_limit":
        return { block_booking: true, message: "Buchung zu weit in der Zukunft" };
      case "max_duration":
        return { limit_duration: true, max_hours: formData.max_duration_hours };
      case "min_duration":
        return { enforce_minimum: true, min_hours: formData.min_duration_hours };
      case "max_bookings_per_user":
        return { block_excess_bookings: true, max_count: formData.max_bookings_per_user };
      case "booking_window":
        return { 
          enforce_window: true,
          start_hour: formData.booking_start_hour,
          end_hour: formData.booking_end_hour
        };
      default:
        return {};
    }
  };

  const handleEditRule = (rule: BookingRule) => {
    setEditingRule(rule);
    setFormData({
      name: rule.name,
      description: rule.description || "",
      rule_type: rule.rule_type as RuleType,
      max_advance_days: rule.conditions?.max_days || 7,
      max_duration_hours: rule.conditions?.max_hours || 2,
      min_duration_hours: rule.conditions?.min_hours || 1,
      max_bookings_per_user: rule.conditions?.max_count || 3,
      booking_start_hour: rule.conditions?.start_hour || 8,
      booking_end_hour: rule.conditions?.end_hour || 22,
      custom_logic: "",
      is_active: rule.is_active
    });
  };

  const handleDeleteRule = async (rule: BookingRule) => {
    if (!confirm("Sind Sie sicher, dass Sie diese Regel löschen möchten?")) {
      return;
    }

    try {
      const { error } = await supabase
        .from("booking_rules")
        .delete()
        .eq("id", rule.id);

      if (error) throw error;

      toast({
        title: "Erfolg",
        description: "Buchungsregel wurde gelöscht"
      });
      loadRules();
    } catch (error) {
      console.error("Error deleting rule:", error);
      toast({
        title: "Fehler",
        description: "Fehler beim Löschen der Regel",
        variant: "destructive"
      });
    }
  };

  const toggleRuleStatus = async (rule: BookingRule) => {
    try {
      const { error } = await supabase
        .from("booking_rules")
        .update({ is_active: !rule.is_active })
        .eq("id", rule.id);

      if (error) throw error;

      toast({
        title: "Erfolg",
        description: `Regel wurde ${!rule.is_active ? 'aktiviert' : 'deaktiviert'}`
      });
      loadRules();
    } catch (error) {
      console.error("Error toggling rule status:", error);
      toast({
        title: "Fehler",
        description: "Fehler beim Ändern des Regel-Status",
        variant: "destructive"
      });
    }
  };

  const handleParseAiDescription = async () => {
    if (!aiDescription.trim()) {
      toast({
        title: "Fehler",
        description: "Bitte geben Sie eine Regelbeschreibung ein",
        variant: "destructive"
      });
      return;
    }

    setIsParsingRule(true);
    try {
      const { data, error } = await supabase.functions.invoke('parse-booking-rule', {
        body: { description: aiDescription }
      });

      if (error) throw error;

      if (data.success && data.rule) {
        const rule = data.rule;
        setFormData({
          name: rule.name,
          description: rule.description,
          rule_type: rule.rule_type,
          max_advance_days: rule.parameters.max_advance_days || 7,
          max_duration_hours: rule.parameters.max_duration_hours || 2,
          min_duration_hours: rule.parameters.min_duration_hours || 1,
          max_bookings_per_user: rule.parameters.max_bookings_per_user || 3,
          booking_start_hour: rule.parameters.booking_start_hour || 8,
          booking_end_hour: rule.parameters.booking_end_hour || 22,
          custom_logic: "",
          is_active: true
        });

        toast({
          title: "Erfolg",
          description: "KI hat die Regel erfolgreich interpretiert"
        });
        setAiDescription("");
      } else {
        throw new Error(data.error || "Unbekannter Fehler");
      }
    } catch (error) {
      console.error("Error parsing AI description:", error);
      toast({
        title: "Fehler",
        description: `KI konnte die Regel nicht interpretieren: ${error.message}`,
        variant: "destructive"
      });
    } finally {
      setIsParsingRule(false);
    }
  };

  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      rule_type: "advance_booking_limit" as RuleType,
      max_advance_days: 7,
      max_duration_hours: 2,
      min_duration_hours: 1,
      max_bookings_per_user: 3,
      booking_start_hour: 8,
      booking_end_hour: 22,
      custom_logic: "",
      is_active: true
    });
    setEditingRule(null);
    setAiDescription("");
  };

  const getRuleIcon = (ruleType: string) => {
    switch (ruleType) {
      case "advance_booking_limit":
        return <Calendar className="h-4 w-4" />;
      case "max_duration":
      case "min_duration":
      case "booking_window":
        return <Clock className="h-4 w-4" />;
      case "max_bookings_per_user":
        return <Users className="h-4 w-4" />;
      default:
        return <Settings className="h-4 w-4" />;
    }
  };

  const getRuleTypeLabel = (ruleType: string) => {
    switch (ruleType) {
      case "advance_booking_limit":
        return "Vorlaufzeit-Limit";
      case "max_duration":
        return "Max. Dauer";
      case "min_duration":
        return "Min. Dauer";
      case "max_bookings_per_user":
        return "Max. Buchungen/User";
      case "booking_window":
        return "Buchungsfenster";
      default:
        return "Benutzerdefiniert";
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!currentClubId) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">Kein Club gefunden. Sie müssen Admin eines Clubs sein.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Create/Edit Rule Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            {editingRule ? "Regel bearbeiten" : "Neue Regel erstellen"}
          </CardTitle>
          <CardDescription>
            {editingRule ? "Bearbeiten Sie die ausgewählte Buchungsregel" : "Erstellen Sie eine neue Buchungsregel mit KI-Unterstützung"}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* AI Rule Description */}
          <AIInterface
            title="KI-Regel Assistent"
            description="Beschreiben Sie die Regel in natürlicher Sprache und lassen Sie die KI sie interpretieren"
            placeholder="z.B. 'Mitglieder können maximal 2 Buchungen im Voraus buchen' oder 'Mitglieder können Einzelbuchungen nur zwischen 10:00 - 16:00 Uhr vornehmen'"
            prompt={aiDescription}
            onPromptChange={setAiDescription}
            onGenerate={handleParseAiDescription}
            isGenerating={isParsingRule}
            generateButtonText="Regel mit KI erstellen"
            generatingText="KI interpretiert..."
            features={["Natürliche Spracherkennung", "Buchungsregeln-optimiert"]}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="rule_name">Regelname</Label>
              <Input
                id="rule_name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="z.B. Vorlaufzeit für Mitglieder"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="rule_type">Regeltyp</Label>
              <Select 
                value={formData.rule_type} 
                onValueChange={(value: RuleType) => setFormData({ ...formData, rule_type: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="advance_booking_limit">Vorlaufzeit-Limit</SelectItem>
                  <SelectItem value="max_duration">Maximale Buchungsdauer</SelectItem>
                  <SelectItem value="min_duration">Minimale Buchungsdauer</SelectItem>
                  <SelectItem value="max_bookings_per_user">Max. Buchungen pro User</SelectItem>
                  <SelectItem value="booking_window">Buchungszeitfenster</SelectItem>
                  <SelectItem value="custom">Benutzerdefiniert</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Beschreibung</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              placeholder="Beschreiben Sie, was diese Regel bewirkt..."
            />
          </div>

          {/* Rule-specific configuration */}
          {formData.rule_type === "advance_booking_limit" && (
            <div className="space-y-2">
              <Label>Maximale Vorlaufzeit (Tage)</Label>
              <Input
                type="number"
                value={formData.max_advance_days}
                onChange={(e) => setFormData({ ...formData, max_advance_days: parseInt(e.target.value) })}
                min="1"
                max="365"
              />
            </div>
          )}

          {formData.rule_type === "max_duration" && (
            <div className="space-y-2">
              <Label>Maximale Buchungsdauer (Stunden)</Label>
              <Input
                type="number"
                value={formData.max_duration_hours}
                onChange={(e) => setFormData({ ...formData, max_duration_hours: parseInt(e.target.value) })}
                min="1"
                max="12"
                step="0.5"
              />
            </div>
          )}

          {formData.rule_type === "min_duration" && (
            <div className="space-y-2">
              <Label>Minimale Buchungsdauer (Stunden)</Label>
              <Input
                type="number"
                value={formData.min_duration_hours}
                onChange={(e) => setFormData({ ...formData, min_duration_hours: parseInt(e.target.value) })}
                min="0.5"
                max="4"
                step="0.5"
              />
            </div>
          )}

          {formData.rule_type === "max_bookings_per_user" && (
            <div className="space-y-2">
              <Label>Maximale Buchungen pro Benutzer</Label>
              <Input
                type="number"
                value={formData.max_bookings_per_user}
                onChange={(e) => setFormData({ ...formData, max_bookings_per_user: parseInt(e.target.value) })}
                min="1"
                max="20"
              />
            </div>
          )}

          {formData.rule_type === "booking_window" && (
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Buchungen möglich ab (Uhrzeit)</Label>
                <Input
                  type="number"
                  value={formData.booking_start_hour}
                  onChange={(e) => setFormData({ ...formData, booking_start_hour: parseInt(e.target.value) })}
                  min="0"
                  max="23"
                />
              </div>
              <div className="space-y-2">
                <Label>Buchungen möglich bis (Uhrzeit)</Label>
                <Input
                  type="number"
                  value={formData.booking_end_hour}
                  onChange={(e) => setFormData({ ...formData, booking_end_hour: parseInt(e.target.value) })}
                  min="1"
                  max="24"
                />
              </div>
            </div>
          )}

          <div className="flex items-center space-x-2">
            <Switch
              id="is_active"
              checked={formData.is_active}
              onCheckedChange={(checked) => setFormData({ ...formData, is_active: checked })}
            />
            <Label htmlFor="is_active">Regel ist aktiv</Label>
          </div>

          <div className="flex justify-end gap-2">
            {editingRule && (
              <Button variant="outline" onClick={resetForm}>
                Abbrechen
              </Button>
            )}
            <Button onClick={handleSaveRule} disabled={!formData.name.trim()}>
              {editingRule ? "Regel aktualisieren" : "Regel erstellen"}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Existing Rules List */}
      <Card>
        <CardHeader>
          <CardTitle>Aktuelle Buchungsregeln</CardTitle>
          <CardDescription>
            Übersicht aller konfigurierten Buchungsregeln für Ihren Club
          </CardDescription>
        </CardHeader>
        <CardContent>
          {rules.length === 0 ? (
            <div className="text-center text-muted-foreground py-8">
              <Settings className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>Noch keine Buchungsregeln konfiguriert</p>
              <p className="text-sm">Erstellen Sie Ihre erste Regel mit dem KI-Assistenten oben</p>
            </div>
          ) : (
            <div className="space-y-4">
              {rules.map((rule) => (
                <div
                  key={rule.id}
                  className="flex items-center justify-between p-4 border rounded-lg"
                >
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2">
                      {getRuleIcon(rule.rule_type)}
                      <div>
                        <div className="font-medium">{rule.name}</div>
                        <div className="text-sm text-muted-foreground">{rule.description}</div>
                      </div>
                    </div>
                    <Badge variant="outline">
                      {getRuleTypeLabel(rule.rule_type)}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Badge variant={rule.is_active ? "default" : "secondary"}>
                      {rule.is_active ? "Aktiv" : "Inaktiv"}
                    </Badge>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => toggleRuleStatus(rule)}
                    >
                      {rule.is_active ? "Deaktivieren" : "Aktivieren"}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEditRule(rule)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteRule(rule)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default BookingRules;
