import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Plus, Edit, Trash2, Users, UserCheck } from 'lucide-react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';

interface Participant {
  id: string;
  tournament_id: string;
  user_id?: string;
  user_name: string;
  user_email: string;
  is_guest: boolean;
  seed_number: number;
  status: 'registered' | 'confirmed' | 'checked_in' | 'withdrawn';
  registered_at: string;
}

interface ParticipantManagementProps {
  tournamentId: string;
  allowGuests: boolean;
  maxParticipants?: number;
}

export function ParticipantManagement({ 
  tournamentId, 
  allowGuests, 
  maxParticipants 
}: ParticipantManagementProps) {
  const [isAddOpen, setIsAddOpen] = useState(false);
  const [editingParticipant, setEditingParticipant] = useState<Participant | null>(null);
  const queryClient = useQueryClient();
  const { user } = useAuth();

  const { data: participants, isLoading } = useQuery({
    queryKey: ['tournament-participants', tournamentId],
    queryFn: async () => {
      // First get tournament participants
      const { data: participantsData, error: participantsError } = await supabase
        .from('tournament_participants')
        .select(`
          id,
          tournament_id,
          user_id,
          guest_name,
          guest_email,
          is_guest,
          seeding,
          status,
          registration_date,
          club_id
        `)
        .eq('tournament_id', tournamentId)
        .order('seeding', { ascending: true });
      
      if (participantsError) throw participantsError;
      
      // Get user IDs for non-guest participants
      const userIds = participantsData?.filter(p => !p.is_guest && p.user_id).map(p => p.user_id) || [];
      
      // Fetch profile data for members from club_memberships  
      let profilesData: any[] = [];
      if (userIds.length > 0) {
        const { data: profiles, error: profilesError } = await supabase
          .from('club_memberships')
          .select('user_id, first_name, last_name, email')
          .in('user_id', userIds)
          .eq('club_id', participantsData?.[0]?.club_id);
        
        if (!profilesError) {
          profilesData = profiles || [];
        }
      }
      
      // Transform the data to match the expected format
      return participantsData?.map(p => {
        const profile = profilesData.find(prof => prof.user_id === p.user_id);
        
        return {
          id: p.id,
          tournament_id: p.tournament_id,
          user_id: p.user_id,
          user_name: p.is_guest 
            ? p.guest_name || 'Gast'
            : profile 
              ? `${profile.first_name} ${profile.last_name}`
              : `User ${p.user_id}`,
          user_email: p.is_guest 
            ? p.guest_email || ''
            : profile?.email || '',
          is_guest: p.is_guest,
          seed_number: p.seeding || 0,
          status: p.status as 'registered' | 'confirmed' | 'checked_in' | 'withdrawn',
          registered_at: p.registration_date
        };
      }) as Participant[] || [];
    },
    enabled: !!tournamentId
  });

  const { data: clubMembers } = useQuery({
    queryKey: ['club-members'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('club_memberships')
        .select('user_id, first_name, last_name, email')
        .eq('is_active', true);
      
      if (error) throw error;
      return data;
    }
  });

  const addParticipantMutation = useMutation({
    mutationFn: async (participantData: {
      user_id?: string;
      user_name: string;
      user_email: string;
      is_guest: boolean;
      seed_number?: number;
    }) => {
      // Use direct supabase insert instead of edge function to avoid UUID issues
      const { data: tournament, error: tournamentError } = await supabase
        .from('tournaments')
        .select('max_participants, allow_guests, status, club_id')
        .eq('id', tournamentId)
        .single();

      if (tournamentError) throw tournamentError;

      if (tournament.status !== 'registration') {
        throw new Error('Anmeldung für dieses Turnier ist geschlossen');
      }

      if (participantData.is_guest && !tournament.allow_guests) {
        throw new Error('Gäste sind für dieses Turnier nicht erlaubt');
      }

      // Check participant limit
      if (tournament.max_participants) {
        const { count } = await supabase
          .from('tournament_participants')
          .select('*', { count: 'exact', head: true })
          .eq('tournament_id', tournamentId);

        if (count && count >= tournament.max_participants) {
          throw new Error('Maximale Teilnehmerzahl erreicht');
        }
      }

      // Auto-assign seed number if not provided
      let finalSeedNumber = participantData.seed_number;
      if (!finalSeedNumber) {
        const { data: lastParticipant } = await supabase
          .from('tournament_participants')
          .select('seeding')
          .eq('tournament_id', tournamentId)
          .order('seeding', { ascending: false })
          .limit(1)
          .maybeSingle();

        finalSeedNumber = (lastParticipant?.seeding || 0) + 1;
      }

      const insertData = {
        tournament_id: tournamentId,
        user_id: participantData.is_guest ? null : (participantData.user_id || null),
        guest_name: participantData.is_guest ? participantData.user_name : null,
        guest_email: participantData.is_guest ? participantData.user_email : null,
        is_guest: participantData.is_guest,
        seeding: finalSeedNumber,
        status: 'registered',
        registration_date: new Date().toISOString(),
        club_id: tournament.club_id
      };

      const { data, error } = await supabase
        .from('tournament_participants')
        .insert(insertData)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tournament-participants'] });
      setIsAddOpen(false);
      toast.success('Teilnehmer erfolgreich hinzugefügt');
    },
    onError: (error: any) => {
      console.error('Add participant error:', error);
      toast.error('Fehler beim Hinzufügen: ' + error.message);
    }
  });

  const updateParticipantMutation = useMutation({
    mutationFn: async ({ id, ...updates }: Partial<Participant> & { id: string }) => {
      const updateData: any = {};
      
      if (updates.status) updateData.status = updates.status;
      if (updates.seed_number !== undefined) updateData.seeding = updates.seed_number;
      if (updates.user_name && updates.is_guest) updateData.guest_name = updates.user_name;
      if (updates.user_email && updates.is_guest) updateData.guest_email = updates.user_email;
      
      const { data, error } = await supabase
        .from('tournament_participants')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tournament-participants'] });
      setEditingParticipant(null);
      toast.success('Teilnehmer erfolgreich aktualisiert');
    },
    onError: (error: any) => {
      toast.error('Fehler beim Aktualisieren: ' + error.message);
    }
  });

  const removeParticipantMutation = useMutation({
    mutationFn: async (participantId: string) => {
      const { error } = await supabase
        .from('tournament_participants')
        .delete()
        .eq('id', participantId);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tournament-participants'] });
      toast.success('Teilnehmer entfernt');
    },
    onError: (error: any) => {
      toast.error('Fehler beim Entfernen: ' + error.message);
    }
  });

  const ParticipantForm = ({ 
    participant, 
    onSubmit, 
    onCancel 
  }: { 
    participant?: Participant; 
    onSubmit: (data: any) => void;
    onCancel: () => void;
  }) => {
    const [formData, setFormData] = useState({
      user_id: participant?.user_id || '',
      user_name: participant?.user_name || '',
      user_email: participant?.user_email || '',
      is_guest: participant?.is_guest ?? false,
      seed_number: participant?.seed_number || undefined,
      status: participant?.status || 'registered'
    });

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();
      onSubmit(formData);
    };

    const handleMemberSelect = (e: React.ChangeEvent<HTMLSelectElement>) => {
      const memberId = e.target.value;
      if (memberId && clubMembers) {
        const member = clubMembers.find(m => m.user_id === memberId);
        if (member) {
          setFormData({
            ...formData,
            user_id: member.user_id,
            user_name: `${member.first_name} ${member.last_name}`,
            user_email: member.email,
            is_guest: false
          });
        }
      } else {
        setFormData({
          ...formData,
          user_id: '',
          user_name: '',
          user_email: '',
          is_guest: true
        });
      }
    };

    return (
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <Label htmlFor="member_select">Mitglied auswählen (optional)</Label>
          <select
            id="member_select"
            className="w-full p-2 border rounded"
            onChange={handleMemberSelect}
            disabled={!clubMembers}
          >
            <option value="">Gast hinzufügen...</option>
            {clubMembers?.map((member) => (
              <option key={member.user_id} value={member.user_id}>
                {member.first_name} {member.last_name} ({member.email})
              </option>
            ))}
          </select>
        </div>

        <div>
          <Label htmlFor="user_name">Name</Label>
          <Input
            id="user_name"
            value={formData.user_name}
            onChange={(e) => setFormData({ ...formData, user_name: e.target.value })}
            placeholder="Vor- und Nachname"
            required
          />
        </div>

        <div>
          <Label htmlFor="user_email">E-Mail</Label>
          <Input
            id="user_email"
            type="email"
            value={formData.user_email}
            onChange={(e) => setFormData({ ...formData, user_email: e.target.value })}
            placeholder="<EMAIL>"
            required
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="seed_number">Seed-Nummer</Label>
            <Input
              id="seed_number"
              type="number"
              value={formData.seed_number || ''}
              onChange={(e) => setFormData({ 
                ...formData, 
                seed_number: e.target.value ? parseInt(e.target.value) : undefined 
              })}
              placeholder="Auto-vergeben"
            />
          </div>

          {participant && (
            <div>
              <Label htmlFor="status">Status</Label>
              <select
                id="status"
                className="w-full p-2 border rounded"
                value={formData.status}
                onChange={(e) => setFormData({ ...formData, status: e.target.value as any })}
              >
                <option value="registered">Angemeldet</option>
                <option value="confirmed">Bestätigt</option>
                <option value="checked_in">Eingecheckt</option>
                <option value="withdrawn">Zurückgezogen</option>
              </select>
            </div>
          )}
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            id="is_guest"
            checked={formData.is_guest}
            onCheckedChange={(checked) => setFormData({ ...formData, is_guest: checked })}
            disabled={!allowGuests}
          />
          <Label htmlFor="is_guest">Gast</Label>
        </div>

        <div className="flex gap-2 pt-4">
          <Button type="submit" disabled={addParticipantMutation.isPending || updateParticipantMutation.isPending}>
            {participant ? 'Aktualisieren' : 'Hinzufügen'}
          </Button>
          <Button type="button" variant="outline" onClick={onCancel}>
            Abbrechen
          </Button>
        </div>
      </form>
    );
  };

  const getStatusBadge = (status: string) => {
    const statusMap = {
      'registered': { label: 'Angemeldet', variant: 'secondary' as const },
      'confirmed': { label: 'Bestätigt', variant: 'default' as const },
      'checked_in': { label: 'Eingecheckt', variant: 'default' as const },
      'withdrawn': { label: 'Zurückgezogen', variant: 'destructive' as const }
    };
    
    const statusInfo = statusMap[status as keyof typeof statusMap] || { label: status, variant: 'secondary' as const };
    return <Badge variant={statusInfo.variant}>{statusInfo.label}</Badge>;
  };

  if (isLoading) {
    return <div>Lade Teilnehmer...</div>;
  }

  const confirmedCount = participants?.filter(p => p.status === 'confirmed').length || 0;

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Users className="h-5 w-5" />
            Teilnehmer ({participants?.length || 0}
            {maxParticipants && `/${maxParticipants}`})
          </h3>
          <Badge variant="outline">
            {confirmedCount} bestätigt
          </Badge>
        </div>
        
        <Dialog open={isAddOpen} onOpenChange={setIsAddOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Teilnehmer hinzufügen
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Neuen Teilnehmer hinzufügen</DialogTitle>
            </DialogHeader>
            <ParticipantForm
              onSubmit={(data) => addParticipantMutation.mutate(data)}
              onCancel={() => setIsAddOpen(false)}
            />
          </DialogContent>
        </Dialog>
      </div>

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Seed</TableHead>
            <TableHead>Name</TableHead>
            <TableHead>E-Mail</TableHead>
            <TableHead>Typ</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Aktionen</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {participants?.map((participant) => (
            <TableRow key={participant.id}>
              <TableCell>#{participant.seed_number}</TableCell>
              <TableCell>{participant.user_name}</TableCell>
              <TableCell>{participant.user_email}</TableCell>
              <TableCell>
                <Badge variant={participant.is_guest ? 'outline' : 'secondary'}>
                  {participant.is_guest ? 'Gast' : 'Mitglied'}
                </Badge>
              </TableCell>
              <TableCell>{getStatusBadge(participant.status)}</TableCell>
              <TableCell>
                <div className="flex gap-2">
                  <Dialog open={editingParticipant?.id === participant.id} onOpenChange={(open) => {
                    if (!open) setEditingParticipant(null);
                    else setEditingParticipant(participant);
                  }}>
                    <DialogTrigger asChild>
                      <Button variant="outline" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Teilnehmer bearbeiten</DialogTitle>
                      </DialogHeader>
                      <ParticipantForm
                        participant={participant}
                        onSubmit={(data) => updateParticipantMutation.mutate({ id: participant.id, ...data })}
                        onCancel={() => setEditingParticipant(null)}
                      />
                    </DialogContent>
                  </Dialog>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => updateParticipantMutation.mutate({
                      id: participant.id,
                      status: participant.status === 'confirmed' ? 'registered' : 'confirmed'
                    })}
                  >
                    <UserCheck className="h-4 w-4" />
                  </Button>
                  
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => removeParticipantMutation.mutate(participant.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
          {!participants?.length && (
            <TableRow>
              <TableCell colSpan={6} className="text-center text-muted-foreground">
                Noch keine Teilnehmer angemeldet
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}