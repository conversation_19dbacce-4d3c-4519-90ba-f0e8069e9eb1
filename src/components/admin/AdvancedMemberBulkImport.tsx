import { useState, useRef } from "react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Upload, Download, FileSpreadsheet, CheckCircle, Users, Brain } from "lucide-react";
import { toast } from "sonner";
import { useAdvancedBulkImport } from "@/hooks/useAdvancedBulkImport";
import { FieldMappingStep } from "./BulkImport/FieldMappingStep";
import { ImportPreviewStep } from "./BulkImport/ImportPreviewStep";
import { ImportExecutionStep } from "./BulkImport/ImportExecutionStep";

const AdvancedMemberBulkImport = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [debugLogs, setDebugLogs] = useState<string[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const {
    csvData,
    headers,
    fieldMappings,
    parsedMembers,
    importProgress,
    isImporting,
    importStats,
    currentStep,
    parseCSV,
    generateFieldMappings,
    setFieldMappings,
    processMembers,
    setParsedMembers,
    executeImport,
    setCurrentStep,
    resetAllData,
    isReadyForMapping,
    isReadyForPreview,
  } = useAdvancedBulkImport();

  const downloadTemplate = () => {
    const template = `first_name,last_name,email,gender,phone,birth_date,address_street,address_postal_code,address_city,membership_type,account_type,title
Max,Mustermann,<EMAIL>,male,+49 123 456789,1990-01-15,Musterstraße 1,12345,Musterstadt,Erwachsener,member,Herr
Anna,Beispiel,<EMAIL>,female,+49 987 654321,1985-05-22,Beispielweg 2,54321,Beispielstadt,Erwachsener,member,Frau
Peter,Schmidt,<EMAIL>,male,+49 555 123456,1992-03-10,Schmidtstr. 5,67890,Schmidtdorf,Jugendlicher,member,Herr`;
    
    const blob = new Blob([template], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'mitglieder_vorlage_erweitert.csv';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    toast.success("Erweiterte CSV-Vorlage heruntergeladen");
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) {
      setDebugLogs(prev => [...prev, "❌ Keine Datei ausgewählt"]);
      return;
    }

    setDebugLogs(prev => [...prev, `📁 Datei ausgewählt: ${file.name} (${file.size} bytes)`]);

    if (!file.name.endsWith('.csv')) {
      setDebugLogs(prev => [...prev, "❌ Datei ist keine CSV-Datei"]);
      toast.error("Bitte wählen Sie eine CSV-Datei aus");
      return;
    }

    setDebugLogs(prev => [...prev, "✅ CSV-Datei erkannt, starte Parsing..."]);

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      setDebugLogs(prev => [...prev, `📄 Datei gelesen: ${content.length} Zeichen`]);
      try {
        parseCSV(content);
        setDebugLogs(prev => [...prev, "✅ CSV erfolgreich geparst"]);
        toast.success("CSV-Datei erfolgreich geladen");
      } catch (error: any) {
        setDebugLogs(prev => [...prev, `❌ CSV Parse-Fehler: ${error.message}`]);
        toast.error(`Fehler beim Laden der CSV: ${error.message}`);
      }
    };
    reader.onerror = () => {
      setDebugLogs(prev => [...prev, "❌ Fehler beim Lesen der Datei"]);
    };
    reader.readAsText(file);
  };

  const handleGenerateMapping = () => {
    setDebugLogs(prev => [...prev, `🚀 Starte Smart-Zuordnung mit ${headers.length} Spalten`]);
    setDebugLogs(prev => [...prev, `📊 Spalten: ${headers.join(', ')}`]);
    console.log('🚀 Starting smart mapping with headers:', headers);
    console.log('📊 Current CSV data length:', csvData.length);
    generateFieldMappings(headers);
    setCurrentStep('mapping');
    setDebugLogs(prev => [...prev, "✅ Smart-Zuordnung abgeschlossen"]);
    toast.success("Smart-Felderzuordnung generiert");
  };

  const handleProcessMembers = async () => {
    try {
      await processMembers();
      toast.success("Mitgliederdaten erfolgreich verarbeitet");
    } catch (error: any) {
      toast.error(`Fehler bei der Verarbeitung: ${error.message}`);
    }
  };

  const handleExecuteImport = async () => {
    try {
      await executeImport();
    } catch (error: any) {
      toast.error(`Import fehlgeschlagen: ${error.message}`);
    }
  };

  const resetImport = () => {
    setCurrentStep('upload');
    setDebugLogs([]);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const clearCSV = () => {
    // Use the reset function from the hook
    resetAllData();
    setDebugLogs(prev => [...prev, "🗑️ CSV-Daten gelöscht"]);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
    toast.success("CSV-Daten gelöscht");
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 'upload':
        return (
          <div className="space-y-6">
            {/* Template Download */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center gap-2">
                  <Download className="h-4 w-4" />
                  1. Erweiterte CSV-Vorlage herunterladen
                </CardTitle>
                <CardDescription>
                  Laden Sie die Smart-Vorlage mit allen unterstützten Feldern herunter
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button onClick={downloadTemplate} variant="outline" className="gap-2">
                  <Download className="h-4 w-4" />
                  Erweiterte CSV-Vorlage herunterladen
                </Button>
              </CardContent>
            </Card>

            {/* File Upload */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center gap-2">
                  <Upload className="h-4 w-4" />
                  2. CSV-Datei hochladen
                </CardTitle>
                <CardDescription>
                  Wählen Sie Ihre ausgefüllte CSV-Datei aus. Die Smart-Erkennung ordnet Spalten automatisch zu.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".csv"
                  onChange={handleFileUpload}
                  className="block w-full text-sm text-muted-foreground file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-primary-foreground hover:file:bg-primary/80"
                />
                
                {csvData && (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2 text-sm text-green-600">
                        <CheckCircle className="h-4 w-4" />
                        CSV-Datei geladen ({headers.length} Spalten erkannt)
                      </div>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={clearCSV}
                        className="text-red-600 hover:text-red-700"
                      >
                        CSV löschen
                      </Button>
                    </div>
                    
                    {debugLogs.length > 0 && (
                      <Alert>
                        <AlertDescription>
                          <div className="text-xs space-y-1 max-h-32 overflow-y-auto">
                            {debugLogs.map((log, index) => (
                              <div key={index}>{log}</div>
                            ))}
                          </div>
                        </AlertDescription>
                      </Alert>
                    )}
                    
                    <Textarea
                      value={csvData}
                      onChange={(e) => parseCSV(e.target.value)}
                      placeholder="CSV-Daten..."
                      className="h-32 text-xs font-mono"
                    />

                    {isReadyForMapping && (
                      <Button 
                        onClick={handleGenerateMapping}
                        className="gap-2"
                      >
                        <Brain className="h-4 w-4" />
                        Smart-Zuordnung starten
                      </Button>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        );

      case 'mapping':
        return (
          <FieldMappingStep
            headers={headers}
            fieldMappings={fieldMappings}
            onMappingChange={setFieldMappings}
            onNext={handleProcessMembers}
            onBack={() => setCurrentStep('upload')}
          />
        );

      case 'preview':
        return (
          <ImportPreviewStep
            parsedMembers={parsedMembers}
            onMembersChange={setParsedMembers}
            onNext={() => setCurrentStep('import')}
            onBack={() => setCurrentStep('mapping')}
          />
        );

      case 'import':
      case 'complete':
        return (
          <ImportExecutionStep
            isImporting={isImporting}
            importProgress={importProgress}
            importStats={importStats}
            onExecute={handleExecuteImport}
            onReset={resetImport}
          />
        );

      default:
        return null;
    }
  };

  const getStepTitle = () => {
    switch (currentStep) {
      case 'upload': return 'Datei-Upload';
      case 'mapping': return 'Felderzuordnung';
      case 'preview': return 'Datenvorschau';
      case 'import': return 'Import-Ausführung';
      case 'complete': return 'Import abgeschlossen';
      default: return 'Smart Import';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="default" className="gap-2">
          <Brain className="h-4 w-4" />
          Smart Import
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Smart Mitglieder-Import
          </DialogTitle>
          <DialogDescription>
            {getStepTitle()} - Erweiterte Bulk-Import-Funktionalität mit smarter Felderzuordnung und Duplikatserkennung
          </DialogDescription>
        </DialogHeader>

        {renderStepContent()}
      </DialogContent>
    </Dialog>
  );
};

export default AdvancedMemberBulkImport;