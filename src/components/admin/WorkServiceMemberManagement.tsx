import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Progress } from "@/components/ui/progress";
import { UserX, UserCheck, Edit } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";

interface MemberWorkTarget {
  id: string;
  user_id: string;
  year: number;
  target_hours: number;
  completed_hours: number;
  is_manually_exempt: boolean;
  exemption_reason?: string;
  is_auto_exempt: boolean;
  auto_exemption_reason?: string;
  club_memberships?: {
    first_name: string;
    last_name: string;
    membership_type?: string;
    birth_date?: string;
  }[];
}

const WorkServiceMemberManagement = () => {
  const [memberTargets, setMemberTargets] = useState<MemberWorkTarget[]>([]);
  const [selectedMember, setSelectedMember] = useState<MemberWorkTarget | null>(null);
  const [isExemptionDialogOpen, setIsExemptionDialogOpen] = useState(false);
  const [exemptionReason, setExemptionReason] = useState("");
  const [isManuallyExempt, setIsManuallyExempt] = useState(false);
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    fetchMemberTargets();
  }, []);

  const fetchMemberTargets = async () => {
    try {
      const currentYear = new Date().getFullYear();
      
      // First, get the club's default work hours and current club ID
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const { data: userMembership } = await supabase
        .from("club_memberships")
        .select("club_id, clubs(default_annual_work_hours)")
        .eq("user_id", user.id)
        .eq("is_active", true)
        .single();

      if (!userMembership?.club_id) return;
      
      const currentClubId = userMembership.club_id;
      const defaultHours = userMembership.clubs?.default_annual_work_hours || 20;
      
      // Get all club members for the current club
      const { data: clubMembers, error: clubError } = await supabase
        .from("club_memberships")
        .select(`
          user_id,
          first_name,
          last_name,
          membership_type,
          birth_date,
          club_id
        `)
        .eq("club_id", currentClubId)
        .eq("is_active", true);

      if (clubError) throw clubError;

      // Then get existing work targets for this club
      const { data: workTargets, error: targetsError } = await supabase
        .from("member_work_service_targets")
        .select("*")
        .eq("club_id", currentClubId)
        .eq("year", currentYear);

      if (targetsError) throw targetsError;

      // Combine the data
      const combinedData = clubMembers?.map(member => {
        const existingTarget = workTargets?.find(target => target.user_id === member.user_id);
        return {
          id: existingTarget?.id || `${member.user_id}-${currentYear}`,
          user_id: member.user_id,
          year: currentYear,
          target_hours: existingTarget?.target_hours || defaultHours,
          completed_hours: existingTarget?.completed_hours || 0,
          is_manually_exempt: existingTarget?.is_manually_exempt || false,
          exemption_reason: existingTarget?.exemption_reason || null,
          is_auto_exempt: existingTarget?.is_auto_exempt || false,
          auto_exemption_reason: existingTarget?.auto_exemption_reason || null,
          club_memberships: [{
            first_name: member.first_name,
            last_name: member.last_name,
            membership_type: member.membership_type,
            birth_date: member.birth_date
          }]
        };
      }) || [];

      setMemberTargets(combinedData);
    } catch (error) {
      console.error("Error fetching member targets:", error);
      toast({
        title: "Fehler",
        description: "Mitglieder konnten nicht geladen werden",
        variant: "destructive"
      });
    }
  };

  const openExemptionDialog = (member: MemberWorkTarget) => {
    setSelectedMember(member);
    setIsManuallyExempt(member.is_manually_exempt);
    setExemptionReason(member.exemption_reason || "");
    setIsExemptionDialogOpen(true);
  };

  const saveExemption = async () => {
    if (!selectedMember) return;

    setLoading(true);
    try {
      const { error } = await supabase
        .from("member_work_service_targets")
        .update({
          is_manually_exempt: isManuallyExempt,
          exemption_reason: isManuallyExempt ? exemptionReason : null
        })
        .eq("id", selectedMember.id);

      if (error) throw error;

      toast({
        title: "Erfolg",
        description: `Ausschluss ${isManuallyExempt ? "aktiviert" : "deaktiviert"}`
      });

      setIsExemptionDialogOpen(false);
      fetchMemberTargets();
    } catch (error) {
      toast({
        title: "Fehler",
        description: "Ausschluss konnte nicht gespeichert werden",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (member: MemberWorkTarget) => {
    if (member.is_manually_exempt) {
      return <Badge variant="secondary">Manuell ausgeschlossen</Badge>;
    }
    if (member.is_auto_exempt) {
      return <Badge variant="outline">Auto-Ausschluss</Badge>;
    }
    
    const progress = member.target_hours > 0 ? (member.completed_hours / member.target_hours) * 100 : 0;
    if (progress >= 100) {
      return <Badge className="bg-green-600 text-white">Erfüllt</Badge>;
    }
    if (progress >= 50) {
      return <Badge variant="default">In Bearbeitung</Badge>;
    }
    if (progress === 0) {
      return <Badge variant="destructive">Rückstand</Badge>;
    }
    // <50% but >0%
    return <Badge className="bg-orange-500 text-white">Rückstand</Badge>;
  };

  const getProgressValue = (member: MemberWorkTarget) => {
    if (member.is_manually_exempt || member.is_auto_exempt) return 100;
    return member.target_hours > 0 ? Math.min((member.completed_hours / member.target_hours) * 100, 100) : 0;
  };

  const getProgressColor = (member: MemberWorkTarget) => {
    if (member.is_manually_exempt || member.is_auto_exempt) return "bg-muted";
    const progress = getProgressValue(member);
    if (progress >= 100) return "bg-success";
    if (progress >= 50) return "bg-primary";
    return "bg-destructive";
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Mitglieder-Arbeitsdienst Verwaltung</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Mitgliedschaftstyp</TableHead>
                <TableHead>Soll-Stunden</TableHead>
                <TableHead>Ist-Stunden</TableHead>
                <TableHead>Fortschritt</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Aktionen</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {memberTargets.map((member) => (
                <TableRow key={member.id}>
                  <TableCell className="font-medium">
                    {member.club_memberships?.[0]?.first_name} {member.club_memberships?.[0]?.last_name}
                  </TableCell>
                  <TableCell>
                    {member.club_memberships?.[0]?.membership_type || "—"}
                  </TableCell>
                  <TableCell>
                    {member.is_manually_exempt || member.is_auto_exempt ? "—" : `${member.target_hours}h`}
                  </TableCell>
                  <TableCell>
                    {member.completed_hours}h
                  </TableCell>
                  <TableCell className="w-32">
                    <div className="space-y-1">
                      <Progress 
                        value={getProgressValue(member)} 
                        className="h-2"
                      />
                      <div className="text-xs text-muted-foreground">
                        {member.is_manually_exempt || member.is_auto_exempt 
                          ? "Ausgeschlossen" 
                          : `${Math.round(getProgressValue(member))}%`
                        }
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(member)}
                    {(member.is_manually_exempt && member.exemption_reason) && (
                      <div className="text-xs text-muted-foreground mt-1">
                        {member.exemption_reason}
                      </div>
                    )}
                    {(member.is_auto_exempt && member.auto_exemption_reason) && (
                      <div className="text-xs text-muted-foreground mt-1">
                        {member.auto_exemption_reason}
                      </div>
                    )}
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => openExemptionDialog(member)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Exemption Dialog */}
      <Dialog open={isExemptionDialogOpen} onOpenChange={setIsExemptionDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Ausschluss verwalten</DialogTitle>
            <DialogDescription>
              Mitglied: {selectedMember?.club_memberships?.[0]?.first_name} {selectedMember?.club_memberships?.[0]?.last_name}
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="manual_exempt">Manuell vom Arbeitsdienst ausschließen</Label>
              <Switch
                id="manual_exempt"
                checked={isManuallyExempt}
                onCheckedChange={setIsManuallyExempt}
              />
            </div>

            {isManuallyExempt && (
              <div>
                <Label htmlFor="exemption_reason">Begründung</Label>
                <Textarea
                  id="exemption_reason"
                  value={exemptionReason}
                  onChange={(e) => setExemptionReason(e.target.value)}
                  placeholder="Grund für den Ausschluss..."
                  rows={3}
                />
              </div>
            )}

            {selectedMember?.is_auto_exempt && (
              <div className="p-3 bg-muted rounded-md">
                <div className="text-sm font-medium">Automatischer Ausschluss</div>
                <div className="text-sm text-muted-foreground">
                  {selectedMember.auto_exemption_reason}
                </div>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsExemptionDialogOpen(false)}>
              Abbrechen
            </Button>
            <Button onClick={saveExemption} disabled={loading}>
              {loading ? "Speichern..." : "Speichern"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default WorkServiceMemberManagement;