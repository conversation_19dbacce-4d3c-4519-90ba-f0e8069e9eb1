import { useState, useEffect } from "react";
import { Calendar } from "@/components/ui/calendar";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardH<PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { CalendarIcon, Clock, Users, MapPin, Settings, Plus } from "lucide-react";
import { format, isSameDay } from "date-fns";
import { de } from "date-fns/locale";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useMockData } from "@/contexts/MockDataContext";
import { useClub } from "@/contexts/ClubContext";

// Import existing booking components
import { DayView } from "@/components/booking/DayView";
import { CompactWeekView } from "@/components/booking/CompactWeekView";
import { ViewSelector } from "@/components/booking/ViewSelector";

interface Court {
  id: string;
  number: number;
  locked: boolean;
  lock_reason: string | null;
  surface_type?: string;
  court_group?: string;
}

interface TimeSlot {
  time: string;
  available: boolean;
  price?: number;
}

interface Booking {
  id: string;
  booking_date: string;
  start_time: string;
  end_time: string;
  court_id: string;
  player_name: string;
  partner_name?: string;
  created_at: string;
  updated_at: string;
}

const BookingCalendar = () => {
  const { showMockData } = useMockData();
  const { currentClubId } = useClub();
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [selectedCourt, setSelectedCourt] = useState<string>("all");
  const [view, setView] = useState<'day' | 'week' | 'compact' | 'calendar'>('calendar');
  const [courts, setCourts] = useState<Court[]>([]);
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);

  // Generate time slots (8:00 to 22:00)
  const timeSlots: TimeSlot[] = Array.from({ length: 15 }, (_, i) => ({
    time: `${8 + i}:00`,
    available: true,
  }));

  useEffect(() => {
    if (showMockData) {
      setMockData();
    } else if (currentClubId) {
      fetchCourts();
      fetchBookings();
    } else {
      setCourts([]);
      setBookings([]);
      setLoading(false);
    }
  }, [showMockData, currentClubId]);

  const setMockData = () => {
    const mockCourts: Court[] = [
      { id: '1', number: 1, locked: false, lock_reason: null, surface_type: 'Hartplatz', court_group: 'Hauptplätze' },
      { id: '2', number: 2, locked: false, lock_reason: null, surface_type: 'Hartplatz', court_group: 'Hauptplätze' },
      { id: '3', number: 3, locked: false, lock_reason: null, surface_type: 'Sand', court_group: 'Sandplätze' },
      { id: '4', number: 4, locked: false, lock_reason: null, surface_type: 'Sand', court_group: 'Sandplätze' },
      { id: '5', number: 5, locked: false, lock_reason: null, surface_type: 'Rasen', court_group: 'Rasenplätze' }
    ];
    setCourts(mockCourts);
    
    const mockBookings: Booking[] = [
      {
        id: '1',
        booking_date: '2024-08-11',
        start_time: '10:00',
        end_time: '11:00',
        court_id: '1',
        player_name: 'Max Mustermann',
        partner_name: 'Anna Beispiel',
        created_at: '2024-08-10T12:00:00Z',
        updated_at: '2024-08-10T12:00:00Z'
      }
    ];
    setBookings(mockBookings);
    setLoading(false);
  };

  const fetchCourts = async () => {
    if (!currentClubId) {
      setCourts([]);
      return;
    }

    try {
      console.log('🔍 BookingCalendar: Fetching courts for club:', currentClubId);
      const { data, error } = await supabase
        .from('courts')
        .select('*')
        .eq('club_id', currentClubId)  // ⭐ CLUB FILTER HINZUGEFÜGT
        .order('number');

      if (error) throw error;
      console.log('🔍 BookingCalendar: Loaded', data?.length || 0, 'courts for club', currentClubId);
      setCourts(data || []);
    } catch (error) {
      console.error('Error fetching courts:', error);
      toast.error('Fehler beim Laden der Plätze');
    }
  };

  const fetchBookings = async () => {
    if (!currentClubId) {
      setBookings([]);
      setLoading(false);
      return;
    }

    try {
      console.log('🔍 BookingCalendar: Fetching bookings for club:', currentClubId);
      const { data, error } = await supabase
        .from('bookings')
        .select('*')
        .eq('club_id', currentClubId)  // ⭐ CLUB FILTER HINZUGEFÜGT
        .order('booking_date', { ascending: true });

      if (error) throw error;
      console.log('🔍 BookingCalendar: Loaded', data?.length || 0, 'bookings for club', currentClubId);
      setBookings(data || []);
    } catch (error) {
      console.error('Error fetching bookings:', error);
      toast.error('Fehler beim Laden der Buchungen');
    } finally {
      setLoading(false);
    }
  };

  const getBookingsForDate = (date: Date) => {
    const dateString = format(date, 'yyyy-MM-dd');
    return bookings.filter(booking => booking.booking_date === dateString);
  };

  const getBookingsForSelectedDate = () => {
    if (!selectedDate) return [];
    return getBookingsForDate(selectedDate);
  };

  const getDaysWithBookings = () => {
    return Array.from(new Set(bookings.map(booking => new Date(booking.booking_date))));
  };

  // Functions for the booking views
  const getSlotStatus = (courtId: string, time: string, date?: Date): string => {
    const targetDate = date || selectedDate;
    if (!targetDate) return 'available';
    
    const dateString = format(targetDate, 'yyyy-MM-dd');
    const booking = bookings.find(b => 
      b.court_id === courtId && 
      b.booking_date === dateString && 
      b.start_time === time + ':00'
    );
    
    return booking ? 'booked' : 'available';
  };

  const getBookedPlayerNames = (courtId: string, time: string): string[] => {
    if (!selectedDate) return [];
    
    const dateString = format(selectedDate, 'yyyy-MM-dd');
    const booking = bookings.find(b => 
      b.court_id === courtId && 
      b.booking_date === dateString && 
      b.start_time === time + ':00'
    );
    
    if (!booking) return [];
    
    const players = [booking.player_name];
    if (booking.partner_name) {
      players.push(booking.partner_name);
    }
    return players;
  };

  const handleSlotSelect = (courtId: string, time: string, date?: Date) => {
    const targetDate = date || selectedDate;
    if (!targetDate) return;
    
    const court = courts.find(c => c.id === courtId);
    toast.info(`Platz ${court?.number} um ${time} ausgewählt (${format(targetDate, 'dd.MM.yyyy')})`);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "confirmed":
        return "bg-green-100 text-green-800 border-green-200";
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "cancelled":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case "single":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "double":
        return "bg-purple-100 text-purple-800 border-purple-200";
      case "training":
        return "bg-orange-100 text-orange-800 border-orange-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const filteredBookings = getBookingsForSelectedDate().filter(booking => 
    selectedCourt === "all" || booking.court_id === selectedCourt
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Lade Buchungsdaten...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <CalendarIcon className="h-5 w-5" />
              Buchungsübersicht
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-2" />
                Einstellungen
              </Button>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Neue Buchung
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex-1">
              <label className="text-sm font-medium text-muted-foreground">
                Platz filtern
              </label>
              <Select value={selectedCourt} onValueChange={setSelectedCourt}>
                <SelectTrigger className="w-full max-w-xs">
                  <SelectValue placeholder="Platz auswählen" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Alle Plätze</SelectItem>
                  {courts.map((court) => (
                    <SelectItem key={court.id} value={court.id}>
                      Platz {court.number}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            {/* View Selector */}
            <div className="flex items-center gap-2">
              <Tabs value={view} onValueChange={(value) => setView(value as 'day' | 'week' | 'compact' | 'calendar')}>
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="calendar">Kalender</TabsTrigger>
                  <TabsTrigger value="day">Tag</TabsTrigger>
                  <TabsTrigger value="compact">Woche</TabsTrigger>
                  <TabsTrigger value="week">Vollansicht</TabsTrigger>
                </TabsList>
              </Tabs>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* View Content */}
      {view === 'calendar' && (
        <div className="grid lg:grid-cols-3 gap-6">
          {/* Calendar */}
          <Card className="lg:col-span-1">
            <CardHeader>
              <CardTitle>Kalender</CardTitle>
            </CardHeader>
            <CardContent>
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={setSelectedDate}
                locale={de}
                className="rounded-md border pointer-events-auto"
                modifiers={{
                  hasBooking: getDaysWithBookings()
                }}
                modifiersStyles={{
                  hasBooking: { 
                    backgroundColor: 'hsl(var(--primary))', 
                    color: 'hsl(var(--primary-foreground))',
                    fontWeight: 'bold'
                  }
                }}
              />
            </CardContent>
          </Card>

          {/* Bookings for Selected Date */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>
                  Buchungen für {selectedDate ? format(selectedDate, "PPP", { locale: de }) : "Datum wählen"}
                </span>
                <Badge variant="outline">
                  {filteredBookings.length} Buchung{filteredBookings.length !== 1 ? "en" : ""}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {filteredBookings.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <CalendarIcon className="h-12 w-12 mx-auto mb-4 opacity-20" />
                  <p>Keine Buchungen für diesen Tag</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredBookings.map((booking) => {
                    const court = courts.find(c => c.id === booking.court_id);
                    return (
                      <div
                        key={booking.id}
                        className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                      >
                        <div className="flex-1 space-y-2">
                          <div className="flex items-center gap-2">
                            <Clock className="h-4 w-4 text-muted-foreground" />
                            <span className="font-medium">
                              {booking.start_time.slice(0, 5)} - {booking.end_time.slice(0, 5)}
                            </span>
                          </div>
                          
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <div className="flex items-center gap-1">
                              <Users className="h-3 w-3" />
                              {booking.player_name}
                              {booking.partner_name && ` & ${booking.partner_name}`}
                            </div>
                            <div className="flex items-center gap-1">
                              <MapPin className="h-3 w-3" />
                              Platz {court?.number}
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <Badge 
                            variant="outline" 
                            className={getTypeColor("single")}
                          >
                            {booking.partner_name ? "Einzel" : "Einzelspiel"}
                          </Badge>
                          <Badge 
                            variant="outline" 
                            className={getStatusColor("confirmed")}
                          >
                            Bestätigt
                          </Badge>
                          <Button variant="outline" size="sm">
                            Details
                          </Button>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {view === 'day' && selectedDate && (
        <DayView
          selectedDate={selectedDate}
          courts={courts}
          timeSlots={timeSlots}
          onSlotSelect={handleSlotSelect}
          getSlotStatus={getSlotStatus}
          getBookedPlayerNames={getBookedPlayerNames}
        />
      )}

      {view === 'compact' && selectedDate && (
        <CompactWeekView
          selectedDate={selectedDate}
          courts={courts}
          timeSlots={timeSlots}
          onSlotSelect={handleSlotSelect}
          getSlotStatus={getSlotStatus}
          getBookedPlayerNames={getBookedPlayerNames}
        />
      )}

      {view === 'week' && selectedDate && (
        <div className="text-center py-8 text-muted-foreground">
          <p>Vollansicht wird implementiert...</p>
        </div>
      )}

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">
                {bookings.length}
              </div>
              <p className="text-sm text-muted-foreground">Gesamtbuchungen</p>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {getBookingsForSelectedDate().length}
              </div>
              <p className="text-sm text-muted-foreground">Heute</p>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {courts.filter(c => !c.locked).length}
              </div>
              <p className="text-sm text-muted-foreground">Verfügbare Plätze</p>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {courts.filter(c => c.locked).length}
              </div>
              <p className="text-sm text-muted-foreground">Gesperrte Plätze</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default BookingCalendar;