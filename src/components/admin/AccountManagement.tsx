import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Plus, Edit, Trash2, Users, AlertTriangle, Info } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMockData } from "@/contexts/MockDataContext";
import { useTenant } from "@/contexts/TenantContext";
import * as z from "zod";

const accountFormSchema = z.object({
  account_type: z.enum(["Member", "Guest", "Trainer", "Admin"]),
  title: z.enum(["Herr", "Frau", "Herr Dr.", "Frau Dr."]).optional(),
  first_name: z.string().min(1, "First name is required"),
  last_name: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address"),
  phone: z.string().min(1, "Phone is required"),
  address_street: z.string().optional(),
  address_postal_code: z.string().optional(),
  address_city: z.string().optional(),
  birth_date: z.string().optional(),
  membership_type: z.enum(["Kinder", "Jugendliche", "Studenten", "Erwachsene", "Senioren", "Paare", "Familien"]).optional(),
});

type AccountFormData = z.infer<typeof accountFormSchema>;

interface Account {
  id: string;
  account_type: string;
  title?: string | null;
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  address_street: string | null;
  address_postal_code: string | null;
  address_city: string | null;
  birth_date: string | null;
  membership_type: string | null;
  age_type_mismatch_flag: boolean;
}

interface AgeMismatchDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onCorrectData: () => void;
  onIgnoreAndSave: () => void;
  age: number;
  membershipType: string;
}

const AgeMismatchDialog = ({ isOpen, onClose, onCorrectData, onIgnoreAndSave, age, membershipType }: AgeMismatchDialogProps) => (
  <AlertDialog open={isOpen} onOpenChange={onClose}>
    <AlertDialogContent>
      <AlertDialogHeader>
        <AlertDialogTitle className="flex items-center gap-2">
          <AlertTriangle className="h-5 w-5 text-warning" />
          Alter/Typ-Konflikt
        </AlertDialogTitle>
        <AlertDialogDescription>
          Das Geburtsdatum zeigt Alter {age}, was nicht zum Mitgliedschaftstyp "{membershipType}" passt.
          Was möchten Sie tun?
        </AlertDialogDescription>
      </AlertDialogHeader>
      <AlertDialogFooter>
        <AlertDialogCancel onClick={onCorrectData}>
          Daten Korrigieren
        </AlertDialogCancel>
        <AlertDialogAction onClick={onIgnoreAndSave}>
          Ignorieren & Speichern
        </AlertDialogAction>
      </AlertDialogFooter>
    </AlertDialogContent>
  </AlertDialog>
);

interface AccountManagementProps {
  defaultFilter?: string;
}

const AccountManagement = ({ defaultFilter }: AccountManagementProps = {}) => {
  const { showMockData } = useMockData();
  const { club, isLoading: clubLoading } = useTenant();
  
  // Helper functions for dynamic text based on filter
  const getRoleDisplayName = (role?: string) => {
    const roleNames: { [key: string]: string } = {
      "Member": "Mitglied",
      "Guest": "Gast", 
      "Trainer": "Trainer",
      "Admin": "Admin"
    };
    return role ? roleNames[role] || role : "Benutzer";
  };

  const getButtonText = () => {
    if (!defaultFilter) return "Benutzer hinzufügen";
    return `${getRoleDisplayName(defaultFilter)} hinzufügen`;
  };

  const getDialogTitle = (isEditing: boolean) => {
    if (isEditing) {
      return defaultFilter ? `${getRoleDisplayName(defaultFilter)} bearbeiten` : "Benutzer bearbeiten";
    }
    return defaultFilter ? `Neuen ${getRoleDisplayName(defaultFilter)} erstellen` : "Neuen Benutzer erstellen";
  };

  const [accounts, setAccounts] = useState<Account[]>([]);
  const [filteredAccounts, setFilteredAccounts] = useState<Account[]>([]);
  const [loading, setLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingAccount, setEditingAccount] = useState<Account | null>(null);
  const [addressValidationError, setAddressValidationError] = useState(false);
  const [showAgeMismatchDialog, setShowAgeMismatchDialog] = useState(false);
  const [pendingFormData, setPendingFormData] = useState<AccountFormData | null>(null);
  const { toast } = useToast();

  const form = useForm<AccountFormData>({
    resolver: zodResolver(accountFormSchema),
    defaultValues: {
      account_type: "Member",
      title: undefined,
      first_name: "",
      last_name: "",
      email: "",
      phone: "",
      address_street: "",
      address_postal_code: "",
      address_city: "",
    },
  });

  useEffect(() => {
    console.log('🔍 AccountManagement: showMockData changed to:', showMockData);
    if (showMockData) {
      console.log('🔍 AccountManagement: Loading mock accounts');
      setMockAccounts();
    } else if (club?.id) {
      console.log('🔍 AccountManagement: Loading real accounts from database for club:', club.id);
      loadAccounts();
    } else {
      console.log('⚠️ AccountManagement: No club ID available, not loading accounts');
      setAccounts([]);
      setLoading(false);
    }
  }, [showMockData, club?.id]);

  const setMockAccounts = () => {
    console.log('🔍 AccountManagement: Setting mock accounts');
    const mockAccounts: Account[] = [
      {
        id: "1",
        account_type: "Admin",
        first_name: "Thomas",
        last_name: "Admin",
        email: "<EMAIL>",
        phone: "+49 123 456801",
        address_street: null,
        address_postal_code: null,
        address_city: null,
        birth_date: null,
        membership_type: null,
        age_type_mismatch_flag: false
      },
      {
        id: "2",
        account_type: "Admin",
        first_name: "Petra",
        last_name: "Verwaltung",
        email: "<EMAIL>",
        phone: "+49 123 456802",
        address_street: null,
        address_postal_code: null,
        address_city: null,
        birth_date: null,
        membership_type: null,
        age_type_mismatch_flag: false
      },
      {
        id: "3",
        account_type: "Trainer",
        first_name: "Michael",
        last_name: "Coach",
        email: "<EMAIL>",
        phone: "+49 123 456803",
        address_street: null,
        address_postal_code: null,
        address_city: null,
        birth_date: null,
        membership_type: null,
        age_type_mismatch_flag: false
      },
      {
        id: "4",
        account_type: "Guest",
        first_name: "Dennis",
        last_name: "Lampe",
        email: "<EMAIL>",
        phone: "************",
        address_street: null,
        address_postal_code: null,
        address_city: null,
        birth_date: null,
        membership_type: "Jugendliche",
        age_type_mismatch_flag: false
      }
    ];
    console.log('🔍 AccountManagement: Mock accounts set:', mockAccounts.length, 'accounts');
    setAccounts(mockAccounts);
    setLoading(false);
  };

  useEffect(() => {
    filterAccounts();
  }, [accounts, defaultFilter]);

  const filterAccounts = () => {
    if (!defaultFilter) {
      setFilteredAccounts(accounts);
    } else {
      const filtered = accounts.filter(account => account.account_type === defaultFilter);
      setFilteredAccounts(filtered);
    }
  };

  const loadAccounts = async () => {
    if (!club?.id) {
      console.log('⚠️ AccountManagement: No club ID available, cannot load accounts');
      setAccounts([]);
      setLoading(false);
      return;
    }

    try {
      console.log('🔍 AccountManagement: Fetching accounts from database for club:', club.id);
      const { data, error } = await supabase
        .from("accounts")
        .select("*")
        .eq("club_id", club.id)
        .order("created_at", { ascending: false });

      if (error) throw error;
      console.log('🔍 AccountManagement: Loaded', data?.length || 0, 'accounts for club', club.id);
      setAccounts(data || []);
    } catch (error) {
      console.error('❌ AccountManagement: Error loading accounts:', error);
      toast({
        title: "Error",
        description: "Failed to load users",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const calculateAge = (birthDate: string): number => {
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    
    return age;
  };

  const getMembershipTypeAgeRange = (type: string): { min: number; max: number } => {
    switch (type) {
      case "Kinder": return { min: 6, max: 13 };
      case "Jugendliche": return { min: 14, max: 17 };
      case "Studenten": return { min: 18, max: 27 };
      case "Erwachsene": return { min: 18, max: 120 };
      case "Senioren": return { min: 50, max: 120 };
      case "Paare": return { min: 18, max: 120 };
      case "Familien": return { min: 18, max: 120 };
      default: return { min: 0, max: 120 };
    }
  };

  const validateAgeAndMembershipType = (birthDate: string, membershipType: string): boolean => {
    const age = calculateAge(birthDate);
    const range = getMembershipTypeAgeRange(membershipType);
    return age >= range.min && age <= range.max;
  };

  const simulateAddressValidation = async (address: string): Promise<boolean> => {
    // Simulate API call - return false for demo purposes to show split fields
    await new Promise(resolve => setTimeout(resolve, 500));
    return address.includes("valid");
  };

  const handleAddressValidation = async (address: string) => {
    try {
      const isValid = await simulateAddressValidation(address);
      if (!isValid) {
        setAddressValidationError(true);
        toast({
          title: "Address Validation Failed",
          description: "Please fill in the address components manually",
          variant: "destructive"
        });
      } else {
        setAddressValidationError(false);
      }
    } catch (error) {
      setAddressValidationError(true);
    }
  };

  const onSubmit = async (data: AccountFormData) => {
    // Check age/membership type mismatch for members
    if (data.account_type === "Member" && data.birth_date && data.membership_type) {
      if (!validateAgeAndMembershipType(data.birth_date, data.membership_type)) {
        const age = calculateAge(data.birth_date);
        setPendingFormData(data);
        setShowAgeMismatchDialog(true);
        return;
      }
    }

    await saveAccount(data, false);
  };

  const saveAccount = async (data: AccountFormData, ageTypeMismatch: boolean = false) => {
    try {
      const accountData = {
        account_type: data.account_type,
        title: data.title || null,
        first_name: data.first_name,
        last_name: data.last_name,
        email: data.email,
        phone: data.phone,
        address_street: data.address_street || null,
        address_postal_code: data.address_postal_code || null,
        address_city: data.address_city || null,
        birth_date: data.birth_date || null,
        membership_type: data.membership_type || null,
        age_type_mismatch_flag: ageTypeMismatch,
      };

      if (editingAccount) {
        const { error } = await supabase
          .from("accounts")
          .update(accountData)
          .eq("id", editingAccount.id);

        if (error) throw error;
        toast({ title: "Success", description: "Account updated successfully" });
      } else {
        const { error } = await supabase
          .from("accounts")
          .insert(accountData);

        if (error) throw error;
        toast({ title: "Success", description: "User created successfully" });
      }

      setIsDialogOpen(false);
      setEditingAccount(null);
      form.reset();
      setAddressValidationError(false);
      loadAccounts();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to save user",
        variant: "destructive"
      });
    }
  };

  const handleEdit = (account: Account) => {
    setEditingAccount(account);
    form.reset({
      account_type: account.account_type as any,
      title: account.title as any,
      first_name: account.first_name,
      last_name: account.last_name,
      email: account.email,
      phone: account.phone,
      address_street: account.address_street || "",
      address_postal_code: account.address_postal_code || "",
      address_city: account.address_city || "",
      birth_date: account.birth_date || "",
      membership_type: account.membership_type as any,
    });
    setIsDialogOpen(true);
  };

  const handleDelete = async (id: string) => {
    try {
      const { error } = await supabase
        .from("accounts")
        .delete()
        .eq("id", id);

      if (error) throw error;
      toast({ title: "Success", description: "User deleted successfully" });
      loadAccounts();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete user",
        variant: "destructive"
      });
    }
  };

  if (loading) {
    return <div>Laden...</div>;
  }

  return (
    <div className="space-y-6">
      {/* Action Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Users className="h-6 w-6 text-primary" />
          <div>
            <h3 className="text-xl font-semibold">
              {defaultFilter ? `${defaultFilter === 'Member' ? 'Mitglieder' : defaultFilter === 'Guest' ? 'Gäste' : defaultFilter === 'Trainer' ? 'Trainer' : defaultFilter === 'Admin' ? 'Admin' : defaultFilter}verwaltung` : 'Benutzerverwaltung'}
            </h3>
            <p className="text-sm text-muted-foreground">
              {defaultFilter ? `Gefiltert nach: ${defaultFilter}` : "Alle Benutzerkonten"}
            </p>
          </div>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => {
              setEditingAccount(null);
              // Set default account type based on current filter
              const defaultAccountType = defaultFilter || "Member";
              form.reset({
                account_type: defaultAccountType as any,
                title: undefined,
                first_name: "",
                last_name: "",
                email: "",
                phone: "",
                address_street: "",
                address_postal_code: "",
                address_city: "",
                birth_date: "",
                membership_type: undefined,
              });
              setAddressValidationError(false);
            }} className="gap-2">
              <Plus className="h-4 w-4" />
              {getButtonText()}
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {getDialogTitle(!!editingAccount)}
              </DialogTitle>
              <DialogDescription>
                Fülle die Benutzerdaten aus. Die Adressvalidierung wird automatisch versucht.
              </DialogDescription>
            </DialogHeader>
            
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Only show account type selection when not filtering by specific role */}
                  {!defaultFilter && (
                    <FormField
                      control={form.control}
                      name="account_type"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Kontotyp</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Wähle Kontotyp" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="Member">Mitglied</SelectItem>
                              <SelectItem value="Guest">Gast</SelectItem>
                              <SelectItem value="Trainer">Trainer</SelectItem>
                              <SelectItem value="Admin">Admin</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                  
                  {/* Show selected role as read-only when filtering by specific role */}
                  {defaultFilter && (
                    <FormItem>
                      <FormLabel>Kontotyp</FormLabel>
                      <Input value={getRoleDisplayName(defaultFilter)} disabled className="bg-muted" />
                    </FormItem>
                  )}

                  <FormField
                    control={form.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Titel (optional)</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Wähle Titel" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="Herr">Herr</SelectItem>
                            <SelectItem value="Frau">Frau</SelectItem>
                            <SelectItem value="Herr Dr.">Herr Dr.</SelectItem>
                            <SelectItem value="Frau Dr.">Frau Dr.</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="first_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Vorname</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="Max" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="last_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Nachname</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="Mustermann" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>E-Mail</FormLabel>
                        <FormControl>
                          <Input {...field} type="email" placeholder="<EMAIL>" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Telefon</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="+49 123 456789" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="birth_date"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Geburtsdatum (optional)</FormLabel>
                        <FormControl>
                          <Input {...field} type="date" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="membership_type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Mitgliedschaftstyp (optional)</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Wähle Mitgliedschaftstyp" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="Kinder">Kinder (6-13 Jahre)</SelectItem>
                            <SelectItem value="Jugendliche">Jugendliche (14-17 Jahre)</SelectItem>
                            <SelectItem value="Studenten">Studenten (18-27 Jahre)</SelectItem>
                            <SelectItem value="Erwachsene">Erwachsene (18+ Jahre)</SelectItem>
                            <SelectItem value="Senioren">Senioren (50+ Jahre)</SelectItem>
                            <SelectItem value="Paare">Paare (2 Erwachsene)</SelectItem>
                            <SelectItem value="Familien">Familien (2 Erwachsene + Kinder)</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="space-y-4">
                  <h4 className="text-sm font-medium text-muted-foreground">Adresse (optional)</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <FormField
                      control={form.control}
                      name="address_street"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Straße</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="Musterstraße 123" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="address_postal_code"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>PLZ</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="12345" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="address_city"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Stadt</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="Musterstadt" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                <DialogFooter>
                  <Button type="submit">
                    {editingAccount ? "Benutzer Aktualisieren" : "Benutzer Erstellen"}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Statistics Cards - only show for "All Users" view */}
      {!defaultFilter && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                  <Users className="h-4 w-4 text-primary" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Gesamt</p>
                  <p className="text-xl font-semibold">{filteredAccounts.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center">
                  <Users className="h-4 w-4 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Mitglieder</p>
                  <p className="text-xl font-semibold">
                    {filteredAccounts.filter(a => a.account_type === 'Member').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                  <Users className="h-4 w-4 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Gäste</p>
                  <p className="text-xl font-semibold">
                    {filteredAccounts.filter(a => a.account_type === 'Guest').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <div className="h-8 w-8 rounded-full bg-orange-100 flex items-center justify-center">
                  <AlertTriangle className="h-4 w-4 text-orange-600" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-1">
                    <p className="text-sm text-muted-foreground">Konflikte</p>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <button className="hover:bg-muted rounded-full p-1">
                            <Info className="h-3 w-3 text-muted-foreground" />
                          </button>
                        </TooltipTrigger>
                        <TooltipContent side="bottom" className="max-w-xs">
                          <p className="text-sm">
                            Zeigt Benutzer mit Alters-/Mitgliedschaftstyp-Konflikten an. 
                            Beispiel: Ein 25-jähriger als "Kind" kategorisiert.
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <p className="text-xl font-semibold">
                    {filteredAccounts.filter(a => a.age_type_mismatch_flag).length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Users Table */}
      <Card>
        <CardHeader>
          <CardTitle>Benutzer</CardTitle>
          <CardDescription>
            Übersicht aller {defaultFilter ? `${defaultFilter}-` : ''}Benutzerkonten
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Typ</TableHead>
                <TableHead>E-Mail</TableHead>
                <TableHead>Telefon</TableHead>
                <TableHead>Mitgliedschaft</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Aktionen</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredAccounts.map((account) => (
                <TableRow key={account.id}>
                  <TableCell>
                    {account.first_name} {account.last_name}
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{account.account_type}</Badge>
                  </TableCell>
                  <TableCell>{account.email}</TableCell>
                  <TableCell>{account.phone}</TableCell>
                  <TableCell>
                    {account.membership_type && (
                      <Badge variant="secondary">{account.membership_type}</Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    {account.age_type_mismatch_flag && (
                      <Badge variant="destructive">Alterskonflikt</Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEdit(account)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(account.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <AgeMismatchDialog
        isOpen={showAgeMismatchDialog}
        onClose={() => {
          setShowAgeMismatchDialog(false);
          setPendingFormData(null);
        }}
        onCorrectData={() => {
          setShowAgeMismatchDialog(false);
          setPendingFormData(null);
        }}
        onIgnoreAndSave={() => {
          if (pendingFormData) {
            saveAccount(pendingFormData, true);
          }
          setShowAgeMismatchDialog(false);
          setPendingFormData(null);
        }}
        age={pendingFormData?.birth_date ? calculateAge(pendingFormData.birth_date) : 0}
        membershipType={pendingFormData?.membership_type || ""}
      />
    </div>
  );
};

export default AccountManagement;