import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Trophy, Play, Edit, Calendar, Plus, Gamepad2, Trash2 } from 'lucide-react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { FormatConfigValidationDialog } from '@/components/tournaments/FormatConfigValidationDialog';
import { SmartSchedulingDialog } from '@/components/tournaments/SmartSchedulingDialog';
import { useTournamentScheduling } from '@/hooks/useTournamentScheduling';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';

interface Match {
  id: string;
  tournament_id: string;
  participant1_id?: string;
  participant2_id?: string;
  participant1?: { id: string; user_name: string } | null;
  participant2?: { id: string; user_name: string } | null;
  round_number: number;
  match_number: number;
  status: 'scheduled' | 'in_progress' | 'completed' | 'walkover';
  sets?: any[];
  winner_id?: string;
  scheduled_at?: string;
  scheduled_date?: string;
  scheduled_time?: string;
  completed_at?: string;
  court_id?: string;
}

interface Tournament {
  id: string;
  club_id: string;
  name: string;
  format_type: string;
  format_config: any;
  max_participants: number;
}

interface Participant {
  id: string;
  user_name: string;
  seed_number?: number;
}

interface MatchManagementProps {
  tournamentId: string;
}

export function MatchManagement({ tournamentId }: MatchManagementProps) {
  const [isScoreOpen, setIsScoreOpen] = useState(false);
  const [editingMatch, setEditingMatch] = useState<Match | null>(null);
  const [isConfigDialogOpen, setIsConfigDialogOpen] = useState(false);
  const [isSmartSchedulingOpen, setIsSmartSchedulingOpen] = useState(false);
  const queryClient = useQueryClient();
  const { generateScheduleMutation } = useTournamentScheduling();

  // Fetch tournament details
  const { data: tournament } = useQuery({
    queryKey: ['tournament', tournamentId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('tournaments')
        .select('*')
        .eq('id', tournamentId)
        .single();
      
      if (error) throw error;
      return data;
    },
    enabled: !!tournamentId
  });

  // Fetch tournament participants
  const { data: participantsData } = useQuery({
    queryKey: ['tournament-participants', tournamentId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('tournament_participants')
        .select('*')
        .eq('tournament_id', tournamentId)
        .eq('status', 'confirmed')
        .order('created_at', { ascending: true });
      
      if (error) {
        console.error('Error fetching participants:', error);
        throw error;
      }
      
      // Get club member names for participants who are not guests
      const userIds = data.filter(p => p.user_id).map(p => p.user_id);
      
      const { data: memberNames } = await supabase
        .from('club_memberships')
        .select('user_id, first_name, last_name')
        .in('user_id', userIds);
      
      const memberNameMap = new Map(
        (memberNames || []).map(m => [m.user_id, `${m.first_name} ${m.last_name}`])
      );
      
      // Transform data to match our interface
      return data.map(p => ({
        id: p.id,
        user_name: p.guest_name || memberNameMap.get(p.user_id) || `Teilnehmer ${p.id.slice(0, 8)}`,
        seed_number: p.seeding || 0
      })) as Participant[];
    },
    enabled: !!tournamentId
  });

  const participants = participantsData || [];

  // Fetch tournament court assignments to check if courts are configured
  const { data: tournamentCourts } = useQuery({
    queryKey: ['tournament-courts', tournamentId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('tournament_courts')
        .select(`
          id,
          court_id,
          start_time,
          end_time,
          available_days,
          court:courts(id, number, surface_type)
        `)
        .eq('tournament_id', tournamentId);
      
      if (error) throw error;
      return data;
    },
    enabled: !!tournamentId
  });

   // Fetch club members to get participant names
  const { data: clubMembers } = useQuery({
    queryKey: ['club-members', tournamentId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('club_memberships')
        .select('user_id, first_name, last_name, email')
        .eq('is_active', true);
      
      if (error) throw error;
      return data;
    },
    enabled: !!tournamentId
  });

  const { data: matches, isLoading } = useQuery({
    queryKey: ['tournament-matches', tournamentId],
    queryFn: async () => {
      console.log('Fetching matches for tournament:', tournamentId);
      
      const { data, error } = await supabase
        .from('tournament_matches')
        .select('*')
        .eq('tournament_id', tournamentId)
        .order('round_number', { ascending: true })
        .order('match_number', { ascending: true });
      
      if (error) {
        console.error('Error fetching matches:', error);
        throw error;
      }
      
      console.log('Matches fetched:', data);
      
      if (!data) return [];
      
      // Get participant info separately and merge
      const { data: allParticipants } = await supabase
        .from('tournament_participants')
        .select('id, user_id, guest_name')
        .eq('tournament_id', tournamentId);
      
      // Get club member names for participants who are not guests
      const userIds = (allParticipants || [])
        .filter(p => p.user_id)
        .map(p => p.user_id);
      
      const { data: memberNames } = await supabase
        .from('club_memberships')
        .select('user_id, first_name, last_name')
        .in('user_id', userIds);
      
      const memberNameMap = new Map(
        (memberNames || []).map(m => [m.user_id, `${m.first_name} ${m.last_name}`])
      );
      
      const participantMap = new Map(
        (allParticipants || []).map(p => [
          p.id, 
          p.guest_name || memberNameMap.get(p.user_id) || 'Unknown'
        ])
      );
      
      // Transform matches to include participant names and sets from score_json
      const transformedMatches = data.map(match => ({
        ...match,
        participant1: match.participant1_id ? {
          id: match.participant1_id,
          user_name: participantMap.get(match.participant1_id) || 'Unknown'
        } : null,
        participant2: match.participant2_id ? {
          id: match.participant2_id,
          user_name: participantMap.get(match.participant2_id) || 'Unknown'
        } : null,
        sets: (match.score_json && typeof match.score_json === 'object' && !Array.isArray(match.score_json) && 'sets' in match.score_json) ? (match.score_json as any).sets || [] : []
      }));
      
      return transformedMatches as Match[];
    },
    enabled: !!tournamentId
  });

  // Helper function to get participant name
  const getParticipantName = (participantId?: string): string => {
    if (!participantId) return 'TBD';
    
    // First try to find in participantsData
    const participant = participantsData?.find(p => p.id === participantId);
    if (participant) {
      return participant.user_name;
    }
    
    // Fallback
    return 'Unknown';
  };

  // Helper function to get court number
  const getCourtNumber = (courtId?: string): string => {
    if (!courtId || !tournamentCourts) return 'N/A';
    
    const court = tournamentCourts.find(tc => tc.court_id === courtId);
    if (court && court.court) {
      return court.court.number?.toString() || 'N/A';
    }
    
    return 'N/A';
  };

  const generateMatchesMutation = useMutation({
    mutationFn: async (config: Record<string, any>) => {
      console.log('Generating matches for tournament:', tournamentId, 'with config:', config);
      
      const { data, error } = await supabase.functions.invoke('tournament-matches', {
        body: { 
          action: 'generate',
          tournament_id: tournamentId,
          format_config: config
        }
      });
      
      if (error) {
        console.error('Edge function error:', error);
        throw new Error(error.message || 'Fehler beim Aufrufen der Edge Function');
      }
      
      console.log('Raw response from edge function:', data);
      
      // Check if the response indicates success
      if (data && data.success === false) {
        throw new Error(data.error || 'Unbekannter Fehler beim Generieren der Spiele');
      }
      
      if (!data || (!data.matches && !data.success)) {
        throw new Error('Ungültige Antwort vom Server erhalten');
      }
      
      console.log('Matches generated successfully:', data);
      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['tournament-matches'] });
      setIsConfigDialogOpen(false);
      const message = data.message || `${data.matches?.length || 0} Spiele erfolgreich generiert`;
      toast.success(message);
    },
    onError: (error: any) => {
      console.error('Generate matches error:', error);
      const errorMessage = error.message || 'Unbekannter Fehler beim Generieren der Spiele';
      toast.error('Fehler beim Generieren: ' + errorMessage);
    }
  });

  const handleGenerateMatches = () => {
    if (!tournament || !participants) {
      toast.error('Turnier-Daten werden geladen...');
      return;
    }

    if (participants.length < 2) {
      toast.error('Mindestens 2 bestätigte Teilnehmer erforderlich');
      return;
    }

    // Check if courts are assigned
    if (!tournamentCourts || tournamentCourts.length === 0) {
      toast.error('Bitte weisen Sie zuerst Plätze für dieses Turnier zu. Gehen Sie zum Tab "Plätze" um Plätze zu konfigurieren.');
      return;
    }

    setIsConfigDialogOpen(true);
  };

  // Transform tournament data to match our interface
  const tournamentForDialog: Tournament | null = tournament ? {
    id: tournament.id,
    club_id: tournament.club_id,
    name: tournament.name,
    format_type: tournament.format_type,
    format_config: typeof tournament.format_config === 'string' 
      ? JSON.parse(tournament.format_config) 
      : tournament.format_config || {},
    max_participants: tournament.max_participants
  } : null;

  const handleConfirmGeneration = (validatedConfig: Record<string, any>, smartSchedulingConfig?: any) => {
    if (smartSchedulingConfig) {
      // Use the smart scheduling flow
      handleSmartScheduleGenerate(smartSchedulingConfig);
    } else {
      // Use the regular flow
      generateMatchesMutation.mutate(validatedConfig);
    }
  };

  const handleSmartScheduleGenerate = async (config: any) => {
    console.log('Starting smart schedule generation with config:', config);
    
    if (!tournament || !tournamentCourts) {
      console.error('Missing tournament or court data:', { tournament: !!tournament, tournamentCourts: !!tournamentCourts });
      toast.error('Tournament or court data not available');
      return;
    }

    // Extract time configuration from tournament format config
    const formatConfig = typeof tournament.format_config === 'string' 
      ? JSON.parse(tournament.format_config) 
      : tournament.format_config || {};
    
    const timeConfig = formatConfig.time_config || {
      match_duration_minutes: 90,
      warmup_minutes: 10,
      changeover_minutes: 5,
      set_break_minutes: 2,
      between_match_buffer_minutes: 10,
      court_preparation_minutes: 5
    };

    console.log('Using time config:', timeConfig);
    console.log('Using smart scheduling config:', config);

    try {
      const result = await generateScheduleMutation.mutateAsync({
        tournament_id: tournamentId,
        time_config: timeConfig,
        start_date: tournament.tournament_start ? new Date(tournament.tournament_start).toISOString().split('T')[0] : undefined,
        start_time: config.tournament_start_time || '09:00',
        smart_scheduling_config: config
      });
      
      console.log('Smart scheduling result:', result);
      setIsConfigDialogOpen(false);
      setIsSmartSchedulingOpen(false);
    } catch (error) {
      console.error('Smart scheduling failed:', error);
      toast.error(`Smart Scheduling fehlgeschlagen: ${error.message}`);
    }
  };

  const deleteAllMatchesMutation = useMutation({
    mutationFn: async () => {
      console.log('Deleting all matches for tournament:', tournamentId);
      
      const { data, error } = await supabase.functions.invoke('tournament-matches', {
        body: { 
          action: 'delete_all',
          tournament_id: tournamentId
        }
      });
      
      if (error) {
        console.error('Edge function error:', error);
        throw new Error(error.message || 'Fehler beim Aufrufen der Edge Function');
      }
      
      console.log('Raw response from edge function:', data);
      
      if (data && data.success === false) {
        throw new Error(data.error || 'Unbekannter Fehler beim Löschen der Spiele');
      }
      
      console.log('Matches deleted successfully:', data);
      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['tournament-matches'] });
      const message = data.message || 'Alle Spiele erfolgreich gelöscht';
      toast.success(message);
    },
    onError: (error: any) => {
      console.error('Delete matches error:', error);
      const errorMessage = error.message || 'Unbekannter Fehler beim Löschen der Spiele';
      toast.error('Fehler beim Löschen: ' + errorMessage);
    }
  });

  const updateScoreMutation = useMutation({
    mutationFn: async ({ matchId, sets, winnerId }: { 
      matchId: string; 
      sets: Array<{ player1_score: number; player2_score: number }>; 
      winnerId: string;
    }) => {
      const { data, error } = await supabase.functions.invoke('tournament-matches', {
        body: {
          action: 'update_score',
          match_id: matchId,
          sets,
          winner_id: winnerId
        }
      });
      
      if (error) {
        console.error('Edge function error:', error);
        throw new Error(error.message || 'Fehler beim Aufrufen der Edge Function');
      }
      
      if (data && data.success === false) {
        throw new Error(data.error || 'Unbekannter Fehler beim Aktualisieren des Spiels');
      }
      
      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['tournament-matches'] });
      setIsScoreOpen(false);
      setEditingMatch(null);
      const message = data?.message || 'Ergebnis erfolgreich eingetragen';
      toast.success(message);
    },
    onError: (error: any) => {
      console.error('Update score error:', error);
      const errorMessage = error.message || 'Unbekannter Fehler beim Eintragen des Ergebnisses';
      toast.error('Fehler beim Eintragen: ' + errorMessage);
    }
  });

  const ScoreEntryForm = ({ 
    match, 
    onSubmit, 
    onCancel 
  }: { 
    match: Match; 
    onSubmit: (sets: any[], winnerId: string) => void;
    onCancel: () => void;
  }) => {
    const [sets, setSets] = useState(match.sets || [{ player1_score: 0, player2_score: 0 }]);

    const addSet = () => {
      if (sets.length < 5) {
        setSets([...sets, { player1_score: 0, player2_score: 0 }]);
      }
    };

    const updateSet = (index: number, field: string, value: number) => {
      const newSets = [...sets];
      newSets[index] = { ...newSets[index], [field]: value };
      setSets(newSets);
    };

    const removeSet = (index: number) => {
      if (sets.length > 1) {
        setSets(sets.filter((_, i) => i !== index));
      }
    };

    const calculateWinner = () => {
      let player1Sets = 0;
      let player2Sets = 0;
      
      sets.forEach(set => {
        if (set.player1_score > set.player2_score) player1Sets++;
        else if (set.player2_score > set.player1_score) player2Sets++;
      });
      
      if (player1Sets > player2Sets) {
        return match.participant1_id!;
      } else if (player2Sets > player1Sets) {
        return match.participant2_id!;
      }
      return '';
    };

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();
      const winnerId = calculateWinner();
      if (!winnerId) {
        toast.error('Bitte geben Sie ein gültiges Ergebnis ein');
        return;
      }
      onSubmit(sets, winnerId);
    };

    return (
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="text-lg font-semibold">
            {match.participant1?.user_name || 'TBD'} vs {match.participant2?.user_name || 'TBD'}
          </div>
          <Badge>Runde {match.round_number}</Badge>
        </div>

        <div className="space-y-3">
          <Label>Sätze</Label>
          {sets.map((set, index) => (
            <div key={index} className="flex items-center gap-4 p-3 border rounded">
              <div className="flex items-center gap-2">
                <Label>Satz {index + 1}:</Label>
                <div className="flex items-center gap-1">
                  <Input
                    type="number"
                    min="0"
                    max="7"
                    value={set.player1_score}
                    onChange={(e) => updateSet(index, 'player1_score', parseInt(e.target.value) || 0)}
                    className="w-16"
                  />
                  <span>:</span>
                  <Input
                    type="number"
                    min="0"
                    max="7"
                    value={set.player2_score}
                    onChange={(e) => updateSet(index, 'player2_score', parseInt(e.target.value) || 0)}
                    className="w-16"
                  />
                </div>
              </div>
              {sets.length > 1 && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => removeSet(index)}
                >
                  Entfernen
                </Button>
              )}
            </div>
          ))}
          
          {sets.length < 5 && (
            <Button type="button" variant="outline" onClick={addSet}>
              <Plus className="h-4 w-4 mr-2" />
              Satz hinzufügen
            </Button>
          )}
        </div>

        <div className="flex gap-2 pt-4">
          <Button type="submit" disabled={updateScoreMutation.isPending}>
            Ergebnis eintragen
          </Button>
          <Button type="button" variant="outline" onClick={onCancel}>
            Abbrechen
          </Button>
        </div>
      </form>
    );
  };

  const getStatusBadge = (status: string) => {
    const statusMap = {
      'scheduled': { label: 'Geplant', variant: 'secondary' as const },
      'in_progress': { label: 'Läuft', variant: 'default' as const },
      'completed': { label: 'Beendet', variant: 'outline' as const },
      'walkover': { label: 'Walkover', variant: 'destructive' as const }
    };
    
    const statusInfo = statusMap[status as keyof typeof statusMap] || { label: status, variant: 'secondary' as const };
    return <Badge variant={statusInfo.variant}>{statusInfo.label}</Badge>;
  };

  const formatSets = (sets: any[], match: any) => {
    if (!sets || sets.length === 0) {
      return (
        <div className="text-sm text-muted-foreground bg-muted/20 px-3 py-2 rounded border border-dashed">
          Noch kein Ergebnis
        </div>
      );
    }

    // Get participant names
    const participant1Name = getParticipantName(match.participant1_id) || 'Spieler 1';
    const participant2Name = getParticipantName(match.participant2_id) || 'Spieler 2';
    const isPlayer1Winner = match.winner_id === match.participant1_id;
    const isPlayer2Winner = match.winner_id === match.participant2_id;
    const hasWinner = isPlayer1Winner || isPlayer2Winner;

    return (
      <div className={`px-3 py-2 rounded border space-y-1 ${hasWinner ? 'bg-green-50 border-green-200' : 'bg-muted/30'}`}>
        <div className={`flex items-center justify-between text-xs font-medium ${isPlayer1Winner ? 'text-green-700' : ''}`}>
          <span className="truncate max-w-[80px] flex items-center gap-1 min-w-0">
            <span className="w-4 flex-shrink-0">
              {isPlayer1Winner && '🏆'}
            </span>
            <span className="truncate">{participant1Name}</span>
          </span>
          <div className="flex gap-1">
            {sets.map((set: any, i: number) => (
              <span key={i} className={`px-1 ${isPlayer1Winner ? 'font-bold' : ''}`}>
                {set.player1}
              </span>
            ))}
          </div>
        </div>
        <div className="h-px bg-border"></div>
        <div className={`flex items-center justify-between text-xs font-medium ${isPlayer2Winner ? 'text-green-700' : ''}`}>
          <span className="truncate max-w-[80px] flex items-center gap-1 min-w-0">
            <span className="w-4 flex-shrink-0">
              {isPlayer2Winner && '🏆'}
            </span>
            <span className="truncate">{participant2Name}</span>
          </span>
          <div className="flex gap-1">
            {sets.map((set: any, i: number) => (
              <span key={i} className={`px-1 ${isPlayer2Winner ? 'font-bold' : ''}`}>
                {set.player2}
              </span>
            ))}
          </div>
        </div>
      </div>
    );
  };

  if (isLoading) {
    return <div>Lade Spiele...</div>;
  }

  const groupedMatches = matches?.reduce((acc, match) => {
    const round = match.round_number;
    if (!acc[round]) acc[round] = [];
    acc[round].push(match);
    return acc;
  }, {} as Record<number, Match[]>);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold flex items-center gap-2">
          <Gamepad2 className="h-5 w-5" />
          Spiele-Verwaltung
        </h3>
        
        <div className="flex gap-2">
          {(!matches || matches.length === 0) && (
            <Button 
              onClick={() => {
                console.log('Generate matches button clicked');
                console.log('Tournament:', tournament);
                console.log('Participants:', participants);
                handleGenerateMatches();
              }}
              disabled={generateMatchesMutation.isPending || !tournament || !participants}
            >
              <Calendar className="h-4 w-4 mr-2" />
              {generateMatchesMutation.isPending ? 'Generiere...' : 'Spiele generieren'}
            </Button>
          )}
          
          {matches && matches.length > 0 && (
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button 
                  variant="destructive"
                  disabled={deleteAllMatchesMutation.isPending}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  {deleteAllMatchesMutation.isPending ? 'Lösche...' : 'Alle Spiele löschen'}
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Alle Spiele löschen?</AlertDialogTitle>
                  <AlertDialogDescription>
                    Diese Aktion löscht alle generierten Spiele und zugehörigen Buchungen für dieses Turnier. 
                    Diese Aktion kann nicht rückgängig gemacht werden.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Abbrechen</AlertDialogCancel>
                  <AlertDialogAction 
                    onClick={() => deleteAllMatchesMutation.mutate()}
                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                  >
                    Alle Spiele löschen
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          )}
        </div>
      </div>

      {!matches || matches.length === 0 ? (
        <Card>
          <CardContent className="text-center py-8">
            <Calendar className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <p className="text-muted-foreground">
              Noch keine Spiele generiert. Klicken Sie auf "Spiele generieren" um zu beginnen.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-6">
          {Object.entries(groupedMatches || {}).map(([round, roundMatches]) => (
            <Card key={round}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Trophy className="h-5 w-5" />
                  Runde {round}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Spiel</TableHead>
                      <TableHead>Spieler 1</TableHead>
                      <TableHead>Spieler 2</TableHead>
                      <TableHead>Platz</TableHead>
                      <TableHead>Zeit</TableHead>
                      <TableHead>Ergebnis</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Aktionen</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {roundMatches.map((match) => (
                      <TableRow key={match.id}>
                        <TableCell>#{match.match_number}</TableCell>
                        <TableCell>
                          {getParticipantName(match.participant1_id) || 'TBD'}
                          {match.winner_id === match.participant1_id && (
                            <Trophy className="h-4 w-4 inline ml-2 text-yellow-500" />
                          )}
                        </TableCell>
                        <TableCell>
                          {match.participant2_id ? getParticipantName(match.participant2_id) || 'TBD' : 'Bye'}
                          {match.winner_id === match.participant2_id && (
                            <Trophy className="h-4 w-4 inline ml-2 text-yellow-500" />
                          )}
                        </TableCell>
                        <TableCell>
                          {match.court_id ? `Platz ${getCourtNumber(match.court_id)}` : 'Nicht zugewiesen'}
                        </TableCell>
                        <TableCell key={`${match.id}-${match.scheduled_date}-${match.scheduled_time}`}>
                          {match.scheduled_date && match.scheduled_time ? (
                            <div>
                              <div className="font-medium">
                                {format(new Date(`${match.scheduled_date}T${match.scheduled_time}`), 'EEEE, dd.MM.yyyy', { locale: de })}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                {format(new Date(`${match.scheduled_date}T${match.scheduled_time}`), 'HH:mm')} 
                                <span className="text-xs text-muted-foreground ml-1">
                                  ({match.scheduled_date})
                                </span>
                              </div>
                            </div>
                          ) : (
                            <div className="text-sm text-muted-foreground">
                              ⏰ Noch nicht geplant
                            </div>
                          )}
                        </TableCell>
                        <TableCell>{formatSets(match.sets || [], match)}</TableCell>
                        <TableCell>{getStatusBadge(match.status)}</TableCell>
                        <TableCell>
                          <div className="flex gap-2">
                            {match.status !== 'completed' && match.participant1 && match.participant2 && (
                              <Dialog open={editingMatch?.id === match.id} onOpenChange={(open) => {
                                if (!open) setEditingMatch(null);
                                else setEditingMatch(match);
                              }}>
                                <DialogTrigger asChild>
                                  <Button variant="outline" size="sm">
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                </DialogTrigger>
                                <DialogContent className="max-w-2xl">
                                  <DialogHeader>
                                    <DialogTitle>Ergebnis eintragen</DialogTitle>
                                  </DialogHeader>
                                  <ScoreEntryForm
                                    match={match}
                                    onSubmit={(sets, winnerId) => updateScoreMutation.mutate({
                                      matchId: match.id,
                                      sets,
                                      winnerId
                                    })}
                                    onCancel={() => setEditingMatch(null)}
                                  />
                                </DialogContent>
                              </Dialog>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Format Configuration Dialog */}
      {tournamentForDialog && participants && (
        <FormatConfigValidationDialog
          open={isConfigDialogOpen}
          onOpenChange={setIsConfigDialogOpen}
          tournament={tournamentForDialog}
          participants={participants}
          availableCourts={tournamentCourts?.map(tc => ({
            id: tc.court_id,
            number: tc.court?.number || 0,
            surface_type: tc.court?.surface_type,
            court_group: undefined,
            is_main_court: tc.court?.number === 1
          })) || []}
          onConfirm={handleConfirmGeneration}
        />
      )}

      {/* Smart Scheduling Dialog */}
      {tournamentForDialog && tournamentCourts && (
        <SmartSchedulingDialog
          open={isSmartSchedulingOpen}
          onOpenChange={setIsSmartSchedulingOpen}
          tournament={tournamentForDialog}
          availableCourts={tournamentCourts.map(tc => ({
            id: tc.court_id,
            number: tc.court?.number || 0,
            surface_type: tc.court?.surface_type,
            court_group: undefined,
            is_main_court: tc.court?.number === 1
          }))}
          onConfirm={(config) => {
            setIsSmartSchedulingOpen(false);
          }}
          onSmartScheduleGenerate={handleSmartScheduleGenerate}
        />
      )}
    </div>
  );
}
