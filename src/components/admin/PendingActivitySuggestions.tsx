import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { CheckCircle, XCircle, Edit, Eye } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useClub } from "@/contexts/ClubContext";
import { useToast } from "@/components/ui/use-toast";

interface PendingActivity {
  id: string;
  name: string;
  description: string | null;
  hourly_rate: number | null;
  requested_by: string;
  created_at: string;
  profiles?: {
    first_name: string;
    last_name: string;
  } | null;
}

interface PendingActivitySuggestionsProps {
  onSuggestionsChange?: () => void;
}

export default function PendingActivitySuggestions({ onSuggestionsChange }: PendingActivitySuggestionsProps = {}) {
  const { currentClubId } = useClub();
  const { toast } = useToast();
  const [pendingActivities, setPendingActivities] = useState<PendingActivity[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedActivity, setSelectedActivity] = useState<PendingActivity | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  
  const [editForm, setEditForm] = useState({
    name: "",
    description: "",
    hourly_rate: ""
  });

  useEffect(() => {
    if (currentClubId) {
      fetchPendingActivities();
    }
  }, [currentClubId]);

  const fetchPendingActivities = async () => {
    if (!currentClubId) return;

    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('activities')
        .select(`
          id,
          name,
          description,
          hourly_rate,
          requested_by,
          created_at
        `)
        .eq('club_id', currentClubId)
        .eq('status', 'pending')
        .order('created_at', { ascending: false });

      if (error) throw error;
      
      // Fetch user profiles separately for each activity
      const activitiesWithProfiles = await Promise.all(
        (data || []).map(async (activity) => {
          const { data: profileData } = await supabase
            .from('profiles')
            .select('first_name, last_name')
            .eq('id', activity.requested_by)
            .maybeSingle();
          
          return {
            ...activity,
            profiles: profileData
          };
        })
      );
      
      setPendingActivities(activitiesWithProfiles);
    } catch (error) {
      console.error('Error fetching pending activities:', error);
      toast({
        title: "Fehler",
        description: "Vorgeschlagene Tätigkeiten konnten nicht geladen werden.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleApprove = async (activity: PendingActivity) => {
    setIsProcessing(true);
    try {
      // Update activity status to approved
      const { error: updateError } = await supabase
        .from('activities')
        .update({ 
          status: 'approved',
          approved_at: new Date().toISOString(),
          approved_by: (await supabase.auth.getUser()).data.user?.id
        })
        .eq('id', activity.id);

      if (updateError) throw updateError;

      // Create automatic assignment to the requester
      const { error: assignError } = await supabase
        .from('activity_assignments')
        .insert([{
          activity_id: activity.id,
          member_id: activity.requested_by,
          club_id: currentClubId,
          status: 'assigned',
          hours_credited: activity.hourly_rate || 0,
          notes: 'Automatisch zugewiesen nach Genehmigung des Vorschlags'
        }]);

      if (assignError) throw assignError;

      toast({
        title: "Tätigkeit genehmigt",
        description: "Die Tätigkeit wurde genehmigt und automatisch dem Antragsteller zugeordnet.",
      });

      fetchPendingActivities();
      onSuggestionsChange?.();
    } catch (error) {
      console.error('Error approving activity:', error);
      toast({
        title: "Fehler",
        description: "Tätigkeit konnte nicht genehmigt werden.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleReject = async (activity: PendingActivity) => {
    const reason = prompt("Grund für die Ablehnung (optional):");
    
    setIsProcessing(true);
    try {
      const { error } = await supabase
        .from('activities')
        .update({ 
          status: 'rejected',
          description: activity.description + (reason ? `\n\nAblehnungsgrund: ${reason}` : '')
        })
        .eq('id', activity.id);

      if (error) throw error;

      toast({
        title: "Tätigkeit abgelehnt",
        description: "Die vorgeschlagene Tätigkeit wurde abgelehnt.",
      });

      fetchPendingActivities();
      onSuggestionsChange?.();
    } catch (error) {
      console.error('Error rejecting activity:', error);
      toast({
        title: "Fehler",
        description: "Tätigkeit konnte nicht abgelehnt werden.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleEditAndApprove = async () => {
    if (!selectedActivity) return;

    setIsProcessing(true);
    try {
      // Update activity with edited values and approve
      const { error: updateError } = await supabase
        .from('activities')
        .update({ 
          name: editForm.name,
          description: editForm.description,
          hourly_rate: editForm.hourly_rate ? parseFloat(editForm.hourly_rate) : null,
          status: 'approved',
          approved_at: new Date().toISOString(),
          approved_by: (await supabase.auth.getUser()).data.user?.id
        })
        .eq('id', selectedActivity.id);

      if (updateError) throw updateError;

      // Create automatic assignment to the requester
      const { error: assignError } = await supabase
        .from('activity_assignments')
        .insert([{
          activity_id: selectedActivity.id,
          member_id: selectedActivity.requested_by,
          club_id: currentClubId,
          status: 'assigned',
          hours_credited: editForm.hourly_rate ? parseFloat(editForm.hourly_rate) : 0,
          notes: 'Automatisch zugewiesen nach Bearbeitung und Genehmigung des Vorschlags'
        }]);

      if (assignError) throw assignError;

      toast({
        title: "Tätigkeit bearbeitet und genehmigt",
        description: "Die Tätigkeit wurde erfolgreich bearbeitet, genehmigt und zugeordnet.",
      });

      setIsEditDialogOpen(false);
      setSelectedActivity(null);
      fetchPendingActivities();
      onSuggestionsChange?.();
    } catch (error) {
      console.error('Error editing and approving activity:', error);
      toast({
        title: "Fehler",
        description: "Tätigkeit konnte nicht bearbeitet werden.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const openEditDialog = (activity: PendingActivity) => {
    setSelectedActivity(activity);
    setEditForm({
      name: activity.name,
      description: activity.description || "",
      hourly_rate: activity.hourly_rate?.toString() || ""
    });
    setIsEditDialogOpen(true);
  };

  const openViewDialog = (activity: PendingActivity) => {
    setSelectedActivity(activity);
    setIsViewDialogOpen(true);
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Vorgeschlagene Tätigkeiten</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Vorgeschlagene Tätigkeiten</CardTitle>
          <p className="text-sm text-muted-foreground">
            {pendingActivities.length} Tätigkeitsvorschläge warten auf Ihre Entscheidung
          </p>
        </CardHeader>
        <CardContent>
          {pendingActivities.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              Keine vorgeschlagenen Tätigkeiten vorhanden.
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Tätigkeit</TableHead>
                    <TableHead>Antragsteller</TableHead>
                    <TableHead>Stunden</TableHead>
                    <TableHead>Eingereicht am</TableHead>
                    <TableHead>Aktionen</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {pendingActivities.map((activity) => (
                    <TableRow key={activity.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{activity.name}</div>
                          {activity.description && (
                            <div className="text-sm text-muted-foreground truncate max-w-xs">
                              {activity.description.substring(0, 60)}
                              {activity.description.length > 60 && "..."}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          {activity.profiles ? 
                            `${activity.profiles.first_name} ${activity.profiles.last_name}` : 
                            'Unbekannt'
                          }
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {activity.hourly_rate || 0}h
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {new Date(activity.created_at).toLocaleDateString('de-DE')}
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => openViewDialog(activity)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => openEditDialog(activity)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="default"
                            onClick={() => handleApprove(activity)}
                            disabled={isProcessing}
                          >
                            <CheckCircle className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => handleReject(activity)}
                            disabled={isProcessing}
                          >
                            <XCircle className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* View Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Tätigkeitsvorschlag ansehen</DialogTitle>
          </DialogHeader>
          {selectedActivity && (
            <div className="space-y-4">
              <div>
                <Label>Tätigkeitsname</Label>
                <p className="mt-1 font-medium">{selectedActivity.name}</p>
              </div>
              <div>
                <Label>Beschreibung</Label>
                <p className="mt-1 text-sm">{selectedActivity.description || "Keine Beschreibung"}</p>
              </div>
              <div>
                <Label>Geschätzte Stundenzahl</Label>
                <p className="mt-1">{selectedActivity.hourly_rate || 0} Stunden</p>
              </div>
              <div>
                <Label>Antragsteller</Label>
                <p className="mt-1">
                  {selectedActivity.profiles ? 
                    `${selectedActivity.profiles.first_name} ${selectedActivity.profiles.last_name}` : 
                    'Unbekannt'
                  }
                </p>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>
              Schließen
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Tätigkeit bearbeiten und genehmigen</DialogTitle>
            <DialogDescription>
              Bearbeiten Sie die Tätigkeitsdetails vor der Genehmigung. Die Tätigkeit wird automatisch dem Antragsteller zugeordnet.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit_name">Tätigkeitsname</Label>
              <Input
                id="edit_name"
                value={editForm.name}
                onChange={(e) => setEditForm({ ...editForm, name: e.target.value })}
              />
            </div>
            <div>
              <Label htmlFor="edit_description">Beschreibung</Label>
              <Textarea
                id="edit_description"
                value={editForm.description}
                onChange={(e) => setEditForm({ ...editForm, description: e.target.value })}
                rows={3}
              />
            </div>
            <div>
              <Label htmlFor="edit_hourly_rate">Stundenzahl</Label>
              <Input
                id="edit_hourly_rate"
                type="number"
                step="0.25"
                min="0.25"
                value={editForm.hourly_rate}
                onChange={(e) => setEditForm({ ...editForm, hourly_rate: e.target.value })}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Abbrechen
            </Button>
            <Button onClick={handleEditAndApprove} disabled={isProcessing}>
              {isProcessing ? "Wird gespeichert..." : "Bearbeiten & Genehmigen"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}