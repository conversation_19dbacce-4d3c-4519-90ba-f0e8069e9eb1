import { useState, useEffect, useRef } from "react";
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Euro, Plus, Edit, Trash2, Check, X, ChevronDown } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { FeeCategoryManager } from "@/components/admin/FeeCategoryManager";

interface FeeType {
  id: string;
  name: string;
  description: string | null;
  amount: number;
  currency: string;
  billing_cycle: string;
  category: string;
  is_active: boolean;
  requires_membership: boolean;
  age_restrictions: any;
  validity_period_days: number | null;
  membership_category: string | null;
  membership_type_id: string | null;
  created_at: string;
  updated_at: string;
}

interface MembershipType {
  id: string;
  value: string;
  display_name: string;
  description: string | null;
  is_active: boolean;
}

const BILLING_CYCLES = [
  { value: "one_time", label: "Einmalig" },
  { value: "monthly", label: "Monatlich" },
  { value: "quarterly", label: "Vierteljährlich" },
  { value: "semi_annual", label: "Halbjährlich" },
  { value: "annual", label: "Jährlich" }
];

const CATEGORIES = [
  { value: "membership", label: "Mitgliedschaft" },
  { value: "course", label: "Kurs/Training" },
  { value: "event", label: "Event/Turnier" },
  { value: "other", label: "Sonstiges" }
];


export function FeeManagement() {
  const [feeTypes, setFeeTypes] = useState<FeeType[]>([]);
  const [membershipTypes, setMembershipTypes] = useState<MembershipType[]>([]);
  const [categories, setCategories] = useState<{ id: string; value: string; display_name: string; is_active: boolean }[]>([]);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    amount: "",
    currency: "EUR",
    billing_cycle: "one_time",
    category: "membership",
    membership_category: "",
    membership_type_id: "none",
    is_active: true,
    requires_membership: false,
    validity_period_days: "",
    min_age: "",
    max_age: ""
  });
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const formRef = useRef<HTMLDivElement>(null);

  const fetchCategories = async () => {
    try {
      const { data, error } = await supabase
        .from("fee_categories")
        .select("id, value, display_name, is_active")
        .eq("is_active", true)
        .order("sort_order", { ascending: true })
        .order("display_name", { ascending: true });
      if (!error) setCategories(data || []);
    } catch (e) {
      console.error("Error fetching fee categories:", e);
    }
  };

  useEffect(() => {
    fetchFeeTypes();
    fetchMembershipTypes();
    fetchCategories();
  }, []);

  const fetchFeeTypes = async () => {
    try {
      const { data, error } = await supabase
        .from("fee_types")
        .select("*")
        .order("category", { ascending: true })
        .order("name", { ascending: true });

      if (error) throw error;
      setFeeTypes(data || []);
    } catch (error) {
      console.error("Error fetching fee types:", error);
      toast({
        title: "Fehler",
        description: "Gebührenarten konnten nicht geladen werden.",
        variant: "destructive",
      });
    }
  };

  const fetchMembershipTypes = async () => {
    try {
      const { data, error } = await supabase
        .from("membership_types")
        .select("*")
        .eq("is_active", true)
        .order("display_name", { ascending: true });

      if (error) throw error;
      setMembershipTypes(data || []);
    } catch (error) {
      console.error("Error fetching membership types:", error);
      toast({
        title: "Fehler",
        description: "Mitgliedschaftsformen konnten nicht geladen werden.",
        variant: "destructive",
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const ageRestrictions = formData.min_age || formData.max_age ? {
        min_age: formData.min_age ? parseInt(formData.min_age) : null,
        max_age: formData.max_age ? parseInt(formData.max_age) : null
      } : null;

      const feeData: any = {
        name: formData.name,
        description: formData.description || null,
        amount: parseFloat(formData.amount),
        currency: formData.currency,
        billing_cycle: formData.billing_cycle,
        category: formData.category,
        category_id: categories.find(c => c.value === formData.category)?.id || null,
        membership_category: formData.membership_category || null,
        membership_type_id: formData.membership_type_id === "none" ? null : formData.membership_type_id || null,
        is_active: formData.is_active,
        requires_membership: formData.requires_membership,
        age_restrictions: ageRestrictions,
        validity_period_days: formData.validity_period_days ? parseInt(formData.validity_period_days) : null
      };

      if (editingId) {
        const { error } = await supabase
          .from("fee_types")
          .update(feeData)
          .eq("id", editingId);

        if (error) throw error;
        toast({ title: "Erfolg", description: "Gebührenart wurde aktualisiert." });
      } else {
        const { error } = await supabase
          .from("fee_types")
          .insert(feeData);

        if (error) throw error;
        toast({ title: "Erfolg", description: "Gebührenart wurde erstellt." });
      }

      resetForm();
      fetchFeeTypes();
    } catch (error) {
      console.error("Error saving fee type:", error);
      toast({
        title: "Fehler",
        description: "Gebührenart konnte nicht gespeichert werden.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleEdit = (feeType: FeeType) => {
    setEditingId(feeType.id);
    setIsFormOpen(true);
    setFormData({
      name: feeType.name,
      description: feeType.description || "",
      amount: feeType.amount.toString(),
      currency: feeType.currency,
      billing_cycle: feeType.billing_cycle,
      category: feeType.category,
      membership_category: feeType.membership_category || "",
      membership_type_id: feeType.membership_type_id || "none",
      is_active: feeType.is_active,
      requires_membership: feeType.requires_membership,
      validity_period_days: feeType.validity_period_days?.toString() || "",
      min_age: feeType.age_restrictions?.min_age?.toString() || "",
      max_age: feeType.age_restrictions?.max_age?.toString() || ""
    });
    
    // Scroll to form with offset and highlight
    setTimeout(() => {
      if (formRef.current) {
        // Calculate position with more offset for better visibility
        const yOffset = -150;
        const element = formRef.current;
        const y = element.getBoundingClientRect().top + window.pageYOffset + yOffset;
        
        // Smooth scroll to position
        window.scrollTo({ top: y, behavior: 'smooth' });
        
        // Add green glow animation
        element.classList.add('animate-pulse', 'ring-4', 'ring-green-400', 'ring-opacity-50', 'shadow-green-400/50', 'shadow-2xl');
        setTimeout(() => {
          element.classList.remove('animate-pulse', 'ring-4', 'ring-green-400', 'ring-opacity-50', 'shadow-green-400/50', 'shadow-2xl');
        }, 2500);
      }
    }, 100);
  };

  const handleDelete = async (id: string) => {
    if (!confirm("Möchten Sie diese Gebührenart wirklich löschen?")) return;

    try {
      const { error } = await supabase
        .from("fee_types")
        .delete()
        .eq("id", id);

      if (error) throw error;
      toast({ title: "Erfolg", description: "Gebührenart wurde gelöscht." });
      fetchFeeTypes();
    } catch (error) {
      console.error("Error deleting fee type:", error);
      toast({
        title: "Fehler",
        description: "Gebührenart konnte nicht gelöscht werden.",
        variant: "destructive",
      });
    }
  };

  const resetForm = () => {
    setEditingId(null);
    setIsFormOpen(false);
    setFormData({
      name: "",
      description: "",
      amount: "",
      currency: "EUR",
      billing_cycle: "one_time",
      category: "membership",
      membership_category: "",
      membership_type_id: "none",
      is_active: true,
      requires_membership: false,
      validity_period_days: "",
      min_age: "",
      max_age: ""
    });
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case "membership": return "bg-blue-100 text-blue-800";
      case "course": return "bg-green-100 text-green-800";
      case "event": return "bg-purple-100 text-purple-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="space-y-6">
      <Collapsible open={isFormOpen} onOpenChange={setIsFormOpen}>
        <Card ref={formRef} className="transition-all duration-300">
          <CollapsibleTrigger asChild>
            <CardHeader className="cursor-pointer hover:bg-muted/50">
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Euro className="h-5 w-5" />
                  {editingId ? "Gebührenart bearbeiten" : "Neue Gebührenart erstellen"}
                </div>
                <ChevronDown className={`h-5 w-5 transition-transform duration-200 ${isFormOpen ? 'rotate-180' : ''}`} />
              </CardTitle>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent>
              <form onSubmit={handleSubmit} className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Name*</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    placeholder="z.B. Jahresmitgliedschaft Erwachsene"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="amount">Betrag (€)*</Label>
                  <Input
                    id="amount"
                    type="number"
                    step="0.01"
                    value={formData.amount}
                    onChange={(e) => setFormData({ ...formData, amount: e.target.value })}
                    placeholder="180.00"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="billing_cycle">Abrechnungszyklus</Label>
                  <Select value={formData.billing_cycle} onValueChange={(value) => setFormData({ ...formData, billing_cycle: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {BILLING_CYCLES.map((cycle) => (
                        <SelectItem key={cycle.value} value={cycle.value}>
                          {cycle.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="category">Kategorie</Label>
                  <div className="flex items-center gap-2">
                    <Select value={formData.category} onValueChange={(value) => setFormData({ ...formData, category: value })}>
                      <SelectTrigger>
                        <SelectValue placeholder="Kategorie wählen" />
                      </SelectTrigger>
                      <SelectContent className="z-50 bg-background">
                        {categories.map((cat) => (
                          <SelectItem key={cat.id} value={cat.value}>
                            {cat.display_name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FeeCategoryManager onChanged={fetchCategories} />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="membership_type_id">Mitgliedschaftsform</Label>
                  <Select value={formData.membership_type_id} onValueChange={(value) => setFormData({ ...formData, membership_type_id: value === "none" ? "" : value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Mitgliedschaftsform wählen" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">Keine spezifische Zuordnung</SelectItem>
                      {membershipTypes.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.display_name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="min_age">Mindestalter</Label>
                  <Input
                    id="min_age"
                    type="number"
                    value={formData.min_age}
                    onChange={(e) => setFormData({ ...formData, min_age: e.target.value })}
                    placeholder="z.B. 18"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="max_age">Höchstalter</Label>
                  <Input
                    id="max_age"
                    type="number"
                    value={formData.max_age}
                    onChange={(e) => setFormData({ ...formData, max_age: e.target.value })}
                    placeholder="z.B. 65"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="validity_period_days">Gültigkeitsdauer (Tage)</Label>
                  <Input
                    id="validity_period_days"
                    type="number"
                    value={formData.validity_period_days}
                    onChange={(e) => setFormData({ ...formData, validity_period_days: e.target.value })}
                    placeholder="365 für ein Jahr, leer für unbegrenzt"
                  />
                </div>

                <div className="md:col-span-2 space-y-2">
                  <Label htmlFor="description">Beschreibung</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    placeholder="Zusätzliche Informationen zur Gebührenart"
                    rows={3}
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="is_active"
                    checked={formData.is_active}
                    onCheckedChange={(checked) => setFormData({ ...formData, is_active: checked })}
                  />
                  <Label htmlFor="is_active">Aktiv</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="requires_membership"
                    checked={formData.requires_membership}
                    onCheckedChange={(checked) => setFormData({ ...formData, requires_membership: checked })}
                  />
                  <Label htmlFor="requires_membership">Mitgliedschaft erforderlich</Label>
                </div>

                <div className="md:col-span-2 flex gap-2">
                  <Button type="submit" disabled={isLoading}>
                    {editingId ? <Check className="h-4 w-4 mr-2" /> : <Plus className="h-4 w-4 mr-2" />}
                    {editingId ? "Aktualisieren" : "Erstellen"}
                  </Button>
                  {editingId && (
                    <Button type="button" variant="outline" onClick={resetForm}>
                      <X className="h-4 w-4 mr-2" />
                      Abbrechen
                    </Button>
                  )}
                </div>
              </form>
            </CardContent>
          </CollapsibleContent>
        </Card>
      </Collapsible>

      <Card>
        <CardHeader>
          <CardTitle>Gebührenarten</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4">
            {feeTypes.map((feeType) => (
              <div key={feeType.id} className="border rounded-lg p-4 space-y-2">
                <div className="flex items-start justify-between">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <h3 className="font-semibold">{feeType.name}</h3>
                      <Badge className={getCategoryColor(feeType.category)}>
                        {categories.find((cat) => cat.value === feeType.category)?.display_name || feeType.category}
                      </Badge>
                      {feeType.membership_type_id && (
                        <Badge variant="outline" className="bg-yellow-50 text-yellow-800 border-yellow-200">
                          {membershipTypes.find(type => type.value === feeType.membership_type_id)?.display_name}
                        </Badge>
                      )}
                      {!feeType.is_active && (
                        <Badge variant="secondary">Inaktiv</Badge>
                      )}
                    </div>
                    {feeType.description && (
                      <p className="text-sm text-muted-foreground">{feeType.description}</p>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="font-bold text-lg">
                      {feeType.amount.toFixed(2)} {feeType.currency}
                    </span>
                    <div className="flex gap-1">
                      <Button size="sm" variant="outline" onClick={() => handleEdit(feeType)}>
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button size="sm" variant="outline" onClick={() => handleDelete(feeType.id)}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
                <div className="flex flex-wrap gap-2 text-sm text-muted-foreground">
                  <span>{BILLING_CYCLES.find(cycle => cycle.value === feeType.billing_cycle)?.label}</span>
                  {feeType.age_restrictions && (
                    <span>
                      • Alter: {feeType.age_restrictions.min_age || 0} - {feeType.age_restrictions.max_age || "∞"}
                    </span>
                  )}
                  {feeType.validity_period_days && (
                    <span>• Gültig: {feeType.validity_period_days} Tage</span>
                  )}
                  {feeType.requires_membership && (
                    <span>• Mitgliedschaft erforderlich</span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}