import { useState } from "react";
import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { MessageSquare, Mail, Bell, Users, BarChart3, Sparkles, Send, Clock, Target } from "lucide-react";
import CampaignCreator from "./communication/CampaignCreator";
import SmartGroups from "./communication/SmartGroups";
import MessageTemplates from "./communication/MessageTemplates";
import LiveFeed from "./communication/LiveFeed";
import AIAssistant from "./communication/AIAssistant";
import PushNotificationDialog from "./communication/PushNotificationDialog";
import { useCommunication } from "@/hooks/useCommunication";
import { useClub } from "@/contexts/ClubContext";
import { getDemoCommunicationStats } from '@/services/DemoDataService';

const CommunicationCenter = () => {
  const [activeTab, setActiveTab] = useState("overview");
  const { currentClubId } = useClub();
  const { stats, isLoading } = useCommunication(currentClubId);

  // 🎯 SMART DATA RESOLUTION: Use real stats if available, fallback to demo data
  const demoStats = getDemoCommunicationStats();
  const displayStats = stats || demoStats;

  const quickStats = [
    { 
      label: "Versendete E-Mails", 
      value: (stats?.total_emails_sent || demoStats?.emails_sent || 0).toString(), 
      icon: Mail, 
      color: "bg-primary" 
    },
    { 
      label: "Push-Nachrichten", 
      value: (stats?.total_push_sent || demoStats?.push_notifications || 0).toString(), 
      icon: Bell, 
      color: "bg-secondary" 
    },
    { 
      label: "Kampagnen", 
      value: (stats?.total_campaigns || demoStats?.campaigns || 0).toString(), 
      icon: Send, 
      color: "bg-accent" 
    },
    { 
      label: "Engagement", 
      value: stats?.avg_engagement ? `${Math.round(stats.avg_engagement)}%` : 
             demoStats?.engagement_rate ? `${Math.round(demoStats.engagement_rate)}%` : "N/A", 
      icon: BarChart3, 
      color: "bg-muted" 
    }
  ];

  const recentCampaigns = [
    { id: 1, title: "Turnier Anmeldung", status: "sent", sent: "2h ago", engagement: "92%" },
    { id: 2, title: "Winterpause Info", status: "scheduled", sent: "in 1 day", engagement: "-" },
    { id: 3, title: "Neue Platzregeln", status: "draft", sent: "-", engagement: "-" }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Kommunikation</h1>
          <p className="text-muted-foreground">
            Zentrale Verwaltung für E-Mails, Push-Nachrichten und Mitgliederkommunikation
          </p>
        </div>
        <div className="flex gap-2">
          <PushNotificationDialog 
            trigger={
              <Button variant="outline" className="gap-2">
                <Bell className="h-4 w-4" />
                Push senden
              </Button>
            }
            clubId={currentClubId}
          />
          <Button variant="outline" className="gap-2">
            <Sparkles className="h-4 w-4" />
            AI Assistant
          </Button>
          <Button className="gap-2" onClick={() => setActiveTab("campaigns")}>
            <Send className="h-4 w-4" />
            Neue Kampagne
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {quickStats.map((stat, index) => (
          <Card key={index} className="relative overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">{stat.label}</p>
                  <p className="text-2xl font-bold">{stat.value}</p>
                </div>
                <div className={`p-3 rounded-full ${stat.color}`}>
                  <stat.icon className="h-5 w-5 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Main Interface */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview" className="gap-2">
            <BarChart3 className="h-4 w-4" />
            Übersicht
          </TabsTrigger>
          <TabsTrigger value="campaigns" className="gap-2">
            <Send className="h-4 w-4" />
            Kampagnen
          </TabsTrigger>
          <TabsTrigger value="groups" className="gap-2">
            <Target className="h-4 w-4" />
            Smart Groups
          </TabsTrigger>
          <TabsTrigger value="templates" className="gap-2">
            <MessageSquare className="h-4 w-4" />
            Templates
          </TabsTrigger>
          <TabsTrigger value="live" className="gap-2">
            <Bell className="h-4 w-4" />
            Live Feed
          </TabsTrigger>
          <TabsTrigger value="ai" className="gap-2">
            <Sparkles className="h-4 w-4" />
            AI Studio
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Recent Campaigns */}
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Aktuelle Kampagnen
                </CardTitle>
                <CardDescription>
                  Status und Performance Ihrer letzten Kommunikation
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentCampaigns.map((campaign) => (
                    <div key={campaign.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <h4 className="font-medium">{campaign.title}</h4>
                        <p className="text-sm text-muted-foreground">{campaign.sent}</p>
                      </div>
                      <div className="flex items-center gap-3">
                        <Badge variant={
                          campaign.status === 'sent' ? 'default' :
                          campaign.status === 'scheduled' ? 'secondary' : 'outline'
                        }>
                          {campaign.status}
                        </Badge>
                        {campaign.engagement !== '-' && (
                          <span className="text-sm font-medium">{campaign.engagement}</span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>One-Touch Actions</CardTitle>
                <CardDescription>
                  Häufig verwendete Aktionen mit einem Klick
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button className="w-full justify-start gap-2" variant="outline">
                  <Mail className="h-4 w-4" />
                  Turnier Ankündigung
                </Button>
                <Button className="w-full justify-start gap-2" variant="outline">
                  <Bell className="h-4 w-4" />
                  Wichtige Mitteilung
                </Button>
                <Button className="w-full justify-start gap-2" variant="outline">
                  <Users className="h-4 w-4" />
                  Mitglieder Update
                </Button>
                <Button className="w-full justify-start gap-2" variant="outline">
                  <MessageSquare className="h-4 w-4" />
                  Chat mit Team
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="campaigns">
          <CampaignCreator />
        </TabsContent>

        <TabsContent value="groups">
          <SmartGroups />
        </TabsContent>

        <TabsContent value="templates">
          <MessageTemplates />
        </TabsContent>

        <TabsContent value="live">
          <LiveFeed />
        </TabsContent>

        <TabsContent value="ai">
          <AIAssistant />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default CommunicationCenter;