import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Plus, Edit, Trash2, Clock, <PERSON><PERSON>hart3, Upload, <PERSON>rkles, ChevronDown } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import WorkServiceDashboard from "./WorkServiceDashboard";
import WorkServiceConfiguration from "./WorkServiceConfiguration";
import WorkServiceMemberManagement from "./WorkServiceMemberManagement";
import WorkActivityAiAssistant from "./WorkActivityAiAssistant";
import PendingActivitySuggestions from "./PendingActivitySuggestions";

interface Activity {
  id: string;
  name: string;
  description?: string;
  hourly_rate?: number;
}


interface Member {
  id: string;
  first_name: string;
  last_name: string;
}

const WorkServiceManagement = () => {
  const [activities, setActivities] = useState<Activity[]>([]);
  const [members, setMembers] = useState<Member[]>([]);
  const [pendingSuggestionsCount, setPendingSuggestionsCount] = useState(0);
  const [isActivityDialogOpen, setIsActivityDialogOpen] = useState(false);
  const [isBulkActivityDialogOpen, setIsBulkActivityDialogOpen] = useState(false);
  const [isAiAssistantOpen, setIsAiAssistantOpen] = useState(false);
  const [editingActivity, setEditingActivity] = useState<Activity | null>(null);
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const [activityForm, setActivityForm] = useState({
    name: "",
    description: "",
    hourly_rate: ""
  });

  const [bulkActivities, setBulkActivities] = useState([
    { name: "", description: "", hourly_rate: "" }
  ]);

  useEffect(() => {
    fetchActivities();
    fetchMembers();
    fetchPendingSuggestionsCount();
  }, []);

  const fetchActivities = async () => {
    try {
      // Get current user's club_id to filter activities by club
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const { data: userMembership } = await supabase
        .from("club_memberships")
        .select("club_id")
        .eq("user_id", user.id)
        .eq("is_active", true)
        .single();

      if (!userMembership?.club_id) return;

      const { data, error } = await supabase
        .from("activities")
        .select("*")
        .eq("club_id", userMembership.club_id)
        .order("name", { ascending: true });

      if (error) throw error;
      setActivities(data || []);
    } catch (error) {
      console.error("Error fetching activities:", error);
      toast({
        title: "Fehler",
        description: "Tätigkeiten konnten nicht geladen werden",
        variant: "destructive",
      });
    }
  };


  const fetchMembers = async () => {
    try {
      const { data, error } = await supabase
        .from("members")
        .select("id, first_name, last_name")
        .order("last_name", { ascending: true });

      if (error) throw error;
      setMembers(data || []);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch members",
        variant: "destructive",
      });
    }
  };

  const fetchPendingSuggestionsCount = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const { data: userMembership } = await supabase
        .from("club_memberships")
        .select("club_id")
        .eq("user_id", user.id)
        .eq("is_active", true)
        .single();

      if (!userMembership?.club_id) return;

      const { count, error } = await supabase
        .from("activities")
        .select("*", { count: 'exact', head: true })
        .eq("club_id", userMembership.club_id)
        .eq("status", "pending");

      if (error) throw error;
      setPendingSuggestionsCount(count || 0);
    } catch (error) {
      console.error("Error fetching pending suggestions count:", error);
    }
  };

  const handleActivitySubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Get current user's club_id
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error("User not authenticated");

      const { data: userMembership } = await supabase
        .from("club_memberships")
        .select("club_id")
        .eq("user_id", user.id)
        .eq("is_active", true)
        .single();

      if (!userMembership?.club_id) throw new Error("No active club membership found");

      const activityData = {
        name: activityForm.name,
        description: activityForm.description || null,
        hourly_rate: activityForm.hourly_rate ? parseFloat(activityForm.hourly_rate) : null,
        club_id: userMembership.club_id
      };

      if (editingActivity) {
        const { error } = await supabase
          .from("activities")
          .update(activityData)
          .eq("id", editingActivity.id);
        
        if (error) throw error;
        
        toast({
          title: "Erfolg",
          description: "Tätigkeit erfolgreich aktualisiert",
        });
      } else {
        const { error } = await supabase
          .from("activities")
          .insert([activityData]);
        
        if (error) throw error;
        
        toast({
          title: "Erfolg",
          description: "Tätigkeit erfolgreich erstellt",
        });
      }

      setIsActivityDialogOpen(false);
      setEditingActivity(null);
      resetActivityForm();
      fetchActivities();
    } catch (error) {
      console.error("Error saving activity:", error);
      toast({
        title: "Fehler",
        description: "Tätigkeit konnte nicht gespeichert werden",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };


  const handleDeleteActivity = async (activityId: string) => {
    if (!confirm("Are you sure you want to delete this activity?")) return;

    try {
      const { error } = await supabase
        .from("activities")
        .delete()
        .eq("id", activityId);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Activity deleted successfully",
      });
      
      fetchActivities();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete activity",
        variant: "destructive",
      });
    }
  };

  const handleEditActivity = (activity: Activity) => {
    setEditingActivity(activity);
    setActivityForm({
      name: activity.name,
      description: activity.description || "",
      hourly_rate: activity.hourly_rate?.toString() || ""
    });
    setIsActivityDialogOpen(true);
  };

  const resetActivityForm = () => {
    setActivityForm({
      name: "",
      description: "",
      hourly_rate: ""
    });
  };

  const openCreateActivityDialog = () => {
    setEditingActivity(null);
    resetActivityForm();
    setIsActivityDialogOpen(true);
  };


  const addBulkActivityRow = () => {
    setBulkActivities([...bulkActivities, { name: "", description: "", hourly_rate: "" }]);
  };

  const removeBulkActivityRow = (index: number) => {
    if (bulkActivities.length > 1) {
      setBulkActivities(bulkActivities.filter((_, i) => i !== index));
    }
  };

  const updateBulkActivity = (index: number, field: string, value: string) => {
    const updated = [...bulkActivities];
    updated[index] = { ...updated[index], [field]: value };
    setBulkActivities(updated);
  };

  const handleBulkActivitySubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Get current user's club_id
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error("User not authenticated");

      const { data: userMembership } = await supabase
        .from("club_memberships")
        .select("club_id")
        .eq("user_id", user.id)
        .eq("is_active", true)
        .single();

      if (!userMembership?.club_id) throw new Error("No active club membership found");

      const validActivities = bulkActivities.filter(activity => activity.name.trim() !== "");
      
      if (validActivities.length === 0) {
        toast({
          title: "Fehler",
          description: "Mindestens eine Tätigkeit mit Namen muss eingegeben werden",
          variant: "destructive",
        });
        return;
      }

      const activityData = validActivities.map(activity => ({
        name: activity.name.trim(),
        description: activity.description.trim() || null,
        hourly_rate: activity.hourly_rate ? parseFloat(activity.hourly_rate) : null,
        club_id: userMembership.club_id
      }));

      const { error } = await supabase
        .from("activities")
        .insert(activityData);
      
      if (error) throw error;
      
      toast({
        title: "Erfolg",
        description: `${validActivities.length} Tätigkeiten erfolgreich erstellt`,
      });

      setIsBulkActivityDialogOpen(false);
      setBulkActivities([{ name: "", description: "", hourly_rate: "" }]);
      fetchActivities();
    } catch (error) {
      console.error("Error creating bulk activities:", error);
      toast({
        title: "Fehler",
        description: "Tätigkeiten konnten nicht erstellt werden",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Tabs defaultValue="dashboard" className="space-y-4">
        <TabsList>
          <TabsTrigger value="dashboard">Übersicht</TabsTrigger>
          <TabsTrigger value="configuration">Konfiguration</TabsTrigger>
          <TabsTrigger value="members">Mitglieder verwalten</TabsTrigger>
          <TabsTrigger value="activities">Tätigkeiten</TabsTrigger>
          <TabsTrigger value="suggestions" className="flex items-center gap-2">
            Vorgeschlagene Tätigkeiten
            {pendingSuggestionsCount > 0 && (
              <Badge variant="destructive" className="h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs">
                {pendingSuggestionsCount}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="reports">Berichte</TabsTrigger>
        </TabsList>

        <TabsContent value="dashboard" className="space-y-4">
          <WorkServiceDashboard />
        </TabsContent>

        <TabsContent value="configuration" className="space-y-4">
          <WorkServiceConfiguration />
        </TabsContent>

        <TabsContent value="members" className="space-y-4">
          <WorkServiceMemberManagement />
        </TabsContent>

        <TabsContent value="suggestions" className="space-y-4">
          <PendingActivitySuggestions onSuggestionsChange={fetchPendingSuggestionsCount} />
        </TabsContent>


        <TabsContent value="activities" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold">Tätigkeitsdefinitionen</h3>
            <div className="flex gap-2">
              <Button 
                onClick={() => setIsAiAssistantOpen(true)}
                className="relative overflow-hidden bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-600/90 hover:to-blue-600/90 text-white border-0 shadow-lg transition-all duration-300 hover:shadow-xl hover:shadow-purple-500/25"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 translate-x-[-100%] hover:translate-x-[100%] transition-transform duration-700 ease-in-out"></div>
                <div className="relative flex items-center gap-2">
                  <Sparkles className="h-4 w-4" />
                  <span className="font-medium">KI-Assistent</span>
                </div>
              </Button>
              
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button className="flex items-center gap-2">
                    <Plus className="h-4 w-4" />
                    Tätigkeiten hinzufügen
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={openCreateActivityDialog}>
                    <Plus className="h-4 w-4 mr-2" />
                    Einzelne Tätigkeit
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setIsBulkActivityDialogOpen(true)}>
                    <Upload className="h-4 w-4 mr-2" />
                    Mehrere Tätigkeiten
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              <Dialog open={isActivityDialogOpen} onOpenChange={setIsActivityDialogOpen}>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>
                      {editingActivity ? "Tätigkeit bearbeiten" : "Neue Tätigkeit hinzufügen"}
                    </DialogTitle>
                    <DialogDescription>
                      Definieren Sie Arbeitstätigkeiten für Vereinsarbeit und Platzpflege. Die angerechnete Zeit bestimmt, wie viele Stunden pro geleisteter Arbeitsstunde gutgeschrieben werden.
                    </DialogDescription>
                  </DialogHeader>
                  
                  <form onSubmit={handleActivitySubmit} className="space-y-4">
                    <div>
                      <Label htmlFor="activity_name">Tätigkeitsname</Label>
                      <Input
                        id="activity_name"
                        value={activityForm.name}
                        onChange={(e) => setActivityForm({ ...activityForm, name: e.target.value })}
                        required
                      />
                    </div>

                    <div>
                      <Label htmlFor="activity_description">Beschreibung</Label>
                      <Textarea
                        id="activity_description"
                        value={activityForm.description}
                        onChange={(e) => setActivityForm({ ...activityForm, description: e.target.value })}
                        rows={3}
                      />
                    </div>

                    <div>
                      <Label htmlFor="hourly_rate">Angerechnete Zeit pro Stunde (in Stunden)</Label>
                      <Input
                        id="hourly_rate"
                        type="number"
                        step="0.25"
                        min="0"
                        value={activityForm.hourly_rate}
                        onChange={(e) => setActivityForm({ ...activityForm, hourly_rate: e.target.value })}
                        placeholder="z.B. 1.5 für 1,5 Stunden oder 0.5 für 30 Minuten"
                      />
                      <p className="text-sm text-muted-foreground mt-1">
                        Wie viele Stunden werden pro geleisteter Arbeitsstunde angerechnet? (Dezimalwerte möglich: 0.25 = 15 Min, 0.5 = 30 Min, 1.0 = 1 Std)
                      </p>
                    </div>

                    <DialogFooter>
                      <Button type="button" variant="outline" onClick={() => setIsActivityDialogOpen(false)}>
                        Abbrechen
                      </Button>
                      <Button type="submit" disabled={loading}>
                        {loading ? "Speichern..." : editingActivity ? "Aktualisieren" : "Erstellen"}
                      </Button>
                    </DialogFooter>
                  </form>
                </DialogContent>
              </Dialog>
              
              <Dialog open={isBulkActivityDialogOpen} onOpenChange={setIsBulkActivityDialogOpen}>
                <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle>Mehrere Tätigkeiten hinzufügen</DialogTitle>
                    <DialogDescription>
                      Erstellen Sie mehrere Tätigkeiten auf einmal. Geben Sie für jede Tätigkeit mindestens einen Namen an.
                    </DialogDescription>
                  </DialogHeader>
                  
                  <form onSubmit={handleBulkActivitySubmit} className="space-y-4">
                    <div className="space-y-3">
                      {bulkActivities.map((activity, index) => (
                        <div key={index} className="grid grid-cols-12 gap-3 items-start p-3 border rounded-lg">
                          <div className="col-span-3">
                            <Label htmlFor={`bulk_name_${index}`} className="text-xs">
                              Tätigkeitsname *
                            </Label>
                            <Input
                              id={`bulk_name_${index}`}
                              value={activity.name}
                              onChange={(e) => updateBulkActivity(index, "name", e.target.value)}
                              placeholder="z.B. Platzpflege"
                              required
                            />
                          </div>
                          
                          <div className="col-span-4">
                            <Label htmlFor={`bulk_description_${index}`} className="text-xs">
                              Beschreibung
                            </Label>
                            <Input
                              id={`bulk_description_${index}`}
                              value={activity.description}
                              onChange={(e) => updateBulkActivity(index, "description", e.target.value)}
                              placeholder="z.B. Tennisplatz reinigen und pflegen"
                            />
                          </div>
                          
                          <div className="col-span-3">
                            <Label htmlFor={`bulk_rate_${index}`} className="text-xs">
                              Zeit pro Stunde
                            </Label>
                            <Input
                              id={`bulk_rate_${index}`}
                              type="number"
                              step="0.25"
                              min="0"
                              value={activity.hourly_rate}
                              onChange={(e) => updateBulkActivity(index, "hourly_rate", e.target.value)}
                              placeholder="1.0"
                            />
                          </div>
                          
                          <div className="col-span-2 flex items-end gap-1">
                            <Button
                              type="button"
                              variant="outline"
                              size="icon"
                              onClick={addBulkActivityRow}
                              className="h-9"
                            >
                              <Plus className="h-4 w-4" />
                            </Button>
                            {bulkActivities.length > 1 && (
                              <Button
                                type="button"
                                variant="outline"
                                size="icon"
                                onClick={() => removeBulkActivityRow(index)}
                                className="h-9"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>

                    <DialogFooter>
                      <Button 
                        type="button" 
                        variant="outline" 
                        onClick={() => setIsBulkActivityDialogOpen(false)}
                      >
                        Abbrechen
                      </Button>
                      <Button type="submit" disabled={loading}>
                        {loading ? "Erstelle..." : `${bulkActivities.filter(a => a.name.trim()).length} Tätigkeiten erstellen`}
                      </Button>
                    </DialogFooter>
                  </form>
                </DialogContent>
              </Dialog>
            </div>
          </div>

          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Beschreibung</TableHead>
                    <TableHead>Angerechnete Zeit/Std</TableHead>
                    <TableHead className="text-right">Aktionen</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {activities.map((activity) => (
                    <TableRow key={activity.id}>
                      <TableCell className="font-medium">{activity.name}</TableCell>
                      <TableCell>{activity.description || "—"}</TableCell>
                      <TableCell>
                        {activity.hourly_rate ? 
                          `${activity.hourly_rate} Std` : 
                          "1,0 Std (Standard)"
                        }
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleEditActivity(activity)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleDeleteActivity(activity.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>


        <TabsContent value="reports" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Arbeitsstunden Zusammenfassung
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Detaillierte Berichte und Analysen zu Arbeitsstunden sind hier verfügbar.
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* KI-Assistent Dialog */}
      <WorkActivityAiAssistant
        isOpen={isAiAssistantOpen}
        onOpenChange={setIsAiAssistantOpen}
        onActivitiesCreated={fetchActivities}
      />
    </div>
  );
};

export default WorkServiceManagement;