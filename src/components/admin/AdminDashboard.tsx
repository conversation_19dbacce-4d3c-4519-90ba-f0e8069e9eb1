import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Users, Calendar, Activity, Settings, TrendingUp, Trophy, ChevronRight, TestTube, Clock, Target } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useClub } from "@/contexts/ClubContext";
import { useClubTimezone } from "@/hooks/useClubTimezone";
import { formatDateInClubTZ } from "@/lib/timezone-utils";
import { getTodayInTimezone } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { useMockData } from "@/contexts/MockDataContext";

interface DashboardStats {
  totalMembers: number;
  upcomingBookings: Array<{
    id: string;
    court_number: number;
    player_name: string;
    start_time: string;
    booking_date: string;
  }>;
  recentActivity: Array<{
    id: string;
    action: string;
    timestamp: string;
    user: string;
  }>;
  workService?: {
    enabled: boolean;
    totalHours: number;
    annualTarget: number;
    activeMembers: number;
    totalMembers: number;
    progressPercentage: number;
  };
}

const AdminDashboard = () => {
  const navigate = useNavigate();
  const { currentClubId, isLoading: clubLoading } = useClub();
  const { showMockData } = useMockData();
  const [stats, setStats] = useState<DashboardStats>({
    totalMembers: 0,
    upcomingBookings: [],
    recentActivity: []
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (currentClubId) {
      fetchDashboardData();
    }
  }, [currentClubId, showMockData]);

  const fetchDashboardData = async () => {
    try {
      // Generate mock data for demo purposes
      const generateMockData = () => {
        const mockActivity = [
          { id: '1', action: 'Neues Mitglied registriert', timestamp: new Date().toISOString(), user: 'System' },
          { id: '2', action: 'Platz 3 gewartet', timestamp: new Date().toISOString(), user: 'Admin' },
          { id: '3', action: 'Buchung storniert', timestamp: new Date().toISOString(), user: 'Mitglied' },
          { id: '4', action: 'System-Backup erstellt', timestamp: new Date().toISOString(), user: 'System' },
          { id: '5', action: 'Mitgliedsbeitrag aktualisiert', timestamp: new Date().toISOString(), user: 'Admin' }
        ];

        const mockBookings = [
          { id: '1', court_number: 1, player_name: 'Max Mustermann', start_time: '10:00', booking_date: '2025-08-08' },
          { id: '2', court_number: 2, player_name: 'Anna Schmidt', start_time: '14:30', booking_date: '2025-08-08' },
          { id: '3', court_number: 3, player_name: 'Peter Weber', start_time: '16:00', booking_date: '2025-08-09' }
        ];

        return {
          totalMembers: 42,
          upcomingBookings: mockBookings,
          recentActivity: mockActivity,
          workService: {
            enabled: true,
            totalHours: 28.5,
            annualTarget: 40,
            activeMembers: 12,
            totalMembers: 42,
            progressPercentage: 71.3
          }
        };
      };

      // If mock data is enabled, use mock data instead of real data
      if (showMockData) {
        setStats(generateMockData());
        setLoading(false);
        return;
      }

      if (!currentClubId) return;

      // Fetch total members count for current club
      const { count: memberCount } = await supabase
        .from('club_memberships')
        .select('*', { count: 'exact', head: true })
        .eq('club_id', currentClubId)
        .eq('is_active', true);

      // Fetch upcoming bookings with court information for current club
      const { data: bookings } = await supabase
        .from('bookings')
        .select(`
          id,
          player_name,
          start_time,
          booking_date,
          courts!inner(number)
        `)
        .eq('club_id', currentClubId)  // ⭐ CLUB FILTER
        .gte('booking_date', getTodayInTimezone(timezone).toISOString().split('T')[0])
        .order('booking_date', { ascending: true })
        .order('start_time', { ascending: true })
        .limit(5);

      const formattedBookings = bookings?.map(booking => ({
        id: booking.id,
        court_number: (booking.courts as any).number,
        player_name: booking.player_name,
        start_time: booking.start_time,
        booking_date: booking.booking_date
      })) || [];

      // Fetch work service data
      let workServiceData = undefined;
      
      // Check if work service is enabled for this club
      const { data: clubData } = await supabase
        .from('clubs')
        .select('work_service_enabled, default_annual_work_hours')
        .eq('id', currentClubId)
        .single();

      if (clubData?.work_service_enabled) {
        const currentYear = new Date().getFullYear();
        
        // Get work service targets and completed hours
        const { data: workTargets } = await supabase
          .from('member_work_service_targets')
          .select('completed_hours, target_hours')
          .eq('club_id', currentClubId)
          .eq('year', currentYear);

        // Calculate totals
        const totalHours = workTargets?.reduce((sum, target) => sum + (target.completed_hours || 0), 0) || 0;
        const activeMembers = workTargets?.filter(target => (target.completed_hours || 0) > 0).length || 0;
        const annualTarget = clubData.default_annual_work_hours || 0;
        const progressPercentage = annualTarget > 0 ? (totalHours / (memberCount || 1) / annualTarget) * 100 : 0;

        workServiceData = {
          enabled: true,
          totalHours,
          annualTarget,
          activeMembers,
          totalMembers: memberCount || 0,
          progressPercentage: Math.min(progressPercentage, 100)
        };
      }


      // Mock data for recent activity
      const mockRecentActivity = [
        { id: '1', action: 'Neues Mitglied registriert', timestamp: new Date().toISOString(), user: 'System' },
        { id: '2', action: 'Platz 3 gewartet', timestamp: new Date().toISOString(), user: 'Admin' },
        { id: '3', action: 'Buchung storniert', timestamp: new Date().toISOString(), user: 'Mitglied' },
        { id: '4', action: 'System-Backup erstellt', timestamp: new Date().toISOString(), user: 'System' },
        { id: '5', action: 'Mitgliedsbeitrag aktualisiert', timestamp: new Date().toISOString(), user: 'Admin' }
      ];

      setStats({
        totalMembers: memberCount || 0,
        upcomingBookings: formattedBookings,
        recentActivity: mockRecentActivity,
        workService: workServiceData
      });

    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  // CRITICAL: Use club timezone-aware formatting
  const { timezone } = useClubTimezone();
  
  const formatTime = (timeString: string) => {
    return timeString.slice(0, 5); // Format HH:MM - already in correct format
  };

  const formatDate = (dateString: string) => {
    return formatDateInClubTZ(dateString, timezone, {
      day: '2-digit',
      month: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="space-y-2">
                <div className="h-4 bg-muted rounded w-3/4"></div>
                <div className="h-8 bg-muted rounded w-1/2"></div>
              </CardHeader>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="bg-card border border-border rounded-lg p-6 shadow">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-foreground">Admin Dashboard</h1>
            <p className="text-muted-foreground">Übersicht über alle wichtigen Vereinsdaten</p>
          </div>
          {showMockData && (
            <Badge variant="outline" className="bg-amber-50 border-amber-200 text-amber-800">
              <TestTube className="h-3 w-3 mr-1" />
              Demo-Modus
            </Badge>
          )}
        </div>
      </div>

      {/* Upcoming Home Games - Only show in mock mode */}
      {showMockData && (
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Trophy className="h-5 w-5 text-tennis-green" />
                <CardTitle className="text-lg">Anstehende Heimspiele</CardTitle>
              </div>
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => navigate('/admin/management?tab=schedule')}
                className="text-tennis-green hover:text-tennis-green/80"
              >
                Alle anzeigen
                <ChevronRight className="h-4 w-4 ml-1" />
              </Button>
            </div>
            <CardDescription>Nächste Mannschaftsspiele zu Hause</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-3">
              <div className="flex items-center justify-between p-3 border rounded-lg bg-card">
                <div>
                  <div className="font-medium text-sm">Herren I vs. TC Musterstadt</div>
                  <div className="text-xs text-muted-foreground">So, 15. Sep • 10:00</div>
                </div>
                <div className="flex items-center gap-1">
                  <Badge variant="outline" className="text-xs">Platz 1-2</Badge>
                </div>
              </div>
              
              <div className="flex items-center justify-between p-3 border rounded-lg bg-card">
                <div>
                  <div className="font-medium text-sm">Damen I vs. SV Beispielort</div>
                  <div className="text-xs text-muted-foreground">Sa, 21. Sep • 14:00</div>
                </div>
                <div className="flex items-center gap-1">
                  <Badge variant="outline" className="text-xs">Platz 3-4</Badge>
                </div>
              </div>
              
              <div className="flex items-center justify-between p-3 border rounded-lg bg-card">
                <div>
                  <div className="font-medium text-sm">Damen Ü40 vs. TC Stadtpark</div>
                  <div className="text-xs text-muted-foreground">So, 29. Sep • 11:00</div>
                </div>
                <div className="flex items-center gap-1">
                  <Badge variant="outline" className="text-xs">Platz 5-6</Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Gesamtmitglieder</CardTitle>
            <Users className="h-4 w-4 text-tennis-green" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalMembers}</div>
            <p className="text-xs text-muted-foreground">
              <TrendingUp className="inline h-3 w-3 mr-1" />
              Aktive Mitglieder
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Laufende Buchungen</CardTitle>
            <Calendar className="h-4 w-4 text-tennis-green" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.upcomingBookings.length}</div>
            <p className="text-xs text-muted-foreground">Nächste 5 Termine</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System-Aktivität</CardTitle>
            <Activity className="h-4 w-4 text-tennis-green" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.recentActivity.length}</div>
            <p className="text-xs text-muted-foreground">Letzte Aktionen</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Aktive Module</CardTitle>
            <Settings className="h-4 w-4 text-tennis-green" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">4</div>
            <p className="text-xs text-muted-foreground">Module verfügbar</p>
          </CardContent>
        </Card>
      </div>

      {/* Work Service Overview */}
      {stats.workService?.enabled && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Arbeitsdienst Übersicht
            </CardTitle>
            <CardDescription>Fortschritt und Statistiken zum Arbeitsdienst {new Date().getFullYear()}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* Progress Card */}
              <div className="flex flex-col space-y-2">
                <div className="flex items-center gap-2">
                  <Target className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Jahresziel</span>
                </div>
                <div className="text-2xl font-bold text-primary">
                  {Math.round(stats.workService.progressPercentage)}%
                </div>
                <p className="text-xs text-muted-foreground">
                  Durchschnittlicher Fortschritt
                </p>
              </div>

              {/* Total Hours Card */}
              <div className="flex flex-col space-y-2">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Geleistete Stunden</span>
                </div>
                <div className="text-2xl font-bold">
                  {stats.workService.totalHours}h
                </div>
                <p className="text-xs text-muted-foreground">
                  Insgesamt absolviert
                </p>
              </div>

              {/* Active Members Card */}
              <div className="flex flex-col space-y-2">
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Aktive Mitglieder</span>
                </div>
                <div className="text-2xl font-bold">
                  {stats.workService.activeMembers}/{stats.workService.totalMembers}
                </div>
                <p className="text-xs text-muted-foreground">
                  {Math.round((stats.workService.activeMembers / stats.workService.totalMembers) * 100)}% haben begonnen
                </p>
              </div>

              {/* Quick Actions */}
              <div className="flex flex-col space-y-2">
                <div className="flex items-center gap-2">
                  <Settings className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Verwaltung</span>
                </div>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => navigate('/admin/work-services')}
                  className="justify-start"
                >
                  <ChevronRight className="h-4 w-4 mr-1" />
                  Arbeitsdienst verwalten
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Detail Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Upcoming Bookings */}
        <Card>
          <CardHeader>
            <CardTitle>Nächste Buchungen</CardTitle>
            <CardDescription>Die nächsten 5 Platzbuchungen</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {stats.upcomingBookings.length > 0 ? (
                stats.upcomingBookings.map((booking) => (
                  <div key={booking.id} className="flex items-center justify-between p-3 bg-card border rounded-lg">
                    <div>
                      <div className="font-medium">Platz {booking.court_number}</div>
                      <div className="text-sm text-muted-foreground">{booking.player_name}</div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium">{formatTime(booking.start_time)}</div>
                      <div className="text-xs text-muted-foreground">{formatDate(booking.booking_date)}</div>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-center text-muted-foreground py-4">Keine anstehenden Buchungen</p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* System Activity */}
        <Card>
          <CardHeader>
            <CardTitle>System-Log</CardTitle>
            <CardDescription>Letzte 5 Systemaktivitäten</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {stats.recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-start space-x-3 p-3 bg-card border rounded-lg">
                  <div className="w-2 h-2 rounded-full bg-tennis-green mt-2 flex-shrink-0"></div>
                  <div className="flex-1">
                    <div className="text-sm font-medium">{activity.action}</div>
                    <div className="text-xs text-muted-foreground">
                      {activity.user} • {new Date(activity.timestamp).toLocaleString('de-DE')}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AdminDashboard;