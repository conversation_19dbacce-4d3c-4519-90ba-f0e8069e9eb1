import { useEffect, useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Trigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Trash2, Plus } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

interface FeeCategory {
  id: string;
  value: string;
  display_name: string;
  is_active: boolean;
  sort_order: number;
}

interface Props {
  onChanged?: () => void;
}

export function FeeCategoryManager({ onChanged }: Props) {
  const [open, setOpen] = useState(false);
  const [categories, setCategories] = useState<FeeCategory[]>([]);
  const [name, setName] = useState("");
  const [saving, setSaving] = useState(false);
  const { toast } = useToast();

  const load = async () => {
    const { data, error } = await supabase
      .from("fee_categories")
      .select("id, value, display_name, is_active, sort_order")
      .order("sort_order", { ascending: true })
      .order("display_name", { ascending: true });
    if (error) {
      console.error("Error loading categories", error);
    } else {
      setCategories(data || []);
    }
  };

  useEffect(() => {
    if (open) load();
  }, [open]);

  const slugify = (text: string) =>
    text
      .toLowerCase()
      .trim()
      .replace(/[^a-z0-9]+/g, "_")
      .replace(/^_+|_+$/g, "");

  const handleAdd = async () => {
    if (!name.trim()) return;
    setSaving(true);
    try {
      const display = name.trim();
      const value = slugify(display);
      const { error } = await supabase
        .from("fee_categories")
        .insert({ value, display_name: display });
      if (error) throw error;
      toast({ title: "Erfolg", description: "Kategorie hinzugefügt." });
      setName("");
      await load();
      onChanged?.();
    } catch (e) {
      console.error(e);
      toast({ title: "Fehler", description: "Kategorie konnte nicht angelegt werden.", variant: "destructive" });
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm("Kategorie wirklich löschen? Verknüpfungen werden entfernt.")) return;
    try {
      const { error } = await supabase.from("fee_categories").delete().eq("id", id);
      if (error) throw error;
      toast({ title: "Erfolg", description: "Kategorie gelöscht." });
      await load();
      onChanged?.();
    } catch (e) {
      console.error(e);
      toast({ title: "Fehler", description: "Kategorie konnte nicht gelöscht werden.", variant: "destructive" });
    }
  };

  const toggleActive = async (id: string, current: boolean) => {
    try {
      const { error } = await supabase.from("fee_categories").update({ is_active: !current }).eq("id", id);
      if (error) throw error;
      await load();
      onChanged?.();
    } catch (e) {
      console.error(e);
      toast({ title: "Fehler", description: "Status konnte nicht geändert werden.", variant: "destructive" });
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">Kategorien verwalten</Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[560px]">
        <DialogHeader>
          <DialogTitle>Gebühren-Kategorien</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 items-end">
            <div className="sm:col-span-2 space-y-2">
              <Label htmlFor="cat_name">Neue Kategorie</Label>
              <Input id="cat_name" value={name} onChange={(e) => setName(e.target.value)} placeholder="z.B. Schnupperkurs" />
            </div>
            <Button onClick={handleAdd} disabled={saving || !name.trim()}>
              <Plus className="h-4 w-4 mr-2" /> Hinzufügen
            </Button>
          </div>

          <div className="space-y-2">
            {categories.length === 0 ? (
              <p className="text-sm text-muted-foreground">Keine Kategorien vorhanden.</p>
            ) : (
              <div className="space-y-2">
                {categories.map((c) => (
                  <div key={c.id} className="flex items-center justify-between border rounded p-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">{c.display_name}</Badge>
                      <span className="text-xs text-muted-foreground">{c.value}</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="flex items-center gap-2">
                        <span className="text-xs">Aktiv</span>
                        <Switch checked={c.is_active} onCheckedChange={() => toggleActive(c.id, c.is_active)} />
                      </div>
                      <Button variant="outline" size="icon" onClick={() => handleDelete(c.id)} aria-label="Löschen">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
