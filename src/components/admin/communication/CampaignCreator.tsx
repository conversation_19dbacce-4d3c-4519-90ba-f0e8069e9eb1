import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useClub } from "@/contexts/ClubContext";
import { CalendarIcon, Send, Save, Eye, Sparkles, Target, Mail, Smartphone, MessageSquare, Monitor, RefreshCw, AlertCircle } from "lucide-react";
import { format } from "date-fns";
import { de } from "date-fns/locale";
import { useTargetGroups } from '@/hooks/useTargetGroups';

const CampaignCreator = () => {
  const { toast } = useToast();
  const { currentClubId } = useClub();
  const [date, setDate] = useState<Date>();
  const [selectedChannels, setSelectedChannels] = useState<string[]>(['email']);
  const [selectedGroups, setSelectedGroups] = useState<string[]>(['all_members']);
  const [isLaunching, setIsLaunching] = useState(false);
  const [campaignData, setCampaignData] = useState({
    title: '',
    subject: '',
    message: ''
  });

  const channels = [
    { id: 'email', label: 'E-Mail', icon: Mail, description: 'Klassische E-Mail Kommunikation' },
    { id: 'push', label: 'Push', icon: Smartphone, description: 'Sofortige Benachrichtigungen' },
    { id: 'dashboard', label: 'Dashboard', icon: Monitor, description: 'News-Bereich im System' },
    { id: 'chat', label: 'Chat', icon: MessageSquare, description: 'Vereins-Chat Integration' }
  ];

  // 🎯 REAL CLUB DATA: Fetch actual target groups for current club
  const { targetGroups, isLoading: isLoadingGroups, error: groupsError } = useTargetGroups();

  const handleChannelToggle = (channelId: string) => {
    setSelectedChannels(prev => 
      prev.includes(channelId) 
        ? prev.filter(id => id !== channelId)
        : [...prev, channelId]
    );
  };

  const handleGroupToggle = (groupId: string) => {
    setSelectedGroups(prev => 
      prev.includes(groupId) 
        ? prev.filter(id => id !== groupId)
        : [...prev, groupId]
    );
  };

  const getTotalRecipients = () => {
    return selectedGroups.reduce((total, groupId) => {
      const group = targetGroups.find(g => g.id === groupId);
      return total + (group?.count || 0);
    }, 0);
  };

  const handleLaunchCampaign = async () => {
    if (!campaignData.title || !campaignData.message) {
      toast({
        title: "Fehler",
        description: "Bitte füllen Sie alle Pflichtfelder aus",
        variant: "destructive"
      });
      return;
    }

    if (selectedGroups.length === 0) {
      toast({
        title: "Fehler",
        description: "Bitte wählen Sie mindestens eine Zielgruppe aus",
        variant: "destructive"
      });
      return;
    }

    if (selectedChannels.length === 0) {
      toast({
        title: "Fehler",
        description: "Bitte wählen Sie mindestens einen Kanal aus",
        variant: "destructive"
      });
      return;
    }

    const totalRecipients = getTotalRecipients();
    if (totalRecipients === 0) {
      toast({
        title: "Warnung",
        description: "Keine Empfänger für die ausgewählten Zielgruppen gefunden",
        variant: "destructive"
      });
      return;
    }

    setIsLaunching(true);
    console.log("🚀 Launching campaign with:", { campaignData, selectedChannels, selectedGroups, totalRecipients });

    try {
      // Create campaign in database
      const { data: campaign, error: campaignError } = await supabase
        .from('communication_campaigns')
        .insert({
          club_id: currentClubId,
          title: campaignData.title,
          message_content: campaignData.message,
          target_groups: selectedGroups,
          channels: selectedChannels,
          status: 'draft',
          scheduled_at: date?.toISOString()
        })
        .select()
        .single();

      if (campaignError) {
        console.error("Campaign creation error:", campaignError);
        throw new Error(`Kampagne konnte nicht erstellt werden: ${campaignError.message}`);
      }

      console.log("✅ Campaign created:", campaign.id);

      // Send campaign through communication hub
      const { data, error } = await supabase.functions.invoke('communication-hub', {
        body: {
          type: 'send_campaign',
          data: {
            campaignId: campaign.id,
            clubId: currentClubId
          }
        }
      });

      if (error) {
        console.error("Campaign sending error:", error);
        // Update campaign status to failed
        await supabase
          .from('communication_campaigns')
          .update({ status: 'failed' })
          .eq('id', campaign.id);

        throw new Error(`Kampagnen-Versand fehlgeschlagen: ${error.message}`);
      }

      console.log("✅ Campaign launched successfully:", data);

      // Show detailed success message
      const successMessage = data?.results ?
        `Kampagne erfolgreich gestartet! ${totalRecipients} Empfänger über ${selectedChannels.join(', ')}` :
        `Kampagne erfolgreich gestartet für ${totalRecipients} Empfänger`;

      toast({
        title: "Erfolg",
        description: successMessage,
      });

      // Reset form
      setCampaignData({ title: '', subject: '', message: '' });
      setSelectedChannels(['email']);
      setSelectedGroups(['all_members']);

    } catch (error: any) {
      console.error("💥 Campaign launch error:", error);

      // Provide more specific error messages
      let errorMessage = "Kampagnen-Start fehlgeschlagen";

      if (error.message?.includes('SMTP')) {
        errorMessage = "E-Mail-Versand fehlgeschlagen. Bitte prüfen Sie die SMTP-Konfiguration in den Club-Einstellungen.";
      } else if (error.message?.includes('Authentifizierung') || error.message?.includes('authentication')) {
        errorMessage = "Authentifizierungsfehler. Bitte melden Sie sich erneut an.";
      } else if (error.message?.includes('Berechtigung') || error.message?.includes('permission')) {
        errorMessage = "Keine Berechtigung zum Versenden von Kampagnen. Nur Club-Admins können Kampagnen versenden.";
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast({
        title: "Fehler",
        description: errorMessage,
        variant: "destructive"
      });
    } finally {
      setIsLaunching(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Neue Kampagne erstellen</h2>
          <p className="text-muted-foreground">
            Multi-Channel Kommunikation mit intelligenter Zielgruppen-Auswahl
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" className="gap-2">
            <Sparkles className="h-4 w-4" />
            AI Hilfe
          </Button>
          <Button variant="outline" className="gap-2">
            <Eye className="h-4 w-4" />
            Vorschau
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Campaign Form */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Kampagnen-Details</CardTitle>
              <CardDescription>
                Grundlegende Informationen für Ihre Kommunikation
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="title">Titel der Kampagne</Label>
                <Input 
                  id="title" 
                  placeholder="z.B. Vereinsturnier Anmeldung geöffnet"
                  value={campaignData.title}
                  onChange={(e) => setCampaignData(prev => ({ ...prev, title: e.target.value }))}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="subject">E-Mail Betreff (optional)</Label>
                <Input 
                  id="subject" 
                  placeholder="Wird automatisch generiert, wenn leer"
                  value={campaignData.subject}
                  onChange={(e) => setCampaignData(prev => ({ ...prev, subject: e.target.value }))}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="message">Nachricht</Label>
                <Textarea 
                  id="message" 
                  placeholder="Ihre Nachricht an die Mitglieder..."
                  className="min-h-[120px]"
                  value={campaignData.message}
                  onChange={(e) => setCampaignData(prev => ({ ...prev, message: e.target.value }))}
                />
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox id="ai-optimize" />
                <Label htmlFor="ai-optimize" className="text-sm">
                  Mit AI optimieren (Ton, Länge, Call-to-Action)
                </Label>
              </div>
            </CardContent>
          </Card>

          {/* Channel Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Kommunikations-Kanäle
              </CardTitle>
              <CardDescription>
                Wählen Sie, über welche Kanäle Ihre Nachricht versendet wird
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {channels.map((channel) => (
                  <div 
                    key={channel.id} 
                    className={`p-4 border rounded-lg cursor-pointer transition-all ${
                      selectedChannels.includes(channel.id) 
                        ? 'border-primary bg-primary/5' 
                        : 'border-border hover:border-primary/50'
                    }`}
                    onClick={() => handleChannelToggle(channel.id)}
                  >
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded ${
                        selectedChannels.includes(channel.id) 
                          ? 'bg-primary text-white' 
                          : 'bg-muted'
                      }`}>
                        <channel.icon className="h-4 w-4" />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium">{channel.label}</h4>
                        <p className="text-xs text-muted-foreground">{channel.description}</p>
                      </div>
                      <Checkbox 
                        checked={selectedChannels.includes(channel.id)}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Target Groups */}
          <Card>
            <CardHeader>
              <CardTitle>Zielgruppen</CardTitle>
              <CardDescription>
                Smart Groups für präzise Kommunikation
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              {isLoadingGroups ? (
                <div className="flex items-center justify-center py-4">
                  <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                  <span className="text-sm text-muted-foreground">Lade Zielgruppen...</span>
                </div>
              ) : groupsError ? (
                <div className="text-sm text-destructive p-3 bg-destructive/10 rounded">
                  Fehler beim Laden der Zielgruppen: {groupsError}
                </div>
              ) : (
                targetGroups.map((group) => (
                  <div
                    key={group.id}
                    className={`flex items-center justify-between p-3 border rounded cursor-pointer transition-all ${
                      selectedGroups.includes(group.id)
                        ? 'border-primary bg-primary/5'
                        : 'border-border hover:border-primary/50'
                    }`}
                    onClick={() => handleGroupToggle(group.id)}
                    title={group.description}
                  >
                    <div className="flex items-center gap-2">
                      <Checkbox
                        checked={selectedGroups.includes(group.id)}
                      />
                      <span className="font-medium">{group.label}</span>
                    </div>
                    <Badge variant="secondary">{group.count}</Badge>
                  </div>
                ))
              )}
              
              <div className="mt-4 p-3 bg-muted rounded">
                <div className="flex justify-between items-center">
                  <span className="font-medium">Gesamt Empfänger:</span>
                  <Badge variant="default">{getTotalRecipients()}</Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Scheduling */}
          <Card>
            <CardHeader>
              <CardTitle>Zeitplanung</CardTitle>
              <CardDescription>
                Sofort senden oder terminieren
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Versand-Option</Label>
                <Select defaultValue="now">
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="now">Sofort senden</SelectItem>
                    <SelectItem value="scheduled">Terminieren</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Datum & Zeit</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button 
                      variant="outline" 
                      className="w-full justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {date ? format(date, "PPP", { locale: de }) : "Datum wählen"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={date}
                      onSelect={setDate}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </CardContent>
          </Card>

          {/* Validation Summary */}
          {(!campaignData.title || !campaignData.message || selectedGroups.length === 0 || selectedChannels.length === 0 || getTotalRecipients() === 0) && (
            <Card className="border-amber-200 bg-amber-50">
              <CardContent className="p-4">
                <div className="flex items-start gap-2">
                  <AlertCircle className="h-4 w-4 text-amber-600 mt-0.5" />
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-amber-800">Vor dem Start erforderlich:</p>
                    <ul className="text-xs text-amber-700 space-y-1">
                      {!campaignData.title && <li>• Titel eingeben</li>}
                      {!campaignData.message && <li>• Nachricht verfassen</li>}
                      {selectedGroups.length === 0 && <li>• Zielgruppe auswählen</li>}
                      {selectedChannels.length === 0 && <li>• Kanal auswählen</li>}
                      {getTotalRecipients() === 0 && <li>• Keine Empfänger gefunden</li>}
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Actions */}
          <Card>
            <CardContent className="p-4 space-y-2">
              {isLaunching && (
                <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center gap-2 text-blue-800">
                    <RefreshCw className="h-4 w-4 animate-spin" />
                    <span className="text-sm font-medium">Kampagne wird versendet...</span>
                  </div>
                  <p className="text-xs text-blue-600 mt-1">
                    Dies kann je nach Anzahl der Empfänger einige Minuten dauern.
                  </p>
                </div>
              )}

              <Button
                className="w-full gap-2"
                onClick={handleLaunchCampaign}
                disabled={
                  isLaunching ||
                  !campaignData.title ||
                  !campaignData.message ||
                  selectedGroups.length === 0 ||
                  selectedChannels.length === 0 ||
                  getTotalRecipients() === 0
                }
              >
                {isLaunching ? (
                  <RefreshCw className="h-4 w-4 animate-spin" />
                ) : (
                  <Send className="h-4 w-4" />
                )}
                {isLaunching ?
                  `Wird gestartet... (${getTotalRecipients()} Empfänger)` :
                  `Kampagne starten (${getTotalRecipients()} Empfänger)`
                }
              </Button>
              <Button variant="outline" className="w-full gap-2" disabled={isLaunching}>
                <Save className="h-4 w-4" />
                Als Entwurf speichern
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default CampaignCreator;