import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useClub } from "@/contexts/ClubContext";
import { CalendarIcon, Send, Save, Eye, Sparkles, Target, Mail, Smartphone, MessageSquare, Monitor, RefreshCw } from "lucide-react";
import { format } from "date-fns";
import { de } from "date-fns/locale";
import { getDemoTargetGroups } from '@/services/DemoDataService';

const CampaignCreator = () => {
  const { toast } = useToast();
  const { currentClubId } = useClub();
  const [date, setDate] = useState<Date>();
  const [selectedChannels, setSelectedChannels] = useState<string[]>(['email']);
  const [selectedGroups, setSelectedGroups] = useState<string[]>(['all_members']);
  const [isLaunching, setIsLaunching] = useState(false);
  const [campaignData, setCampaignData] = useState({
    title: '',
    subject: '',
    message: ''
  });

  const channels = [
    { id: 'email', label: 'E-Mail', icon: Mail, description: 'Klassische E-Mail Kommunikation' },
    { id: 'push', label: 'Push', icon: Smartphone, description: 'Sofortige Benachrichtigungen' },
    { id: 'dashboard', label: 'Dashboard', icon: Monitor, description: 'News-Bereich im System' },
    { id: 'chat', label: 'Chat', icon: MessageSquare, description: 'Vereins-Chat Integration' }
  ];

  // 🎯 CENTRALIZED DEMO DATA: Now comes from DemoDataService
  const targetGroups = getDemoTargetGroups();

  const handleChannelToggle = (channelId: string) => {
    setSelectedChannels(prev => 
      prev.includes(channelId) 
        ? prev.filter(id => id !== channelId)
        : [...prev, channelId]
    );
  };

  const handleGroupToggle = (groupId: string) => {
    setSelectedGroups(prev => 
      prev.includes(groupId) 
        ? prev.filter(id => id !== groupId)
        : [...prev, groupId]
    );
  };

  const getTotalRecipients = () => {
    return selectedGroups.reduce((total, groupId) => {
      const group = targetGroups.find(g => g.id === groupId);
      return total + (group?.count || 0);
    }, 0);
  };

  const handleLaunchCampaign = async () => {
    if (!campaignData.title || !campaignData.message) {
      toast({
        title: "Fehler",
        description: "Bitte füllen Sie alle Pflichtfelder aus",
        variant: "destructive"
      });
      return;
    }

    setIsLaunching(true);
    console.log("🚀 Launching campaign with:", { campaignData, selectedChannels, selectedGroups });

    try {
      // Create campaign in database
      const { data: campaign, error: campaignError } = await supabase
        .from('communication_campaigns')
        .insert({
          club_id: currentClubId,
          title: campaignData.title,
          message_content: campaignData.message,
          target_groups: selectedGroups,
          channels: selectedChannels,
          status: 'draft',
          scheduled_at: date?.toISOString()
        })
        .select()
        .single();

      if (campaignError) {
        throw new Error(campaignError.message);
      }

      // Send campaign through communication hub
      const { data, error } = await supabase.functions.invoke('communication-hub', {
        body: {
          type: 'send_campaign',
          data: {
            campaignId: campaign.id,
            clubId: currentClubId
          }
        }
      });

      if (error) {
        throw new Error(error.message);
      }

      console.log("✅ Campaign launched successfully:", data);
      toast({
        title: "Erfolg",
        description: `Kampagne erfolgreich gestartet für ${getTotalRecipients()} Empfänger`,
      });

      // Reset form
      setCampaignData({ title: '', subject: '', message: '' });
      setSelectedChannels(['email']);
      setSelectedGroups(['all_members']);

    } catch (error: any) {
      console.error("💥 Campaign launch error:", error);
      toast({
        title: "Fehler",
        description: error.message || "Kampagnen-Start fehlgeschlagen",
        variant: "destructive"
      });
    } finally {
      setIsLaunching(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Neue Kampagne erstellen</h2>
          <p className="text-muted-foreground">
            Multi-Channel Kommunikation mit intelligenter Zielgruppen-Auswahl
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" className="gap-2">
            <Sparkles className="h-4 w-4" />
            AI Hilfe
          </Button>
          <Button variant="outline" className="gap-2">
            <Eye className="h-4 w-4" />
            Vorschau
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Campaign Form */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Kampagnen-Details</CardTitle>
              <CardDescription>
                Grundlegende Informationen für Ihre Kommunikation
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="title">Titel der Kampagne</Label>
                <Input 
                  id="title" 
                  placeholder="z.B. Vereinsturnier Anmeldung geöffnet"
                  value={campaignData.title}
                  onChange={(e) => setCampaignData(prev => ({ ...prev, title: e.target.value }))}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="subject">E-Mail Betreff (optional)</Label>
                <Input 
                  id="subject" 
                  placeholder="Wird automatisch generiert, wenn leer"
                  value={campaignData.subject}
                  onChange={(e) => setCampaignData(prev => ({ ...prev, subject: e.target.value }))}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="message">Nachricht</Label>
                <Textarea 
                  id="message" 
                  placeholder="Ihre Nachricht an die Mitglieder..."
                  className="min-h-[120px]"
                  value={campaignData.message}
                  onChange={(e) => setCampaignData(prev => ({ ...prev, message: e.target.value }))}
                />
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox id="ai-optimize" />
                <Label htmlFor="ai-optimize" className="text-sm">
                  Mit AI optimieren (Ton, Länge, Call-to-Action)
                </Label>
              </div>
            </CardContent>
          </Card>

          {/* Channel Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Kommunikations-Kanäle
              </CardTitle>
              <CardDescription>
                Wählen Sie, über welche Kanäle Ihre Nachricht versendet wird
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {channels.map((channel) => (
                  <div 
                    key={channel.id} 
                    className={`p-4 border rounded-lg cursor-pointer transition-all ${
                      selectedChannels.includes(channel.id) 
                        ? 'border-primary bg-primary/5' 
                        : 'border-border hover:border-primary/50'
                    }`}
                    onClick={() => handleChannelToggle(channel.id)}
                  >
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded ${
                        selectedChannels.includes(channel.id) 
                          ? 'bg-primary text-white' 
                          : 'bg-muted'
                      }`}>
                        <channel.icon className="h-4 w-4" />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium">{channel.label}</h4>
                        <p className="text-xs text-muted-foreground">{channel.description}</p>
                      </div>
                      <Checkbox 
                        checked={selectedChannels.includes(channel.id)}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Target Groups */}
          <Card>
            <CardHeader>
              <CardTitle>Zielgruppen</CardTitle>
              <CardDescription>
                Smart Groups für präzise Kommunikation
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              {targetGroups.map((group) => (
                <div 
                  key={group.id}
                  className={`flex items-center justify-between p-3 border rounded cursor-pointer transition-all ${
                    selectedGroups.includes(group.id) 
                      ? 'border-primary bg-primary/5' 
                      : 'border-border hover:border-primary/50'
                  }`}
                  onClick={() => handleGroupToggle(group.id)}
                >
                  <div className="flex items-center gap-2">
                    <Checkbox 
                      checked={selectedGroups.includes(group.id)}
                    />
                    <span className="font-medium">{group.label}</span>
                  </div>
                  <Badge variant="secondary">{group.count}</Badge>
                </div>
              ))}
              
              <div className="mt-4 p-3 bg-muted rounded">
                <div className="flex justify-between items-center">
                  <span className="font-medium">Gesamt Empfänger:</span>
                  <Badge variant="default">{getTotalRecipients()}</Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Scheduling */}
          <Card>
            <CardHeader>
              <CardTitle>Zeitplanung</CardTitle>
              <CardDescription>
                Sofort senden oder terminieren
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Versand-Option</Label>
                <Select defaultValue="now">
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="now">Sofort senden</SelectItem>
                    <SelectItem value="scheduled">Terminieren</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Datum & Zeit</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button 
                      variant="outline" 
                      className="w-full justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {date ? format(date, "PPP", { locale: de }) : "Datum wählen"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={date}
                      onSelect={setDate}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <Card>
            <CardContent className="p-4 space-y-2">
              <Button 
                className="w-full gap-2"
                onClick={handleLaunchCampaign}
                disabled={isLaunching || !campaignData.title || !campaignData.message}
              >
                {isLaunching ? (
                  <RefreshCw className="h-4 w-4 animate-spin" />
                ) : (
                  <Send className="h-4 w-4" />
                )}
                {isLaunching ? 'Wird gestartet...' : 'Kampagne starten'}
              </Button>
              <Button variant="outline" className="w-full gap-2">
                <Save className="h-4 w-4" />
                Als Entwurf speichern
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default CampaignCreator;