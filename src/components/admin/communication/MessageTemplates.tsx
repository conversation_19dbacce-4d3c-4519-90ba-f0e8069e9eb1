import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { MessageSquare, Plus, Edit2, Copy, Trash2, Mail, Smartphone, Monitor, Sparkles, Heart, Zap, AlertTriangle } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { AIInterface } from "@/components/ui/ai-interface";

const MessageTemplates = () => {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [selectedPersona, setSelectedPersona] = useState("friendly");
  const [isGenerating, setIsGenerating] = useState(false);
  const [aiPrompt, setAiPrompt] = useState("");
  const [templateName, setTemplateName] = useState("");
  const [templateType, setTemplateType] = useState("");
  const [subject, setSubject] = useState("");
  const [content, setContent] = useState("");
  const { toast } = useToast();

  const resetForm = () => {
    setAiPrompt("");
    setTemplateName("");
    setTemplateType("");
    setSubject("");
    setContent("");
    setSelectedPersona("friendly");
  };

  const handleDialogChange = (open: boolean) => {
    setIsCreateDialogOpen(open);
    if (!open) {
      resetForm();
    }
  };

  const availableVariables = [
    { var: "{{member_name}}", desc: "Name des Empfängers" },
    { var: "{{club_name}}", desc: "Name des Vereins" },
    { var: "{{date}}", desc: "Aktuelles Datum" },
    { var: "{{event_name}}", desc: "Name der Veranstaltung" },
    { var: "{{tournament_dates}}", desc: "Turnier Termine" },
    { var: "{{contact_info}}", desc: "Kontakt Information" },
    { var: "{{registration_link}}", desc: "Anmeldelink" },
    { var: "{{fee}}", desc: "Gebühr" },
    { var: "{{categories}}", desc: "Kategorien" },
    { var: "{{court_number}}", desc: "Platznummer" },
    { var: "{{start_date}}", desc: "Startdatum" },
    { var: "{{end_date}}", desc: "Enddatum" }
  ];

  const generateWithAI = async () => {
    if (!aiPrompt.trim()) {
      toast({
        title: "Eingabe erforderlich",
        description: "Bitte geben Sie eine Beschreibung für Ihr Template ein.",
        variant: "destructive"
      });
      return;
    }

    setIsGenerating(true);
    try {
      const response = await supabase.functions.invoke('ai-message-generator', {
        body: {
          prompt: aiPrompt,
          persona: selectedPersona,
          availableVariables: availableVariables.map(v => v.var),
          clubContext: "Tennis-Verein"
        }
      });

      if (response.error) {
        throw new Error(response.error.message);
      }

      const { data } = response;
      if (data?.templateName) setTemplateName(data.templateName);
      if (data?.subject) setSubject(data.subject);
      if (data?.content) setContent(data.content);
      if (data?.suggestedType) setTemplateType(data.suggestedType);

      toast({
        title: "Template generiert",
        description: "Das AI-Template wurde erfolgreich erstellt."
      });
    } catch (error) {
      console.error('AI generation error:', error);
      toast({
        title: "Fehler",
        description: "Template konnte nicht generiert werden.",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const templates = [
    {
      id: 1,
      name: "Turnier Ankündigung",
      type: "email",
      persona: "energetic",
      subject: "🎾 Großes Vereinsturnier - Anmeldung startet!",
      content: "Liebe Tennis-Freunde,\n\nUnser jährliches Vereinsturnier steht vor der Tür! Meldet euch jetzt an und zeigt euer Können auf dem Platz.\n\n📅 Termine: {{tournament_dates}}\n🏆 Kategorien: {{categories}}\n💰 Anmeldegebühr: {{fee}}\n\nJetzt anmelden: {{registration_link}}\n\nWir freuen uns auf spannende Matches!\nEuer Tennis-Team",
      variables: ["tournament_dates", "categories", "fee", "registration_link"],
      lastUsed: "2024-01-15",
      usage: 23
    },
    {
      id: 2,
      name: "Wichtige Mitteilung",
      type: "push",
      persona: "urgent",
      subject: "Wichtige Information",
      content: "⚠️ WICHTIG: {{message}}\n\nBitte beachtet diese wichtige Information für unseren Verein.\n\nBei Fragen meldet euch beim Vorstand.\n\n{{contact_info}}",
      variables: ["message", "contact_info"],
      lastUsed: "2024-01-10",
      usage: 8
    },
    {
      id: 3,
      name: "Willkommen neue Mitglieder",
      type: "email",
      persona: "friendly",
      subject: "Herzlich willkommen im {{club_name}}! 🎾",
      content: "Liebe/r {{member_name}},\n\nherzlich willkommen in unserem Tennis-Verein! Wir freuen uns riesig, dass du dabei bist.\n\n👋 Dein Ansprechpartner: {{contact_person}}\n🎾 Erste Schritte: {{onboarding_link}}\n📱 App herunterladen: {{app_link}}\n\nFalls du Fragen hast, melde dich gerne!\n\nSportliche Grüße,\nDein {{club_name}} Team",
      variables: ["member_name", "club_name", "contact_person", "onboarding_link", "app_link"],
      lastUsed: "2024-01-12",
      usage: 15
    },
    {
      id: 4,
      name: "Platz-Wartung",
      type: "dashboard",
      persona: "formal",
      subject: "Platz-Wartungsarbeiten",
      content: "Aufgrund von Wartungsarbeiten ist Platz {{court_number}} vom {{start_date}} bis {{end_date}} nicht verfügbar.\n\nBuchungen werden automatisch auf andere Plätze umgelegt.\n\nEntschuldigt die Unannehmlichkeiten.\n\nVereinsleitung",
      variables: ["court_number", "start_date", "end_date"],
      lastUsed: "2024-01-08",
      usage: 3
    }
  ];

  const personas = [
    { id: "friendly", label: "Freundlich", icon: Heart, color: "bg-green-500", description: "Warm und einladend" },
    { id: "formal", label: "Formal", icon: MessageSquare, color: "bg-blue-500", description: "Professionell und sachlich" },
    { id: "urgent", label: "Dringend", icon: AlertTriangle, color: "bg-red-500", description: "Wichtig und zeitkritisch" },
    { id: "energetic", label: "Energisch", icon: Zap, color: "bg-yellow-500", description: "Motivierend und dynamisch" }
  ];

  const typeIcons = {
    email: Mail,
    push: Smartphone,
    dashboard: Monitor,
    chat: MessageSquare
  };

  const getPersonaInfo = (personaId: string) => {
    return personas.find(p => p.id === personaId) || personas[0];
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Message Templates</h2>
          <p className="text-muted-foreground">
            One-Touch Kommunikation mit intelligenten Personas
          </p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={handleDialogChange}>
          <DialogTrigger asChild>
            <Button className="gap-2">
              <Plus className="h-4 w-4" />
              Neue Vorlage
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-7xl max-h-[90vh] overflow-hidden flex flex-col">
            <DialogHeader>
              <DialogTitle>Neue Template erstellen</DialogTitle>
              <DialogDescription>
                Erstellen Sie eine wiederverwendbare Nachrichtenvorlage
              </DialogDescription>
            </DialogHeader>
            
            {/* AI Prompting Section */}
            <AIInterface
              title="AI Template Generator"
              description="Intelligente Nachrichtenerstellung mit KI-Power"
              placeholder="Beschreiben Sie Ihr Template (z.B. 'Turnier-Anmeldung mit Ermäßigung für Mitglieder')"
              prompt={aiPrompt}
              onPromptChange={setAiPrompt}
              onGenerate={generateWithAI}
              isGenerating={isGenerating}
              generateButtonText="Generieren"
              generatingText="Generiert..."
              features={["AI erkennt automatisch Variablen", "Vereins-optimiert"]}
            />
            
            <div className="flex gap-6 flex-1 min-h-0">
              {/* Main Content */}
              <div className="flex-1 overflow-y-auto min-h-0">
                <Tabs defaultValue="content" className="h-full flex flex-col">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="content">Inhalt</TabsTrigger>
                    <TabsTrigger value="persona">Persona</TabsTrigger>
                  </TabsList>

                  <TabsContent value="content" className="space-y-4 mt-4 flex-1 overflow-y-auto">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="template-name">Template Name</Label>
                        <Input 
                          id="template-name" 
                          placeholder="z.B. Turnier Reminder"
                          value={templateName}
                          onChange={(e) => setTemplateName(e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="template-type">Kanal-Typ</Label>
                        <Select value={templateType} onValueChange={setTemplateType}>
                          <SelectTrigger>
                            <SelectValue placeholder="Kanal wählen" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="email">E-Mail</SelectItem>
                            <SelectItem value="push">Push Notification</SelectItem>
                            <SelectItem value="dashboard">Dashboard News</SelectItem>
                            <SelectItem value="chat">Chat Nachricht</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="subject">Betreff / Titel</Label>
                      <Input 
                        id="subject" 
                        placeholder="E-Mail Betreff oder Titel der Nachricht"
                        value={subject}
                        onChange={(e) => setSubject(e.target.value)}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="content">Nachrichteninhalt</Label>
                      <Textarea 
                        id="content" 
                        placeholder="Ihr Nachrichtentext... Ziehen Sie Variablen aus der Seitenleiste hierher"
                        className="min-h-[200px]"
                        value={content}
                        onChange={(e) => setContent(e.target.value)}
                        onDrop={(e) => {
                          e.preventDefault();
                          const variable = e.dataTransfer.getData('text/plain');
                          const textarea = e.target as HTMLTextAreaElement;
                          const start = textarea.selectionStart;
                          const end = textarea.selectionEnd;
                          const currentValue = textarea.value;
                          const newValue = currentValue.substring(0, start) + variable + currentValue.substring(end);
                          textarea.value = newValue;
                          textarea.focus();
                          textarea.setSelectionRange(start + variable.length, start + variable.length);
                        }}
                        onDragOver={(e) => e.preventDefault()}
                      />
                    </div>
                  </TabsContent>

                  <TabsContent value="persona" className="space-y-4 mt-4 flex-1 overflow-y-auto">
                    <div className="space-y-3">
                      <Label>Communication Persona wählen</Label>
                      <div className="grid grid-cols-2 gap-3">
                        {personas.map((persona) => (
                          <div 
                            key={persona.id}
                            className={`p-4 border rounded-lg cursor-pointer transition-all ${
                              selectedPersona === persona.id 
                                ? 'border-primary bg-primary/5' 
                                : 'border-border hover:border-primary/50'
                            }`}
                            onClick={() => setSelectedPersona(persona.id)}
                          >
                            <div className="flex items-center gap-3">
                              <div className={`p-2 rounded-full ${persona.color}`}>
                                <persona.icon className="h-4 w-4 text-white" />
                              </div>
                              <div>
                                <h4 className="font-medium">{persona.label}</h4>
                                <p className="text-sm text-muted-foreground">{persona.description}</p>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="p-4 bg-muted rounded-lg">
                      <h4 className="font-medium mb-2">AI-Optimierung</h4>
                      <p className="text-sm text-muted-foreground mb-3">
                        Die gewählte Persona beeinflusst automatisch Ton, Wortwahl und Struktur Ihrer Nachrichten.
                      </p>
                      <Button variant="outline" className="gap-2" size="sm">
                        <Sparkles className="h-4 w-4" />
                        Vorschau mit AI generieren
                      </Button>
                    </div>
                  </TabsContent>
                </Tabs>
              </div>

              {/* Variables Sidebar */}
              <div className="w-80 border-l pl-6 overflow-y-auto flex-shrink-0">
                <div className="space-y-4">
                  <div>
                    <Label className="text-base font-medium">Verfügbare Variablen</Label>
                    <p className="text-sm text-muted-foreground">Ziehen Sie Variablen in Ihre Nachricht</p>
                  </div>
                  
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-muted-foreground">Standard Variablen</h4>
                    <div className="space-y-2">
                      {availableVariables.map((item) => (
                        <div 
                          key={item.var}
                          className="p-3 bg-muted rounded-lg cursor-grab hover:bg-muted/80 transition-colors border"
                          draggable
                          onDragStart={(e) => {
                            e.dataTransfer.setData('text/plain', item.var);
                          }}
                        >
                          <div className="flex flex-col gap-1">
                            <code className="text-xs font-mono text-primary">{item.var}</code>
                            <span className="text-xs text-muted-foreground">{item.desc}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-muted-foreground">Benutzerdefiniert</h4>
                    <Input 
                      placeholder="Neue Variable hinzufügen" 
                      className="text-sm"
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          const input = e.target as HTMLInputElement;
                          const value = input.value.trim();
                          if (value) {
                            // Here you would add the custom variable to state
                            input.value = '';
                          }
                        }
                      }}
                    />
                    <p className="text-xs text-muted-foreground">
                      Enter drücken zum Hinzufügen
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Footer with action buttons */}
            <div className="flex justify-end gap-2 pt-4 border-t mt-4">
              <Button variant="outline" onClick={() => handleDialogChange(false)}>
                Abbrechen
              </Button>
              <Button onClick={() => handleDialogChange(false)}>
                Template erstellen
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {templates.map((template) => {
          const TypeIcon = typeIcons[template.type as keyof typeof typeIcons];
          const persona = getPersonaInfo(template.persona);
          
          return (
            <Card key={template.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <TypeIcon className="h-5 w-5 text-muted-foreground" />
                    <Badge variant="outline" className="text-xs">
                      {template.type}
                    </Badge>
                  </div>
                  <div className="flex gap-1">
                    <Button variant="ghost" size="sm">
                      <Copy className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Edit2 className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <CardTitle className="text-lg">{template.name}</CardTitle>
                <CardDescription className="text-sm font-medium">
                  {template.subject}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-2">
                  <div className={`p-1 rounded-full ${persona.color}`}>
                    <persona.icon className="h-3 w-3 text-white" />
                  </div>
                  <span className="text-sm font-medium">{persona.label}</span>
                </div>

                <div className="text-sm text-muted-foreground line-clamp-3">
                  {template.content}
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Variablen:</span>
                    <Badge variant="secondary">{template.variables.length}</Badge>
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {template.variables.slice(0, 3).map((variable) => (
                      <Badge key={variable} variant="outline" className="text-xs">
                        {variable}
                      </Badge>
                    ))}
                    {template.variables.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{template.variables.length - 3}
                      </Badge>
                    )}
                  </div>
                </div>

                <div className="flex justify-between text-sm text-muted-foreground">
                  <span>Verwendet: {template.usage}x</span>
                  <span>Zuletzt: {template.lastUsed}</span>
                </div>

                <Button variant="outline" className="w-full gap-2">
                  <MessageSquare className="h-4 w-4" />
                  Template verwenden
                </Button>
              </CardContent>
            </Card>
          );
        })}

        {/* Add New Template Card */}
        <Card className="border-dashed border-2 hover:border-primary transition-colors cursor-pointer" 
              onClick={() => handleDialogChange(true)}>
          <CardContent className="flex flex-col items-center justify-center h-full min-h-[200px] space-y-4">
            <div className="p-4 bg-muted rounded-full">
              <Plus className="h-8 w-8 text-muted-foreground" />
            </div>
            <div className="text-center">
              <h3 className="font-medium">Neue Vorlage</h3>
              <p className="text-sm text-muted-foreground">
                Template mit AI-Power erstellen
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5" />
            One-Touch Templates
          </CardTitle>
          <CardDescription>
            Häufig verwendete Nachrichten – sofort einsatzbereit
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
            <Button variant="outline" className="justify-start gap-2 h-auto p-4">
              <div className="text-left">
                <div className="font-medium">Turnier Reminder</div>
                <div className="text-xs text-muted-foreground">3 Tage vor Event</div>
              </div>
            </Button>
            <Button variant="outline" className="justify-start gap-2 h-auto p-4">
              <div className="text-left">
                <div className="font-medium">Platz gesperrt</div>
                <div className="text-xs text-muted-foreground">Wartungsarbeiten</div>
              </div>
            </Button>
            <Button variant="outline" className="justify-start gap-2 h-auto p-4">
              <div className="text-left">
                <div className="font-medium">Neues Mitglied</div>
                <div className="text-xs text-muted-foreground">Willkommens-Paket</div>
              </div>
            </Button>
            <Button variant="outline" className="justify-start gap-2 h-auto p-4">
              <div className="text-left">
                <div className="font-medium">Gebühren-Info</div>
                <div className="text-xs text-muted-foreground">Monatliche Erinnerung</div>
              </div>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default MessageTemplates;