import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Bell, Mail, MessageSquare, Monitor, Search, Filter, Clock, Eye, ChevronRight, Activity } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { de } from "date-fns/locale";

const LiveFeed = () => {
  const [filterChannel, setFilterChannel] = useState("all");
  const [filterStatus, setFilterStatus] = useState("all");

  const activities = [
    {
      id: 1,
      type: "campaign_sent",
      title: "Turnier Anmeldung",
      description: "E-Mail Kampagne an 'Aktive Spieler' gesendet",
      channel: "email",
      status: "sent",
      recipients: 189,
      engagement: { opened: 156, clicked: 89 },
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2h ago
      user: "<PERSON> <PERSON>ermann"
    },
    {
      id: 2,
      type: "message_created",
      title: "Dashboard Ankündigung",
      description: "Neue Platzregeln veröffentlicht",
      channel: "dashboard",
      status: "live",
      recipients: 247,
      engagement: { views: 203, interactions: 45 },
      timestamp: new Date(Date.now() - 45 * 60 * 1000), // 45m ago
      user: "Anna Weber"
    },
    {
      id: 3,
      type: "push_sent",
      title: "Wetterwarnung",
      description: "Push-Benachrichtigung wegen Gewitterrisiko",
      channel: "push",
      status: "sent",
      recipients: 134,
      engagement: { delivered: 128, opened: 98 },
      timestamp: new Date(Date.now() - 25 * 60 * 1000), // 25m ago
      user: "System"
    },
    {
      id: 4,
      type: "chat_message",
      title: "Trainer Chat",
      description: "Neue Nachricht in 'Trainings-Koordination'",
      channel: "chat",
      status: "active",
      recipients: 8,
      engagement: { participants: 6, messages: 23 },
      timestamp: new Date(Date.now() - 15 * 60 * 1000), // 15m ago
      user: "Lisa Schmidt"
    },
    {
      id: 5,
      type: "campaign_scheduled",
      title: "Wochenend-Reminder",
      description: "Automatische Erinnerung für Samstag-Matches",
      channel: "email",
      status: "scheduled",
      recipients: 67,
      engagement: {},
      timestamp: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // in 2 days
      user: "Automation"
    },
    {
      id: 6,
      type: "template_used",
      title: "Neue Mitglieder Begrüßung",
      description: "Willkommens-Template an 3 neue Mitglieder",
      channel: "email",
      status: "sent",
      recipients: 3,
      engagement: { opened: 3, clicked: 2 },
      timestamp: new Date(Date.now() - 90 * 60 * 1000), // 1.5h ago
      user: "System"
    }
  ];

  const channelIcons = {
    email: Mail,
    push: Bell,
    dashboard: Monitor,
    chat: MessageSquare
  };

  const statusColors = {
    sent: "bg-green-500",
    live: "bg-blue-500",
    active: "bg-yellow-500",
    scheduled: "bg-purple-500",
    failed: "bg-red-500"
  };

  const getEngagementText = (activity: any) => {
    const { engagement } = activity;
    if (engagement.opened) return `${engagement.opened}/${activity.recipients} geöffnet`;
    if (engagement.views) return `${engagement.views} Aufrufe`;
    if (engagement.delivered) return `${engagement.delivered}/${activity.recipients} zugestellt`;
    if (engagement.participants) return `${engagement.participants} Teilnehmer`;
    return `${activity.recipients} Empfänger`;
  };

  const filteredActivities = activities.filter(activity => {
    const channelMatch = filterChannel === "all" || activity.channel === filterChannel;
    const statusMatch = filterStatus === "all" || activity.status === filterStatus;
    return channelMatch && statusMatch;
  });

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Live Communication Feed</h2>
          <p className="text-muted-foreground">
            Echtzeit-Übersicht aller Kommunikationsaktivitäten
          </p>
        </div>
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-2">
            <Activity className="h-4 w-4 text-green-500" />
            <span className="text-sm font-medium">Live</span>
          </div>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">Filter & Suche</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-[200px]">
              <Input placeholder="Suche nach Kampagnen, Nachrichten..." className="w-full" />
            </div>
            <Select value={filterChannel} onValueChange={setFilterChannel}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Kanal" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Alle Kanäle</SelectItem>
                <SelectItem value="email">E-Mail</SelectItem>
                <SelectItem value="push">Push</SelectItem>
                <SelectItem value="dashboard">Dashboard</SelectItem>
                <SelectItem value="chat">Chat</SelectItem>
              </SelectContent>
            </Select>
            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Alle Status</SelectItem>
                <SelectItem value="sent">Gesendet</SelectItem>
                <SelectItem value="live">Live</SelectItem>
                <SelectItem value="active">Aktiv</SelectItem>
                <SelectItem value="scheduled">Geplant</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Activity Feed */}
      <div className="space-y-4">
        {filteredActivities.map((activity) => {
          const ChannelIcon = channelIcons[activity.channel as keyof typeof channelIcons];
          const statusColor = statusColors[activity.status as keyof typeof statusColors];
          
          return (
            <Card key={activity.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-4 flex-1">
                    {/* Icon & Status */}
                    <div className="relative">
                      <div className="p-3 bg-muted rounded-full">
                        <ChannelIcon className="h-5 w-5" />
                      </div>
                      <div className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full ${statusColor} border-2 border-white`} />
                    </div>

                    {/* Content */}
                    <div className="flex-1 space-y-2">
                      <div className="flex items-center gap-2">
                        <h3 className="font-semibold">{activity.title}</h3>
                        <Badge variant="outline" className="text-xs">
                          {activity.channel}
                        </Badge>
                        <Badge variant={activity.status === 'sent' ? 'default' : 'secondary'} className="text-xs">
                          {activity.status}
                        </Badge>
                      </div>
                      
                      <p className="text-muted-foreground">{activity.description}</p>
                      
                      <div className="flex items-center gap-4 text-sm">
                        <span className="flex items-center gap-1">
                          <Eye className="h-4 w-4" />
                          {getEngagementText(activity)}
                        </span>
                        <span className="flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          {formatDistanceToNow(activity.timestamp, { addSuffix: true, locale: de })}
                        </span>
                        <span className="text-muted-foreground">von {activity.user}</span>
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center gap-2">
                    <Button variant="ghost" size="sm">
                      Details
                      <ChevronRight className="h-4 w-4 ml-1" />
                    </Button>
                  </div>
                </div>

                {/* Engagement Details for sent items */}
                {activity.status === 'sent' && Object.keys(activity.engagement).length > 0 && (
                  <div className="mt-4 pt-4 border-t">
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      {Object.entries(activity.engagement).map(([key, value]) => (
                        <div key={key} className="text-center">
                          <div className="font-bold text-lg">{value as number}</div>
                          <div className="text-muted-foreground capitalize">{key}</div>
                        </div>
                      ))}
                      {activity.engagement.opened && activity.recipients && (
                        <div className="text-center">
                          <div className="font-bold text-lg">
                            {Math.round((activity.engagement.opened / activity.recipients) * 100)}%
                          </div>
                          <div className="text-muted-foreground">Öffnungsrate</div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Load More */}
      <div className="text-center">
        <Button variant="outline">
          Weitere Aktivitäten laden
        </Button>
      </div>
    </div>
  );
};

export default LiveFeed;