import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Target, Plus, Edit2, Trash2, Users, Filter, Clock, Trophy, UserCheck } from "lucide-react";

const SmartGroups = () => {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);

  const smartGroups = [
    {
      id: 1,
      name: "Aktive Turnierspieler",
      description: "Mitgli<PERSON><PERSON> mit Turnierpartizipation in den letzten 6 Monaten",
      criteria: {
        tournaments: "last_6_months",
        status: "active",
        bookings: "min_5_per_month"
      },
      memberCount: 73,
      isDynamic: true,
      icon: Trophy,
      color: "bg-yellow-500"
    },
    {
      id: 2,
      name: "Neue Mitglieder",
      description: "Mitglieder, die in den letzten 30 Tagen beigetreten sind",
      criteria: {
        joined: "last_30_days",
        status: "active"
      },
      memberCount: 12,
      isDynamic: true,
      icon: UserCheck,
      color: "bg-green-500"
    },
    {
      id: 3,
      name: "Inaktive Spieler",
      description: "Keine Plätze in den letzten 3 Monaten gebucht",
      criteria: {
        bookings: "none_3_months",
        status: "active"
      },
      memberCount: 34,
      isDynamic: true,
      icon: Clock,
      color: "bg-orange-500"
    },
    {
      id: 4,
      name: "Vorstand & Trainer",
      description: "Alle Führungskräfte und Trainer des Vereins",
      criteria: {
        roles: ["admin", "trainer", "board_member"]
      },
      memberCount: 15,
      isDynamic: false,
      icon: Users,
      color: "bg-blue-500"
    }
  ];

  const criteriaOptions = [
    { category: "Mitgliedschaft", options: [
      { value: "joined_last_week", label: "Letzte Woche beigetreten" },
      { value: "joined_last_month", label: "Letzten Monat beigetreten" },
      { value: "joined_last_3_months", label: "Letzte 3 Monate beigetreten" },
      { value: "member_1_year", label: "Mindestens 1 Jahr Mitglied" }
    ]},
    { category: "Buchungsverhalten", options: [
      { value: "active_booker", label: "Aktiv buchend (>5/Monat)" },
      { value: "regular_booker", label: "Regelmäßig buchend (1-5/Monat)" },
      { value: "inactive_booker", label: "Inaktiv (keine Buchungen 3+ Monate)" },
      { value: "weekend_only", label: "Nur Wochenende" }
    ]},
    { category: "Turniere", options: [
      { value: "tournament_active", label: "Aktive Turnierteilnahme" },
      { value: "tournament_winner", label: "Turniersieger letztes Jahr" },
      { value: "tournament_never", label: "Noch nie an Turnier teilgenommen" }
    ]},
    { category: "Alter & Kategorie", options: [
      { value: "junior", label: "Jugendliche (unter 18)" },
      { value: "adult", label: "Erwachsene (18-65)" },
      { value: "senior", label: "Senioren (über 65)" }
    ]}
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Smart Groups</h2>
          <p className="text-muted-foreground">
            Intelligente, dynamische Zielgruppen für präzise Kommunikation
          </p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="gap-2">
              <Plus className="h-4 w-4" />
              Neue Gruppe
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Neue Smart Group erstellen</DialogTitle>
              <DialogDescription>
                Definieren Sie Kriterien für eine automatisch aktualisierte Zielgruppe
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-6 py-4">
              <div className="space-y-2">
                <Label htmlFor="group-name">Gruppenname</Label>
                <Input id="group-name" placeholder="z.B. Aktive Wochenend-Spieler" />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="group-description">Beschreibung</Label>
                <Input id="group-description" placeholder="Kurze Beschreibung der Zielgruppe" />
              </div>

              <div className="space-y-4">
                <Label>Kriterien auswählen</Label>
                {criteriaOptions.map((category) => (
                  <div key={category.category} className="space-y-2">
                    <h4 className="font-medium text-sm">{category.category}</h4>
                    <div className="grid grid-cols-1 gap-2">
                      {category.options.map((option) => (
                        <div key={option.value} className="flex items-center space-x-2">
                          <Checkbox id={option.value} />
                          <Label htmlFor={option.value} className="text-sm font-normal">
                            {option.label}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox id="dynamic" defaultChecked />
                <Label htmlFor="dynamic" className="text-sm">
                  Dynamische Gruppe (automatisch aktualisiert)
                </Label>
              </div>

              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Abbrechen
                </Button>
                <Button onClick={() => setIsCreateDialogOpen(false)}>
                  Gruppe erstellen
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {smartGroups.map((group) => (
          <Card key={group.id} className="relative overflow-hidden hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className={`p-2 rounded-full ${group.color}`}>
                  <group.icon className="h-5 w-5 text-white" />
                </div>
                <div className="flex gap-1">
                  <Button variant="ghost" size="sm">
                    <Edit2 className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm">
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <CardTitle className="text-lg">{group.name}</CardTitle>
              <CardDescription className="text-sm">
                {group.description}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Mitglieder:</span>
                  <Badge variant="secondary" className="font-bold">
                    {group.memberCount}
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Typ:</span>
                  <Badge variant={group.isDynamic ? "default" : "outline"}>
                    {group.isDynamic ? "Dynamisch" : "Statisch"}
                  </Badge>
                </div>

                <div className="space-y-1">
                  <span className="text-sm font-medium">Kriterien:</span>
                  <div className="flex flex-wrap gap-1">
                    {Object.values(group.criteria).map((criterion, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {criterion.toString()}
                      </Badge>
                    ))}
                  </div>
                </div>

                <Button variant="outline" className="w-full mt-4 gap-2">
                  <Target className="h-4 w-4" />
                  Für Kampagne verwenden
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}

        {/* Add New Group Card */}
        <Card className="border-dashed border-2 hover:border-primary transition-colors cursor-pointer" 
              onClick={() => setIsCreateDialogOpen(true)}>
          <CardContent className="flex flex-col items-center justify-center h-full min-h-[200px] space-y-4">
            <div className="p-4 bg-muted rounded-full">
              <Plus className="h-8 w-8 text-muted-foreground" />
            </div>
            <div className="text-center">
              <h3 className="font-medium">Neue Smart Group</h3>
              <p className="text-sm text-muted-foreground">
                Intelligente Zielgruppe erstellen
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Statistics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Gruppen-Analytics
          </CardTitle>
          <CardDescription>
            Übersicht der Zielgruppen-Performance
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">4</div>
              <div className="text-sm text-muted-foreground">Aktive Gruppen</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-secondary">134</div>
              <div className="text-sm text-muted-foreground">Ø Gruppengröße</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-accent">92%</div>
              <div className="text-sm text-muted-foreground">Treffergenauigkeit</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-muted">24h</div>
              <div className="text-sm text-muted-foreground">Update-Intervall</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SmartGroups;