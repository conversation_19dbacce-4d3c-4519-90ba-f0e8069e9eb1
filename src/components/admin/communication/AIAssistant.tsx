import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useClub } from "@/contexts/ClubContext";
import { demoDataService } from '@/services/DemoDataService';
import { Sparkles, MessageSquare, BarChart3, Target, Lightbulb, Send, RefreshCw, Copy, Download } from "lucide-react";

const AIAssistant = () => {
  const { toast } = useToast();
  const { currentClubId } = useClub();
  const [message, setMessage] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [selectedTone, setSelectedTone] = useState("friendly");

  // 🎯 CENTRALIZED DEMO DATA: Campaign suggestions from service
  const suggestions = demoDataService.getDemoCampaignSuggestions();

  const aiCapabilities = [
    {
      title: "Content Generation",
      description: "Automatische Erstellung von Nachrichten basierend auf Kontext und Zielgruppe",
      features: ["Multi-Kanal Optimierung", "Persona-basierter Ton", "A/B Test Varianten"]
    },
    {
      title: "Smart Analytics",
      description: "Intelligente Analyse von Kommunikationsmustern und Engagement",
      features: ["Engagement Vorhersage", "Optimale Zeiten", "Zielgruppen-Insights"]
    },
    {
      title: "Automation",
      description: "Automatisierte Workflows basierend auf Mitgliederverhalten",
      features: ["Trigger-basierte Messages", "Follow-up Sequenzen", "Smart Scheduling"]
    }
  ];

  const handleGenerate = async () => {
    if (!message) return;
    
    setIsGenerating(true);
    console.log("🤖 Generating AI content with:", { message, selectedTone });
    
    try {
      const { data, error } = await supabase.functions.invoke('ai-message-generator', {
        body: {
          prompt: message,
          tone: selectedTone,
          targetGroup: 'all',
          channels: ['email', 'push', 'dashboard'],
          clubId: currentClubId
        }
      });

      if (error) {
        console.error("❌ AI generation error:", error);
        toast({
          title: "Fehler",
          description: "AI-Generierung fehlgeschlagen",
          variant: "destructive"
        });
        return;
      }

      console.log("✅ AI generation successful:", data);
      toast({
        title: "Erfolg",
        description: "AI-Inhalte erfolgreich generiert",
      });

    } catch (error) {
      console.error("💥 Unexpected error:", error);
      toast({
        title: "Fehler", 
        description: "Unerwarteter Fehler bei AI-Generierung",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">AI Communication Studio</h2>
          <p className="text-muted-foreground">
            Intelligente Kommunikation powered by OpenAI
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" className="gap-2">
            <BarChart3 className="h-4 w-4" />
            Analytics
          </Button>
          <Button className="gap-2">
            <Sparkles className="h-4 w-4" />
            AI Magic
          </Button>
        </div>
      </div>

      <Tabs defaultValue="generate" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="generate" className="gap-2">
            <Sparkles className="h-4 w-4" />
            Generate
          </TabsTrigger>
          <TabsTrigger value="insights" className="gap-2">
            <BarChart3 className="h-4 w-4" />
            Insights
          </TabsTrigger>
          <TabsTrigger value="suggestions" className="gap-2">
            <Lightbulb className="h-4 w-4" />
            Suggestions
          </TabsTrigger>
          <TabsTrigger value="automation" className="gap-2">
            <Target className="h-4 w-4" />
            Automation
          </TabsTrigger>
        </TabsList>

        <TabsContent value="generate" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* AI Input */}
            <div className="lg:col-span-2 space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MessageSquare className="h-5 w-5" />
                    AI Message Generator
                  </CardTitle>
                  <CardDescription>
                    Beschreiben Sie, was Sie kommunizieren möchten
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Textarea 
                    placeholder="z.B. 'Informiere alle Mitglieder über das Sommerturnier am 15. Juli. Betonung auf Anmeldefrist bis 1. Juli und Preise für Gewinner.'"
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    className="min-h-[100px]"
                  />
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Zielgruppe</label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Gruppe wählen" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">Alle Mitglieder</SelectItem>
                          <SelectItem value="active">Aktive Spieler</SelectItem>
                          <SelectItem value="tournament">Turnierspieler</SelectItem>
                          <SelectItem value="junior">Jugendliche</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Kommunikations-Ton</label>
                      <Select value={selectedTone} onValueChange={setSelectedTone}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="friendly">Freundlich</SelectItem>
                          <SelectItem value="formal">Formal</SelectItem>
                          <SelectItem value="urgent">Dringend</SelectItem>
                          <SelectItem value="energetic">Energisch</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <Button 
                    onClick={handleGenerate} 
                    disabled={!message || isGenerating}
                    className="w-full gap-2"
                  >
                    {isGenerating ? (
                      <>
                        <RefreshCw className="h-4 w-4 animate-spin" />
                        AI generiert...
                      </>
                    ) : (
                      <>
                        <Sparkles className="h-4 w-4" />
                        Mit AI generieren
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>

              {/* Generated Results */}
              <Card>
                <CardHeader>
                  <CardTitle>AI-Generierte Nachrichten</CardTitle>
                  <CardDescription>
                    Optimiert für verschiedene Kanäle
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Tabs defaultValue="email" className="w-full">
                    <TabsList className="grid w-full grid-cols-3">
                      <TabsTrigger value="email">E-Mail</TabsTrigger>
                      <TabsTrigger value="push">Push</TabsTrigger>
                      <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
                    </TabsList>
                    
                    <TabsContent value="email" className="space-y-3">
                      <div className="p-4 bg-muted rounded-lg">
                        <div className="font-medium mb-2">Betreff:</div>
                        <div>🎾 Sommerturnier 2024 - Jetzt anmelden und gewinnen!</div>
                      </div>
                      <div className="p-4 bg-muted rounded-lg">
                        <div className="font-medium mb-2">Nachricht:</div>
                        <div className="whitespace-pre-wrap">
                          Liebe Tennis-Freunde,

                          unser großes Sommerturnier steht vor der Tür! 🏆

                          📅 Wann: 15. Juli 2024
                          🏟️ Wo: Vereinsanlage
                          💰 Tolle Preise für alle Gewinner

                          ⏰ WICHTIG: Anmeldeschluss ist der 1. Juli!

                          Meldet euch jetzt an und zeigt euer Können auf dem Platz.

                          Sportliche Grüße,
                          Euer Tennis-Team
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm" className="gap-2">
                          <Copy className="h-4 w-4" />
                          Kopieren
                        </Button>
                        <Button variant="outline" size="sm" className="gap-2">
                          <RefreshCw className="h-4 w-4" />
                          Neu generieren
                        </Button>
                        <Button size="sm" className="gap-2">
                          <Send className="h-4 w-4" />
                          Verwenden
                        </Button>
                      </div>
                    </TabsContent>
                    
                    <TabsContent value="push" className="space-y-3">
                      <div className="p-4 bg-muted rounded-lg">
                        <div className="font-medium mb-2">Push Notification:</div>
                        <div>🎾 Sommerturnier: Nur noch 2 Wochen bis Anmeldeschluss! Jetzt anmelden.</div>
                      </div>
                    </TabsContent>
                    
                    <TabsContent value="dashboard" className="space-y-3">
                      <div className="p-4 bg-muted rounded-lg">
                        <div className="font-medium mb-2">Dashboard News:</div>
                        <div>
                          <h3 className="font-bold">Sommerturnier 2024 - Anmeldung läuft!</h3>
                          <p>Sichert euch jetzt euren Platz beim großen Vereinsturnier am 15. Juli. Anmeldeschluss: 1. Juli.</p>
                        </div>
                      </div>
                    </TabsContent>
                  </Tabs>
                </CardContent>
              </Card>
            </div>

            {/* AI Capabilities */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>AI Features</CardTitle>
                  <CardDescription>
                    Was unser AI Assistant kann
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {aiCapabilities.map((capability, index) => (
                    <div key={index} className="space-y-2">
                      <h4 className="font-medium">{capability.title}</h4>
                      <p className="text-sm text-muted-foreground">{capability.description}</p>
                      <div className="flex flex-wrap gap-1">
                        {capability.features.map((feature, idx) => (
                          <Badge key={idx} variant="outline" className="text-xs">
                            {feature}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <Button variant="outline" className="w-full justify-start gap-2">
                    <Lightbulb className="h-4 w-4" />
                    Content-Ideen generieren
                  </Button>
                  <Button variant="outline" className="w-full justify-start gap-2">
                    <Target className="h-4 w-4" />
                    Zielgruppe optimieren
                  </Button>
                  <Button variant="outline" className="w-full justify-start gap-2">
                    <BarChart3 className="h-4 w-4" />
                    Performance analysieren
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="insights" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6 text-center">
                <div className="text-2xl font-bold text-green-600">94%</div>
                <div className="text-sm text-muted-foreground">Zustellrate</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6 text-center">
                <div className="text-2xl font-bold text-blue-600">67%</div>
                <div className="text-sm text-muted-foreground">Öffnungsrate</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6 text-center">
                <div className="text-2xl font-bold text-purple-600">23%</div>
                <div className="text-sm text-muted-foreground">Click-Rate</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6 text-center">
                <div className="text-2xl font-bold text-orange-600">18:30</div>
                <div className="text-sm text-muted-foreground">Beste Zeit</div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="suggestions" className="space-y-6">
          <div className="space-y-4">
            {suggestions.map((suggestion) => (
              <Card key={suggestion.id}>
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <Badge variant="outline">{suggestion.category}</Badge>
                        <div className={`w-2 h-2 rounded-full ${getPriorityColor(suggestion.priority)}`} />
                      </div>
                      <h3 className="font-semibold mb-1">{suggestion.title}</h3>
                      <p className="text-muted-foreground text-sm mb-3">{suggestion.description}</p>
                      <p className="text-sm font-medium text-primary">{suggestion.action}</p>
                    </div>
                    <Button variant="outline" size="sm">
                      Umsetzen
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="automation">
          <Card>
            <CardHeader>
              <CardTitle>Automation Workflows</CardTitle>
              <CardDescription>
                Intelligente Automatisierung kommt bald...
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center py-12">
              <div className="space-y-4">
                <div className="text-4xl">🚀</div>
                <h3 className="text-xl font-semibold">Coming Soon</h3>
                <p className="text-muted-foreground">
                  Workflow-Automation wird in der nächsten Version verfügbar sein
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AIAssistant;