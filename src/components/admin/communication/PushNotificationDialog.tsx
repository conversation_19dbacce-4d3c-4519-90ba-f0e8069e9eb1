import { useState } from "react";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useCommunication } from "@/hooks/useCommunication";
import { Bell, Send, Target, Users } from "lucide-react";

interface PushNotificationDialogProps {
  trigger?: React.ReactNode;
  clubId?: string;
}

const PushNotificationDialog = ({ trigger, clubId }: PushNotificationDialogProps) => {
  const { sendPushNotification } = useCommunication(clubId);
  const [isOpen, setIsOpen] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    message: '',
    targetGroup: 'all_members'
  });

  const targetGroups = [
    { id: 'all_members', label: 'Alle Mitglieder', count: 247 },
    { id: 'active_players', label: 'Aktive Spieler', count: 189 },
    { id: 'tournament_players', label: 'Turnierspieler', count: 73 },
    { id: 'junior_members', label: 'Jugendliche', count: 54 },
    { id: 'board_members', label: 'Vorstand', count: 8 }
  ];

  const getRecipientCount = () => {
    const group = targetGroups.find(g => g.id === formData.targetGroup);
    return group?.count || 0;
  };

  const handleSend = async () => {
    if (!formData.title || !formData.message) return;

    setIsSending(true);
    try {
      // This would normally fetch the actual recipients based on the target group
      const mockRecipients = Array.from({ length: getRecipientCount() }, (_, i) => `user-${i}`);
      
      await sendPushNotification(mockRecipients, formData.title, formData.message);
      
      // Reset form and close dialog
      setFormData({ title: '', message: '', targetGroup: 'all_members' });
      setIsOpen(false);
    } catch (error) {
      console.error("Error sending push notification:", error);
    } finally {
      setIsSending(false);
    }
  };

  const defaultTrigger = (
    <Button className="gap-2">
      <Bell className="h-4 w-4" />
      Push-Nachricht senden
    </Button>
  );

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Push-Benachrichtigung senden
          </DialogTitle>
          <DialogDescription>
            Senden Sie eine sofortige Benachrichtigung an ausgewählte Mitglieder
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="title">Titel *</Label>
            <Input
              id="title"
              placeholder="z.B. Wichtige Mitteilung"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="message">Nachricht *</Label>
            <Textarea
              id="message"
              placeholder="Ihre Push-Nachricht..."
              className="min-h-[80px]"
              value={formData.message}
              onChange={(e) => setFormData(prev => ({ ...prev, message: e.target.value }))}
            />
            <div className="text-xs text-muted-foreground">
              {formData.message.length}/100 Zeichen (empfohlen)
            </div>
          </div>

          <div className="space-y-2">
            <Label>Zielgruppe</Label>
            <Select 
              value={formData.targetGroup} 
              onValueChange={(value) => setFormData(prev => ({ ...prev, targetGroup: value }))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {targetGroups.map((group) => (
                  <SelectItem key={group.id} value={group.id}>
                    <div className="flex items-center justify-between w-full">
                      <span>{group.label}</span>
                      <Badge variant="secondary" className="ml-2">
                        {group.count}
                      </Badge>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
            <Target className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm">
              Wird an <strong>{getRecipientCount()}</strong> Empfänger gesendet
            </span>
          </div>

          <div className="flex gap-2 pt-4">
            <Button 
              variant="outline" 
              className="flex-1"
              onClick={() => setIsOpen(false)}
            >
              Abbrechen
            </Button>
            <Button 
              className="flex-1 gap-2"
              onClick={handleSend}
              disabled={!formData.title || !formData.message || isSending}
            >
              {isSending ? (
                <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
              {isSending ? 'Wird gesendet...' : 'Senden'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PushNotificationDialog;