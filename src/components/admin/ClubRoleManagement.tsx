import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>tings, Shield, Check, X, Plus, Trash2, AlertTriangle, RefreshCw, Crown, UserCheck, GraduationCap, UserPlus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";
import { useTenant } from "@/contexts/TenantContext";
import { useAdminRole } from "@/hooks/useAdminRole";
import type { Database } from "@/integrations/supabase/types";

// Type-safe interfaces using Supabase generated types
type ClubRole = Database['public']['Tables']['club_roles']['Row'];
type ClubRoleInsert = Database['public']['Tables']['club_roles']['Insert'];
type ClubRoleAssignment = Database['public']['Tables']['club_role_assignments']['Row'];

interface ClubRoleStats {
  role: ClubRole;
  userCount: number;
}

interface SystemRole {
  name: string;
  display_name: string;
  description: string;
  icon: any;
  color: string;
  is_system: true;
}

const ClubRoleManagement = () => {
  const [clubRoles, setClubRoles] = useState<ClubRole[]>([]);
  const [roleStats, setRoleStats] = useState<ClubRoleStats[]>([]);
  const [systemRoleStats, setSystemRoleStats] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [newRoleName, setNewRoleName] = useState('');
  const [newRoleDisplayName, setNewRoleDisplayName] = useState('');
  const [newRoleDescription, setNewRoleDescription] = useState('');
  const [creatingRole, setCreatingRole] = useState(false);
  
  const { club: currentClub } = useTenant();
  const { isClubAdmin } = useAdminRole();

  // System-Rollen (unveränderlich)
  const systemRoles: SystemRole[] = [
    { 
      name: 'admin', 
      display_name: 'Club Admin', 
      description: 'Vollzugriff auf Club-Verwaltung und -Einstellungen',
      icon: Crown,
      color: 'bg-red-100 text-red-800 border-red-200',
      is_system: true
    },
    { 
      name: 'trainer', 
      display_name: 'Trainer', 
      description: 'Training, Kurse und Teilnehmerverwaltung',
      icon: GraduationCap,
      color: 'bg-blue-100 text-blue-800 border-blue-200',
      is_system: true
    },
    { 
      name: 'member', 
      display_name: 'Mitglied', 
      description: 'Standard Club-Mitglied mit Buchungsrechten',
      icon: UserCheck,
      color: 'bg-green-100 text-green-800 border-green-200',
      is_system: true
    },
    { 
      name: 'guest', 
      display_name: 'Gast', 
      description: 'Temporärer Zugang mit eingeschränkten Rechten',
      icon: UserPlus,
      color: 'bg-gray-100 text-gray-800 border-gray-200',
      is_system: true
    }
  ];

  // Standard club-spezifische Rollen
  const defaultClubRoles = [
    { name: 'vereinsvorstand', display_name: 'Vereinsvorstand', description: 'Führungsverantwortung im Verein' },
    { name: 'platzwart', display_name: 'Platzwart', description: 'Verantwortlich für Platzpflege und -wartung' },
    { name: 'jugendtrainer', display_name: 'Jugendtrainer', description: 'Training und Betreuung der Jugend' },
    { name: 'kassierer', display_name: 'Kassierer', description: 'Finanzverantwortung des Vereins' },
    { name: 'sportleiter', display_name: 'Sportleiter', description: 'Organisation von Turnieren und Events' }
  ];

  useEffect(() => {
    if (isDialogOpen && currentClub?.id && isClubAdmin) {
      fetchClubRoles();
      fetchSystemRoleStats();
    }
  }, [isDialogOpen, currentClub?.id, isClubAdmin]);

  const fetchSystemRoleStats = async () => {
    if (!currentClub?.id) return;
    
    try {
      // Hole System-Rollen Statistiken aus club_memberships
      const { data: memberships, error } = await supabase
        .from('club_memberships')
        .select('role')
        .eq('club_id', currentClub.id)
        .eq('is_active', true);

      if (error) {
        console.error('Error fetching system role stats:', error);
        return;
      }

      // Berechne Statistiken für System-Rollen
      const stats = systemRoles.map(systemRole => {
        const userCount = memberships?.filter(m => m.role === systemRole.name).length || 0;
        return {
          ...systemRole,
          userCount
        };
      });

      setSystemRoleStats(stats);
    } catch (error) {
      console.error('Error fetching system role stats:', error);
    }
  };

  const fetchClubRoles = async () => {
    if (!currentClub?.id) return;
    
    try {
      setLoading(true);
      
      // Erstelle club_roles Tabelle falls sie nicht existiert (graceful fallback)
      await initializeClubRolesTable();
      
      // Fetch club-spezifische Rollen - TYPE SAFE
      const { data: roles, error: rolesError } = await supabase
        .from('club_roles')
        .select('*')
        .eq('club_id', currentClub.id)
        .eq('is_active', true)
        .order('display_name');

      if (rolesError) {
        console.error('Error fetching club roles:', rolesError);
        // Graceful degradation statt crash
        setClubRoles([]);
        toast({
          title: "Warnung",
          description: "Club-Rollen konnten nicht geladen werden. Bitte versuchen Sie es erneut.",
          variant: "destructive",
        });
        return;
      }

      // Fetch Rollenzuweisungen für Statistiken - TYPE SAFE  
      const { data: assignments, error: assignmentsError } = await supabase
        .from('club_role_assignments')
        .select('club_role_id')
        .eq('club_id', currentClub.id);

      if (assignmentsError) {
        console.error('Error fetching assignments:', assignmentsError);
        // Continue with empty assignments for statistics
      }

      setClubRoles(roles || []);

      // Berechne Statistiken - TYPE SAFE
      const stats = (roles || []).map(role => {
        const userCount = assignments?.filter(a => a.club_role_id === role.id).length || 0;
        return {
          role,
          userCount
        };
      });

      setRoleStats(stats);
      
    } catch (error) {
      console.error('Error fetching club roles:', error);
      toast({
        title: "Information",
        description: "Club-spezifische Rollen sind noch nicht eingerichtet. Standard-Rollen werden verwendet.",
        variant: "default",
      });
    } finally {
      setLoading(false);
    }
  };

  const initializeClubRolesTable = async () => {
    // Diese Funktion würde normalerweise über eine Migration laufen
    // Hier als graceful fallback für Demo-Zwecke
    console.log('Club roles system would be initialized via migration in production');
  };

  const createClubRole = async () => {
    if (!newRoleName.trim() || !newRoleDisplayName.trim() || !currentClub?.id) {
      toast({
        title: "Fehler",
        description: "Bitte füllen Sie alle Pflichtfelder aus.",
        variant: "destructive",
      });
      return;
    }

    try {
      setCreatingRole(true);
      
      // Prüfe ob Rolle bereits existiert
      const existingRole = clubRoles.find(r => 
        r.name.toLowerCase() === newRoleName.toLowerCase().trim()
      );
      
      if (existingRole) {
        toast({
          title: "Fehler",
          description: "Eine Rolle mit diesem Namen existiert bereits.",
          variant: "destructive",
        });
        return;
      }

      // TYPE SAFE Insert mit korrekten Supabase Types + User Context
      const newRole: ClubRoleInsert = {
        name: newRoleName.toLowerCase().trim(),
        display_name: newRoleDisplayName.trim(),
        description: newRoleDescription.trim() || null,
        club_id: currentClub.id,
        is_active: true,
        created_by: null // User context will be added in future iteration
      };

      const { data, error } = await supabase
        .from('club_roles')
        .insert(newRole)
        .select()
        .maybeSingle(); // SECURITY FIX: Safe single row fetch

      if (error) {
        console.error('Database error creating role:', error);
        throw new Error(`Datenbankfehler: ${error.message}`);
      }

      if (!data) {
        throw new Error('Rolle wurde nicht erstellt - unbekannter Fehler');
      }

      toast({
        title: "✅ Rolle erfolgreich erstellt",
        description: `Club-Rolle "${newRoleDisplayName}" ist jetzt verfügbar.`,
      });

      // Reset form und refresh data
      setNewRoleName('');
      setNewRoleDisplayName('');
      setNewRoleDescription('');
      await fetchClubRoles();
      
    } catch (error) {
      console.error('Error creating club role:', error);
      toast({
        title: "❌ Fehler beim Erstellen",
        description: error instanceof Error ? error.message : "Die Club-Rolle konnte nicht erstellt werden.",
        variant: "destructive",
      });
    } finally {
      setCreatingRole(false);
    }
  };

  const toggleRoleStatus = async (roleId: string, isActive: boolean) => {
    try {
      // TYPE SAFE Update
      const { error } = await supabase
        .from('club_roles')
        .update({ is_active: !isActive, updated_at: new Date().toISOString() })
        .eq('id', roleId)
        .eq('club_id', currentClub?.id);

      if (error) throw error;

      toast({
        title: "Erfolgreich",
        description: `Rolle wurde ${!isActive ? 'aktiviert' : 'deaktiviert'}.`,
      });

      await fetchClubRoles();
    } catch (error) {
      console.error('Error toggling role status:', error);
      toast({
        title: "Fehler",
        description: "Rollenstatus konnte nicht geändert werden.",
        variant: "destructive",
      });
    }
  };

  // Frühe Return-Guards für optimale UX
  if (!isClubAdmin) {
    return (
      <Button variant="outline" disabled className="gap-2">
        <Shield className="h-4 w-4" />
        Keine Berechtigung für Club-Rollen
      </Button>
    );
  }

  if (!currentClub?.id) {
    return (
      <Button variant="outline" disabled className="gap-2">
        <Shield className="h-4 w-4" />
        Club-Rollen nicht verfügbar
      </Button>
    );
  }

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="gap-2">
          <Shield className="h-4 w-4" />
          Club-Rollen verwalten
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Rollen-Übersicht - {currentClub.name}
          </DialogTitle>
          <DialogDescription>
            Übersicht aller System-Rollen (unveränderlich) und Club-spezifischen Rollen für {currentClub.name}
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Alle Rollen</TabsTrigger>
            <TabsTrigger value="system">System-Rollen</TabsTrigger>
            <TabsTrigger value="manage">Club-Rollen verwalten</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="mt-6">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-semibold">Komplette Rollen-Übersicht</h3>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  fetchClubRoles();
                  fetchSystemRoleStats();
                }}
                disabled={loading}
                className="gap-2"
              >
                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                Aktualisieren
              </Button>
            </div>

            <div className="space-y-6">
              {/* System-Rollen Sektion */}
              <div>
                <h4 className="text-md font-medium mb-3 flex items-center gap-2">
                  <Crown className="h-4 w-4 text-orange-600" />
                  System-Rollen (unveränderlich)
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
                  {systemRoleStats.map((stat) => {
                    const IconComponent = stat.icon;
                    return (
                      <Card key={stat.name} className="border-2 border-dashed">
                        <CardHeader className="pb-3">
                          <CardTitle className="text-sm flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <IconComponent className="h-4 w-4" />
                              {stat.display_name}
                            </div>
                            <Badge variant="outline" className={stat.color}>
                              System
                            </Badge>
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-2">
                            <div>
                              <p className="text-xs text-muted-foreground">Aktive Benutzer</p>
                              <p className="text-lg font-bold">{stat.userCount}</p>
                            </div>
                            <div>
                              <p className="text-xs text-muted-foreground">Beschreibung</p>
                              <p className="text-xs">{stat.description}</p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              </div>

              {/* Club-Rollen Sektion */}
              <div>
                <h4 className="text-md font-medium mb-3 flex items-center gap-2">
                  <Users className="h-4 w-4 text-blue-600" />
                  Club-spezifische Rollen
                </h4>
                {roleStats.length === 0 ? (
                  <Card className="border-dashed">
                    <CardContent className="pt-6">
                      <div className="text-center space-y-4">
                        <AlertTriangle className="h-8 w-8 mx-auto text-orange-500" />
                        <div>
                          <h4 className="font-medium">Noch keine Club-Rollen definiert</h4>
                          <p className="text-sm text-muted-foreground mt-1">
                            Wechseln Sie zum Tab "Club-Rollen verwalten" um neue Rollen zu erstellen
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {roleStats.map((stat) => (
                      <Card key={stat.role.id}>
                        <CardHeader className="pb-3">
                          <CardTitle className="text-sm flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <Users className="h-4 w-4" />
                              {stat.role.display_name}
                            </div>
                            <Switch
                              checked={stat.role.is_active}
                              onCheckedChange={() => toggleRoleStatus(stat.role.id, stat.role.is_active)}
                              disabled={loading}
                            />
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-2">
                            <div>
                              <p className="text-xs text-muted-foreground">Zuweisungen</p>
                              <p className="text-lg font-bold">{stat.userCount}</p>
                            </div>
                            {stat.role.description && (
                              <div>
                                <p className="text-xs text-muted-foreground">Beschreibung</p>
                                <p className="text-xs">{stat.role.description}</p>
                              </div>
                            )}
                            <div>
                              <Badge variant={stat.role.is_active ? "default" : "secondary"} className="text-xs">
                                {stat.role.is_active ? "Aktiv" : "Inaktiv"}
                              </Badge>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="system" className="mt-6">
            <div className="space-y-4">
              <div className="flex items-center gap-2 mb-4">
                <Crown className="h-5 w-5 text-orange-600" />
                <h3 className="text-lg font-semibold">System-Rollen</h3>
                <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
                  Unveränderlich
                </Badge>
              </div>
              
              <div className="bg-amber-50 p-4 rounded-lg border border-amber-200">
                <div className="flex items-start gap-2">
                  <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-amber-800">System-Rollen Information</h4>
                    <p className="text-sm text-amber-700 mt-1">
                      Diese Rollen sind fester Bestandteil des Systems und können nicht geändert oder gelöscht werden. 
                      Sie definieren die grundlegenden Zugriffsebenen für alle Benutzer.
                    </p>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {systemRoleStats.map((stat) => {
                  const IconComponent = stat.icon;
                  return (
                    <Card key={stat.name} className="border-2">
                      <CardHeader>
                        <CardTitle className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className="p-2 rounded-full bg-muted">
                              <IconComponent className="h-5 w-5" />
                            </div>
                            <div>
                              <h4 className="text-lg">{stat.display_name}</h4>
                              <p className="text-xs text-muted-foreground font-mono">
                                system.{stat.name}
                              </p>
                            </div>
                          </div>
                          <Badge variant="outline" className={stat.color}>
                            {stat.userCount} aktiv
                          </Badge>
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-muted-foreground">{stat.description}</p>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="manage" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Neue Club-Rolle erstellen</CardTitle>
                <p className="text-sm text-muted-foreground">
                  Erstellen Sie eine neue club-spezifische Rolle für Ihren Verein
                </p>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="roleName">Rollenname (System) *</Label>
                    <Input
                      id="roleName"
                      value={newRoleName}
                      onChange={(e) => setNewRoleName(e.target.value)}
                      placeholder="z.B. vereinsvorstand"
                      disabled={creatingRole}
                    />
                    <p className="text-xs text-muted-foreground">
                      Kleinbuchstaben, keine Leerzeichen
                    </p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="roleDisplayName">Anzeigename *</Label>
                    <Input
                      id="roleDisplayName"
                      value={newRoleDisplayName}
                      onChange={(e) => setNewRoleDisplayName(e.target.value)}
                      placeholder="z.B. Vereinsvorstand"
                      disabled={creatingRole}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="roleDescription">Beschreibung</Label>
                  <Input
                    id="roleDescription"
                    value={newRoleDescription}
                    onChange={(e) => setNewRoleDescription(e.target.value)}
                    placeholder="z.B. Führungsverantwortung im Verein"
                    disabled={creatingRole}
                  />
                </div>

                <div className="bg-amber-50 p-4 rounded-lg">
                  <div className="flex items-start gap-2">
                    <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-amber-800">Wichtiger Hinweis</h4>
                      <p className="text-sm text-amber-700 mt-1">
                        Diese Club-Rollen sind zusätzlich zu den System-Rollen (Admin, Mitglied, etc.) 
                        und dienen der internen Vereinsorganisation.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="flex justify-end gap-2">
                  <Button
                    onClick={createClubRole}
                    disabled={creatingRole || !newRoleName.trim() || !newRoleDisplayName.trim()}
                    className="gap-2"
                  >
                    {creatingRole ? (
                      <RefreshCw className="h-4 w-4 animate-spin" />
                    ) : (
                      <Plus className="h-4 w-4" />
                    )}
                    Rolle erstellen
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card className="mt-6">
              <CardHeader>
                <CardTitle>Empfohlene Standard-Rollen</CardTitle>
                <p className="text-sm text-muted-foreground">
                  Klicken Sie auf eine Rolle, um sie schnell zu erstellen
                </p>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {defaultClubRoles.map((role, index) => (
                    <Card key={index} className="cursor-pointer hover:bg-muted/50 transition-colors"
                          onClick={() => {
                            setNewRoleName(role.name);
                            setNewRoleDisplayName(role.display_name);
                            setNewRoleDescription(role.description);
                          }}>
                      <CardContent className="pt-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="font-medium">{role.display_name}</h4>
                            <p className="text-sm text-muted-foreground">{role.description}</p>
                          </div>
                          <Plus className="h-4 w-4 text-muted-foreground" />
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};

export default ClubRoleManagement;
