import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, Di<PERSON>Footer, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

interface FormatConfig {
  id: string;
  club_id: string;
  tournament_id?: string;
  format_type: string;
  custom_rules: Record<string, any>;
  custom_description?: string;
  custom_name?: string;
  time_config: Record<string, any>;
  created_at: string;
  updated_at: string;
}

interface FormatConfigDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  config: FormatConfig;
  onSave: (config: FormatConfig) => void;
}

// Common rule templates for different formats
const RULE_TEMPLATES = {
  uts: {
    live_display: { type: 'boolean', label: 'Live-Anzeige verfügbar', default: false },
    shot_clock: { type: 'boolean', label: 'Shot Clock (automatisch)', default: false },
    manual_scoring: { type: 'boolean', label: 'Manuelle Punktezählung', default: true },
    sudden_death: { type: 'boolean', label: 'Sudden Death bei Gleichstand', default: true },
    no_advantage: { type: 'boolean', label: 'No-Ad Scoring', default: true },
    timeout_allowed: { type: 'boolean', label: 'Timeouts erlaubt', default: true },
    coaching_allowed: { type: 'boolean', label: 'Coaching zwischen Punkten', default: true }
  },
  knockout: {
    best_of_sets: { type: 'number', label: 'Best-of Sätze', default: 3 },
    tiebreak_final_set: { type: 'boolean', label: 'Tiebreak im Entscheidungssatz', default: true },
    double_elimination: { type: 'boolean', label: 'Doppel-K.O. System', default: false },
    consolation_bracket: { type: 'boolean', label: 'Trostrunde', default: false },
    walkover_advance: { type: 'boolean', label: 'Freilos bei ungerader Teilnehmerzahl', default: true }
  },
  round_robin: {
    sets_to_win: { type: 'number', label: 'Gewinnsätze', default: 2 },
    group_size: { type: 'number', label: 'Gruppengröße', default: 4 },
    advancement_count: { type: 'number', label: 'Qualifikation pro Gruppe', default: 2 },
    playoff_format: { type: 'select', label: 'Playoff-Format', options: ['knockout', 'final_only'], default: 'knockout' }
  }
};

export function FormatConfigDialog({ open, onOpenChange, config, onSave }: FormatConfigDialogProps) {
  const [editedConfig, setEditedConfig] = useState<FormatConfig>(config);
  const [activeTab, setActiveTab] = useState('general');

  useEffect(() => {
    setEditedConfig(config);
  }, [config]);

  const handleSave = () => {
    onSave(editedConfig);
  };

  const updateCustomRules = (key: string, value: any) => {
    setEditedConfig(prev => ({
      ...prev,
      custom_rules: {
        ...prev.custom_rules,
        [key]: value
      }
    }));
  };

  const updateTimeConfig = (key: string, value: any) => {
    setEditedConfig(prev => ({
      ...prev,
      time_config: {
        ...prev.time_config,
        [key]: value
      }
    }));
  };

  const getFormatLabel = () => {
    const labels = {
      uts: 'UTS (Ultimate Tennis Showdown)',
      knockout: 'K.O.-System',
      round_robin: 'Jeder gegen Jeden',
      swiss: 'Schweizer System',
      double_elimination: 'Doppel-K.O.'
    };
    return labels[editedConfig.format_type as keyof typeof labels] || editedConfig.format_type;
  };

  const renderRuleFields = () => {
    const templates = RULE_TEMPLATES[editedConfig.format_type as keyof typeof RULE_TEMPLATES];
    if (!templates) return <div>Keine speziellen Regeln für dieses Format verfügbar.</div>;

    return (
      <div className="space-y-4">
        {Object.entries(templates).map(([key, template]) => (
          <div key={key} className="flex items-center justify-between">
            <Label className="text-sm font-medium">{template.label}</Label>
            {template.type === 'boolean' && (
              <Checkbox
                checked={editedConfig.custom_rules[key] ?? template.default}
                onCheckedChange={(checked) => updateCustomRules(key, checked)}
              />
            )}
            {template.type === 'number' && (
              <Input
                type="number"
                className="w-20"
                value={editedConfig.custom_rules[key] ?? template.default}
                onChange={(e) => updateCustomRules(key, parseInt(e.target.value))}
              />
            )}
            {template.type === 'select' && (
              <select
                className="w-32 p-1 border rounded"
                value={editedConfig.custom_rules[key] ?? template.default}
                onChange={(e) => updateCustomRules(key, e.target.value)}
              >
                {template.options?.map(option => (
                  <option key={option} value={option}>{option}</option>
                ))}
              </select>
            )}
          </div>
        ))}
      </div>
    );
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            Format konfigurieren: {getFormatLabel()}
          </DialogTitle>
          <DialogDescription>
            Passen Sie das Turnierformat an die Gegebenheiten Ihres Vereins an.
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="general">Allgemein</TabsTrigger>
            <TabsTrigger value="rules">Spielregeln</TabsTrigger>
            <TabsTrigger value="timing">Zeitplanung</TabsTrigger>
            <TabsTrigger value="preview">Vorschau</TabsTrigger>
          </TabsList>

          <TabsContent value="general" className="space-y-4">
            <div className="grid gap-4">
              <div className="space-y-2">
                <Label htmlFor="custom_name">Angepasster Name (optional)</Label>
                <Input
                  id="custom_name"
                  value={editedConfig.custom_name || ''}
                  onChange={(e) => setEditedConfig(prev => ({ ...prev, custom_name: e.target.value }))}
                  placeholder={getFormatLabel()}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="custom_description">Angepasste Beschreibung</Label>
                <Textarea
                  id="custom_description"
                  value={editedConfig.custom_description || ''}
                  onChange={(e) => setEditedConfig(prev => ({ ...prev, custom_description: e.target.value }))}
                  placeholder="Beschreiben Sie die Besonderheiten dieses Formats in Ihrem Verein..."
                  rows={4}
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="rules" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Format-spezifische Regeln</CardTitle>
                <CardDescription>
                  Aktivieren oder deaktivieren Sie Features basierend auf den Möglichkeiten Ihres Vereins.
                </CardDescription>
              </CardHeader>
              <CardContent>
                {renderRuleFields()}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="timing" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Zeitkonfiguration</CardTitle>
                <CardDescription>
                  Legen Sie zeitliche Parameter für das Format fest.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="match_duration">Spieldauer (Minuten)</Label>
                    <Input
                      id="match_duration"
                      type="number"
                      value={editedConfig.time_config.match_duration || 90}
                      onChange={(e) => updateTimeConfig('match_duration', parseInt(e.target.value))}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="warmup_time">Aufwärmzeit (Minuten)</Label>
                    <Input
                      id="warmup_time"
                      type="number"
                      value={editedConfig.time_config.warmup_time || 5}
                      onChange={(e) => updateTimeConfig('warmup_time', parseInt(e.target.value))}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="changeover_time">Seitenwechsel (Sekunden)</Label>
                    <Input
                      id="changeover_time"
                      type="number"
                      value={editedConfig.time_config.changeover_time || 90}
                      onChange={(e) => updateTimeConfig('changeover_time', parseInt(e.target.value))}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="between_sets">Pause zwischen Sätzen (Minuten)</Label>
                    <Input
                      id="between_sets"
                      type="number"
                      value={editedConfig.time_config.between_sets || 2}
                      onChange={(e) => updateTimeConfig('between_sets', parseInt(e.target.value))}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="preview" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Vorschau der finalen Beschreibung</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <h4 className="font-semibold">
                    {editedConfig.custom_name || getFormatLabel()}
                  </h4>
                  <p className="text-muted-foreground">
                    {editedConfig.custom_description || 'Keine spezielle Beschreibung definiert.'}
                  </p>
                  
                  <div className="mt-4">
                    <h5 className="font-medium mb-2">Aktivierte Features:</h5>
                    <div className="flex flex-wrap gap-2">
                      {Object.entries(editedConfig.custom_rules)
                        .filter(([_, value]) => value === true)
                        .map(([key, _]) => {
                          const template = RULE_TEMPLATES[editedConfig.format_type as keyof typeof RULE_TEMPLATES]?.[key];
                          return template ? (
                            <span key={key} className="px-2 py-1 bg-primary/10 rounded text-sm">
                              {template.label}
                            </span>
                          ) : null;
                        })}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Abbrechen
          </Button>
          <Button onClick={handleSave}>
            Konfiguration speichern
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}