import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { X } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/contexts/AuthContext";

interface ClubSettings {
  work_service_enabled: boolean;
  work_service_minimum_hours_enabled: boolean;
  default_annual_work_hours: number;
  work_service_min_age?: number;
  work_service_max_age?: number;
  work_service_exempt_membership_types: string[];
  work_service_exempt_roles: string[];
}

interface MembershipType {
  value: string;
  display_name: string;
}

const WorkServiceConfiguration = () => {
  const [settings, setSettings] = useState<ClubSettings>({
    work_service_enabled: false,
    work_service_minimum_hours_enabled: false,
    default_annual_work_hours: 20,
    work_service_exempt_membership_types: [],
    work_service_exempt_roles: []
  });
  const [membershipTypes, setMembershipTypes] = useState<MembershipType[]>([]);
  const [loading, setLoading] = useState(false);
  const [newExemptType, setNewExemptType] = useState("");
  const [newExemptRole, setNewExemptRole] = useState("");
  const { toast } = useToast();
  const { user } = useAuth();
  const [clubSlug, setClubSlug] = useState<string>("");

  useEffect(() => {
    const fetchUserClub = async () => {
      if (!user) return;
      
      try {
        const { data, error } = await supabase
          .from("club_memberships")
          .select("clubs(slug)")
          .eq("user_id", user.id)
          .eq("is_active", true)
          .single();

        if (error) throw error;
        if (data?.clubs?.slug) {
          setClubSlug(data.clubs.slug);
        }
      } catch (error) {
        console.error("Error fetching club:", error);
      }
    };

    fetchUserClub();
  }, [user]);

  useEffect(() => {
    if (clubSlug) {
      fetchClubSettings();
      fetchMembershipTypes();
    }
  }, [clubSlug]);

  const fetchClubSettings = async () => {
    try {
      const { data, error } = await supabase
        .from("clubs")
        .select(`
          work_service_enabled,
          work_service_minimum_hours_enabled,
          default_annual_work_hours,
          work_service_min_age,
          work_service_max_age,
          work_service_exempt_membership_types,
          work_service_exempt_roles
        `)
        .eq("slug", clubSlug)
        .single();

      if (error) throw error;
      
      if (data) {
        setSettings({
          work_service_enabled: data.work_service_enabled || false,
          work_service_minimum_hours_enabled: data.work_service_minimum_hours_enabled || false,
          default_annual_work_hours: data.default_annual_work_hours || 20,
          work_service_min_age: data.work_service_min_age,
          work_service_max_age: data.work_service_max_age,
          work_service_exempt_membership_types: data.work_service_exempt_membership_types || [],
          work_service_exempt_roles: data.work_service_exempt_roles || []
        });
      }
    } catch (error) {
      toast({
        title: "Fehler",
        description: "Konfiguration konnte nicht geladen werden",
        variant: "destructive"
      });
    }
  };

  const fetchMembershipTypes = async () => {
    try {
      const { data, error } = await supabase
        .from("membership_types")
        .select("value, display_name")
        .eq("is_active", true)
        .order("display_name");

      if (error) throw error;
      setMembershipTypes(data || []);
    } catch (error) {
      console.error("Error fetching membership types:", error);
    }
  };

  const saveSettings = async () => {
    if (!clubSlug) return;
    
    setLoading(true);
    try {
      console.log("Saving work service settings:", settings);
      console.log("Club slug:", clubSlug);
      
      const { data, error } = await supabase
        .from("clubs")
        .update({
          work_service_enabled: settings.work_service_enabled,
          work_service_minimum_hours_enabled: settings.work_service_minimum_hours_enabled,
          default_annual_work_hours: settings.default_annual_work_hours,
          work_service_min_age: settings.work_service_min_age,
          work_service_max_age: settings.work_service_max_age,
          work_service_exempt_membership_types: settings.work_service_exempt_membership_types,
          work_service_exempt_roles: settings.work_service_exempt_roles
        })
        .eq("slug", clubSlug)
        .select();

      console.log("Update result:", { data, error });

      if (error) throw error;

      toast({
        title: "Erfolg",
        description: "Konfiguration wurde gespeichert"
      });
      
      // Refresh the settings to confirm save
      setTimeout(() => {
        fetchClubSettings();
      }, 500);
    } catch (error) {
      console.error("Error saving settings:", error);
      toast({
        title: "Fehler",
        description: "Konfiguration konnte nicht gespeichert werden",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const addExemptMembershipType = () => {
    if (newExemptType && !settings.work_service_exempt_membership_types.includes(newExemptType)) {
      setSettings(prev => ({
        ...prev,
        work_service_exempt_membership_types: [...prev.work_service_exempt_membership_types, newExemptType]
      }));
      setNewExemptType("");
    }
  };

  const removeExemptMembershipType = (type: string) => {
    setSettings(prev => ({
      ...prev,
      work_service_exempt_membership_types: prev.work_service_exempt_membership_types.filter(t => t !== type)
    }));
  };

  const addExemptRole = () => {
    if (newExemptRole && !settings.work_service_exempt_roles.includes(newExemptRole)) {
      setSettings(prev => ({
        ...prev,
        work_service_exempt_roles: [...prev.work_service_exempt_roles, newExemptRole]
      }));
      setNewExemptRole("");
    }
  };

  const removeExemptRole = (role: string) => {
    setSettings(prev => ({
      ...prev,
      work_service_exempt_roles: prev.work_service_exempt_roles.filter(r => r !== role)
    }));
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Arbeitsdienst-Konfiguration</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Basic Settings */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="work_service_enabled">Arbeitsdienst aktivieren</Label>
              <Switch
                id="work_service_enabled"
                checked={settings.work_service_enabled}
                onCheckedChange={(checked) => 
                  setSettings(prev => ({ ...prev, work_service_enabled: checked }))
                }
              />
            </div>

            {settings.work_service_enabled && (
              <>
                <div className="flex items-center justify-between">
                  <Label htmlFor="minimum_hours_enabled">Mindestarbeitsstunden (Pflichtmodus)</Label>
                  <Switch
                    id="minimum_hours_enabled"
                    checked={settings.work_service_minimum_hours_enabled}
                    onCheckedChange={(checked) => 
                      setSettings(prev => ({ ...prev, work_service_minimum_hours_enabled: checked }))
                    }
                  />
                </div>

                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="default_hours">Standard Jahresstunden</Label>
                    <Input
                      id="default_hours"
                      type="number"
                      min="0"
                      value={settings.default_annual_work_hours}
                      onChange={(e) => 
                        setSettings(prev => ({ 
                          ...prev, 
                          default_annual_work_hours: parseInt(e.target.value) || 0 
                        }))
                      }
                    />
                  </div>
                  <div>
                    <Label htmlFor="min_age">Mindestalter</Label>
                    <Input
                      id="min_age"
                      type="number"
                      min="0"
                      placeholder="Optional"
                      value={settings.work_service_min_age || ""}
                      onChange={(e) => 
                        setSettings(prev => ({ 
                          ...prev, 
                          work_service_min_age: e.target.value ? parseInt(e.target.value) : undefined
                        }))
                      }
                    />
                  </div>
                  <div>
                    <Label htmlFor="max_age">Höchstalter</Label>
                    <Input
                      id="max_age"
                      type="number"
                      min="0"
                      placeholder="Optional"
                      value={settings.work_service_max_age || ""}
                      onChange={(e) => 
                        setSettings(prev => ({ 
                          ...prev, 
                          work_service_max_age: e.target.value ? parseInt(e.target.value) : undefined
                        }))
                      }
                    />
                  </div>
                </div>
              </>
            )}
          </div>

          {/* Exemption Settings */}
          {settings.work_service_enabled && settings.work_service_minimum_hours_enabled && (
            <div className="space-y-4">
              <h4 className="font-medium">Ausschluss-Konfiguration</h4>
              
              {/* Exempt Membership Types */}
              <div>
                <Label>Ausgeschlossene Mitgliedschaftstypen</Label>
                <div className="flex gap-2 mt-2">
                  <Input
                    placeholder="Neuen Typ hinzufügen..."
                    value={newExemptType}
                    onChange={(e) => setNewExemptType(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && addExemptMembershipType()}
                  />
                  <Button onClick={addExemptMembershipType} disabled={!newExemptType}>
                    Hinzufügen
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2 mt-2">
                  {settings.work_service_exempt_membership_types.map((type) => (
                    <Badge key={type} variant="secondary" className="flex items-center gap-1">
                      {type}
                      <X 
                        className="h-3 w-3 cursor-pointer" 
                        onClick={() => removeExemptMembershipType(type)}
                      />
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Exempt Roles */}
              <div>
                <Label>Ausgeschlossene Rollen</Label>
                <div className="flex gap-2 mt-2">
                  <Input
                    placeholder="Neue Rolle hinzufügen..."
                    value={newExemptRole}
                    onChange={(e) => setNewExemptRole(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && addExemptRole()}
                  />
                  <Button onClick={addExemptRole} disabled={!newExemptRole}>
                    Hinzufügen
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2 mt-2">
                  {settings.work_service_exempt_roles.map((role) => (
                    <Badge key={role} variant="secondary" className="flex items-center gap-1">
                      {role}
                      <X 
                        className="h-3 w-3 cursor-pointer" 
                        onClick={() => removeExemptRole(role)}
                      />
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Quick Exemption Buttons */}
              <div>
                <Label>Schnell-Ausschlüsse</Label>
                <div className="flex gap-2 mt-2">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => {
                      if (!settings.work_service_exempt_membership_types.includes("Jugendlich")) {
                        setSettings(prev => ({
                          ...prev,
                          work_service_exempt_membership_types: [...prev.work_service_exempt_membership_types, "Jugendlich"]
                        }));
                      }
                    }}
                  >
                    Jugendliche ausschließen
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => {
                      if (!settings.work_service_exempt_membership_types.includes("Senior")) {
                        setSettings(prev => ({
                          ...prev,
                          work_service_exempt_membership_types: [...prev.work_service_exempt_membership_types, "Senior"]
                        }));
                      }
                    }}
                  >
                    Senioren ausschließen
                  </Button>
                </div>
              </div>
            </div>
          )}

          <div className="flex justify-end">
            <Button onClick={saveSettings} disabled={loading}>
              {loading ? "Speichern..." : "Konfiguration speichern"}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default WorkServiceConfiguration;