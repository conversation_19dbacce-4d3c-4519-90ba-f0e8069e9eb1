import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Clock, Users, TrendingUp, Calendar } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import { format } from "date-fns";
import { de } from "date-fns/locale";

interface WorkStatsData {
  totalHours: number;
  activeMembers: number;
  completedTasks: number;
  monthlyHours: number;
  annualTargetHours: number;
}

interface MemberWorkSummary {
  member_id: string;
  member_name: string;
  total_hours: number;
  recent_activities: string[];
}

interface RecentActivity {
  id: string;
  activity_name: string;
  member_name: string;
  date: string;
  duration_hours: number;
}

const WorkServiceDashboard = () => {
  const [stats, setStats] = useState<WorkStatsData>({
    totalHours: 0,
    activeMembers: 0,
    completedTasks: 0,
    monthlyHours: 0,
    annualTargetHours: 0
  });
  const [memberSummaries, setMemberSummaries] = useState<MemberWorkSummary[]>([]);
  const [recentActivities, setRecentActivities] = useState<RecentActivity[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);

      // Get current user's club_id
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const { data: userMembership } = await supabase
        .from("club_memberships")
        .select("club_id")
        .eq("user_id", user.id)
        .eq("is_active", true)
        .single();

      if (!userMembership?.club_id) return;

      const clubId = userMembership.club_id;

      // Fetch all activities for this club to calculate annual target
      const { data: activities } = await supabase
        .from("activities")
        .select("hourly_rate")
        .eq("club_id", clubId)
        .eq("status", "approved");

      const annualTargetHours = activities?.reduce((sum, activity) => sum + (activity.hourly_rate || 0), 0) || 0;

      // Fetch member work service targets (completed hours)
      const { data: memberTargets } = await supabase
        .from("member_work_service_targets")
        .select("*")
        .eq("club_id", clubId)
        .eq("year", new Date().getFullYear());

      // Fetch activity assignments (completed tasks)
      const { data: assignments } = await supabase
        .from("activity_assignments")
        .select(`
          *,
          activities!inner(name, hourly_rate)
        `)
        .eq("club_id", clubId)
        .eq("status", "completed");

      // Fetch club memberships for member names
      const { data: clubMemberships } = await supabase
        .from("club_memberships")
        .select("user_id, first_name, last_name")
        .eq("club_id", clubId)
        .eq("is_active", true);

      // Calculate total completed hours from member targets
      const totalHours = memberTargets?.reduce((sum, target) => sum + (target.completed_hours || 0), 0) || 0;

      // Calculate monthly hours (from assignments completed this month)
      const currentMonth = new Date().getMonth();
      const currentYear = new Date().getFullYear();
      
      const monthlyAssignments = assignments?.filter(assignment => {
        if (!assignment.completed_at) return false;
        const completedDate = new Date(assignment.completed_at);
        return completedDate.getMonth() === currentMonth && completedDate.getFullYear() === currentYear;
      }) || [];

      const monthlyHours = monthlyAssignments.reduce((sum, assignment) => 
        sum + (assignment.hours_credited || 0), 0
      );

      // Count active members (those who have completed hours)
      const activeMembers = memberTargets?.filter(target => target.completed_hours > 0).length || 0;

      // Count completed tasks
      const completedTasks = assignments?.length || 0;

      setStats({
        totalHours,
        activeMembers,
        completedTasks,
        monthlyHours,
        annualTargetHours
      });

      // Process member summaries from member targets
      const memberSummaries: MemberWorkSummary[] = memberTargets?.map(target => {
        // Get recent activities for this member
        const memberAssignments = assignments?.filter(assignment => 
          assignment.member_id === target.user_id
        ) || [];

        const recentActivities = memberAssignments
          .slice(0, 3)
          .map(assignment => assignment.activities?.name)
          .filter(name => name);

        // Get member name from club_memberships
        const memberInfo = clubMemberships?.find(cm => cm.user_id === target.user_id);
        const memberName = memberInfo 
          ? `${memberInfo.first_name} ${memberInfo.last_name}`
          : 'Unbekannt';

        return {
          member_id: target.user_id,
          member_name: memberName,
          total_hours: target.completed_hours || 0,
          recent_activities: recentActivities
        };
      }).filter(member => member.total_hours > 0)
        .sort((a, b) => b.total_hours - a.total_hours)
        .slice(0, 10) || [];

      setMemberSummaries(memberSummaries);

      // Get recent activities from assignments
      const recentActivitiesData: RecentActivity[] = assignments
        ?.filter(assignment => assignment.completed_at)
        .sort((a, b) => new Date(b.completed_at!).getTime() - new Date(a.completed_at!).getTime())
        .slice(0, 5)
        .map(assignment => {
          const memberInfo = clubMemberships?.find(cm => cm.user_id === assignment.member_id);
          const memberName = memberInfo 
            ? `${memberInfo.first_name} ${memberInfo.last_name}`
            : 'Unbekannt';
          
          return {
            id: assignment.id,
            activity_name: assignment.activities?.name || 'Unbekannte Aktivität',
            member_name: memberName,
            date: assignment.completed_at!,
            duration_hours: assignment.hours_credited || 0
          };
        }) || [];

      setRecentActivities(recentActivitiesData);

    } catch (error) {
      console.error("Error fetching dashboard data:", error);
      toast({
        title: "Fehler",
        description: "Konnte Dashboard-Daten nicht laden",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };


  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="h-4 w-20 bg-muted animate-pulse rounded" />
                <div className="h-4 w-4 bg-muted animate-pulse rounded" />
              </CardHeader>
              <CardContent>
                <div className="h-8 w-16 bg-muted animate-pulse rounded mb-1" />
                <div className="h-3 w-32 bg-muted animate-pulse rounded" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Gesamtstunden</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalHours}</div>
            <p className="text-xs text-muted-foreground">
              +{stats.monthlyHours} diesen Monat
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Aktive Mitglieder</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeMembers}</div>
            <p className="text-xs text-muted-foreground">
              Haben Arbeitsstunden geleistet
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Erledigte Aufgaben</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.completedTasks}</div>
            <p className="text-xs text-muted-foreground">
              Arbeitseinträge insgesamt
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Jährliches Ziel</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats.annualTargetHours > 0 ? Math.round((stats.totalHours / stats.annualTargetHours) * 100) : 0}%
            </div>
            <Progress 
              value={stats.annualTargetHours > 0 ? (stats.totalHours / stats.annualTargetHours) * 100 : 0} 
              className="mt-2" 
            />
            <p className="text-xs text-muted-foreground mt-1">
              {stats.totalHours} von {stats.annualTargetHours} Stunden
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Top Contributors */}
        <Card>
          <CardHeader>
            <CardTitle>Top Mitglieder</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {memberSummaries.map((member, index) => (
                <div key={member.member_id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline" className="w-8 h-8 rounded-full p-0 flex items-center justify-center">
                      {index + 1}
                    </Badge>
                    <div>
                      <p className="text-sm font-medium">{member.member_name}</p>
                      <p className="text-xs text-muted-foreground">
                        {member.recent_activities.slice(0, 2).join(", ")}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium">{member.total_hours}h</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Activities */}
        <Card>
          <CardHeader>
            <CardTitle>Neueste Aktivitäten</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">{activity.activity_name}</p>
                    <p className="text-xs text-muted-foreground">
                      {activity.member_name} • {format(new Date(activity.date), "dd.MM.yyyy", { locale: de })}
                    </p>
                  </div>
                  <Badge variant="secondary">
                    {activity.duration_hours}h
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default WorkServiceDashboard;