import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { 
  Bell, 
  Mail, 
  Users, 
  Settings, 
  Calendar,
  UserCheck,
  AlertTriangle,
  Send,
  Clock,
  TestTube,
  ChevronDown,
  ChevronUp
} from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { useMultiTenant } from "@/services/MultiTenantService";
import { supabase } from "@/integrations/supabase/client";

interface NotificationConfig {
  enabled: boolean;
  template?: string;
  timing?: string;
  recipients?: string[];
}

interface NotificationSettings {
  waitlist: {
    slotAvailable: NotificationConfig;
    autoBookingSuccess: NotificationConfig;
    positionUpdate: NotificationConfig;
    expiryReminder: NotificationConfig;
  };
  bookings: {
    confirmation: NotificationConfig;
    cancellation: NotificationConfig;
    reminder24h: NotificationConfig;
    reminder2h: NotificationConfig;
    modification: NotificationConfig;
  };
  members: {
    campaigns: NotificationConfig;
    newsletter: NotificationConfig;
    events: NotificationConfig;
    announcements: NotificationConfig;
  };
  admin: {
    systemWarnings: NotificationConfig;
    newRegistrations: NotificationConfig;
    paymentReminders: NotificationConfig;
    statisticsReports: NotificationConfig;
  };
  automated: {
    birthdayEmails: NotificationConfig;
    membershipExpiry: NotificationConfig;
    inactivityReminders: NotificationConfig;
  };
}

const defaultSettings: NotificationSettings = {
  waitlist: {
    slotAvailable: { enabled: true, timing: "immediate" },
    autoBookingSuccess: { enabled: true, timing: "immediate" },
    positionUpdate: { enabled: false, timing: "immediate" },
    expiryReminder: { enabled: false, timing: "24h" }
  },
  bookings: {
    confirmation: { enabled: true, timing: "immediate" },
    cancellation: { enabled: true, timing: "immediate" },
    reminder24h: { enabled: false, timing: "24h" },
    reminder2h: { enabled: false, timing: "2h" },
    modification: { enabled: false, timing: "immediate" }
  },
  members: {
    campaigns: { enabled: true, timing: "immediate" },
    newsletter: { enabled: false, timing: "immediate" },
    events: { enabled: false, timing: "immediate" },
    announcements: { enabled: false, timing: "immediate" }
  },
  admin: {
    systemWarnings: { enabled: true, timing: "immediate", recipients: ["admin"] },
    newRegistrations: { enabled: false, timing: "immediate", recipients: ["admin"] },
    paymentReminders: { enabled: false, timing: "weekly", recipients: ["admin"] },
    statisticsReports: { enabled: false, timing: "monthly", recipients: ["admin"] }
  },
  automated: {
    birthdayEmails: { enabled: false, timing: "8:00" },
    membershipExpiry: { enabled: false, timing: "30d" },
    inactivityReminders: { enabled: false, timing: "90d" }
  }
};

export const NotificationSettings = () => {
  const { toast } = useToast();
  const { currentClub } = useMultiTenant();
  const [settings, setSettings] = useState<NotificationSettings>(defaultSettings);
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(["waitlist", "bookings"]));
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Load notification settings from database
  useEffect(() => {
    const loadNotificationSettings = async () => {
      if (!currentClub?.id) return;
      
      try {
        const { data, error } = await supabase
          .from('clubs')
          .select('settings')
          .eq('id', currentClub.id)
          .single();

        if (error) throw error;

        const notificationSettings = (data?.settings as any)?.notifications;
        if (notificationSettings) {
          setSettings(notificationSettings);
        }
      } catch (error) {
        console.error('Error loading notification settings:', error);
        toast({
          title: "Fehler beim Laden",
          description: "Benachrichtigungseinstellungen konnten nicht geladen werden.",
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadNotificationSettings();
  }, [currentClub?.id, toast]);

  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(section)) {
      newExpanded.delete(section);
    } else {
      newExpanded.add(section);
    }
    setExpandedSections(newExpanded);
  };

  const updateNotificationConfig = (
    category: keyof NotificationSettings,
    key: string,
    field: keyof NotificationConfig,
    value: any
  ) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: {
          ...(prev[category] as any)[key],
          [field]: value
        }
      }
    }));
  };

  const enableAllInCategory = (category: keyof NotificationSettings, enabled: boolean) => {
    setSettings(prev => {
      const categorySettings = prev[category] as Record<string, NotificationConfig>;
      const updatedCategory = Object.keys(categorySettings).reduce((acc, key) => {
        acc[key] = { ...categorySettings[key], enabled };
        return acc;
      }, {} as Record<string, NotificationConfig>);
      
      return { ...prev, [category]: updatedCategory };
    });
  };

  const sendTestEmail = async (category: string, type: string) => {
    toast({
      title: "Test-E-Mail gesendet",
      description: `Test-E-Mail für ${category} - ${type} wurde versendet.`,
    });
  };

  const handleSave = async () => {
    if (!currentClub?.id) {
      toast({
        title: "Fehler",
        description: "Kein Club-Kontext verfügbar.",
        variant: "destructive"
      });
      return;
    }

    setIsSaving(true);
    try {
      const { error } = await supabase
        .from('clubs')
        .update({
          settings: {
            ...(currentClub.settings as any),
            notifications: settings
          }
        })
        .eq('id', currentClub.id);

      if (error) throw error;

      toast({
        title: "Benachrichtigungen gespeichert",
        description: "Alle Benachrichtigungseinstellungen wurden erfolgreich gespeichert.",
      });
    } catch (error) {
      console.error('Error saving notification settings:', error);
      toast({
        title: "Fehler beim Speichern",
        description: "Die Einstellungen konnten nicht gespeichert werden.",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleReset = async () => {
    if (!currentClub?.id) return;

    try {
      const { error } = await supabase
        .from('clubs')
        .update({
          settings: {
            ...(currentClub.settings as any),
            notifications: defaultSettings
          }
        })
        .eq('id', currentClub.id);

      if (error) throw error;

      setSettings(defaultSettings);
      toast({
        title: "Einstellungen zurückgesetzt",
        description: "Alle Benachrichtigungseinstellungen wurden auf Standardwerte zurückgesetzt.",
      });
    } catch (error) {
      console.error('Error resetting notification settings:', error);
      toast({
        title: "Fehler beim Zurücksetzen",
        description: "Benachrichtigungseinstellungen konnten nicht zurückgesetzt werden.",
        variant: "destructive"
      });
    }
  };

  const NotificationCard = ({ 
    title, 
    description, 
    icon: Icon, 
    category, 
    configs 
  }: {
    title: string;
    description: string;
    icon: any;
    category: keyof NotificationSettings;
    configs: Array<{ key: string; label: string; description: string; hasTemplate?: boolean; hasTiming?: boolean; hasRecipients?: boolean }>;
  }) => {
    const isExpanded = expandedSections.has(category);
    const enabledCount = Object.values(settings[category]).filter(config => config.enabled).length;
    const totalCount = Object.keys(settings[category]).length;

    return (
      <Card>
        <Collapsible open={isExpanded} onOpenChange={() => toggleSection(category)}>
          <CardHeader>
            <CollapsibleTrigger asChild>
              <div className="flex items-center justify-between cursor-pointer">
                <div className="flex items-center gap-3">
                  <Icon className="h-5 w-5" />
                  <div>
                    <CardTitle className="text-lg">{title}</CardTitle>
                    <CardDescription>{description}</CardDescription>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={enabledCount > 0 ? "default" : "secondary"}>
                    {enabledCount}/{totalCount} aktiv
                  </Badge>
                  {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                </div>
              </div>
            </CollapsibleTrigger>
          </CardHeader>
          
          <CollapsibleContent>
            <CardContent className="space-y-4">
              {/* Bulk Actions */}
              <div className="flex gap-2 mb-4">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => enableAllInCategory(category, true)}
                >
                  Alle aktivieren
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => enableAllInCategory(category, false)}
                >
                  Alle deaktivieren
                </Button>
              </div>

              {configs.map((config, index) => {
                const notificationConfig = (settings[category] as any)[config.key];
                
                return (
                  <div key={config.key} className="space-y-3">
                    {index > 0 && <Separator />}
                    
                    {/* Main Toggle */}
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>{config.label}</Label>
                        <p className="text-sm text-muted-foreground">
                          {config.description}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Switch 
                          checked={notificationConfig.enabled}
                          onCheckedChange={(enabled) => updateNotificationConfig(category, config.key, 'enabled', enabled)}
                        />
                        {notificationConfig.enabled && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => sendTestEmail(category, config.key)}
                          >
                            <TestTube className="h-3 w-3" />
                          </Button>
                        )}
                      </div>
                    </div>

                    {/* Additional Settings when enabled */}
                    {notificationConfig.enabled && (
                      <div className="ml-4 space-y-3 border-l-2 border-muted pl-4">
                        {config.hasTiming && (
                          <div className="space-y-2">
                            <Label className="text-xs">Timing</Label>
                            <Select 
                              value={notificationConfig.timing || "immediate"}
                              onValueChange={(value) => updateNotificationConfig(category, config.key, 'timing', value)}
                            >
                              <SelectTrigger className="h-8">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="immediate">Sofort</SelectItem>
                                <SelectItem value="2h">2 Stunden vorher</SelectItem>
                                <SelectItem value="24h">24 Stunden vorher</SelectItem>
                                <SelectItem value="weekly">Wöchentlich</SelectItem>
                                <SelectItem value="monthly">Monatlich</SelectItem>
                                <SelectItem value="30d">30 Tage vorher</SelectItem>
                                <SelectItem value="90d">90 Tage vorher</SelectItem>
                                <SelectItem value="8:00">8:00 Uhr</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        )}

                        {config.hasRecipients && (
                          <div className="space-y-2">
                            <Label className="text-xs">Empfänger</Label>
                            <Select 
                              value={(notificationConfig.recipients?.[0]) || "admin"}
                              onValueChange={(value) => updateNotificationConfig(category, config.key, 'recipients', [value])}
                            >
                              <SelectTrigger className="h-8">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="admin">Nur Admins</SelectItem>
                                <SelectItem value="all">Alle Mitglieder</SelectItem>
                                <SelectItem value="members">Nur Mitglieder</SelectItem>
                                <SelectItem value="trainers">Nur Trainer</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        )}

                        {config.hasTemplate && (
                          <div className="space-y-2">
                            <Label className="text-xs">E-Mail Template anpassen</Label>
                            <Textarea 
                              placeholder="Benutzerdefinierte Nachricht..."
                              className="h-20 text-xs"
                              value={notificationConfig.template || ""}
                              onChange={(e) => updateNotificationConfig(category, config.key, 'template', e.target.value)}
                            />
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                );
              })}
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>
    );
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="p-6">
            <div className="text-center">Lade Benachrichtigungseinstellungen...</div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Waitlist Notifications */}
      <NotificationCard
        title="Warteliste"
        description="Benachrichtigungen für Wartelisten-Ereignisse"
        icon={Clock}
        category="waitlist"
        configs={[
          { 
            key: "slotAvailable", 
            label: "Platz verfügbar", 
            description: "E-Mail wenn ein Platz auf der Warteliste frei wird",
            hasTemplate: true,
            hasTiming: true
          },
          { 
            key: "autoBookingSuccess", 
            label: "Auto-Buchung erfolgreich", 
            description: "Bestätigung bei automatischer Buchung",
            hasTemplate: true
          },
          { 
            key: "positionUpdate", 
            label: "Position geändert", 
            description: "Benachrichtigung bei Änderung der Wartelisten-Position",
            hasTemplate: true
          },
          { 
            key: "expiryReminder", 
            label: "Ablauf-Erinnerung", 
            description: "Erinnerung vor Ablauf der Wartelisten-Position",
            hasTemplate: true,
            hasTiming: true
          }
        ]}
      />

      {/* Booking Notifications */}
      <NotificationCard
        title="Buchungen"
        description="Benachrichtigungen für Buchungsereignisse"
        icon={Calendar}
        category="bookings"
        configs={[
          { 
            key: "confirmation", 
            label: "Buchungsbestätigung", 
            description: "E-Mail bei neuen Buchungen",
            hasTemplate: true
          },
          { 
            key: "cancellation", 
            label: "Stornierungsbenachrichtigung", 
            description: "E-Mail bei Buchungsstornierungen",
            hasTemplate: true
          },
          { 
            key: "reminder24h", 
            label: "24h Erinnerung", 
            description: "Erinnerung 24 Stunden vor dem Termin",
            hasTemplate: true
          },
          { 
            key: "reminder2h", 
            label: "2h Erinnerung", 
            description: "Erinnerung 2 Stunden vor dem Termin",
            hasTemplate: true
          },
          { 
            key: "modification", 
            label: "Buchungsänderung", 
            description: "E-Mail bei Änderungen an bestehenden Buchungen",
            hasTemplate: true
          }
        ]}
      />

      {/* Member Communication */}
      <NotificationCard
        title="Mitglieder-Kommunikation"
        description="Newsletter und Vereinskommunikation"
        icon={Users}
        category="members"
        configs={[
          { 
            key: "campaigns", 
            label: "E-Mail Kampagnen", 
            description: "Gezielte E-Mail Kampagnen an Mitgliedergruppen",
            hasTemplate: true,
            hasRecipients: true
          },
          { 
            key: "newsletter", 
            label: "Newsletter", 
            description: "Regelmäßiger Vereinsnewsletter",
            hasTemplate: true,
            hasRecipients: true
          },
          { 
            key: "events", 
            label: "Event-Ankündigungen", 
            description: "Benachrichtigungen über Veranstaltungen",
            hasTemplate: true,
            hasRecipients: true
          },
          { 
            key: "announcements", 
            label: "Vereinsnachrichten", 
            description: "Allgemeine Mitteilungen des Vereins",
            hasTemplate: true,
            hasRecipients: true
          }
        ]}
      />

      {/* Admin Notifications */}
      <NotificationCard
        title="Admin-Benachrichtigungen"
        description="Benachrichtigungen für Vereinsverwalter"
        icon={Settings}
        category="admin"
        configs={[
          { 
            key: "systemWarnings", 
            label: "System-Warnungen", 
            description: "Kritische Systemereignisse und Fehler",
            hasRecipients: true
          },
          { 
            key: "newRegistrations", 
            label: "Neue Registrierungen", 
            description: "Benachrichtigung bei neuen Mitgliederanmeldungen",
            hasRecipients: true
          },
          { 
            key: "paymentReminders", 
            label: "Zahlungserinnerungen", 
            description: "Übersicht über ausstehende Zahlungen",
            hasRecipients: true,
            hasTiming: true
          },
          { 
            key: "statisticsReports", 
            label: "Statistik-Reports", 
            description: "Regelmäßige Berichte über Buchungen und Aktivitäten",
            hasRecipients: true,
            hasTiming: true
          }
        ]}
      />

      {/* Automated Notifications */}
      <NotificationCard
        title="Automatische Benachrichtigungen"
        description="Zeitgesteuerte und ereignisbasierte E-Mails"
        icon={UserCheck}
        category="automated"
        configs={[
          { 
            key: "birthdayEmails", 
            label: "Geburtstags-E-Mails", 
            description: "Automatische Glückwünsche an Mitglieder",
            hasTemplate: true,
            hasTiming: true
          },
          { 
            key: "membershipExpiry", 
            label: "Mitgliedschaft läuft ab", 
            description: "Erinnerung vor Ablauf der Mitgliedschaft",
            hasTemplate: true,
            hasTiming: true
          },
          { 
            key: "inactivityReminders", 
            label: "Inaktivitäts-Erinnerung", 
            description: "Erinnerung für lange inaktive Mitglieder",
            hasTemplate: true,
            hasTiming: true
          }
        ]}
      />

      {/* Save Button */}
      <div className="flex justify-between">
        <Button 
          variant="outline" 
          onClick={handleReset}
        >
          Auf Standard zurücksetzen
        </Button>
        <Button onClick={handleSave} disabled={isSaving} size="lg">
          <Send className="mr-2 h-4 w-4" />
          {isSaving ? "Speichere..." : "Benachrichtigungen speichern"}
        </Button>
      </div>
    </div>
  );
};