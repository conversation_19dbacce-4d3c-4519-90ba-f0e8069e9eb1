import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Plus, Edit, Trash2, Calendar, AlertCircle } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { useBookingRuleValidation } from "@/hooks/useBookingRuleValidation";
import { useClub } from "@/contexts/ClubContext";

interface Booking {
  id: string;
  court_id: string;
  booking_date: string;
  start_time: string;
  end_time: string;
  player_name: string;
  partner_name: string | null;
  created_at: string;
  updated_at: string;
  courts?: {
    number: number;
  };
}

interface Court {
  id: string;
  number: number;
}

const BookingManagement = () => {
  const { currentClubId, isLoading: clubLoading } = useClub();
  const { validateAndShowErrors, isValidating } = useBookingRuleValidation();
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [courts, setCourts] = useState<Court[]>([]);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingBooking, setEditingBooking] = useState<Booking | null>(null);
  const [formData, setFormData] = useState({
    court_id: "",
    booking_date: "",
    start_time: "",
    end_time: "",
    player_name: "",
    partner_name: ""
  });
  const { toast } = useToast();

  useEffect(() => {
    if (currentClubId) {
      loadData();
    }
  }, [currentClubId]);

  const loadData = async () => {
    if (!currentClubId) {
      console.log('BookingManagement: No club ID available');
      return;
    }

    try {
      // Load bookings with court information for current club
      const { data: bookingsData, error: bookingsError } = await supabase
        .from("bookings")
        .select(`
          *,
          courts (
            number
          )
        `)
        .eq("club_id", currentClubId)  // ⭐ CLUB FILTER HINZUGEFÜGT
        .order("booking_date", { ascending: false });

      if (bookingsError) throw bookingsError;

      // Load courts for current club
      const { data: courtsData, error: courtsError } = await supabase
        .from("courts")
        .select("id, number")
        .eq("club_id", currentClubId)  // ⭐ CLUB FILTER HINZUGEFÜGT
        .order("number");

      if (courtsError) throw courtsError;

      setBookings(bookingsData || []);
      setCourts(courtsData || []);
      console.log('BookingManagement: Loaded', bookingsData?.length, 'bookings and', courtsData?.length, 'courts for club', currentClubId);
    } catch (error) {
      toast({
        title: "Fehler",
        description: "Fehler beim Laden der Daten",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      court_id: "",
      booking_date: "",
      start_time: "",
      end_time: "",
      player_name: "",
      partner_name: ""
    });
    setEditingBooking(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.court_id || !formData.booking_date || !formData.start_time || !formData.end_time || !formData.player_name) {
      toast({
        title: "Fehler",
        description: "Bitte füllen Sie alle Pflichtfelder aus",
        variant: "destructive"
      });
      return;
    }

    try {
      if (editingBooking) {
        // Update existing booking (keep created_by unchanged)
        const { error } = await supabase
          .from("bookings")
          .update({
            court_id: formData.court_id,
            booking_date: formData.booking_date,
            start_time: formData.start_time,
            end_time: formData.end_time,
            player_name: formData.player_name,
            partner_name: formData.partner_name || null
          })
          .eq("id", editingBooking.id);

        if (error) throw error;

        toast({
          title: "Erfolg",
          description: "Reservierung wurde aktualisiert"
        });
      } else {
        // Create new booking (set created_by)
        const { data: auth } = await supabase.auth.getUser();
        const userId = auth?.user?.id;
        if (!userId) {
          toast({
            title: "Anmeldung erforderlich",
            description: "Bitte melden Sie sich an, um eine Reservierung zu erstellen.",
            variant: "destructive"
          });
          return;
        }

        const payloadBase: any = {
          court_id: formData.court_id,
          booking_date: formData.booking_date,
          start_time: formData.start_time,
          end_time: formData.end_time,
          player_name: formData.player_name,
          partner_name: formData.partner_name || null,
          created_by: userId,
        };

        // Start/End als Timestamp (timestamptz)
        const toIso = (dateStr: string, timeStr: string) => {
          const [y, m, d] = dateStr.split("-").map(Number);
          const [hh, mm] = timeStr.split(":" ).map(Number);
          const dt = new Date(y!, (m! - 1), d!, hh || 0, mm || 0, 0, 0);
          return dt.toISOString();
        };
        const startAt = toIso(formData.booking_date, formData.start_time);
        const endAt = toIso(formData.booking_date, formData.end_time);

        // Eigenen Account ermitteln (falls vorhanden)
        const { data: account } = await supabase
          .from("accounts")
          .select("id")
          .eq("user_id", userId)
          .limit(1)
          .maybeSingle();

        const payload: any = {
          ...payloadBase,
          club_id: currentClubId,  // ⭐ CLUB ID HINZUGEFÜGT
          start_at: startAt,
          end_at: endAt,
          ...(account?.id ? { acting_account_id: account.id, booked_for_account_id: account.id } : {}),
        };

        // Buchungsregeln validieren
        if (currentClubId) {
          const isValid = await validateAndShowErrors({
            club_id: currentClubId,
            booking_date: formData.booking_date,
            start_time: formData.start_time,
            end_time: formData.end_time,
            player_name: formData.player_name,
            created_by: userId,
            court_id: formData.court_id
          });

          if (!isValid) {
            return;
          }
        }

        const { error } = await supabase
          .from("bookings")
          .insert(payload);

        if (error) throw error;

        toast({
          title: "Erfolg",
          description: "Reservierung wurde erstellt"
        });
      }

      setDialogOpen(false);
      resetForm();
      loadData();
    } catch (error) {
      toast({
        title: "Fehler",
        description: `Fehler beim ${editingBooking ? 'Aktualisieren' : 'Erstellen'} der Reservierung`,
        variant: "destructive"
      });
    }
  };

  const handleEdit = (booking: Booking) => {
    setEditingBooking(booking);
    setFormData({
      court_id: booking.court_id,
      booking_date: booking.booking_date,
      start_time: booking.start_time,
      end_time: booking.end_time,
      player_name: booking.player_name,
      partner_name: booking.partner_name || ""
    });
    setDialogOpen(true);
  };

  const handleDelete = async (booking: Booking) => {
    if (!confirm("Sind Sie sicher, dass Sie diese Reservierung löschen möchten?")) {
      return;
    }

    try {
      // Store booking details before deletion for waitlist processing
      const bookingDetails = {
        club_id: currentClubId,
        court_id: booking.court_id,
        booking_date: booking.booking_date,
        start_time: booking.start_time,
        end_time: booking.end_time
      };

      const { error } = await supabase
        .from("bookings")
        .delete()
        .eq("id", booking.id);

      if (error) throw error;

      // Process waitlist for auto-booking after successful deletion
      try {
        await supabase.functions.invoke('waitlist-manager', {
          body: {
            action: 'process_booking_cancellation',
            ...bookingDetails
          }
        });
      } catch (waitlistError) {
        console.error('Waitlist processing error:', waitlistError);
        // Don't show error to user as booking deletion was successful
      }

      toast({
        title: "Erfolg",
        description: "Reservierung wurde gelöscht"
      });
      loadData();
    } catch (error) {
      toast({
        title: "Fehler",
        description: "Fehler beim Löschen der Reservierung",
        variant: "destructive"
      });
    }
  };

  if (clubLoading) {
    return <div>Laden...</div>;
  }

  if (!currentClubId) {
    return (
      <Alert>
        <AlertDescription>
          Kein Club gefunden. Bitte stellen Sie sicher, dass Sie einer aktiven Club-Mitgliedschaft zugeordnet sind.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Reservierungen verwalten
            </CardTitle>
            <CardDescription>
              Verwalten Sie Platz-Reservierungen (aktuelles Schema)
            </CardDescription>
          </div>
          <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={resetForm} className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                Neue Reservierung
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>
                  {editingBooking ? "Reservierung bearbeiten" : "Neue Reservierung"}
                </DialogTitle>
                <DialogDescription>
                  {editingBooking ? "Bearbeiten Sie die Reservierungs-Details" : "Erstellen Sie eine neue Platz-Reservierung"}
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="court_id">Platz *</Label>
                  <Select
                    value={formData.court_id}
                    onValueChange={(value) => setFormData({ ...formData, court_id: value })}
                    required
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Platz auswählen" />
                    </SelectTrigger>
                    <SelectContent>
                      {courts.map((court) => (
                        <SelectItem key={court.id} value={court.id}>
                          Platz {court.number}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="booking_date">Datum *</Label>
                  <Input
                    id="booking_date"
                    type="date"
                    value={formData.booking_date}
                    onChange={(e) => setFormData({ ...formData, booking_date: e.target.value })}
                    required
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="start_time">Startzeit *</Label>
                    <Input
                      id="start_time"
                      type="time"
                      value={formData.start_time}
                      onChange={(e) => setFormData({ ...formData, start_time: e.target.value })}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="end_time">Endzeit *</Label>
                    <Input
                      id="end_time"
                      type="time"
                      value={formData.end_time}
                      onChange={(e) => setFormData({ ...formData, end_time: e.target.value })}
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="player_name">Spielername *</Label>
                  <Input
                    id="player_name"
                    value={formData.player_name}
                    onChange={(e) => setFormData({ ...formData, player_name: e.target.value })}
                    placeholder="Name des Hauptspielers"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="partner_name">Partner (optional)</Label>
                  <Input
                    id="partner_name"
                    value={formData.partner_name}
                    onChange={(e) => setFormData({ ...formData, partner_name: e.target.value })}
                    placeholder="Name des Partners"
                  />
                </div>

                <div className="flex justify-end gap-2">
                  <Button type="button" variant="outline" onClick={() => setDialogOpen(false)}>
                    Abbrechen
                  </Button>
                  <Button type="submit">
                    {editingBooking ? "Aktualisieren" : "Erstellen"}
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        <Alert className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Hinweis: Dies verwendet das aktuelle Datenbankschema. Für erweiterte Felder (Mitglieder-Verknüpfung, Status, etc.) muss die Datenbank-Migration ausgeführt werden.
          </AlertDescription>
        </Alert>
        
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Platz</TableHead>
              <TableHead>Datum</TableHead>
              <TableHead>Zeit</TableHead>
              <TableHead>Spieler</TableHead>
              <TableHead>Partner</TableHead>
              <TableHead className="text-right">Aktionen</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {bookings.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center text-muted-foreground">
                  Keine Reservierungen gefunden
                </TableCell>
              </TableRow>
            ) : (
              bookings.map((booking) => (
                <TableRow key={booking.id}>
                  <TableCell className="font-medium">
                    Platz {booking.courts?.number || booking.court_id}
                  </TableCell>
                  <TableCell>{booking.booking_date}</TableCell>
                  <TableCell>{booking.start_time} - {booking.end_time}</TableCell>
                  <TableCell>{booking.player_name}</TableCell>
                  <TableCell>{booking.partner_name || "-"}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEdit(booking)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(booking)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};

export default BookingManagement;
