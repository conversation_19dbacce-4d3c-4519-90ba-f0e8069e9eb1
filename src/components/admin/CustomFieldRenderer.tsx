import { FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { CustomField } from "@/hooks/useCustomFields";

interface CustomFieldRendererProps {
  field: CustomField;
  value: any;
  onChange: (value: any) => void;
  error?: string;
}

export const CustomFieldRenderer = ({ field, value, onChange, error }: CustomFieldRendererProps) => {
  const renderInput = () => {
    switch (field.field_type) {
      case 'text':
      case 'tennis_lk':
        return (
          <Input
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={
              field.field_type === 'tennis_lk' 
                ? "z.B. LK1, LK14,2, LK22,8, Keine"
                : `${field.field_name} eingeben`
            }
          />
        );
      
      case 'number':
        return (
          <Input
            type="number"
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={`${field.field_name} eingeben`}
          />
        );
      
      case 'select':
        return (
          <Select value={value || ''} onValueChange={onChange}>
            <SelectTrigger>
              <SelectValue placeholder={`${field.field_name} auswählen`} />
            </SelectTrigger>
            <SelectContent>
              {field.field_options.map((option: string) => (
                <SelectItem key={option} value={option}>
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
      
      default:
        return (
          <Input
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={`${field.field_name} eingeben`}
          />
        );
    }
  };

  return (
    <FormItem>
      <FormLabel>
        {field.field_name}
        {field.is_required && <span className="text-destructive ml-1">*</span>}
        {field.field_type === 'tennis_lk' && (
          <span className="text-xs text-muted-foreground ml-2">
            (Format: LK1-25 oder mit Nachkomma z.B. LK14,2)
          </span>
        )}
      </FormLabel>
      <FormControl>
        {renderInput()}
      </FormControl>
      {error && <FormMessage>{error}</FormMessage>}
    </FormItem>
  );
};