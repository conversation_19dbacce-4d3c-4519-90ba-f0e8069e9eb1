import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Search, Filter } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";

interface Member {
  id: string;
  first_name: string;
  last_name: string;
  birth_date: string;
  address: string;
  email: string;
  phone: string;
  iban: string;
  membership_type: "Child" | "Youth" | "Student" | "Adult" | "Couple" | "Family" | "Kinder" | "Jugendliche" | "Studenten" | "Erwachsene" | "Senioren" | "Paare" | "Familien";
  annual_fee: number;
  payment_status: string;
  created_at: string;
}

const MemberDirectory = () => {
  const [members, setMembers] = useState<Member[]>([]);
  const [filteredMembers, setFilteredMembers] = useState<Member[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  // Filter states
  const [searchTerm, setSearchTerm] = useState("");
  const [membershipTypeFilter, setMembershipTypeFilter] = useState("");
  const [paymentStatusFilter, setPaymentStatusFilter] = useState("");
  const [feeRangeFilter, setFeeRangeFilter] = useState("");

  useEffect(() => {
    fetchMembers();
  }, []);

  useEffect(() => {
    filterMembers();
  }, [members, searchTerm, membershipTypeFilter, paymentStatusFilter, feeRangeFilter]);

  const fetchMembers = async () => {
    try {
      const { data, error } = await supabase
        .from("members")
        .select("*")
        .order("last_name", { ascending: true });

      if (error) throw error;
      setMembers(data || []);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch members",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const filterMembers = () => {
    let filtered = [...members];

    // Search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter((member) =>
        member.first_name.toLowerCase().includes(term) ||
        member.last_name.toLowerCase().includes(term) ||
        member.email.toLowerCase().includes(term)
      );
    }

    // Membership type filter
    if (membershipTypeFilter) {
      filtered = filtered.filter((member) => member.membership_type === membershipTypeFilter);
    }

    // Payment status filter
    if (paymentStatusFilter) {
      filtered = filtered.filter((member) => member.payment_status === paymentStatusFilter);
    }

    // Fee range filter
    if (feeRangeFilter) {
      switch (feeRangeFilter) {
        case "low":
          filtered = filtered.filter((member) => member.annual_fee <= 130);
          break;
        case "medium":
          filtered = filtered.filter((member) => member.annual_fee > 130 && member.annual_fee <= 230);
          break;
        case "high":
          filtered = filtered.filter((member) => member.annual_fee > 230);
          break;
      }
    }

    setFilteredMembers(filtered);
  };

  const calculateAge = (birthDate: string): number => {
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    
    return age;
  };

  const clearFilters = () => {
    setSearchTerm("");
    setMembershipTypeFilter("");
    setPaymentStatusFilter("");
    setFeeRangeFilter("");
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-6">
      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center gap-2 mb-4">
            <Filter className="h-4 w-4" />
            <h3 className="font-semibold">Filters & Search</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div>
              <Label htmlFor="search">Search Members</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Name or email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-9"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="membership-type">Membership Type</Label>
              <Select value={membershipTypeFilter} onValueChange={setMembershipTypeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All types</SelectItem>
                  <SelectItem value="Child">Child</SelectItem>
                  <SelectItem value="Youth">Youth</SelectItem>
                  <SelectItem value="Student">Student</SelectItem>
                  <SelectItem value="Adult">Adult</SelectItem>
                  <SelectItem value="Couple">Couple</SelectItem>
                  <SelectItem value="Family">Family</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="payment-status">Payment Status</Label>
              <Select value={paymentStatusFilter} onValueChange={setPaymentStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All statuses</SelectItem>
                  <SelectItem value="paid">Paid</SelectItem>
                  <SelectItem value="unpaid">Unpaid</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="fee-range">Fee Range</Label>
              <Select value={feeRangeFilter} onValueChange={setFeeRangeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All ranges" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All ranges</SelectItem>
                  <SelectItem value="low">Low (≤€130)</SelectItem>
                  <SelectItem value="medium">Medium (€131-230)</SelectItem>
                  <SelectItem value="high">High (&gt;€230)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-end">
              <button
                onClick={clearFilters}
                className="text-sm text-muted-foreground hover:text-foreground underline"
              >
                Clear Filters
              </button>
            </div>
          </div>

          <div className="mt-4 text-sm text-muted-foreground">
            Showing {filteredMembers.length} of {members.length} members
          </div>
        </CardContent>
      </Card>

      {/* Member Table */}
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Age</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Annual Fee</TableHead>
                <TableHead>Payment Status</TableHead>
                <TableHead>IBAN</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Phone</TableHead>
                <TableHead>Member Since</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredMembers.map((member) => (
                <TableRow key={member.id}>
                  <TableCell className="font-medium">
                    {member.first_name} {member.last_name}
                  </TableCell>
                  <TableCell>{calculateAge(member.birth_date)}</TableCell>
                  <TableCell>
                    <Badge variant="secondary">{member.membership_type}</Badge>
                  </TableCell>
                  <TableCell className="font-medium">€{member.annual_fee}</TableCell>
                  <TableCell>
                    <Badge 
                      variant={member.payment_status === "paid" ? "default" : "destructive"}
                    >
                      {member.payment_status}
                    </Badge>
                  </TableCell>
                  <TableCell className="font-mono text-sm">
                    {member.iban.replace(/(.{4})/g, "$1 ").trim()}
                  </TableCell>
                  <TableCell>{member.email}</TableCell>
                  <TableCell>{member.phone}</TableCell>
                  <TableCell>
                    {new Date(member.created_at).toLocaleDateString()}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {filteredMembers.length === 0 && !loading && (
        <Card>
          <CardContent className="p-8 text-center">
            <p className="text-muted-foreground">
              No members found matching your search criteria.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default MemberDirectory;