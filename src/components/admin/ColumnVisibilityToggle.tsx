import { Settings2, <PERSON><PERSON><PERSON><PERSON>c<PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ColumnConfig } from "@/hooks/useColumnVisibility";

interface ColumnVisibilityToggleProps {
  columns: ColumnConfig[];
  onToggleColumn: (key: string) => void;
  onReset: () => void;
}

export const ColumnVisibilityToggle = ({ columns, onToggleColumn, onReset }: ColumnVisibilityToggleProps) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm">
          <Settings2 className="h-4 w-4 mr-2" />
          Spalten
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel>Sichtbare Spalten</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {columns.map((column) => (
          <DropdownMenuItem
            key={column.key}
            onClick={(e) => {
              e.preventDefault();
              if (!column.required) {
                onToggleColumn(column.key);
              }
            }}
            className={column.required ? "opacity-50 cursor-not-allowed" : "cursor-pointer"}
          >
            <Checkbox
              checked={column.visible}
              disabled={column.required}
              className="mr-2"
            />
            <span className="flex-1">{column.label}</span>
            {column.required && (
              <span className="text-xs text-muted-foreground ml-2">
                Erforderlich
              </span>
            )}
          </DropdownMenuItem>
        ))}
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={onReset}>
          <RotateCcw className="h-4 w-4 mr-2" />
          Zurücksetzen
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};