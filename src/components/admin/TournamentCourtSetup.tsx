import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Calendar, Clock, MapPin, AlertTriangle } from 'lucide-react';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';
import { toast } from '@/hooks/use-toast';

interface TournamentCourtSetupProps {
  tournamentId: string;
  onComplete?: () => void;
}

interface Court {
  id: string;
  number: number;
  surface_type?: string;
  court_group?: string;
}

interface TournamentCourt {
  id: string;
  court_id: string;
  start_time: string;
  end_time: string;
  available_days: number[];
  court: Court;
}

interface TournamentInfo {
  id: string;
  name: string;
  tournament_start: string;
  tournament_end: string;
  format_type: string;
}

const WEEKDAYS = [
  { value: 1, label: 'Montag' },
  { value: 2, label: 'Dienstag' },
  { value: 3, label: 'Mittwoch' },
  { value: 4, label: 'Donnerstag' },
  { value: 5, label: 'Freitag' },
  { value: 6, label: 'Samstag' },
  { value: 7, label: 'Sonntag' },
];

export function TournamentCourtSetup({ tournamentId, onComplete }: TournamentCourtSetupProps) {
  const [selectedCourts, setSelectedCourts] = useState<Record<string, {
    startTime: string;
    endTime: string;
    availableDays: number[];
  }>>({});

  const queryClient = useQueryClient();

  // Fetch tournament info
  const { data: tournament } = useQuery({
    queryKey: ['tournament', tournamentId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('tournaments')
        .select('id, name, tournament_start, tournament_end, format_type')
        .eq('id', tournamentId)
        .single();
      
      if (error) throw error;
      return data as TournamentInfo;
    },
  });

  // Fetch available courts
  const { data: courts = [] } = useQuery({
    queryKey: ['courts'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('courts')
        .select('id, number, surface_type, court_group')
        .order('number');
      
      if (error) throw error;
      return data as Court[];
    },
  });

  // Fetch existing tournament court assignments
  const { data: existingAssignments = [] } = useQuery({
    queryKey: ['tournament-courts', tournamentId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('tournament_courts')
        .select(`
          id,
          court_id,
          start_time,
          end_time,
          available_days,
          court:courts(id, number, surface_type, court_group)
        `)
        .eq('tournament_id', tournamentId);
      
      if (error) throw error;
      return data as TournamentCourt[];
    },
  });

  // Initialize form with existing data
  useEffect(() => {
    if (existingAssignments.length > 0) {
      const assignments: Record<string, any> = {};
      existingAssignments.forEach(assignment => {
        assignments[assignment.court_id] = {
          startTime: assignment.start_time,
          endTime: assignment.end_time,
          availableDays: assignment.available_days,
        };
      });
      setSelectedCourts(assignments);
    }
  }, [existingAssignments]);

  // Save tournament court assignments
  const saveAssignmentsMutation = useMutation({
    mutationFn: async (assignments: typeof selectedCourts) => {
      // First, delete existing assignments
      await supabase
        .from('tournament_courts')
        .delete()
        .eq('tournament_id', tournamentId);

      // Then insert new assignments
      const insertData = Object.entries(assignments).map(([courtId, config]) => ({
        tournament_id: tournamentId,
        court_id: courtId,
        start_time: config.startTime,
        end_time: config.endTime,
        available_days: config.availableDays,
      }));

      if (insertData.length > 0) {
        const { error } = await supabase
          .from('tournament_courts')
          .insert(insertData);
        
        if (error) throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tournament-courts', tournamentId] });
      toast({
        title: 'Plätze zugewiesen',
        description: 'Die Platzzuweisungen wurden erfolgreich gespeichert.',
      });
      onComplete?.();
    },
    onError: (error) => {
      toast({
        title: 'Fehler',
        description: 'Die Platzzuweisungen konnten nicht gespeichert werden.',
        variant: 'destructive',
      });
      console.error('Save error:', error);
    },
  });

  const handleCourtToggle = (courtId: string) => {
    setSelectedCourts(prev => {
      const newSelection = { ...prev };
      if (newSelection[courtId]) {
        delete newSelection[courtId];
      } else {
        newSelection[courtId] = {
          startTime: '08:00',
          endTime: '20:00',
          availableDays: [1, 2, 3, 4, 5, 6, 7], // All days by default
        };
      }
      return newSelection;
    });
  };

  const updateCourtConfig = (courtId: string, field: string, value: any) => {
    setSelectedCourts(prev => ({
      ...prev,
      [courtId]: {
        ...prev[courtId],
        [field]: value,
      },
    }));
  };

  const handleDayToggle = (courtId: string, day: number) => {
    const currentDays = selectedCourts[courtId]?.availableDays || [];
    const newDays = currentDays.includes(day)
      ? currentDays.filter(d => d !== day)
      : [...currentDays, day].sort();
    
    updateCourtConfig(courtId, 'availableDays', newDays);
  };

  const getTournamentDuration = () => {
    if (!tournament) return '';
    
    const start = new Date(tournament.tournament_start);
    const end = new Date(tournament.tournament_end);
    
    if (start.toDateString() === end.toDateString()) {
      return format(start, 'dd.MM.yyyy', { locale: de });
    }
    
    return `${format(start, 'dd.MM.yyyy', { locale: de })} - ${format(end, 'dd.MM.yyyy', { locale: de })}`;
  };

  const selectedCount = Object.keys(selectedCourts).length;

  return (
    <div className="space-y-6">
      {/* Tournament Info */}
      {tournament && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Turnier: {tournament.name}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Clock className="h-4 w-4" />
              <span>{getTournamentDuration()}</span>
            </div>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Badge variant="outline">{tournament.format_type}</Badge>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Instructions */}
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Wählen Sie die Plätze aus, die für dieses Turnier zur Verfügung stehen sollen. 
          Für jeden Platz können Sie individuelle Zeiten und verfügbare Tage festlegen.
        </AlertDescription>
      </Alert>

      {/* Court Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            Platzzuweisung ({selectedCount} {selectedCount === 1 ? 'Platz' : 'Plätze'} ausgewählt)
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {courts.map(court => (
            <div key={court.id} className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id={`court-${court.id}`}
                  checked={!!selectedCourts[court.id]}
                  onCheckedChange={() => handleCourtToggle(court.id)}
                />
                <Label 
                  htmlFor={`court-${court.id}`}
                  className="flex items-center gap-2 cursor-pointer"
                >
                  <span className="font-medium">Platz {court.number}</span>
                  {court.surface_type && (
                    <Badge variant="secondary" className="text-xs">
                      {court.surface_type}
                    </Badge>
                  )}
                  {court.court_group && (
                    <Badge variant="outline" className="text-xs">
                      {court.court_group}
                    </Badge>
                  )}
                </Label>
              </div>

              {selectedCourts[court.id] && (
                <div className="ml-6 p-4 bg-muted rounded-lg space-y-4">
                  {/* Time Configuration */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm">Von</Label>
                      <input
                        type="time"
                        value={selectedCourts[court.id].startTime}
                        onChange={(e) => updateCourtConfig(court.id, 'startTime', e.target.value)}
                        className="w-full mt-1 px-3 py-2 border rounded-md text-sm"
                      />
                    </div>
                    <div>
                      <Label className="text-sm">Bis</Label>
                      <input
                        type="time"
                        value={selectedCourts[court.id].endTime}
                        onChange={(e) => updateCourtConfig(court.id, 'endTime', e.target.value)}
                        className="w-full mt-1 px-3 py-2 border rounded-md text-sm"
                      />
                    </div>
                  </div>

                  {/* Available Days */}
                  <div>
                    <Label className="text-sm">Verfügbare Tage</Label>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {WEEKDAYS.map(day => (
                        <Button
                          key={day.value}
                          variant={selectedCourts[court.id].availableDays.includes(day.value) ? "default" : "outline"}
                          size="sm"
                          onClick={() => handleDayToggle(court.id, day.value)}
                          className="text-xs"
                        >
                          {day.label.slice(0, 2)}
                        </Button>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              <Separator />
            </div>
          ))}

          {courts.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              Keine Plätze verfügbar. Bitte erstellen Sie zuerst Plätze in der Platzverwaltung.
            </div>
          )}
        </CardContent>
      </Card>

      {/* Actions */}
      <div className="flex justify-end gap-3">
        <Button
          onClick={() => saveAssignmentsMutation.mutate(selectedCourts)}
          disabled={selectedCount === 0 || saveAssignmentsMutation.isPending}
          className="min-w-32"
        >
          {saveAssignmentsMutation.isPending ? 'Speichern...' : 'Speichern'}
        </Button>
      </div>
    </div>
  );
}