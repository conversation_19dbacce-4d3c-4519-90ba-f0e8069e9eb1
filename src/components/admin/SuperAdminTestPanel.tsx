import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Shield, UserPlus, AlertTriangle } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext";

export function SuperAdminTestPanel() {
  const { user } = useAuth();
  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const grantSuperAdmin = async () => {
    if (!email) {
      toast.error("Bitte geben Sie eine E-Mail-Adresse ein");
      return;
    }

    setIsLoading(true);
    try {
      const { data, error } = await supabase.rpc('grant_super_admin', { 
        _user_email: email 
      });

      if (error) throw error;

      if (data) {
        toast.success(`SuperAdmin-Berechtigung für ${email} erteilt!`);
        setEmail("");
      } else {
        toast.info(`${email} hat bereits SuperAdmin-Berechtigung oder Benutzer nicht gefunden`);
      }
    } catch (error) {
      console.error('Error granting super admin:', error);
      toast.error('Fehler beim Erteilen der SuperAdmin-Berechtigung');
    } finally {
      setIsLoading(false);
    }
  };

  const grantCurrentUser = async () => {
    if (!user?.email) {
      toast.error("Kein angemeldeter Benutzer gefunden");
      return;
    }

    setIsLoading(true);
    try {
      const { data, error } = await supabase.rpc('grant_super_admin', { 
        _user_email: user.email 
      });

      if (error) throw error;

      if (data) {
        toast.success("SuperAdmin-Berechtigung für aktuellen Benutzer erteilt!");
        // Reload page to update permissions
        setTimeout(() => window.location.reload(), 1000);
      } else {
        toast.info("Sie haben bereits SuperAdmin-Berechtigung");
      }
    } catch (error) {
      console.error('Error granting super admin:', error);
      toast.error('Fehler beim Erteilen der SuperAdmin-Berechtigung');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="border-orange-200 bg-orange-50">
      <CardHeader>
        <div className="flex items-center gap-2">
          <AlertTriangle className="h-5 w-5 text-orange-600" />
          <CardTitle className="text-orange-800">SuperAdmin Test Panel</CardTitle>
        </div>
        <CardDescription className="text-orange-700">
          Nur für Entwicklung und Testing - SuperAdmin-Berechtigungen erteilen
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center gap-2 p-3 bg-orange-100 rounded-lg">
          <Shield className="h-4 w-4 text-orange-600" />
          <span className="text-sm text-orange-800">
            Aktueller Benutzer: <Badge variant="secondary">{user?.email}</Badge>
          </span>
        </div>

        <div className="space-y-2">
          <Button 
            onClick={grantCurrentUser}
            disabled={isLoading}
            className="w-full bg-orange-600 hover:bg-orange-700"
          >
            <UserPlus className="h-4 w-4 mr-2" />
            Mir SuperAdmin-Berechtigung geben
          </Button>
        </div>

        <div className="space-y-2">
          <Label htmlFor="email">Oder anderen Benutzer berechtigen:</Label>
          <div className="flex gap-2">
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="flex-1"
            />
            <Button 
              onClick={grantSuperAdmin}
              disabled={isLoading || !email}
              variant="outline"
            >
              <Shield className="h-4 w-4 mr-2" />
              Berechtigen
            </Button>
          </div>
        </div>

        <div className="text-xs text-orange-600 space-y-1">
          <p>⚠️ Nur in Entwicklungsumgebung verwenden</p>
          <p>📋 Nach Berechtigung Seite neu laden für Effekt</p>
          <p>🔐 SuperAdmin hat uneingeschränkten Zugriff auf alle Bereiche</p>
        </div>
      </CardContent>
    </Card>
  );
}