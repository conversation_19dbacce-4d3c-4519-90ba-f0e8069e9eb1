import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Users, Activity, FileText, Settings, Calendar, UserCheck, MapPin, Wrench, FolderOpen } from "lucide-react";
import WorkServiceManagement from "./WorkServiceManagement";
import MemberDirectory from "./MemberDirectory";
import CourtConfigurationImproved from "./CourtConfigurationImproved";
import CourtManagement from "./CourtManagement";
import BookingManagement from "./BookingManagement";
import AccountManagement from "./AccountManagement";
import CourtSetup from "./CourtSetup";

const AdminPanel = () => {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-foreground mb-2">TennisVerein Management</h1>
        <p className="text-muted-foreground"><PERSON>erwalte Mitglieder, Aktivitäten und Vereinsabläufe</p>
      </div>

      <Tabs defaultValue="courts" className="space-y-6">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="courts" className="flex items-center gap-2">
            <MapPin className="h-4 w-4" />
            Konfiguration
          </TabsTrigger>
          <TabsTrigger value="court-mgmt" className="flex items-center gap-2">
            <MapPin className="h-4 w-4" />
            Plätze
          </TabsTrigger>
          <TabsTrigger value="bookings" className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Reservierungen
          </TabsTrigger>
          <TabsTrigger value="users" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Benutzer
          </TabsTrigger>
          <TabsTrigger value="work-service" className="flex items-center gap-2">
            <Wrench className="h-4 w-4" />
            Arbeitsdienst
          </TabsTrigger>
          <TabsTrigger value="directory" className="flex items-center gap-2">
            <FolderOpen className="h-4 w-4" />
            Verzeichnis
          </TabsTrigger>
        </TabsList>

        <TabsContent value="courts" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Konfiguration</CardTitle>
              <CardDescription>
                Allgemeine Systemkonfiguration (Bereich ist derzeit leer)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">Dieser Bereich wird in Zukunft für systemweite Einstellungen verwendet.</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="court-mgmt" className="space-y-6">
          <CourtSetup />
          <CourtConfigurationImproved />
        </TabsContent>

        <TabsContent value="bookings" className="space-y-6">
          <BookingManagement />
        </TabsContent>

        <TabsContent value="users" className="space-y-6">
          <AccountManagement />
        </TabsContent>

        <TabsContent value="work-service" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Arbeitsdienst-Verwaltung</CardTitle>
              <CardDescription>
                Definiere Aktivitäten und erfasse Arbeitsstunden für Vereinserhaltung und -betrieb
              </CardDescription>
            </CardHeader>
            <CardContent>
              <WorkServiceManagement />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="directory" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Mitgliederverzeichnis</CardTitle>
              <CardDescription>
                Durchsuche und filtere alle Vereinsmitglieder mit erweiterten Suchfunktionen
              </CardDescription>
            </CardHeader>
            <CardContent>
              <MemberDirectory />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AdminPanel;