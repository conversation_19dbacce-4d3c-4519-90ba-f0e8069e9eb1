import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Settings, Palette, Wand2, RotateCcw } from "lucide-react";
import { useTheme, ThemeVariant, ThemeSettings } from "@/contexts/ThemeContext";
import { useState } from "react";
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";

interface ThemeCustomizerProps {
  baseTheme: ThemeVariant;
  onClose: () => void;
}

const animationStyles = [
  { value: 'default', label: 'Standard', description: 'Ausgewogene Animationen' },
  { value: 'smooth', label: 'Smooth', description: '<PERSON><PERSON><PERSON>, fließende Übergänge' },
  { value: 'bouncy', label: 'Bouncy', description: '<PERSON><PERSON>fte, federnd Effekte' },
  { value: 'minimal', label: 'Minimal', description: 'Subtile, reduzierte Animationen' }
];

const getBaseThemeInfo = (variant: ThemeVariant) => {
  const themes = {
    klassik: { name: 'Klassik', colors: ['#2d5016', '#4d7c0f', '#f97316'] },
    'courtwaive-ai': { name: 'Courtwaive AI', colors: ['#a855f7', '#3b82f6', '#8b5cf6'] },
    'corporate-blue': { name: 'Corporate Blue', colors: ['#2563eb', '#1d4ed8', '#60a5fa'] },
    elegance: { name: 'Elegance', colors: ['#1a1a1a', '#fbbf24', '#374151'] },
    nature: { name: 'Nature', colors: ['#92400e', '#a3a3a3', '#15803d'] }
  };
  return themes[variant] || { name: variant, colors: [] };
};

export const ThemeCustomizer = ({ baseTheme, onClose }: ThemeCustomizerProps) => {
  const { createCustomVariant } = useTheme();
  const { toast } = useToast();
  const baseInfo = getBaseThemeInfo(baseTheme);
  
  const [customizations, setCustomizations] = useState<Partial<ThemeSettings>>({
    customColors: {
      primary: '',
      secondary: '',
      accent: '',
      background: '',
      foreground: ''
    },
    animationStyle: 'default'
  });

  const handleColorChange = (colorType: keyof NonNullable<ThemeSettings['customColors']>, value: string) => {
    setCustomizations(prev => ({
      ...prev,
      customColors: {
        ...prev.customColors,
        [colorType]: value
      }
    }));
  };

  const handleAnimationChange = (value: string) => {
    setCustomizations(prev => ({
      ...prev,
      animationStyle: value as ThemeSettings['animationStyle']
    }));
  };

  const handleSave = async () => {
    try {
      await createCustomVariant(baseTheme, customizations);
      toast({
        title: "Custom Theme erstellt",
        description: `${baseInfo.name} Custom wurde erfolgreich gespeichert.`
      });
      onClose();
    } catch (error) {
      toast({
        title: "Fehler",
        description: "Custom Theme konnte nicht gespeichert werden.",
        variant: "destructive"
      });
    }
  };

  const handleReset = () => {
    setCustomizations({
      customColors: {
        primary: '',
        secondary: '',
        accent: '',
        background: '',
        foreground: ''
      },
      animationStyle: 'default'
    });
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wand2 className="h-5 w-5" />
            {baseInfo.name} Anpassen
          </CardTitle>
          <CardDescription>
            Erstelle deine eigene Variante von {baseInfo.name} mit individuellen Anpassungen
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Base Theme Preview */}
          <div className="p-4 border rounded-lg bg-gradient-to-r from-primary/5 to-accent/5">
            <div className="flex items-center gap-3 mb-3">
              <Badge variant="outline">Basis: {baseInfo.name}</Badge>
              <div className="flex gap-1">
                {baseInfo.colors.map((color, index) => (
                  <div
                    key={index}
                    className="w-4 h-4 rounded-full border border-border"
                    style={{ backgroundColor: color }}
                  />
                ))}
              </div>
            </div>
            <p className="text-sm text-muted-foreground">
              Deine Anpassungen werden auf diese Basis-Farben angewendet
            </p>
          </div>

          <Separator />

          {/* Color Customization */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium flex items-center gap-2">
              <Palette className="h-4 w-4" />
              Farb-Overrides
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="custom-primary">Primärfarbe Override</Label>
                <div className="flex gap-2">
                  <Input
                    id="custom-primary"
                    type="color"
                    value={customizations.customColors?.primary || ''}
                    onChange={(e) => handleColorChange('primary', e.target.value)}
                    className="w-16 h-10 p-1 rounded cursor-pointer"
                  />
                  <Input
                    value={customizations.customColors?.primary || ''}
                    onChange={(e) => handleColorChange('primary', e.target.value)}
                    placeholder="Leer = Basis-Farbe"
                    className="flex-1"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="custom-secondary">Sekundärfarbe Override</Label>
                <div className="flex gap-2">
                  <Input
                    id="custom-secondary"
                    type="color"
                    value={customizations.customColors?.secondary || ''}
                    onChange={(e) => handleColorChange('secondary', e.target.value)}
                    className="w-16 h-10 p-1 rounded cursor-pointer"
                  />
                  <Input
                    value={customizations.customColors?.secondary || ''}
                    onChange={(e) => handleColorChange('secondary', e.target.value)}
                    placeholder="Leer = Basis-Farbe"
                    className="flex-1"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="custom-accent">Akzentfarbe Override</Label>
                <div className="flex gap-2">
                  <Input
                    id="custom-accent"
                    type="color"
                    value={customizations.customColors?.accent || ''}
                    onChange={(e) => handleColorChange('accent', e.target.value)}
                    className="w-16 h-10 p-1 rounded cursor-pointer"
                  />
                  <Input
                    value={customizations.customColors?.accent || ''}
                    onChange={(e) => handleColorChange('accent', e.target.value)}
                    placeholder="Leer = Basis-Farbe"
                    className="flex-1"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="custom-background">Hintergrund Override</Label>
                <div className="flex gap-2">
                  <Input
                    id="custom-background"
                    type="color"
                    value={customizations.customColors?.background || ''}
                    onChange={(e) => handleColorChange('background', e.target.value)}
                    className="w-16 h-10 p-1 rounded cursor-pointer"
                  />
                  <Input
                    value={customizations.customColors?.background || ''}
                    onChange={(e) => handleColorChange('background', e.target.value)}
                    placeholder="Leer = Basis-Farbe"
                    className="flex-1"
                  />
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Animation Style */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Animations-Stil
            </h3>
            
            <Select value={customizations.animationStyle} onValueChange={handleAnimationChange}>
              <SelectTrigger>
                <SelectValue placeholder="Animations-Stil auswählen" />
              </SelectTrigger>
              <SelectContent>
                {animationStyles.map((style) => (
                  <SelectItem key={style.value} value={style.value}>
                    <div>
                      <div className="font-medium">{style.label}</div>
                      <div className="text-sm text-muted-foreground">{style.description}</div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <Separator />

          {/* Actions */}
          <div className="flex flex-wrap gap-2">
            <Button onClick={handleSave} className="flex items-center gap-2">
              <Wand2 className="h-4 w-4" />
              Custom Theme erstellen
            </Button>
            
            <Button variant="outline" onClick={handleReset} className="flex items-center gap-2">
              <RotateCcw className="h-4 w-4" />
              Zurücksetzen
            </Button>
            
            <Button variant="ghost" onClick={onClose}>
              Abbrechen
            </Button>
          </div>

          {/* Preview */}
          <div className="p-4 border rounded-lg bg-gradient-to-r from-muted/20 to-muted/10">
            <h4 className="font-medium mb-3">Live-Vorschau</h4>
            <div className="space-y-2">
              <div className="flex gap-2">
                <div 
                  className="h-8 rounded flex items-center justify-center text-xs font-medium px-3"
                  style={{ 
                    backgroundColor: customizations.customColors?.primary || 'var(--primary)',
                    color: 'white'
                  }}
                >
                  Primär
                </div>
                <div 
                  className="h-8 rounded flex items-center justify-center text-xs font-medium px-3"
                  style={{ 
                    backgroundColor: customizations.customColors?.secondary || 'var(--secondary)',
                    color: customizations.customColors?.foreground || 'var(--foreground)'
                  }}
                >
                  Sekundär
                </div>
                <div 
                  className="h-8 rounded flex items-center justify-center text-xs font-medium px-3"
                  style={{ 
                    backgroundColor: customizations.customColors?.accent || 'var(--accent)',
                    color: 'white'
                  }}
                >
                  Akzent
                </div>
              </div>
              <p className="text-xs text-muted-foreground">
                Animation: {animationStyles.find(s => s.value === customizations.animationStyle)?.label}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};