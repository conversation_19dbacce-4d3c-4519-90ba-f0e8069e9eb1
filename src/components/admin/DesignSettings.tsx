import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { Palette, Paintbrush, Sparkles, Building, Leaf, Crown, Settings2, Wand2 } from "lucide-react";
import { useTheme, ThemeVariant } from "@/contexts/ThemeContext";
import { AdvancedDesignSettings } from "./AdvancedDesignSettings";
import { ThemeCustomizer } from "./ThemeCustomizer";
import { useState } from "react";
import { cn } from "@/lib/utils";

const themePresets = [
  {
    id: 'klassik' as ThemeVariant,
    name: 'Klassik',
    description: 'Tennis-grünes Design mit traditionellen Elementen',
    icon: Paintbrush,
    colors: ['hsl(142, 69%, 32%)', 'hsl(142, 50%, 45%)', 'hsl(25, 85%, 65%)'],
    preview: 'bg-gradient-to-r from-tennis-green to-tennis-green-light'
  },
  {
    id: 'courtwaive-ai' as ThemeVariant,
    name: 'Courtwaive AI',
    description: 'Moderne AI-Gradients und futuristische Akzente',
    icon: Sparkles,
    colors: ['hsl(259, 100%, 65%)', 'hsl(220, 90%, 60%)', 'hsl(140, 60%, 35%)'],
    preview: 'bg-gradient-to-r from-purple-500 to-blue-500'
  },
  {
    id: 'corporate-blue' as ThemeVariant,
    name: 'Corporate Blue',
    description: 'Professionelle blaue Farbpalette für Business-Clubs',
    icon: Building,
    colors: ['hsl(220, 90%, 60%)', 'hsl(220, 70%, 50%)', 'hsl(220, 80%, 70%)'],
    preview: 'bg-gradient-to-r from-blue-600 to-blue-400'
  },
  {
    id: 'elegance' as ThemeVariant,
    name: 'Elegance',
    description: 'Elegante Schwarz-Gold Kombination für Premium-Clubs',
    icon: Crown,
    colors: ['hsl(0, 0%, 10%)', 'hsl(45, 100%, 50%)', 'hsl(0, 0%, 20%)'],
    preview: 'bg-gradient-to-r from-gray-900 to-yellow-500'
  },
  {
    id: 'nature' as ThemeVariant,
    name: 'Nature',
    description: 'Natürliche Erdtöne für Outdoor-Clubs',
    icon: Leaf,
    colors: ['hsl(25, 50%, 45%)', 'hsl(60, 30%, 60%)', 'hsl(120, 25%, 35%)'],
    preview: 'bg-gradient-to-r from-amber-700 to-green-600'
  }
];

export const DesignSettings = () => {
  const { theme, setTheme, isLoading } = useTheme();
  const [customizingTheme, setCustomizingTheme] = useState<ThemeVariant | null>(null);
  const [customColors, setCustomColors] = useState({
    primary: theme.customColors?.primary || '',
    secondary: theme.customColors?.secondary || '',
    accent: theme.customColors?.accent || ''
  });

  const handlePresetSelect = async (presetId: ThemeVariant) => {
    console.log('🎯 Selecting theme preset:', presetId);
    await setTheme({ variant: presetId });
  };

  const handleCustomizeTheme = (themeId: ThemeVariant) => {
    setCustomizingTheme(themeId);
  };

  const handleCustomColorChange = (colorType: 'primary' | 'secondary' | 'accent', value: string) => {
    setCustomColors(prev => ({
      ...prev,
      [colorType]: value
    }));
  };

  const handleSaveCustomColors = async () => {
    await setTheme({
      variant: 'custom',
      customColors
    });
  };

  if (customizingTheme) {
    return (
      <ThemeCustomizer 
        baseTheme={customizingTheme} 
        onClose={() => setCustomizingTheme(null)} 
      />
    );
  }

  return (
    <Tabs defaultValue="themes" className="space-y-6">
      <TabsList>
        <TabsTrigger value="themes">Themes</TabsTrigger>
        <TabsTrigger value="customizer">Individuell</TabsTrigger>
        <TabsTrigger value="advanced">Erweitert</TabsTrigger>
      </TabsList>

      <TabsContent value="themes" className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Palette className="h-5 w-5" />
              Design-Themes
            </CardTitle>
            <CardDescription>
              Wähle ein vorgefertigtes Design-Theme für deinen Verein
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {themePresets.map((preset) => {
                const Icon = preset.icon;
                const isSelected = theme.variant === preset.id;
                
                return (
                  <Card 
                    key={preset.id}
                    className={cn(
                      "cursor-pointer transition-all hover:shadow-md border-2",
                      isSelected ? "border-primary ring-2 ring-primary/20" : "border-border"
                    )}
                  >
                    <CardContent className="p-4">
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Icon className="h-4 w-4 text-muted-foreground" />
                            <h3 className="font-medium">{preset.name}</h3>
                          </div>
                          {isSelected && (
                            <Badge variant="default" className="text-xs">
                              Aktiv
                            </Badge>
                          )}
                        </div>
                        
                        <div className={cn(
                          "h-16 rounded-md",
                          preset.preview
                        )} />
                        
                        <p className="text-xs text-muted-foreground">
                          {preset.description}
                        </p>
                        
                        <div className="flex gap-1 mb-3">
                          {preset.colors.map((color, index) => (
                            <div
                              key={index}
                              className="w-4 h-4 rounded-full border border-border"
                              style={{ backgroundColor: color }}
                            />
                          ))}
                        </div>

                        <div className="flex gap-2">
                          <Button 
                            size="sm" 
                            variant={isSelected ? "default" : "outline"}
                            onClick={() => handlePresetSelect(preset.id)}
                            className="flex-1"
                          >
                            {isSelected ? "Aktiv" : "Auswählen"}
                          </Button>
                          <Button 
                            size="sm" 
                            variant="ghost"
                            onClick={() => handleCustomizeTheme(preset.id)}
                            className="flex items-center gap-1"
                          >
                            <Wand2 className="h-3 w-3" />
                            Anpassen
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Individuelle Farbanpassung</CardTitle>
            <CardDescription>
              Definiere eigene Farben für dein Club-Design
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="primary-color">Primärfarbe</Label>
                <div className="flex gap-2">
                  <Input
                    id="primary-color"
                    type="color"
                    value={customColors.primary}
                    onChange={(e) => handleCustomColorChange('primary', e.target.value)}
                    className="w-16 h-10 p-1 rounded cursor-pointer"
                  />
                  <Input
                    value={customColors.primary}
                    onChange={(e) => handleCustomColorChange('primary', e.target.value)}
                    placeholder="#000000"
                    className="flex-1"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="secondary-color">Sekundärfarbe</Label>
                <div className="flex gap-2">
                  <Input
                    id="secondary-color"
                    type="color"
                    value={customColors.secondary}
                    onChange={(e) => handleCustomColorChange('secondary', e.target.value)}
                    className="w-16 h-10 p-1 rounded cursor-pointer"
                  />
                  <Input
                    value={customColors.secondary}
                    onChange={(e) => handleCustomColorChange('secondary', e.target.value)}
                    placeholder="#000000"
                    className="flex-1"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="accent-color">Akzentfarbe</Label>
                <div className="flex gap-2">
                  <Input
                    id="accent-color"
                    type="color"
                    value={customColors.accent}
                    onChange={(e) => handleCustomColorChange('accent', e.target.value)}
                    className="w-16 h-10 p-1 rounded cursor-pointer"
                  />
                  <Input
                    value={customColors.accent}
                    onChange={(e) => handleCustomColorChange('accent', e.target.value)}
                    placeholder="#000000"
                    className="flex-1"
                  />
                </div>
              </div>
            </div>

            <Button 
              onClick={handleSaveCustomColors}
              disabled={isLoading}
              className="w-full md:w-auto"
            >
              Individuelle Farben speichern
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Basis-Vorschau</CardTitle>
            <CardDescription>
              So sieht dein ausgewähltes Design aus
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="p-6 border rounded-lg bg-gradient-to-r from-primary/10 to-accent/10">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-xl font-semibold text-primary">Beispiel Buchungskalender</h3>
                  <Button size="sm">Beispiel Button</Button>
                </div>
                <div className="grid grid-cols-3 gap-2">
                  <div className="h-12 bg-primary/20 rounded flex items-center justify-center text-sm">
                    Platz 1
                  </div>
                  <div className="h-12 bg-accent/20 rounded flex items-center justify-center text-sm">
                    Platz 2
                  </div>
                  <div className="h-12 bg-secondary/20 rounded flex items-center justify-center text-sm">
                    Platz 3
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="customizer" className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Wand2 className="h-5 w-5" />
              Theme Anpassungen
            </CardTitle>
            <CardDescription>
              Wähle ein Theme aus und passe es individuell an
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {themePresets.map((preset) => {
                const Icon = preset.icon;
                return (
                  <Button
                    key={preset.id}
                    variant="outline"
                    onClick={() => handleCustomizeTheme(preset.id)}
                    className="h-auto p-4 flex flex-col items-center gap-2 hover:bg-muted/50"
                  >
                    <Icon className="h-5 w-5" />
                    <span className="text-sm">{preset.name}</span>
                    <span className="text-xs text-muted-foreground">anpassen</span>
                  </Button>
                );
              })}
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="advanced" className="space-y-6">
        <AdvancedDesignSettings />
      </TabsContent>
    </Tabs>
  );
};