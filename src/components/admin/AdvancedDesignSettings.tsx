import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Upload, Download, RotateCcw } from "lucide-react";
import { useTheme, ThemeSettings } from "@/contexts/ThemeContext";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";

const googleFonts = [
  { name: 'Inter', value: 'Inter, sans-serif' },
  { name: 'Roboto', value: 'Roboto, sans-serif' },
  { name: 'Open Sans', value: 'Open Sans, sans-serif' },
  { name: 'Lato', value: 'Lato, sans-serif' },
  { name: '<PERSON><PERSON><PERSON>', value: 'Montser<PERSON>, sans-serif' },
  { name: '<PERSON><PERSON><PERSON>', value: 'Poppins, sans-serif' },
  { name: 'Playfair Display', value: 'Playfair Display, serif' },
  { name: 'Merriweather', value: 'Merriweather, serif' },
  { name: 'Source Code Pro', value: 'Source Code Pro, monospace' }
];

export const AdvancedDesignSettings = () => {
  const { theme, setTheme } = useTheme();
  const { toast } = useToast();
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [selectedFont, setSelectedFont] = useState(theme.customFont || 'Inter, sans-serif');

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.size > 2 * 1024 * 1024) { // 2MB limit
        toast({
          title: "Datei zu groß",
          description: "Logo darf maximal 2MB groß sein.",
          variant: "destructive"
        });
        return;
      }
      setLogoFile(file);
      
      // Create preview URL and save to theme
      const reader = new FileReader();
      reader.onload = async (e) => {
        const logoUrl = e.target?.result as string;
        await setTheme({
          ...theme,
          customLogo: logoUrl
        });
      };
      reader.readAsDataURL(file);
    }
  };

  const handleFontChange = async (fontValue: string) => {
    setSelectedFont(fontValue);
    await setTheme({
      ...theme,
      customFont: fontValue
    });
    
    // Apply font immediately
    document.documentElement.style.setProperty('--font-family', fontValue);
  };

  const exportTheme = () => {
    const themeConfig = {
      name: `${theme.variant}-theme`,
      version: '1.0',
      settings: theme,
      exportDate: new Date().toISOString()
    };
    
    const blob = new Blob([JSON.stringify(themeConfig, null, 2)], { 
      type: 'application/json' 
    });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${theme.variant}-theme.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    
    toast({
      title: "Theme exportiert",
      description: "Theme-Konfiguration wurde heruntergeladen."
    });
  };

  const importTheme = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = async (e) => {
        try {
          const themeConfig = JSON.parse(e.target?.result as string);
          if (themeConfig.settings) {
            await setTheme(themeConfig.settings);
            toast({
              title: "Theme importiert",
              description: `${themeConfig.name} wurde erfolgreich geladen.`
            });
          }
        } catch (error) {
          toast({
            title: "Import fehlgeschlagen",
            description: "Ungültige Theme-Datei.",
            variant: "destructive"
          });
        }
      };
      reader.readAsText(file);
    }
  };

  const resetToDefault = async () => {
    await setTheme({ variant: 'klassik' });
    setSelectedFont('Inter, sans-serif');
    setLogoFile(null);
    document.documentElement.style.removeProperty('--font-family');
    
    toast({
      title: "Theme zurückgesetzt",
      description: "Design wurde auf Standard zurückgesetzt."
    });
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Logo & Branding</CardTitle>
          <CardDescription>
            Lade dein Club-Logo hoch und passe die Typografie an
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="logo-upload">Club-Logo</Label>
            <div className="flex items-center gap-4">
              {theme.customLogo && (
                <div className="w-16 h-16 border rounded-lg overflow-hidden bg-muted flex items-center justify-center">
                  <img 
                    src={theme.customLogo} 
                    alt="Club Logo" 
                    className="max-w-full max-h-full object-contain"
                  />
                </div>
              )}
              <div className="flex-1">
                <Input
                  id="logo-upload"
                  type="file"
                  accept="image/*"
                  onChange={handleLogoUpload}
                  className="file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:bg-muted file:text-foreground"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  PNG, JPG oder SVG. Maximal 2MB.
                </p>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="font-select">Schriftart</Label>
            <Select value={selectedFont} onValueChange={handleFontChange}>
              <SelectTrigger>
                <SelectValue placeholder="Schriftart auswählen" />
              </SelectTrigger>
              <SelectContent>
                {googleFonts.map((font) => (
                  <SelectItem key={font.value} value={font.value}>
                    <span style={{ fontFamily: font.value }}>
                      {font.name}
                    </span>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Theme-Verwaltung</CardTitle>
          <CardDescription>
            Exportiere, importiere oder setze dein Theme zurück
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button 
              variant="outline" 
              onClick={exportTheme}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Theme exportieren
            </Button>
            
            <Button 
              variant="outline" 
              className="flex items-center gap-2 relative"
            >
              <Upload className="h-4 w-4" />
              Theme importieren
              <Input
                type="file"
                accept=".json"
                onChange={importTheme}
                className="absolute inset-0 opacity-0 cursor-pointer"
              />
            </Button>
            
            <Button 
              variant="outline" 
              onClick={resetToDefault}
              className="flex items-center gap-2"
            >
              <RotateCcw className="h-4 w-4" />
              Zurücksetzen
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Live-Vorschau</CardTitle>
          <CardDescription>
            Erweiterte Vorschau deines Designs mit verschiedenen Komponenten
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 p-6 border rounded-lg bg-gradient-to-r from-primary/5 to-accent/5">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {theme.customLogo && (
                  <img 
                    src={theme.customLogo} 
                    alt="Logo" 
                    className="w-8 h-8 object-contain"
                  />
                )}
                <h3 
                  className="text-xl font-semibold text-primary"
                  style={{ fontFamily: selectedFont }}
                >
                  TennisClub Demo
                </h3>
              </div>
              <div className="flex gap-2">
                <Button size="sm" variant="default">Primär</Button>
                <Button size="sm" variant="secondary">Sekundär</Button>
                <Button size="sm" variant="outline">Outline</Button>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card className="bg-card/50">
                <CardContent className="p-4">
                  <h4 className="font-medium mb-2" style={{ fontFamily: selectedFont }}>
                    Booking Card
                  </h4>
                  <div className="space-y-2">
                    <div className="h-2 bg-primary/20 rounded" />
                    <div className="h-2 bg-accent/20 rounded w-3/4" />
                  </div>
                </CardContent>
              </Card>
              
              <Card className="bg-card/50">
                <CardContent className="p-4">
                  <h4 className="font-medium mb-2" style={{ fontFamily: selectedFont }}>
                    Navigation
                  </h4>
                  <div className="space-y-1">
                    <div className="h-3 bg-secondary/30 rounded" />
                    <div className="h-3 bg-primary/20 rounded" />
                    <div className="h-3 bg-muted/30 rounded" />
                  </div>
                </CardContent>
              </Card>
              
              <Card className="bg-card/50">
                <CardContent className="p-4">
                  <h4 className="font-medium mb-2" style={{ fontFamily: selectedFont }}>
                    Statistics
                  </h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Plätze</span>
                      <span className="text-sm font-medium">6</span>
                    </div>
                    <div className="h-1 bg-primary/30 rounded" />
                  </div>
                </CardContent>
              </Card>
            </div>
            
            <div className="text-sm text-muted-foreground" style={{ fontFamily: selectedFont }}>
              Theme: {theme.variant} • Font: {googleFonts.find(f => f.value === selectedFont)?.name}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};