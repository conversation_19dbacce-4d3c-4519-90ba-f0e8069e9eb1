import { useState, useEffect } from "react";
import { Loader2, <PERSON>, ChevronDown, Settings2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger, DialogFooter } from "@/components/ui/dialog";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Plus, Edit, Trash2, <PERSON>, <PERSON>r<PERSON><PERSON><PERSON> } from "lucide-react";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { useTenant } from "@/contexts/TenantContext";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { format } from "date-fns";
import { de } from "date-fns/locale";
import { useToast } from "@/hooks/use-toast";
import { useColumnVisibility } from "@/hooks/useColumnVisibility";
import { ColumnVisibilityToggle } from "@/components/admin/ColumnVisibilityToggle";
import { useCustomFields } from "@/hooks/useCustomFields";
import { CustomFieldRenderer } from "./CustomFieldRenderer";

const memberFormSchema = z.object({
  title: z.string().optional(),
  first_name: z.string().min(1, "Vorname ist erforderlich"),
  last_name: z.string().min(1, "Nachname ist erforderlich"),
  email: z.string().email("Ungültige E-Mail-Adresse"),
  gender: z.enum(["male", "female", "diverse"], {
    required_error: "Geschlecht ist erforderlich",
  }),
  phone: z.string().optional(),
  address_street: z.string().optional(),
  address_postal_code: z.string().optional(),
  address_city: z.string().optional(),
  birth_date: z.string().optional(),
  membership_type: z.string().optional(),
  role: z.enum(["admin", "member", "trainer", "guest"]),
});

type MemberFormData = z.infer<typeof memberFormSchema>;

interface ClubMember {
  id: string;
  user_id: string;
  club_id: string;
  role: string;
  is_active: boolean;
  joined_at: string;
  title?: string;
  first_name?: string;
  last_name?: string;
  email?: string;
  gender?: 'male' | 'female' | 'diverse';
  phone?: string;
  address_street?: string;
  address_postal_code?: string;
  address_city?: string;
  birth_date?: string;
  membership_type?: string;
  account_type?: string;
  custom_data?: Record<string, any>;
}

interface UnifiedMemberManagementProps {
  defaultFilter?: "all" | "member" | "guest" | "trainer" | "admin";
  customFieldFilter?: {
    fieldKey: string;
    value: string;
  };
}

interface MembershipType {
  id: string;
  value: string;
  display_name: string;
}

const UnifiedMemberManagement = ({ defaultFilter = "all", customFieldFilter }: UnifiedMemberManagementProps) => {
  const { club, isLoading: clubLoading } = useTenant();
  const [members, setMembers] = useState<ClubMember[]>([]);
  const [filteredMembers, setFilteredMembers] = useState<ClubMember[]>([]);
  const [membershipTypes, setMembershipTypes] = useState<{[key: string]: string}>({});
  const [availableMembershipTypes, setAvailableMembershipTypes] = useState<MembershipType[]>([]);
  const [loading, setLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingMember, setEditingMember] = useState<ClubMember | null>(null);
  const [customFieldValues, setCustomFieldValues] = useState<Record<string, any>>({});
  const { toast } = useToast();
  const { columns, visibleColumns, toggleColumn, resetColumns } = useColumnVisibility();
  const { customFields, validateCustomData } = useCustomFields(club?.id);

  const form = useForm<MemberFormData>({
    resolver: zodResolver(memberFormSchema),
    defaultValues: {
      title: "",
      first_name: "",
      last_name: "",
      email: "",
      gender: undefined,
      phone: "",
      address_street: "",
      address_postal_code: "",
      address_city: "",
      birth_date: "",
      membership_type: "",
      role: (defaultFilter && defaultFilter !== "all") ? defaultFilter : "member",
    },
  });

  useEffect(() => {
    if (club?.id) {
      loadMembers();
      loadMembershipTypes();
    }
  }, [club?.id]);

  useEffect(() => {
    const filtered = filterMembers();
    setFilteredMembers(filtered);
  }, [members, defaultFilter, customFieldFilter]);

  const filterMembers = () => {
    if (!members) return [];
    
    if (customFieldFilter) {
      return members.filter(member => {
        const customData = member.custom_data;
        if (!customData) return false;
        
        try {
          const data = typeof customData === 'string' ? JSON.parse(customData) : customData;
          return data[customFieldFilter.fieldKey] === customFieldFilter.value;
        } catch {
          return false;
        }
      });
    }
    
    if (defaultFilter === "all") {
      return members;
    }
    
    return members.filter(member => 
      member.account_type?.toLowerCase() === defaultFilter
    );
  };

  const loadMembers = async () => {
    if (!club?.id) {
      console.log('⚠️ UnifiedMemberManagement: No club ID available');
      setMembers([]);
      setLoading(false);
      return;
    }

    try {
      console.log('🔍 UnifiedMemberManagement: Loading members for club:', club.id);
      const { data, error } = await supabase
        .from("club_memberships")
        .select("*")
        .eq("club_id", club.id)
        .eq("is_active", true)
        .order("joined_at", { ascending: false });

      if (error) throw error;
      
      console.log('✅ UnifiedMemberManagement: Loaded', data?.length || 0, 'members');
      // Transform data to handle Json type properly
      const transformedData = (data || []).map(member => ({
        ...member,
        custom_data: typeof member.custom_data === 'string' 
          ? JSON.parse(member.custom_data) 
          : (member.custom_data as Record<string, any>) || {}
      }));
      setMembers(transformedData);
    } catch (error) {
      console.error('❌ UnifiedMemberManagement: Error loading members:', error);
      toast({
        title: "Fehler",
        description: "Mitglieder konnten nicht geladen werden",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const loadMembershipTypes = async () => {
    try {
      const { data, error } = await supabase
        .from("membership_types")
        .select("id, value, display_name")
        .eq("is_active", true)
        .order("display_name");

      if (error) throw error;
      
      setAvailableMembershipTypes(data || []);
      
      // Create mapping from value to display_name
      const mapping: {[key: string]: string} = {};
      data?.forEach(type => {
        mapping[type.value] = type.display_name;
      });
      
      // Add legacy mappings for existing data
      mapping["Erwachsene"] = "Erwachsener";
      mapping["Kinder"] = "Jugendlicher";
      mapping["Jugendliche"] = "Jugendlicher";
      mapping["Studenten"] = "Student";
      mapping["Senioren"] = "Senioren";
      mapping["Paare"] = "Paare";
      mapping["Familien"] = "Familie";
      
      setMembershipTypes(mapping);
    } catch (error) {
      console.error('❌ UnifiedMemberManagement: Error loading membership types:', error);
    }
  };

  const getMembershipTypeDisplay = (membershipType: string | null | undefined): string => {
    if (!membershipType) return '-';
    return membershipTypes[membershipType] || membershipType;
  };

  const onSubmit = async (data: MemberFormData) => {
    try {
      // Validate custom fields
      const validation = validateCustomData(customFieldValues);
      if (!validation.isValid) {
        toast({
          title: "Validierungsfehler",
          description: validation.errors.join(', '),
          variant: "destructive"
        });
        return;
      }

      if (editingMember) {
        // Update existing member in club_memberships
        const { error: clubError } = await supabase
          .from("club_memberships")
          .update({
            title: data.title,
            first_name: data.first_name,
            last_name: data.last_name,
            email: data.email,
            gender: data.gender,
            phone: data.phone,
            address_street: data.address_street,
            address_postal_code: data.address_postal_code,
            address_city: data.address_city,
            birth_date: data.birth_date,
            membership_type: data.membership_type,
            role: data.role,
            custom_data: customFieldValues,
          })
          .eq("id", editingMember.id);

        if (clubError) throw clubError;

        // Also update the user's profile if user_id exists
        if (editingMember.user_id) {
          const { error: profileError } = await supabase
            .from("profiles")
            .update({
              title: data.title,
              first_name: data.first_name,
              last_name: data.last_name,
              email: data.email,
              gender: data.gender,
              birth_date: data.birth_date,
              street: data.address_street,
              house_number: "", // Not captured in club_memberships form
              postal_code: data.address_postal_code,
              city: data.address_city,
            })
            .eq("id", editingMember.user_id);

          // Don't throw error if profile update fails, as it might not exist yet
          if (profileError) {
            console.warn('Profile update failed:', profileError);
          }
        }

        toast({ title: "Erfolg", description: "Mitglied wurde aktualisiert" });
      } else {
        // Create new member - Note: This would typically require user registration first
        toast({ 
          title: "Info", 
          description: "Das Erstellen neuer Mitglieder erfordert eine vollständige Benutzerregistrierung",
          variant: "default"
        });
        return;
      }

      setIsDialogOpen(false);
      setEditingMember(null);
      form.reset();
      loadMembers();
    } catch (error: any) {
      toast({
        title: "Fehler",
        description: error.message || "Fehler beim Speichern",
        variant: "destructive"
      });
    }
  };

  const handleEdit = (member: ClubMember) => {
    setEditingMember(member);
    form.reset({
      title: member.title || "",
      first_name: member.first_name || "",
      last_name: member.last_name || "",
      email: member.email || "",
      gender: member.gender,
      phone: member.phone || "",
      address_street: member.address_street || "",
      address_postal_code: member.address_postal_code || "",
      address_city: member.address_city || "",
      birth_date: member.birth_date || "",
      membership_type: member.membership_type || "",
      role: member.role as any,
    });
    setCustomFieldValues(member.custom_data || {});
    setIsDialogOpen(true);
  };

  const handleDeactivate = async (memberId: string) => {
    try {
      const { error } = await supabase
        .from("club_memberships")
        .update({ is_active: false })
        .eq("id", memberId);

      if (error) throw error;
      toast({ title: "Erfolg", description: "Mitglied wurde deaktiviert" });
      loadMembers();
    } catch (error) {
      toast({
        title: "Fehler",
        description: "Fehler beim Deaktivieren des Mitglieds",
        variant: "destructive"
      });
    }
  };

  const getRoleBadge = (role: string) => {
    switch (role) {
      case 'admin':
        return <Badge variant="secondary" className="gap-1 bg-emerald-100 text-emerald-700 border-emerald-200 hover:bg-emerald-200"><Crown className="h-3 w-3" />Admin</Badge>;
      case 'trainer':
        return <Badge variant="secondary" className="gap-1"><UserCheck className="h-3 w-3" />Trainer</Badge>;
      case 'guest':
        return <Badge variant="secondary" className="gap-1 bg-sky-100 text-sky-700 border-sky-200 hover:bg-sky-200"><Users className="h-3 w-3" />Gast</Badge>;
      case 'member':
      default:
        return <Badge variant="outline" className="gap-1"><Users className="h-3 w-3" />Mitglied</Badge>;
    }
  };

  const getTitle = () => {
    if (customFieldFilter) {
      return `${customFieldFilter.value}`;
    }
    
    switch (defaultFilter) {
      case "member": return "Mitglieder";
      case "guest": return "Gäste";
      case "trainer": return "Trainer";
      case "admin": return "Admins";
      default: return "Alle Benutzer";
    }
  };

  const renderCellContent = (member: ClubMember, columnKey: string) => {
    switch (columnKey) {
      case 'name':
        const nameWithTitle = [member.title, member.first_name, member.last_name]
          .filter(Boolean)
          .join(' ');
        return nameWithTitle || 'Unvollständiges Profil';
      
      case 'email':
        return member.email || 'Keine E-Mail';
      
      case 'gender':
        const genderLabels = {
          'male': 'Männlich',
          'female': 'Weiblich', 
          'diverse': 'Divers'
        };
        return genderLabels[member.gender as keyof typeof genderLabels] || '-';
      
      case 'phone':
        return member.phone || '-';
      
      case 'role':
        return getRoleBadge(member.role);
      
      case 'membership_type':
        return getMembershipTypeDisplay(member.membership_type);
      
      case 'birth_date':
        return member.birth_date 
          ? new Date(member.birth_date).toLocaleDateString('de-DE')
          : '-';
      
      case 'address':
        const addressParts = [
          member.address_street,
          member.address_postal_code,
          member.address_city
        ].filter(Boolean);
        return addressParts.length > 0 ? addressParts.join(', ') : '-';
      
      case 'joined_at':
        return new Date(member.joined_at).toLocaleDateString('de-DE');
      
      case 'tennis_lk':
        return member.custom_data?.tennis_lk || '-';
      
      case 'actions':
        return (
          <div className="flex gap-2 justify-end">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleEdit(member)}
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleDeactivate(member.id)}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        );
      
      default:
        return '-';
    }
  };

  if (loading) {
    return <div className="flex items-center justify-center p-8">Laden...</div>;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Users className="h-6 w-6 text-primary" />
          <div>
            <h3 className="text-xl font-semibold">{getTitle()}</h3>
            <p className="text-sm text-muted-foreground">
              {filteredMembers.length} {defaultFilter ? `${defaultFilter}s` : 'Mitglieder'} gefunden
            </p>
          </div>
        </div>

        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
             <Button onClick={() => {
               setEditingMember(null);
               setCustomFieldValues({});
                 form.reset({
                   title: "",
                   first_name: "",
                  last_name: "",
                  email: "",
                  gender: undefined,
                  phone: "",
                  address_street: "",
                  address_postal_code: "",
                  address_city: "",
                  birth_date: "",
                  membership_type: "",
                  role: (defaultFilter && defaultFilter !== "all") ? defaultFilter : "member",
                });
             }} className="gap-2">
              <Plus className="h-4 w-4" />
              Mitglied bearbeiten
            </Button>
          </DialogTrigger>
          
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>
                {editingMember ? 'Mitglied bearbeiten' : 'Neues Mitglied'}
              </DialogTitle>
              <DialogDescription>
                {editingMember ? 'Bearbeiten Sie die Mitgliederdaten.' : 'Erstellen Sie ein neues Mitglied.'}
              </DialogDescription>
            </DialogHeader>
            
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                {(() => {
                  const selectedRole = form.watch("role");
                  const isGuest = selectedRole === "guest";
                  
                  return (
                    <>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <FormField
                          control={form.control}
                          name="title"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Anrede</FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Wähle Anrede" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="Herr">Herr</SelectItem>
                                  <SelectItem value="Frau">Frau</SelectItem>
                                  <SelectItem value="Herr Dr.">Herr Dr.</SelectItem>
                                  <SelectItem value="Frau Dr.">Frau Dr.</SelectItem>
                                  <SelectItem value="Herr Prof.">Herr Prof.</SelectItem>
                                  <SelectItem value="Frau Prof.">Frau Prof.</SelectItem>
                                  <SelectItem value="Herr Prof. Dr.">Herr Prof. Dr.</SelectItem>
                                  <SelectItem value="Frau Prof. Dr.">Frau Prof. Dr.</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                    control={form.control}
                    name="first_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Vorname *</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="last_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Nachname *</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>E-Mail *</FormLabel>
                        <FormControl>
                          <Input type="email" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                   />
                       </div>
                       
                       {/* Gender Field - Required */}
                       <div className="grid grid-cols-1 gap-4">
                         <FormField
                           control={form.control}
                           name="gender"
                           render={({ field }) => (
                             <FormItem>
                               <FormLabel>Geschlecht *</FormLabel>
                               <Select onValueChange={field.onChange} defaultValue={field.value}>
                                 <FormControl>
                                   <SelectTrigger>
                                     <SelectValue placeholder="Wähle Geschlecht" />
                                   </SelectTrigger>
                                 </FormControl>
                                 <SelectContent>
                                   <SelectItem value="male">Männlich</SelectItem>
                                   <SelectItem value="female">Weiblich</SelectItem>
                                   <SelectItem value="diverse">Divers</SelectItem>
                                 </SelectContent>
                               </Select>
                               <FormMessage />
                             </FormItem>
                           )}
                         />
                       </div>
                       
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                         <FormField
                           control={form.control}
                           name="phone"
                           render={({ field }) => (
                             <FormItem>
                               <FormLabel>Telefon</FormLabel>
                               <FormControl>
                                 <Input {...field} />
                               </FormControl>
                               <FormMessage />
                             </FormItem>
                           )}
                         />
                  
                  <FormField
                    control={form.control}
                    name="role"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Rolle</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Wähle Rolle" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="member">Mitglied</SelectItem>
                            <SelectItem value="trainer">Trainer</SelectItem>
                            <SelectItem value="admin">Administrator</SelectItem>
                            <SelectItem value="guest">Gast</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  {/* Membership Type - Only show for non-guests */}
                  {!isGuest && (
                    <FormField
                      control={form.control}
                      name="membership_type"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Mitgliedschaftsform</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Wähle Mitgliedschaftsform" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {availableMembershipTypes.map((type) => (
                                <SelectItem key={type.id} value={type.value}>
                                  {type.display_name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                     />
                  )}
                </div>
                 
                 {/* Address and Personal Information */}
                 <div className="space-y-4">
                   <div className="border-t pt-4">
                     <h4 className="text-sm font-medium mb-3">Adresse und persönliche Informationen</h4>
                     <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                       <FormField
                         control={form.control}
                         name="address_street"
                         render={({ field }) => (
                           <FormItem>
                             <FormLabel>Straße</FormLabel>
                             <FormControl>
                               <Input {...field} />
                             </FormControl>
                             <FormMessage />
                           </FormItem>
                         )}
                       />
                       
                       <FormField
                         control={form.control}
                         name="address_postal_code"
                         render={({ field }) => (
                           <FormItem>
                             <FormLabel>PLZ</FormLabel>
                             <FormControl>
                               <Input {...field} />
                             </FormControl>
                             <FormMessage />
                           </FormItem>
                         )}
                       />
                       
                       <FormField
                         control={form.control}
                         name="address_city"
                         render={({ field }) => (
                           <FormItem>
                             <FormLabel>Stadt</FormLabel>
                             <FormControl>
                               <Input {...field} />
                             </FormControl>
                             <FormMessage />
                           </FormItem>
                         )}
                       />
                       
                       <FormField
                         control={form.control}
                         name="birth_date"
                         render={({ field }) => (
                           <FormItem>
                             <FormLabel>Geburtsdatum</FormLabel>
                             <FormControl>
                               <Input type="date" {...field} />
                             </FormControl>
                             <FormMessage />
                           </FormItem>
                         )}
                       />
                     </div>
                   </div>
                 </div>
                 
                 {/* Custom Fields Section */}
                 {customFields.length > 0 && (
                   <div className="space-y-4">
                     <div className="border-t pt-4">
                       <h4 className="text-sm font-medium mb-3">Zusätzliche Informationen</h4>
                       <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                         {customFields.map((field) => (
                           <CustomFieldRenderer
                             key={field.id}
                             field={field}
                             value={customFieldValues[field.field_key]}
                             onChange={(value) => 
                               setCustomFieldValues(prev => ({
                                 ...prev,
                                 [field.field_key]: value
                               }))
                             }
                           />
                         ))}
                       </div>
                     </div>
                   </div>
                 )}
                 
                 <DialogFooter>
                   <Button type="submit">
                     {editingMember ? 'Aktualisieren' : 'Erstellen'}
                   </Button>
                 </DialogFooter>
                    </>
                  );
                })()}
               </form>
             </Form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Members Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Mitgliederliste</CardTitle>
              <CardDescription>
                Verwalten Sie die Clubmitglieder und deren Informationen
              </CardDescription>
            </div>
            <ColumnVisibilityToggle 
              columns={columns}
              onToggleColumn={toggleColumn}
              onReset={resetColumns}
            />
          </div>
        </CardHeader>
        <CardContent>
          {filteredMembers.length === 0 ? (
            <div className="text-center py-8">
              <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium text-muted-foreground mb-2">
                Keine {defaultFilter ? defaultFilter + 's' : 'Mitglieder'} gefunden
              </h3>
              <p className="text-sm text-muted-foreground">
                {defaultFilter === 'admin' 
                  ? 'Es sind keine Administratoren für diesen Club eingerichtet.' 
                  : 'Beginnen Sie mit dem Hinzufügen von Mitgliedern zu Ihrem Club.'}
              </p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  {visibleColumns.map((column) => (
                    <TableHead 
                      key={column.key}
                      className={column.key === 'actions' ? 'text-right' : ''}
                    >
                      {column.label}
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredMembers.map((member) => (
                  <TableRow key={member.id}>
                    {visibleColumns.map((column) => (
                      <TableCell 
                        key={`${member.id}-${column.key}`}
                        className={
                          column.key === 'name' ? 'font-medium' : 
                          column.key === 'actions' ? 'text-right' : ''
                        }
                      >
                        {renderCellContent(member, column.key)}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default UnifiedMemberManagement;