import { useState, useEffect } from "react";
import { Plus, Edit2, Trash2, Save, X } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { useTenant } from "@/contexts/TenantContext";

interface CustomField {
  id: string;
  field_key: string;
  field_name: string;
  field_type: string;
  field_options: any;
  is_required: boolean;
  is_active: boolean;
  display_order: number;
}

interface NewCustomField {
  field_key: string;
  field_name: string;
  field_type: string;
  field_options: string[];
  is_required: boolean;
  display_order: number;
}

const FIELD_TYPES = [
  { value: 'text', label: 'Text' },
  { value: 'number', label: '<PERSON>ahl' },
  { value: 'select', label: 'Auswahl' },
  { value: 'tennis_lk', label: 'Tennis LK' }
];

export default function CustomFieldsManager() {
  const { club } = useTenant();
  const [customFields, setCustomFields] = useState<CustomField[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [editingField, setEditingField] = useState<string | null>(null);
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [newField, setNewField] = useState<NewCustomField>({
    field_key: '',
    field_name: '',
    field_type: 'text',
    field_options: [],
    is_required: false,
    display_order: 0
  });

  useEffect(() => {
    if (club?.id) {
      fetchCustomFields();
    }
  }, [club?.id]);

  const fetchCustomFields = async () => {
    if (!club?.id) return;

    try {
      const { data, error } = await supabase
        .from('club_custom_fields')
        .select('*')
        .eq('club_id', club.id)
        .order('display_order', { ascending: true });

      if (error) throw error;
      setCustomFields((data || []).map(field => ({
        ...field,
        field_options: Array.isArray(field.field_options) ? field.field_options : []
      })));
    } catch (error) {
      console.error('Error fetching custom fields:', error);
      toast.error('Fehler beim Laden der benutzerdefinierten Felder');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveNew = async () => {
    if (!club?.id || !newField.field_key || !newField.field_name) {
      toast.error('Bitte füllen Sie alle Pflichtfelder aus');
      return;
    }

    try {
      const { error } = await supabase
        .from('club_custom_fields')
        .insert({
          club_id: club.id,
          field_key: newField.field_key,
          field_name: newField.field_name,
          field_type: newField.field_type,
          field_options: newField.field_options,
          is_required: newField.is_required,
          display_order: customFields.length
        });

      if (error) throw error;

      toast.success('Benutzerdefiniertes Feld erstellt');
      setIsAddingNew(false);
      setNewField({
        field_key: '',
        field_name: '',
        field_type: 'text',
        field_options: [],
        is_required: false,
        display_order: 0
      });
      fetchCustomFields();
    } catch (error) {
      console.error('Error creating custom field:', error);
      toast.error('Fehler beim Erstellen des Feldes');
    }
  };

  const handleDelete = async (fieldId: string) => {
    try {
      const { error } = await supabase
        .from('club_custom_fields')
        .delete()
        .eq('id', fieldId);

      if (error) throw error;

      toast.success('Feld gelöscht');
      fetchCustomFields();
    } catch (error) {
      console.error('Error deleting custom field:', error);
      toast.error('Fehler beim Löschen des Feldes');
    }
  };

  const handleToggleActive = async (fieldId: string, isActive: boolean) => {
    try {
      const { error } = await supabase
        .from('club_custom_fields')
        .update({ is_active: isActive })
        .eq('id', fieldId);

      if (error) throw error;

      toast.success(isActive ? 'Feld aktiviert' : 'Feld deaktiviert');
      fetchCustomFields();
    } catch (error) {
      console.error('Error updating field status:', error);
      toast.error('Fehler beim Aktualisieren des Feldes');
    }
  };

  if (isLoading) {
    return <div className="text-center p-4">Laden...</div>;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Benutzerdefinierte Felder
          <Button
            onClick={() => setIsAddingNew(true)}
            disabled={isAddingNew}
            size="sm"
          >
            <Plus className="h-4 w-4 mr-2" />
            Neues Feld
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {isAddingNew && (
          <Card className="border-primary">
            <CardContent className="pt-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="field_key">Feld-Schlüssel *</Label>
                  <Input
                    id="field_key"
                    value={newField.field_key}
                    onChange={(e) => setNewField({ ...newField, field_key: e.target.value })}
                    placeholder="z.B. vereins_id"
                  />
                </div>
                <div>
                  <Label htmlFor="field_name">Anzeigename *</Label>
                  <Input
                    id="field_name"
                    value={newField.field_name}
                    onChange={(e) => setNewField({ ...newField, field_name: e.target.value })}
                    placeholder="z.B. Vereins-ID"
                  />
                </div>
                <div>
                  <Label htmlFor="field_type">Feldtyp</Label>
                  <Select
                    value={newField.field_type}
                    onValueChange={(value) => setNewField({ ...newField, field_type: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {FIELD_TYPES.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center space-x-2 pt-6">
                  <Switch
                    id="is_required"
                    checked={newField.is_required}
                    onCheckedChange={(checked) => setNewField({ ...newField, is_required: checked })}
                  />
                  <Label htmlFor="is_required">Pflichtfeld</Label>
                </div>
              </div>
              
              {newField.field_type === 'select' && (
                <div className="mt-4">
                  <Label>Auswahloptionen (eine pro Zeile)</Label>
                  <textarea
                    className="w-full p-2 border rounded mt-1"
                    rows={3}
                    placeholder="Option 1&#10;Option 2&#10;Option 3"
                    onChange={(e) => setNewField({ 
                      ...newField, 
                      field_options: e.target.value.split('\n').filter(opt => opt.trim()) 
                    })}
                  />
                </div>
              )}

              <div className="flex gap-2 mt-4">
                <Button onClick={handleSaveNew} size="sm">
                  <Save className="h-4 w-4 mr-2" />
                  Speichern
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => setIsAddingNew(false)}
                  size="sm"
                >
                  <X className="h-4 w-4 mr-2" />
                  Abbrechen
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {customFields.length === 0 && !isAddingNew ? (
          <div className="text-center text-muted-foreground py-8">
            Noch keine benutzerdefinierten Felder erstellt
          </div>
        ) : (
          <div className="space-y-2">
            {customFields.map((field) => (
              <div key={field.id} className="flex items-center justify-between p-3 border rounded">
                <div className="flex items-center gap-4">
                  <div>
                    <div className="font-medium">{field.field_name}</div>
                    <div className="text-sm text-muted-foreground">
                      {field.field_key} • {FIELD_TYPES.find(t => t.value === field.field_type)?.label}
                    </div>
                  </div>
                  <div className="flex gap-2">
                    {field.is_required && (
                      <Badge variant="secondary">Pflichtfeld</Badge>
                    )}
                    <Badge variant={field.is_active ? "default" : "outline"}>
                      {field.is_active ? "Aktiv" : "Inaktiv"}
                    </Badge>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Switch
                    checked={field.is_active}
                    onCheckedChange={(checked) => handleToggleActive(field.id, checked)}
                  />
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDelete(field.id)}
                    className="text-destructive hover:text-destructive"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}