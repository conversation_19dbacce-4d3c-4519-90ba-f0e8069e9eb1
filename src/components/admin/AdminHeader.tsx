import { useState, useEffect } from "react";
import { Bell, User, TestTube, Users, LogOut } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useMockData } from "@/contexts/MockDataContext";
import { useClubUrl } from "@/contexts/TenantContext";
import { useClub } from "@/contexts/ClubContext";
import { Link } from "react-router-dom";
import { toast } from "sonner";

const AdminHeader = () => {
  const { user } = useAuth();
  const { showMockData, toggleMockData } = useMockData();
  const buildClubUrl = useClubUrl();
  const { currentClub } = useClub();
  const [clubInitials, setClubInitials] = useState("TV");
  const [currentUserName, setCurrentUserName] = useState<string | null>(null);

  const handleSignOut = async () => {
    try {
      await supabase.auth.signOut();
      toast.success("Erfolgreich abgemeldet");
    } catch (error) {
      toast.error("Fehler beim Abmelden");
    }
  };

  // Generate club initials from current club name
  useEffect(() => {
    if (currentClub?.name) {
      const name = currentClub.name;
      // Generate initials from club name
      const words = name.split(' ').filter(word => word.length > 0);
      const initials = words.length >= 2 
        ? words[0].charAt(0) + words[1].charAt(0)
        : words[0]?.substring(0, 2) || 'TV';
      setClubInitials(initials.toUpperCase());
    }
  }, [currentClub]);

  // Fetch current user's display name from database
  useEffect(() => {
    const fetchCurrentUserName = async () => {
      if (!user?.id) return;

      try {
        // First try to get name from club_memberships (most up-to-date)
        const { data: membershipData, error: membershipError } = await supabase
          .from('club_memberships')
          .select('first_name, last_name')
          .eq('user_id', user.id)
          .eq('is_active', true)
          .limit(1)
          .single();

        if (membershipData && !membershipError) {
          const fullName = `${membershipData.first_name} ${membershipData.last_name}`.trim();
          if (fullName) {
            setCurrentUserName(fullName);
            return;
          }
        }

        // Fallback to profiles table
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('first_name, last_name')
          .eq('id', user.id)
          .single();

        if (profileData && !profileError) {
          const fullName = `${profileData.first_name} ${profileData.last_name}`.trim();
          if (fullName) {
            setCurrentUserName(fullName);
            return;
          }
        }

        // Last fallback to user metadata
        const fallbackName = user.user_metadata?.first_name 
          ? `${user.user_metadata.first_name} ${user.user_metadata.last_name || ''}`.trim()
          : null;
        setCurrentUserName(fallbackName);

      } catch (error) {
        console.error('Error fetching user name:', error);
        setCurrentUserName(user.user_metadata?.first_name || null);
      }
    };

    fetchCurrentUserName();
  }, [user]);

  // Mock admin user data - would come from auth context
  const adminUser = {
    name: currentUserName || user?.user_metadata?.first_name || "Admin",
    email: user?.email || "<EMAIL>",
    avatar: null
  };

  const notifications = [
    { id: 1, message: "Neue Buchungsanfrage", time: "vor 5 Min" },
    { id: 2, message: "Wartung Platz 2 fällig", time: "vor 1 Std" },
    { id: 3, message: "Mitgliedsbeitrag überfällig", time: "vor 2 Std" }
  ];

  return (
    <header className="sticky top-0 z-40 w-full border-b border-border bg-background">
      <div className="container flex h-16 items-center justify-between px-6">
        {/* Logo */}
        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-tennis rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">{clubInitials}</span>
            </div>
            <div>
              <h1 className="text-lg font-semibold text-foreground">{currentClub?.name || "TennisVerein"}</h1>
              <p className="text-xs text-muted-foreground">Admin Dashboard</p>
            </div>
          </div>
        </div>

        {/* Right Side - Notifications & User */}
        <div className="flex items-center space-x-4">
          {/* Notifications */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="relative">
                <Bell className="h-5 w-5" />
                {notifications.length > 0 && (
                  <Badge 
                    variant="destructive" 
                    className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
                  >
                    {notifications.length}
                  </Badge>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-80">
              <DropdownMenuLabel>Benachrichtigungen</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {notifications.length > 0 ? (
                notifications.map((notification) => (
                  <DropdownMenuItem key={notification.id} className="p-3">
                    <div className="flex flex-col space-y-1">
                      <p className="text-sm font-medium">{notification.message}</p>
                      <p className="text-xs text-muted-foreground">{notification.time}</p>
                    </div>
                  </DropdownMenuItem>
                ))
              ) : (
                <DropdownMenuItem>
                  <p className="text-sm text-muted-foreground">Keine neuen Benachrichtigungen</p>
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* User Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="relative h-10 w-10 rounded-full">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={adminUser.avatar || ""} alt={adminUser.name} />
                  <AvatarFallback className="bg-tennis-green text-primary-foreground">
                    <User className="h-4 w-4" />
                  </AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56" align="end">
              <DropdownMenuLabel className="font-normal">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none">{adminUser.name}</p>
                  <p className="text-xs leading-none text-muted-foreground">
                    {adminUser.email}
                  </p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link to={buildClubUrl("/member")}>
                  <Users className="mr-2 h-4 w-4" />
                  Als Mitglied wechseln
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                Profil bearbeiten
              </DropdownMenuItem>
              <DropdownMenuItem>
                Einstellungen
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <div className="px-2 py-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <TestTube className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">Demo-Daten</span>
                  </div>
                  <Switch
                    checked={showMockData}
                    onCheckedChange={toggleMockData}
                  />
                </div>
                <p className="text-xs text-muted-foreground mt-1 ml-6">
                  Zeigt Beispieldaten für Demonstration
                </p>
              </div>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleSignOut} className="text-destructive">
                <LogOut className="mr-2 h-4 w-4" />
                Abmelden
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
};

export default AdminHeader;