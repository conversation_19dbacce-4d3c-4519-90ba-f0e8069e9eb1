import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Pencil, Plus, Trash2 } from 'lucide-react';
import { useClub } from '@/contexts/ClubContext';
import { supabase } from '@/integrations/supabase/client';
import { FormatConfigDialog } from './FormatConfigDialog';
import { toast } from 'sonner';

interface FormatConfig {
  id: string;
  club_id: string;
  tournament_id?: string;
  format_type: string;
  custom_rules: Record<string, any>;
  custom_description?: string;
  custom_name?: string;
  time_config: Record<string, any>;
  created_at: string;
  updated_at: string;
}

const FORMAT_TYPES = [
  { value: 'uts', label: 'UTS (Ultimate Tennis Showdown)', description: 'Innovatives Format mit Zeitlimits' },
  { value: 'knockout', label: 'K.O.-System', description: 'Klassisches Ausscheidungsturnier' },
  { value: 'round_robin', label: 'Jeder gegen Jeden', description: 'Alle Teilnehmer spielen gegeneinander' },
  { value: 'swiss', label: 'Schweizer System', description: 'Paarungen nach Leistungsstärke' },
  { value: 'double_elimination', label: 'Doppel-K.O.', description: 'Zweite Chance nach Niederlage' }
];

export function FormatConfigurationManager() {
  const { currentClub } = useClub();
  const [configs, setConfigs] = useState<FormatConfig[]>([]);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingConfig, setEditingConfig] = useState<FormatConfig | null>(null);
  const [activeTab, setActiveTab] = useState('club-defaults');

  useEffect(() => {
    if (currentClub?.id) {
      fetchConfigs();
    }
  }, [currentClub?.id]);

  const fetchConfigs = async () => {
    if (!currentClub?.id) return;

    try {
      const { data, error } = await supabase.functions.invoke('tournament-format-config', {
        body: { action: 'get', club_id: currentClub.id }
      });

      if (error) throw error;

      setConfigs(data.data || []);
    } catch (error) {
      console.error('Error fetching format configs:', error);
      toast.error('Fehler beim Laden der Format-Konfigurationen');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateConfig = (formatType: string) => {
    setEditingConfig({
      id: '',
      club_id: currentClub?.id || '',
      format_type: formatType,
      custom_rules: {},
      custom_description: '',
      custom_name: '',
      time_config: {},
      created_at: '',
      updated_at: ''
    });
    setDialogOpen(true);
  };

  const handleEditConfig = (config: FormatConfig) => {
    setEditingConfig(config);
    setDialogOpen(true);
  };

  const handleDeleteConfig = async (configId: string) => {
    try {
      const { error } = await supabase.functions.invoke('tournament-format-config', {
        body: { action: 'delete', config_id: configId }
      });

      if (error) throw error;

      toast.success('Format-Konfiguration gelöscht');
      fetchConfigs();
    } catch (error) {
      console.error('Error deleting config:', error);
      toast.error('Fehler beim Löschen der Konfiguration');
    }
  };

  const handleSaveConfig = async (config: FormatConfig) => {
    try {
      const { error } = await supabase.functions.invoke('tournament-format-config', {
        body: { action: 'save', ...config }
      });

      if (error) throw error;

      toast.success('Format-Konfiguration gespeichert');
      setDialogOpen(false);
      setEditingConfig(null);
      fetchConfigs();
    } catch (error) {
      console.error('Error saving config:', error);
      toast.error('Fehler beim Speichern der Konfiguration');
    }
  };

  const getConfigsForTab = (tab: string) => {
    switch (tab) {
      case 'club-defaults':
        return configs.filter(c => !c.tournament_id);
      case 'tournament-specific':
        return configs.filter(c => c.tournament_id);
      default:
        return configs;
    }
  };

  const getConfigForFormat = (formatType: string) => {
    return configs.find(c => c.format_type === formatType && !c.tournament_id);
  };

  if (loading) {
    return <div>Lade Format-Konfigurationen...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Format-Konfiguration</h2>
          <p className="text-muted-foreground">
            Passen Sie Turnierformate an die Gegebenheiten Ihres Vereins an
          </p>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="club-defaults">Vereins-Standards</TabsTrigger>
          <TabsTrigger value="tournament-specific">Turnier-spezifisch</TabsTrigger>
        </TabsList>

        <TabsContent value="club-defaults" className="space-y-4">
          <div className="grid gap-4">
            {FORMAT_TYPES.map((format) => {
              const existingConfig = getConfigForFormat(format.value);
              
              return (
                <Card key={format.value}>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <div>
                      <CardTitle className="text-base">{format.label}</CardTitle>
                      <CardDescription>{format.description}</CardDescription>
                    </div>
                    <div className="flex items-center gap-2">
                      {existingConfig && (
                        <Badge variant="secondary">Angepasst</Badge>
                      )}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => existingConfig ? handleEditConfig(existingConfig) : handleCreateConfig(format.value)}
                      >
                        {existingConfig ? <Pencil className="h-4 w-4" /> : <Plus className="h-4 w-4" />}
                        {existingConfig ? 'Bearbeiten' : 'Anpassen'}
                      </Button>
                      {existingConfig && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteConfig(existingConfig.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </CardHeader>
                  {existingConfig && (
                    <CardContent>
                      <div className="text-sm text-muted-foreground">
                        {existingConfig.custom_description || 'Keine spezielle Beschreibung'}
                      </div>
                    </CardContent>
                  )}
                </Card>
              );
            })}
          </div>
        </TabsContent>

        <TabsContent value="tournament-specific" className="space-y-4">
          <div className="text-center py-8 text-muted-foreground">
            <p>Turnier-spezifische Anpassungen werden beim Erstellen eines Turniers konfiguriert.</p>
            <p>Hier werden alle existierenden turnier-spezifischen Konfigurationen angezeigt.</p>
          </div>
          
          {getConfigsForTab('tournament-specific').map((config) => (
            <Card key={config.id}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div>
                  <CardTitle className="text-base">
                    {config.custom_name || FORMAT_TYPES.find(f => f.value === config.format_type)?.label}
                  </CardTitle>
                  <CardDescription>
                    Turnier-ID: {config.tournament_id}
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEditConfig(config)}
                  >
                    <Pencil className="h-4 w-4" />
                    Bearbeiten
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDeleteConfig(config.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
            </Card>
          ))}
        </TabsContent>
      </Tabs>

      {editingConfig && (
        <FormatConfigDialog
          open={dialogOpen}
          onOpenChange={setDialogOpen}
          config={editingConfig}
          onSave={handleSaveConfig}
        />
      )}
    </div>
  );
}