import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Plus, Settings, Trophy, Users, Eye, Trash2, Clock, Calendar } from 'lucide-react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { ParticipantManagement } from './ParticipantManagement';
import { MatchManagement } from './MatchManagement';
import { TournamentCourtSetup } from './TournamentCourtSetup';
import { TournamentCalendar } from './TournamentCalendar';
import { FormatConfigDialog } from '@/components/tournaments/FormatConfigDialog';
import { useContext } from 'react';
import { useClub } from '@/contexts/ClubContext';

interface Tournament {
  id: string;
  name: string;
  description?: string;
  status: 'registration' | 'active' | 'completed' | 'cancelled';
  format_type?: string;
  format_config?: any;
  registration_start?: string;
  registration_end?: string;
  tournament_start?: string;
  tournament_end?: string;
  max_participants?: number;
  allow_guests: boolean;
  created_at: string;
}

// Import tournament format engine
import { tournamentFormatEngine } from '@/lib/tournament/TournamentFormatEngine';

const TOURNAMENT_FORMATS = [
  // Standard Formats  
  {
    type: 'knockout',
    name: 'K.o.-System (Single Elimination)',
    description: 'Klassisches Ausscheidungsturnier - jeder Spieler scheidet nach der ersten Niederlage aus. Ideal für Turniere mit vielen Teilnehmern. Seeding nach Ranking möglich. Optional: Consolation-Plate für bessere Platzierung der frühzeitig Ausgeschiedenen.',
    config: { seeding: 'random', consolation_plate: false },
    category: 'Standard'
  },
  {
    type: 'round_robin',
    name: 'Round Robin (Jeder gegen jeden)',
    description: 'Jeder Teilnehmer spielt gegen jeden anderen genau einmal. Platzierung nach Matchbilanz, bei Gleichstand entscheiden Satz-/Spielverhältnis oder direkter Vergleich. Perfekt für kleinere Gruppen (4-8 Spieler). Garantiert allen Spielern gleich viele Matches.',
    config: { groups: 1, tiebreaker: ['head_to_head', 'set_ratio'] },
    category: 'Standard'
  },
  {
    type: 'compass',
    name: 'Compass Draw (E/W/N/S)',
    description: 'Vollständige Platzierung aller Teilnehmer durch East/West/North/South-Draws. Nach ersten Niederlagen weitere Brackets für komplette Rangfolge. Besonders fair, da alle Spieler ähnlicher Stärke aufeinandertreffen. Ideal für Vereinsturniere mit präziser Platzierung.',
    config: { main_draw_size: 16, seeding: 'rating' },
    category: 'Standard'
  },
  {
    type: 'groups_to_ko',
    name: 'Gruppen → K.o.',
    description: 'Vorrunde in mehreren Gruppen (Round Robin), danach K.O.-Phase mit den Besten jeder Gruppe. Kombiniert Vorteile beider Systeme: Jeder spielt mehrere Matches, aber trotzdem kompakt. Standard bei WM/EM. Meist 2 Spieler pro Gruppe ins Achtelfinale.',
    config: { groups: 4, group_size: 4, advance_per_group: 2 },
    category: 'Standard'
  },
  {
    type: 'double_elimination',
    name: 'Doppel-K.o. (Double Elimination)',
    description: 'Jeder Spieler muss zweimal verlieren, um auszuscheiden. Winner-Bracket für Ungeschlagene, Loser-Bracket für erste Niederlagen. Finale zwischen beiden Bracket-Siegern. Optional: Bracket Reset falls Loser-Champion das Finale gewinnt. Sehr fair, aber längere Turnierdauer.',
    config: { bracket_reset: true, consolation: false },
    category: 'Standard'
  },
  {
    type: 'swiss',
    name: 'Schweizer System',
    description: 'Paarung nach aktueller Bilanz - Gewinner treffen auf Gewinner, Verlierer auf Verlierer. Kein Ausscheiden, alle spielen alle Runden. Feste Rundenzahl (meist 5-7). Optional: Cut zu K.O.-Phase mit Top-8. Aus dem Schach bekannt, perfekt für große Teilnehmerfelder.',
    config: { rounds: 5, cut_to_ko: false },
    category: 'Standard'
  },
  {
    type: 'qualifying_main_draw',
    name: 'Qualifying + Main Draw',
    description: 'Vorqualifikation für schwächere Spieler, um ins Hauptfeld zu gelangen. Lucky Loser (knapp in Quali Ausgeschiedene) rücken bei Absagen nach. Wie bei ATP/WTA-Turnieren. Qualifying meist Best-of-1, Main Draw Best-of-3. Ideal für große Turniere mit unterschiedlichen Spielstärken.',
    config: { qualifying_spots: 8, lucky_losers: 4 },
    category: 'Standard'
  },

  // Innovative Formats
  {
    type: 'uts',
    name: 'UTS (Universal Tennis Scoring)',
    description: 'Revolutionäres Format von Patrick Mouratoglou: 4 Punkte = 1 Game, bei 3:3 Sudden Death ("Next Point Wins"). Kurze Sätze, schnelle Matches. No-Ad Scoring für Tempo. Perfekt für TV/Streaming mit festen Zeitfenstern. Kombiniert Spannung mit Kalkulierbarkeit.',
    config: { best_of: 3, seeding: 'rating', sets_to_win: 2 },
    category: 'Innovative'
  },

  // Team Formats
  {
    type: 'team_tie',
    name: 'Team Tie (Davis Cup Style)',
    description: 'Teams spielen gegeneinander mit mehreren Einzeln und Doppel. Klassisch: 2 Einzel Tag 1, Doppel Tag 2, 2 Einzel Tag 3. Verschiedene Rubber-Kombinationen möglich. Team gewinnt bei Mehrheit der Matches. Ideal für Vereinswettkämpfe und internationale Duelle.',
    config: { rubbers: ['singles1', 'singles2', 'doubles', 'singles3', 'singles4'] },
    category: 'Team'
  },
  {
    type: 'team_challenge',
    name: 'Team Challenge',
    description: 'Flexible Team-Herausforderungen über eine Saison. Teams können sich gegenseitig herausfordern. Fenster für Annahme der Challenge (meist 14 Tage). Verschiedene Spielmodi möglich. Ideal für Vereinsligen mit flexiblen Terminen. Fördern Eigeninitiative und Aktivität.',
    config: { team_size: 4, challenge_window_days: 14 },
    category: 'Team'
  },

  // Time-based Formats
  {
    type: 'timeboxed',
    name: 'Timeboxed Tournament',
    description: 'Matches mit festem Zeitlimit (z.B. 60min). Bei Zeitende entscheidet aktueller Spielstand oder Sudden Death. Scoring flexibel: Games, Punkte oder Sätze. Perfekt für straffe Zeitpläne und TV-Übertragungen. Planungssicherheit für Spieler und Zuschauer.',
    config: { time_limit_minutes: 60, scoring: 'games', tiebreak_at: 6 },
    category: 'Time-based'
  },
  {
    type: 'timeboxed_doubles',
    name: 'Rotationsdoppel',
    description: 'Doppel mit wechselnden Partnern und fester Zeit pro Runde (z.B. 20min). Alle spielen mit allen zusammen. Individuelle Wertung trotz Teamformat. Geselliges Format mit hoher Interaktion. Ideal für Vereinsfeste und soziale Events. Keine festen Paare erforderlich.',
    config: { round_minutes: 20, partner_rotation: true },
    category: 'Time-based'
  },

  // Fun Formats
  {
    type: 'social_mixer',
    name: 'Social Mixer (Partner Rotation)',
    description: 'Geselliges Doppelformat mit ständig wechselnden Partnern und Gegnern. Meist 6-8 kurze Runden. Jeder spielt mit und gegen (fast) jeden. Punktwertung individuell. Perfekt zum Kennenlernen neuer Spieler. Keine Voranmeldung fester Paare nötig. Entspannte Atmosphäre.',
    config: { rounds: 6, partner_rotation: true },
    category: 'Fun'
  },
  {
    type: 'pro_set',
    name: 'Pro Set Event',
    description: 'Ein einziger langer Satz bis 8 oder 9 Games (mit 2 Games Vorsprung). Bei 8:8 bzw. 9:9 Tiebreak. Schneller als 3-Satz-Match, aber länger als Short Sets. Beliebtes Format für Qualifikationen und Trainingsmatches. Guter Kompromiss zwischen Aussagekraft und Dauer.',
    config: { games_to_win: 8, tiebreak_at: 8, seeding: 'random' },
    category: 'Fun'
  },
  {
    type: 'king_of_court',
    name: 'King/Queen of the Court',
    description: 'Mehrere Plätze in Hierarchie: Hauptplatz = "King Court", dann absteigende Plätze. Gewinner rücken auf, Verlierer ab. Kurze Matches (10-15min). Ständige Bewegung zwischen Plätzen. Ziel: Hauptplatz erobern und halten. Sehr dynamisch und unterhaltsam für Zuschauer.',
    config: { courts: 3, round_minutes: 15 },
    category: 'Fun'
  },
  {
    type: 'fast4_short_sets',
    name: 'Fast4 / Short Sets',
    description: 'Kurze Sätze bis nur 4 Games (statt 6). No-Ad Scoring für Tempo. Bei 3:3 Match-Tiebreak oder Sudden Death. Optional: Match-Tiebreak statt 3. Satz. Von Tennis Australia entwickelt. Perfekt für Jugend, Einsteiger und schnelle Turniere mit vielen Matches.',
    config: { games_per_set: 4, no_ad: true, match_tiebreak: true },
    category: 'Fun'
  },

  // Short Formats
  {
    type: 'tiebreak_series',
    name: 'Tiebreak-Series',
    description: 'Nur Tiebreaks gespielt - kein normales Tennis. Best-of-3 oder Best-of-5 Tiebreaks. Standard-Tiebreak bis 7 oder Match-Tiebreak bis 10. Extrem kurze Matches (10-20min). Maximale Spannung in Minimalzeit. Perfekt für Veranstaltungen mit vielen Teilnehmern und wenig Zeit.',
    config: { tiebreak_type: 'tb7', best_of: 3 },
    category: 'Short'
  },
  {
    type: 'match_tiebreak_ko',
    name: 'Match-Tiebreak K.O.',
    description: 'K.O.-System, aber jedes Match ist nur ein einziger Match-Tiebreak bis 10 (oder mehr bei Gleichstand). Advantage Rule ab 9:9. Kürzestmögliches K.O.-Format. Maximale Unberechenbarkeit. Ideal für große Teilnehmerfelder mit wenig Zeit. Hoher Spaßfaktor durch Spannung.',
    config: { tiebreak_to: 10, advantage_at: 9 },
    category: 'Short'
  },

  // Ladder Systems
  {
    type: 'challenge_ladder',
    name: 'Challenge Ladder',
    description: 'Flexible Ranglistenleiter - Spieler können andere in Reichweite herausfordern (meist 1-3 Plätze höher). Gewinner tauscht Platz mit Verlierer. Zeitfenster für Annahme (7-14 Tage). Limits pro Woche. Läuft über Monate/Saison. Belohnt Aktivität und Ehrgeiz. Kein fester Terminplan.',
    config: { challenge_window_days: 7, max_challenges_per_week: 2 },
    category: 'Ladder'
  },
  {
    type: 'ladder_box_league',
    name: 'Ladder Box Liga',
    description: 'Liga in mehreren "Boxen" (Leistungsklassen) mit je 4-6 Spielern. Inner-Box Round Robin, dann Auf-/Abstieg zwischen Boxen. Box-Sieger steigt auf, Letzte steigen ab. Kombiniert Liga-Charakter mit Leistungsorientierung. Alle spielen gegen ähnlich Starke. Motivation durch Aufstiegsmöglichkeit.',
    config: { boxes: 4, players_per_box: 6, promotion_relegation: true },
    category: 'Ladder'
  },

  // League Formats
  {
    type: 'league',
    name: 'Liga (Medenspiel Style)',
    description: 'Klassische Liga mit festen Teams, festem Spielplan und Hin-/Rückrunde. Termine zentral vergeben, Heim-/Auswärtsspiele. Teams in Tabelle nach Punkten gerankt. Optional: Playoffs um Meisterschaft und Auf-/Abstieg. Deutsche Medenspiele als Vorbild. Vereinsübergreifende Wettkämpfe.',
    config: { double_round_robin: true, home_away: true },
    category: 'League'
  },
  {
    type: 'cup_competition',
    name: 'Pokal-Wettbewerb',
    description: 'K.O.-Pokal mit Hin- und Rückspiel pro Runde (bis Finale). Auswärtstorregel optional für Gleichstand. Bei Gesamt-Gleichstand: Extra Time oder Penalty Shootout. Erlaubt spannende Aufholjagden. Teams können schlechte Hinspiele ausgleichen. Bekannt aus Fußball, adaptiert für Tennis.',
    config: { legs: 2, away_goals_rule: false },
    category: 'League'
  }
];

interface TournamentManagementProps {
  onTournamentSelect?: (tournamentId: string, tournamentName: string) => void;
}

export function TournamentManagement({ onTournamentSelect }: TournamentManagementProps) {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [selectedTournament, setSelectedTournament] = useState<Tournament | null>(null);
  const [selectedFormat, setSelectedFormat] = useState<typeof TOURNAMENT_FORMATS[0] | null>(null);
  const [isFormatConfigOpen, setIsFormatConfigOpen] = useState(false);
  const [formatConfigToEdit, setFormatConfigToEdit] = useState<any>(null);
  const [currentStep, setCurrentStep] = useState<'basic' | 'format' | 'config' | 'confirm'>('basic');
  const [tournamentBasicData, setTournamentBasicData] = useState<any>(null);
  const queryClient = useQueryClient();
  const { currentClubId } = useClub();

  // Fetch tournaments with all columns that exist
  const { data: tournaments, isLoading } = useQuery({
    queryKey: ['tournaments'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('tournaments')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      return data as Tournament[];
    }
  });

  // Create tournament mutation
  const createTournamentMutation = useMutation({
    mutationFn: async (tournamentData: Partial<Tournament>) => {
      if (!currentClubId) {
        throw new Error('Kein Club ausgewählt');
      }

      // Merge format config with time config if available
      let finalFormatConfig = selectedFormat?.config || {};
      if (formatConfigToEdit) {
        finalFormatConfig = { ...finalFormatConfig, ...formatConfigToEdit };
      }

      const { data, error } = await supabase
        .from('tournaments')
        .insert([{
          name: tournamentData.name!,
          description: tournamentData.description,
          status: tournamentData.status || 'registration',
          format_type: selectedFormat?.type || 'knockout',
          format_config: finalFormatConfig,
          registration_start: tournamentData.registration_start,
          registration_end: tournamentData.registration_end,
          tournament_start: tournamentData.tournament_start,
          tournament_end: tournamentData.tournament_end,
          max_participants: tournamentData.max_participants,
          allow_guests: tournamentData.allow_guests ?? true,
          club_id: currentClubId
        }])
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tournaments'] });
      toast.success('Turnier erfolgreich erstellt');
      setIsCreateDialogOpen(false);
      setSelectedFormat(null);
      setFormatConfigToEdit(null);
      setCurrentStep('basic');
      setTournamentBasicData(null);
    },
    onError: (error) => {
      toast.error(`Fehler beim Erstellen: ${error.message}`);
    }
  });

  // Update tournament mutation
  const updateTournamentMutation = useMutation({
    mutationFn: async ({ id, ...updates }: Partial<Tournament> & { id: string }) => {
      const { data, error } = await supabase
        .from('tournaments')
        .update(updates)
        .eq('id', id)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tournaments'] });
      toast.success('Turnier erfolgreich aktualisiert');
      setSelectedTournament(null);
    },
    onError: (error) => {
      toast.error(`Fehler beim Aktualisieren: ${error.message}`);
    }
  });

  // Delete tournament mutation
  const deleteTournamentMutation = useMutation({
    mutationFn: async (tournamentId: string) => {
      const { error } = await supabase
        .from('tournaments')
        .delete()
        .eq('id', tournamentId);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tournaments'] });
      toast.success('Turnier erfolgreich gelöscht');
      setSelectedTournament(null);
    },
    onError: (error) => {
      toast.error(`Fehler beim Löschen: ${error.message}`);
    }
  });

  const getStatusBadge = (status: string) => {
    const variants = {
      'registration': 'secondary' as const,
      'active': 'default' as const,
      'completed': 'outline' as const,
      'cancelled': 'destructive' as const
    };
    
    const labels = {
      'registration': 'Anmeldung',
      'active': 'Aktiv',
      'completed': 'Abgeschlossen',
      'cancelled': 'Abgesagt'
    };

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'secondary'}>
        {labels[status as keyof typeof labels] || status}
      </Badge>
    );
  };

  const getFormatDisplay = (formatType?: string) => {
    const format = TOURNAMENT_FORMATS.find(f => f.type === formatType);
    return format ? format.name : formatType || 'Standard';
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-muted rounded w-1/3"></div>
          <div className="h-64 bg-muted rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Create Button */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-foreground">Turnier-Übersicht</h2>
          <p className="text-muted-foreground">
            Verwalten Sie Turniere mit verschiedenen Formaten und Konfigurationen
          </p>
        </div>
        
        <Dialog open={isCreateDialogOpen} onOpenChange={(open) => {
          setIsCreateDialogOpen(open);
          if (!open) {
            setSelectedFormat(null);
            setFormatConfigToEdit(null);
            setCurrentStep('basic');
            setTournamentBasicData(null);
          }
        }}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Neues Turnier
            </Button>
          </DialogTrigger>
        </Dialog>
      </div>

      {/* Tournaments Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Trophy className="h-5 w-5" />
            Turniere
          </CardTitle>
          <CardDescription>
            Detaillierte Übersicht aller Turniere mit Status, Teilnehmern und Zeitplänen
          </CardDescription>
        </CardHeader>
        <CardContent>
          {tournaments && tournaments.length > 0 ? (
            <div className="space-y-6">
              {tournaments.map((tournament) => (
                <Card key={tournament.id} className="border-l-4 border-l-primary">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-xl font-semibold">{tournament.name}</h3>
                          {getStatusBadge(tournament.status)}
                        </div>
                        
                        {tournament.description && (
                          <p className="text-muted-foreground mb-3">{tournament.description}</p>
                        )}
                        
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <span className="font-medium text-muted-foreground">Format:</span>
                            <div className="font-medium">{getFormatDisplay(tournament.format_type)}</div>
                          </div>
                          
                          <div>
                            <span className="font-medium text-muted-foreground">Max. Teilnehmer:</span>
                            <div className="font-medium">{tournament.max_participants || 'Unbegrenzt'}</div>
                          </div>
                          
                          <div>
                            <span className="font-medium text-muted-foreground">Gäste erlaubt:</span>
                            <div className="font-medium">{tournament.allow_guests ? 'Ja' : 'Nein'}</div>
                          </div>
                          
                          <div>
                            <span className="font-medium text-muted-foreground">Erstellt am:</span>
                            <div className="font-medium">
                              {new Date(tournament.created_at).toLocaleDateString('de-DE')}
                            </div>
                          </div>
                        </div>
                        
                        {/* Tournament Dates */}
                        {(tournament.registration_start || tournament.registration_end || tournament.tournament_start || tournament.tournament_end) && (
                          <div className="mt-4 pt-4 border-t">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                              {tournament.registration_start && (
                                <div className="flex items-center gap-2">
                                  <Clock className="h-4 w-4 text-muted-foreground" />
                                  <span className="font-medium text-muted-foreground">Anmeldung ab:</span>
                                  <span className="font-medium">
                                    {new Date(tournament.registration_start).toLocaleDateString('de-DE')}
                                  </span>
                                </div>
                              )}
                              
                              {tournament.registration_end && (
                                <div className="flex items-center gap-2">
                                  <Clock className="h-4 w-4 text-muted-foreground" />
                                  <span className="font-medium text-muted-foreground">Anmeldung bis:</span>
                                  <span className="font-medium">
                                    {new Date(tournament.registration_end).toLocaleDateString('de-DE')}
                                  </span>
                                </div>
                              )}
                              
                              {tournament.tournament_start && (
                                <div className="flex items-center gap-2">
                                  <Trophy className="h-4 w-4 text-muted-foreground" />
                                  <span className="font-medium text-muted-foreground">Turnier Start:</span>
                                  <span className="font-medium">
                                    {new Date(tournament.tournament_start).toLocaleDateString('de-DE')}
                                  </span>
                                </div>
                              )}
                              
                              {tournament.tournament_end && (
                                <div className="flex items-center gap-2">
                                  <Trophy className="h-4 w-4 text-muted-foreground" />
                                  <span className="font-medium text-muted-foreground">Turnier Ende:</span>
                                  <span className="font-medium">
                                    {new Date(tournament.tournament_end).toLocaleDateString('de-DE')}
                                  </span>
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                      
                      <div className="flex flex-col gap-2 ml-4">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSelectedTournament(tournament)}
                        >
                          <Settings className="h-4 w-4 mr-2" />
                          Verwalten
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            onTournamentSelect?.(tournament.id, tournament.name);
                          }}
                        >
                          <Calendar className="h-4 w-4 mr-2" />
                          Kalender
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => window.open(`/tournament/${tournament.id}`, '_blank')}
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          Öffentlich
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => {
                            if (confirm(`Möchten Sie das Turnier "${tournament.name}" wirklich löschen? Diese Aktion kann nicht rückgängig gemacht werden.`)) {
                              deleteTournamentMutation.mutate(tournament.id);
                            }
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <Trophy className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">Keine Turniere vorhanden</h3>
              <p className="text-muted-foreground mb-4">
                Erstellen Sie Ihr erstes Turnier mit einem der verfügbaren Formate.
              </p>
              <Button onClick={() => setIsCreateDialogOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Erstes Turnier erstellen
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create Tournament Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={(open) => {
        setIsCreateDialogOpen(open);
        if (!open) {
          setSelectedFormat(null);
          setFormatConfigToEdit(null);
          setCurrentStep('basic');
          setTournamentBasicData(null);
        }
      }}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Neues Turnier erstellen</DialogTitle>
            <DialogDescription>
              {currentStep === 'basic' && 'Geben Sie die grundlegenden Turnierdaten ein.'}
              {currentStep === 'format' && 'Wählen Sie das gewünschte Turnier-Format.'}
              {currentStep === 'config' && 'Konfigurieren Sie die Format-spezifischen Einstellungen.'}
              {currentStep === 'confirm' && 'Überprüfen Sie alle Einstellungen und erstellen Sie das Turnier.'}
            </DialogDescription>
          </DialogHeader>

          {/* Progress Indicator */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-2">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm ${
                currentStep === 'basic' ? 'bg-primary text-primary-foreground' : 
                ['format', 'config', 'confirm'].includes(currentStep) ? 'bg-primary text-primary-foreground' : 'bg-muted'
              }`}>
                1
              </div>
              <span className="text-sm">Basis</span>
            </div>
            <div className="flex-1 h-px bg-border mx-2" />
            <div className="flex items-center space-x-2">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm ${
                currentStep === 'format' ? 'bg-primary text-primary-foreground' : 
                ['config', 'confirm'].includes(currentStep) ? 'bg-primary text-primary-foreground' : 'bg-muted'
              }`}>
                2
              </div>
              <span className="text-sm">Format</span>
            </div>
            <div className="flex-1 h-px bg-border mx-2" />
            <div className="flex items-center space-x-2">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm ${
                currentStep === 'config' ? 'bg-primary text-primary-foreground' : 
                currentStep === 'confirm' ? 'bg-primary text-primary-foreground' : 'bg-muted'
              }`}>
                3
              </div>
              <span className="text-sm">Konfiguration</span>
            </div>
            <div className="flex-1 h-px bg-border mx-2" />
            <div className="flex items-center space-x-2">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm ${
                currentStep === 'confirm' ? 'bg-primary text-primary-foreground' : 'bg-muted'
              }`}>
                4
              </div>
              <span className="text-sm">Bestätigung</span>
            </div>
          </div>
          
          {/* Step Content */}
          {currentStep === 'basic' && (
            <form onSubmit={(e) => {
              e.preventDefault();
              const formData = new FormData(e.currentTarget);
              const basicData = {
                name: formData.get('name') as string,
                description: formData.get('description') as string,
                status: formData.get('status') as Tournament['status'],
                registration_start: formData.get('registration_start') as string || undefined,
                registration_end: formData.get('registration_end') as string || undefined,
                tournament_start: formData.get('tournament_start') as string || undefined,
                tournament_end: formData.get('tournament_end') as string || undefined,
                max_participants: formData.get('max_participants') ? parseInt(formData.get('max_participants') as string) : undefined,
                allow_guests: formData.get('allow_guests') === 'on'
              };
              setTournamentBasicData(basicData);
              setCurrentStep('format');
            }} className="space-y-6">
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Turniername *</Label>
                  <Input id="name" name="name" required />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Select name="status" defaultValue="registration">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="registration">Anmeldung</SelectItem>
                      <SelectItem value="active">Aktiv</SelectItem>
                      <SelectItem value="completed">Abgeschlossen</SelectItem>
                      <SelectItem value="cancelled">Abgesagt</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Beschreibung</Label>
                <Textarea id="description" name="description" rows={3} />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="max_participants">Max. Teilnehmer</Label>
                  <Input id="max_participants" name="max_participants" type="number" min="2" />
                </div>
                <div className="flex items-center space-x-2">
                  <Switch id="allow_guests" name="allow_guests" defaultChecked />
                  <Label htmlFor="allow_guests">Gäste erlauben</Label>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="registration_start">Anmeldung Start</Label>
                  <Input id="registration_start" name="registration_start" type="datetime-local" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="registration_end">Anmeldung Ende</Label>
                  <Input id="registration_end" name="registration_end" type="datetime-local" />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="tournament_start">Turnier Start</Label>
                  <Input id="tournament_start" name="tournament_start" type="datetime-local" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="tournament_end">Turnier Ende</Label>
                  <Input id="tournament_end" name="tournament_end" type="datetime-local" />
                </div>
              </div>

              <div className="flex justify-between pt-6">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsCreateDialogOpen(false)}
                >
                  Abbrechen
                </Button>
                <Button type="submit">
                  Weiter zu Format
                </Button>
              </div>
            </form>
          )}

          {currentStep === 'format' && (
            <div className="space-y-6">
              <h4 className="font-medium">Turnier-Format auswählen</h4>
              
              <Tabs defaultValue="standard" className="w-full">
                <TabsList className="grid grid-cols-6 w-full">
                  <TabsTrigger value="standard">Standard</TabsTrigger>
                  <TabsTrigger value="innovative">Innovative</TabsTrigger>
                  <TabsTrigger value="team">Team</TabsTrigger>
                  <TabsTrigger value="fun">Fun</TabsTrigger>
                  <TabsTrigger value="ladder">Ladder</TabsTrigger>
                  <TabsTrigger value="league">Liga</TabsTrigger>
                </TabsList>

                <TabsContent value="standard" className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {TOURNAMENT_FORMATS.filter(f => f.category === 'Standard').map((format) => (
                      <Card 
                        key={format.type}
                        className={`cursor-pointer transition-all hover:shadow-md ${
                          selectedFormat?.type === format.type 
                            ? 'ring-2 ring-primary ring-offset-2' 
                            : 'hover:border-primary/50'
                        }`}
                        onClick={() => setSelectedFormat(format)}
                      >
                        <CardHeader className="pb-3">
                          <CardTitle className="text-sm">{format.name}</CardTitle>
                        </CardHeader>
                        <CardContent className="pt-0">
                          <p className="text-xs text-muted-foreground mb-3">{format.description}</p>
                          <Button
                            size="sm"
                            variant={selectedFormat?.type === format.type ? "default" : "outline"}
                            className="w-full text-xs"
                            onClick={(e) => {
                              e.stopPropagation();
                              setSelectedFormat(format);
                            }}
                          >
                            {selectedFormat?.type === format.type ? 'Ausgewählt' : 'Auswählen'}
                          </Button>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </TabsContent>

                <TabsContent value="innovative" className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {TOURNAMENT_FORMATS.filter(f => f.category === 'Innovative').map((format) => (
                      <Card 
                        key={format.type}
                        className={`cursor-pointer transition-all hover:shadow-md ${
                          selectedFormat?.type === format.type 
                            ? 'ring-2 ring-primary ring-offset-2' 
                            : 'hover:border-primary/50'
                        }`}
                        onClick={() => setSelectedFormat(format)}
                      >
                        <CardHeader className="pb-3">
                          <CardTitle className="text-sm">{format.name}</CardTitle>
                        </CardHeader>
                        <CardContent className="pt-0">
                          <p className="text-xs text-muted-foreground mb-3">{format.description}</p>
                          <Button
                            size="sm"
                            variant={selectedFormat?.type === format.type ? "default" : "outline"}
                            className="w-full text-xs"
                            onClick={(e) => {
                              e.stopPropagation();
                              setSelectedFormat(format);
                            }}
                          >
                            {selectedFormat?.type === format.type ? 'Ausgewählt' : 'Auswählen'}
                          </Button>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </TabsContent>

                <TabsContent value="team" className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {TOURNAMENT_FORMATS.filter(f => f.category === 'Team').map((format) => (
                      <Card 
                        key={format.type}
                        className={`cursor-pointer transition-all hover:shadow-md ${
                          selectedFormat?.type === format.type 
                            ? 'ring-2 ring-primary ring-offset-2' 
                            : 'hover:border-primary/50'
                        }`}
                        onClick={() => setSelectedFormat(format)}
                      >
                        <CardHeader className="pb-3">
                          <CardTitle className="text-sm">{format.name}</CardTitle>
                        </CardHeader>
                        <CardContent className="pt-0">
                          <p className="text-xs text-muted-foreground mb-3">{format.description}</p>
                          <Button
                            size="sm"
                            variant={selectedFormat?.type === format.type ? "default" : "outline"}
                            className="w-full text-xs"
                            onClick={(e) => {
                              e.stopPropagation();
                              setSelectedFormat(format);
                            }}
                          >
                            {selectedFormat?.type === format.type ? 'Ausgewählt' : 'Auswählen'}
                          </Button>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </TabsContent>

                <TabsContent value="fun" className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {TOURNAMENT_FORMATS.filter(f => ['Fun', 'Time-based'].includes(f.category)).map((format) => (
                      <Card 
                        key={format.type}
                        className={`cursor-pointer transition-all hover:shadow-md ${
                          selectedFormat?.type === format.type 
                            ? 'ring-2 ring-primary ring-offset-2' 
                            : 'hover:border-primary/50'
                        }`}
                        onClick={() => setSelectedFormat(format)}
                      >
                        <CardHeader className="pb-3">
                          <CardTitle className="text-sm">{format.name}</CardTitle>
                        </CardHeader>
                        <CardContent className="pt-0">
                          <p className="text-xs text-muted-foreground mb-3">{format.description}</p>
                          <Button
                            size="sm"
                            variant={selectedFormat?.type === format.type ? "default" : "outline"}
                            className="w-full text-xs"
                            onClick={(e) => {
                              e.stopPropagation();
                              setSelectedFormat(format);
                            }}
                          >
                            {selectedFormat?.type === format.type ? 'Ausgewählt' : 'Auswählen'}
                          </Button>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </TabsContent>

                <TabsContent value="ladder" className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {TOURNAMENT_FORMATS.filter(f => f.category === 'Ladder').map((format) => (
                      <Card 
                        key={format.type}
                        className={`cursor-pointer transition-all hover:shadow-md ${
                          selectedFormat?.type === format.type 
                            ? 'ring-2 ring-primary ring-offset-2' 
                            : 'hover:border-primary/50'
                        }`}
                        onClick={() => setSelectedFormat(format)}
                      >
                        <CardHeader className="pb-3">
                          <CardTitle className="text-sm">{format.name}</CardTitle>
                        </CardHeader>
                        <CardContent className="pt-0">
                          <p className="text-xs text-muted-foreground mb-3">{format.description}</p>
                          <Button
                            size="sm"
                            variant={selectedFormat?.type === format.type ? "default" : "outline"}
                            className="w-full text-xs"
                            onClick={(e) => {
                              e.stopPropagation();
                              setSelectedFormat(format);
                            }}
                          >
                            {selectedFormat?.type === format.type ? 'Ausgewählt' : 'Auswählen'}
                          </Button>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </TabsContent>

                <TabsContent value="league" className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {TOURNAMENT_FORMATS.filter(f => ['League', 'Short'].includes(f.category)).map((format) => (
                      <Card 
                        key={format.type}
                        className={`cursor-pointer transition-all hover:shadow-md ${
                          selectedFormat?.type === format.type 
                            ? 'ring-2 ring-primary ring-offset-2' 
                            : 'hover:border-primary/50'
                        }`}
                        onClick={() => setSelectedFormat(format)}
                      >
                        <CardHeader className="pb-3">
                          <CardTitle className="text-sm">{format.name}</CardTitle>
                        </CardHeader>
                        <CardContent className="pt-0">
                          <p className="text-xs text-muted-foreground mb-3">{format.description}</p>
                          <Button
                            size="sm"
                            variant={selectedFormat?.type === format.type ? "default" : "outline"}
                            className="w-full text-xs"
                            onClick={(e) => {
                              e.stopPropagation();
                              setSelectedFormat(format);
                            }}
                          >
                            {selectedFormat?.type === format.type ? 'Ausgewählt' : 'Auswählen'}
                          </Button>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </TabsContent>
              </Tabs>
              
              {selectedFormat && (
                <div className="p-4 bg-muted rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h5 className="font-medium">Gewähltes Format: {selectedFormat.name}</h5>
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">{selectedFormat.description}</p>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">{selectedFormat.category}</Badge>
                  </div>
                </div>
              )}

              <div className="flex justify-between pt-6">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setCurrentStep('basic')}
                >
                  Zurück
                </Button>
                <Button
                  type="button"
                  disabled={!selectedFormat}
                  onClick={() => setCurrentStep('config')}
                >
                  Weiter zu Konfiguration
                </Button>
              </div>
            </div>
          )}

          {currentStep === 'config' && selectedFormat && (
            <div className="space-y-6">
              <div className="p-4 bg-muted rounded-lg">
                <h5 className="font-medium mb-2">Format: {selectedFormat.name}</h5>
                <p className="text-sm text-muted-foreground">{selectedFormat.description}</p>
              </div>

              <div className="text-center">
                <Button
                  variant="outline"
                  onClick={() => {
                    setFormatConfigToEdit(selectedFormat.config);
                    setIsFormatConfigOpen(true);
                  }}
                >
                  <Settings className="h-4 w-4 mr-2" />
                  Format konfigurieren (optional)
                </Button>
                {formatConfigToEdit && (
                  <p className="text-sm text-muted-foreground mt-2">
                    ✓ Zeitplan und Format-Einstellungen konfiguriert
                  </p>
                )}
              </div>

              <div className="flex justify-between pt-6">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setCurrentStep('format')}
                >
                  Zurück
                </Button>
                <Button
                  type="button"
                  onClick={() => setCurrentStep('confirm')}
                >
                  Weiter zur Bestätigung
                </Button>
              </div>
            </div>
          )}

          {currentStep === 'confirm' && selectedFormat && tournamentBasicData && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Turnier-Details</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div>
                      <span className="font-medium">Name:</span> {tournamentBasicData.name}
                    </div>
                    {tournamentBasicData.description && (
                      <div>
                        <span className="font-medium">Beschreibung:</span> {tournamentBasicData.description}
                      </div>
                    )}
                    <div>
                      <span className="font-medium">Max. Teilnehmer:</span> {tournamentBasicData.max_participants || 'Unbegrenzt'}
                    </div>
                    <div>
                      <span className="font-medium">Gäste erlaubt:</span> {tournamentBasicData.allow_guests ? 'Ja' : 'Nein'}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Format & Konfiguration</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div>
                      <span className="font-medium">Format:</span> {selectedFormat.name}
                    </div>
                    <div>
                      <span className="font-medium">Kategorie:</span> {selectedFormat.category}
                    </div>
                    <div>
                      <span className="font-medium">Konfiguration:</span> {formatConfigToEdit ? 'Angepasst' : 'Standard'}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Time Configuration Display */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Spielzeit-Konfiguration</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {(() => {
                      // Get time config from format config or use defaults
                      const timeConfig = formatConfigToEdit?.time_config;
                      const matchDuration = timeConfig?.match_duration_minutes || 90;
                      const warmupTime = timeConfig?.warmup_time_minutes || 5;
                      const breakTime = timeConfig?.break_time_minutes || 5;
                      const preparationTime = timeConfig?.court_preparation_minutes || 10;
                      const totalTime = matchDuration + warmupTime + breakTime + preparationTime;

                      return (
                        <>
                          <div className="text-center p-3 bg-muted/50 rounded-lg">
                            <div className="text-2xl font-bold text-primary">{matchDuration}</div>
                            <div className="text-sm text-muted-foreground">Spielzeit (Min)</div>
                          </div>
                          <div className="text-center p-3 bg-muted/50 rounded-lg">
                            <div className="text-2xl font-bold text-blue-600">{warmupTime}</div>
                            <div className="text-sm text-muted-foreground">Einspielzeit (Min)</div>
                          </div>
                          <div className="text-center p-3 bg-muted/50 rounded-lg">
                            <div className="text-2xl font-bold text-orange-600">{breakTime}</div>
                            <div className="text-sm text-muted-foreground">Pause (Min)</div>
                          </div>
                          <div className="text-center p-3 bg-muted/50 rounded-lg">
                            <div className="text-2xl font-bold text-green-600">{preparationTime}</div>
                            <div className="text-sm text-muted-foreground">Vorbereitung (Min)</div>
                          </div>
                        </>
                      );
                    })()}
                  </div>
                  <div className="mt-4 p-3 bg-primary/10 rounded-lg border-l-4 border-primary">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">Gesamtzeit pro Spiel:</span>
                      <span className="text-xl font-bold text-primary">
                        {(() => {
                          const timeConfig = formatConfigToEdit?.time_config;
                          const matchDuration = timeConfig?.match_duration_minutes || 90;
                          const warmupTime = timeConfig?.warmup_time_minutes || 5;
                          const breakTime = timeConfig?.break_time_minutes || 5;
                          const preparationTime = timeConfig?.court_preparation_minutes || 10;
                          return matchDuration + warmupTime + breakTime + preparationTime;
                        })()} Minuten
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <div className="flex justify-between pt-6">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setCurrentStep('config')}
                >
                  Zurück
                </Button>
                <Button
                  type="button"
                  disabled={createTournamentMutation.isPending}
                  onClick={() => {
                    createTournamentMutation.mutate(tournamentBasicData);
                  }}
                >
                  {createTournamentMutation.isPending ? 'Erstelle...' : 'Turnier erstellen'}
                </Button>
              </div>

              {/* Tournament Creation Loading Modal */}
              <Dialog open={createTournamentMutation.isPending}>
                <DialogContent className="sm:max-w-md">
                  <div className="flex flex-col items-center space-y-6 py-8">
                    {/* Animated Logo/Icon */}
                    <div className="relative">
                      <div className="w-16 h-16 rounded-full bg-primary/20 flex items-center justify-center animate-pulse">
                        <div className="w-8 h-8 rounded-full bg-primary animate-scale-in"></div>
                      </div>
                      <div className="absolute inset-0 w-16 h-16 rounded-full border-4 border-primary/30 border-t-primary animate-spin"></div>
                    </div>

                    {/* Main Title */}
                    <div className="text-center">
                      <h3 className="text-xl font-semibold mb-2 animate-fade-in">
                        Turnier wird erstellt...
                      </h3>
                      <p className="text-muted-foreground animate-fade-in" style={{ animationDelay: '0.2s' }}>
                        Einen Moment bitte, wir bereiten alles vor
                      </p>
                    </div>

                    {/* Loading Steps */}
                    <div className="w-full space-y-3">
                      {[
                        { text: "Turnier-Konfiguration wird gespeichert", delay: "0.4s" },
                        { text: "Spielzeiten werden berechnet", delay: "0.6s" },
                        { text: "Plätze werden zugewiesen", delay: "0.8s" },
                        { text: "Teilnehmer-Liste wird vorbereitet", delay: "1.0s" }
                      ].map((step, index) => (
                        <div 
                          key={index} 
                          className="flex items-center space-x-3 animate-fade-in"
                          style={{ animationDelay: step.delay }}
                        >
                          <div className="w-2 h-2 rounded-full bg-primary animate-pulse"></div>
                          <span className="text-sm text-muted-foreground">{step.text}</span>
                        </div>
                      ))}
                    </div>

                    {/* Progress Bar */}
                    <div className="w-full bg-muted rounded-full h-2 animate-fade-in" style={{ animationDelay: '1.2s' }}>
                      <div className="bg-primary h-2 rounded-full animate-[width_2s_ease-in-out_infinite_alternate] w-1/3"></div>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Tournament Details Dialog */}
      {selectedTournament && (
        <Dialog open={!!selectedTournament} onOpenChange={() => setSelectedTournament(null)}>
          <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Trophy className="h-5 w-5" />
                {selectedTournament.name}
                <Badge variant="outline">{getFormatDisplay(selectedTournament.format_type)}</Badge>
              </DialogTitle>
              <DialogDescription>
                {selectedTournament.description || 'Keine Beschreibung verfügbar'}
              </DialogDescription>
            </DialogHeader>

            <Tabs defaultValue="participants" className="w-full">
              <TabsList>
                <TabsTrigger value="participants">Teilnehmer</TabsTrigger>
                <TabsTrigger value="courts">Plätze</TabsTrigger>
                <TabsTrigger value="matches">Spiele</TabsTrigger>
                <TabsTrigger value="settings">Einstellungen</TabsTrigger>
              </TabsList>

              <TabsContent value="participants" className="space-y-4">
                <ParticipantManagement
                  tournamentId={selectedTournament.id}
                  maxParticipants={selectedTournament.max_participants}
                  allowGuests={selectedTournament.allow_guests}
                />
              </TabsContent>

              <TabsContent value="courts" className="space-y-4">
                <TournamentCourtSetup 
                  tournamentId={selectedTournament.id}
                />
              </TabsContent>

              <TabsContent value="matches" className="space-y-4">
                <MatchManagement tournamentId={selectedTournament.id} />
              </TabsContent>

              <TabsContent value="settings" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Turnier-Einstellungen</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <form onSubmit={(e) => {
                      e.preventDefault();
                      const formData = new FormData(e.currentTarget);
                      const updates = {
                        name: formData.get('name') as string,
                        description: formData.get('description') as string,
                        registration_start: formData.get('registration_start') ? new Date(formData.get('registration_start') as string).toISOString() : null,
                        registration_end: formData.get('registration_end') ? new Date(formData.get('registration_end') as string).toISOString() : null,
                        tournament_start: formData.get('tournament_start') ? new Date(formData.get('tournament_start') as string).toISOString() : null,
                        tournament_end: formData.get('tournament_end') ? new Date(formData.get('tournament_end') as string).toISOString() : null,
                        max_participants: formData.get('max_participants') ? parseInt(formData.get('max_participants') as string) : null,
                        allow_guests: formData.get('allow_guests') === 'on'
                      };
                      
                      updateTournamentMutation.mutate({ id: selectedTournament!.id, ...updates });
                    }}>
                      
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="edit_name">Turnier-Name</Label>
                          <Input 
                            id="edit_name" 
                            name="name" 
                            defaultValue={selectedTournament.name}
                            required 
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="edit_max_participants">Max. Teilnehmer</Label>
                          <Input 
                            id="edit_max_participants" 
                            name="max_participants" 
                            type="number"
                            defaultValue={selectedTournament.max_participants?.toString()}
                            placeholder="Unbegrenzt"
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="edit_description">Beschreibung</Label>
                        <Textarea 
                          id="edit_description" 
                          name="description"
                          defaultValue={selectedTournament.description}
                          placeholder="Optionale Turnier-Beschreibung"
                        />
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="edit_registration_start">Anmeldung Start</Label>
                          <Input 
                            id="edit_registration_start" 
                            name="registration_start" 
                            type="datetime-local"
                            defaultValue={selectedTournament.registration_start ? 
                              new Date(selectedTournament.registration_start).toISOString().slice(0, 16) : ''
                            }
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="edit_registration_end">Anmeldung Ende</Label>
                          <Input 
                            id="edit_registration_end" 
                            name="registration_end" 
                            type="datetime-local"
                            defaultValue={selectedTournament.registration_end ? 
                              new Date(selectedTournament.registration_end).toISOString().slice(0, 16) : ''
                            }
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="edit_tournament_start">Turnier Start</Label>
                          <Input 
                            id="edit_tournament_start" 
                            name="tournament_start" 
                            type="datetime-local"
                            defaultValue={selectedTournament.tournament_start ? 
                              new Date(selectedTournament.tournament_start).toISOString().slice(0, 16) : ''
                            }
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="edit_tournament_end">Turnier Ende</Label>
                          <Input 
                            id="edit_tournament_end" 
                            name="tournament_end" 
                            type="datetime-local"
                            defaultValue={selectedTournament.tournament_end ? 
                              new Date(selectedTournament.tournament_end).toISOString().slice(0, 16) : ''
                            }
                          />
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Switch 
                          id="edit_allow_guests" 
                          name="allow_guests"
                          defaultChecked={selectedTournament.allow_guests}
                        />
                        <Label htmlFor="edit_allow_guests">Gäste erlauben</Label>
                      </div>

                      <div className="space-y-2">
                        <Label className="text-sm font-medium">Format & Status (nicht editierbar)</Label>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-sm text-muted-foreground">
                              {getFormatDisplay(selectedTournament.format_type)}
                            </p>
                          </div>
                          <div>
                            {getStatusBadge(selectedTournament.status)}
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex gap-2 pt-4">
                        <Button 
                          type="submit" 
                          disabled={updateTournamentMutation.isPending}
                        >
                          {updateTournamentMutation.isPending ? 'Speichere...' : 'Änderungen speichern'}
                        </Button>
                      </div>
                    </form>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </DialogContent>
        </Dialog>
      )}

      {/* Format Configuration Dialog */}
      {selectedFormat && (
        <FormatConfigDialog
          format={{
            format_type: selectedFormat.type,
            name: selectedFormat.name,
            description: selectedFormat.description,
            default_config: selectedFormat.config,
            default_time_config: {
              match_duration_minutes: 90,
              warmup_minutes: 5,
              changeover_minutes: 3,
              set_break_minutes: 2,
              between_match_buffer_minutes: 10,
              court_preparation_minutes: 5
            },
            category: selectedFormat.category
          }}
          isOpen={isFormatConfigOpen}
          onClose={() => setIsFormatConfigOpen(false)}
          onSave={(config) => {
            setFormatConfigToEdit(config);
            setIsFormatConfigOpen(false);
            toast.success('Format-Konfiguration gespeichert');
          }}
          initialConfig={formatConfigToEdit}
        />
      )}
    </div>
  );
}