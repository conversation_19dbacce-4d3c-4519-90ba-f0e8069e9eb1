import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Mail, TestTube, CheckCircle, AlertCircle } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { useMultiTenant } from "@/services/MultiTenantService";
import { supabase } from "@/integrations/supabase/client";

const SMTP_PROVIDERS = {
  gmail: {
    name: "Gmail / Google Workspace",
    host: "smtp.gmail.com",
    port: 587,
    secure: true,
    helpText: "Verwende dein Gmail-Passwort oder App-spezifisches Passwort bei 2FA"
  },
  outlook: {
    name: "Outlook / Office 365",
    host: "smtp-mail.outlook.com", 
    port: 587,
    secure: true,
    helpText: "Verwende deine Outlook.com oder Office 365 Anmeldedaten"
  },
  ionos: {
    name: "1&1 IONOS",
    host: "smtp.ionos.de",
    port: 587,
    secure: true,
    helpText: "SMTP-Einstellungen von deinem IONOS Control Panel"
  },
  strato: {
    name: "Strato",
    host: "smtp.strato.de",
    port: 465,
    secure: true,
    helpText: "Verwende deine Strato E-Mail Anmeldedaten"
  },
  custom: {
    name: "Benutzerdefiniert",
    host: "",
    port: 587,
    secure: true,
    helpText: "Gib deine eigenen SMTP-Einstellungen ein"
  }
};

export const SmtpConfiguration = () => {
  const { toast } = useToast();
  const { currentClub } = useMultiTenant();
  const [isEnabled, setIsEnabled] = useState(false);
  const [selectedProvider, setSelectedProvider] = useState<string>("");
  const [smtpConfig, setSmtpConfig] = useState({
    host: "",
    port: 587,
    secure: true,
    user: "",
    password: "",
    fromName: "",
    fromEmail: "",
    replyTo: ""
  });
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [testResult, setTestResult] = useState<"success" | "error" | null>(null);
  const [testEmail, setTestEmail] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  // Load SMTP settings from database
  useEffect(() => {
    const loadSmtpSettings = async () => {
      if (!currentClub?.id) return;
      
      try {
        const { data, error } = await supabase
          .from('clubs')
          .select('settings')
          .eq('id', currentClub.id)
          .single();

        if (error) throw error;

        const smtpSettings = (data?.settings as any)?.smtp;
        if (smtpSettings) {
          setIsEnabled(smtpSettings.enabled || false);
          setSelectedProvider(smtpSettings.provider || "");
          setSmtpConfig({
            host: smtpSettings.host || "",
            port: smtpSettings.port || 587,
            secure: smtpSettings.secure ?? true,
            user: smtpSettings.user || "",
            password: smtpSettings.password || "",
            fromName: smtpSettings.fromName || "",
            fromEmail: smtpSettings.fromEmail || "",
            replyTo: smtpSettings.replyTo || ""
          });
        }
      } catch (error) {
        console.error('Error loading SMTP settings:', error);
        toast({
          title: "Fehler beim Laden",
          description: "SMTP-Einstellungen konnten nicht geladen werden.",
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadSmtpSettings();
  }, [currentClub?.id, toast]);

  const handleProviderChange = (provider: string) => {
    setSelectedProvider(provider);
    if (provider && SMTP_PROVIDERS[provider as keyof typeof SMTP_PROVIDERS]) {
      const config = SMTP_PROVIDERS[provider as keyof typeof SMTP_PROVIDERS];
      setSmtpConfig(prev => ({
        ...prev,
        host: config.host,
        port: config.port,
        secure: config.secure
      }));
    }
  };

  const handleTestConnection = async () => {
    if (!testEmail || !testEmail.includes('@')) {
      toast({
        title: "Ungültige E-Mail",
        description: "Bitte geben Sie eine gültige Test-E-Mail-Adresse ein.",
        variant: "destructive",
      });
      return;
    }

    if (!smtpConfig.host || !smtpConfig.port || !smtpConfig.user || !smtpConfig.password) {
      toast({
        title: "Unvollständige Konfiguration",
        description: "Bitte füllen Sie alle SMTP-Felder aus.",
        variant: "destructive",
      });
      return;
    }

    setIsTestingConnection(true);
    setTestResult(null);
    
    try {
      const { data, error } = await supabase.functions.invoke('smtp-test', {
        body: {
          smtpConfig: {
            host: smtpConfig.host,
            port: smtpConfig.port,
            username: smtpConfig.user,
            password: smtpConfig.password,
            secure: smtpConfig.secure,
            senderName: smtpConfig.fromName,
            senderEmail: smtpConfig.fromEmail
          },
          testEmail,
          clubName: currentClub?.name || 'Unbekannter Verein'
        }
      });

      if (error) throw error;

      if (data.success) {
        setTestResult("success");
        toast({
          title: "Test erfolgreich",
          description: data.message,
        });
      } else {
        setTestResult("error");
        toast({
          title: "Test fehlgeschlagen",
          description: data.message,
          variant: "destructive",
        });
      }
    } catch (error: any) {
      setTestResult("error");
      toast({
        title: "Fehler",
        description: error.message || 'Unbekannter Fehler beim Testen',
        variant: "destructive",
      });
    } finally {
      setIsTestingConnection(false);
    }
  };

  const handleSave = async () => {
    if (!currentClub?.id) {
      toast({
        title: "Fehler",
        description: "Kein Club-Kontext verfügbar.",
        variant: "destructive"
      });
      return;
    }

    setIsSaving(true);
    try {
      const smtpSettings = {
        enabled: isEnabled,
        provider: selectedProvider,
        host: smtpConfig.host,
        port: smtpConfig.port,
        secure: smtpConfig.secure,
        user: smtpConfig.user,
        password: smtpConfig.password,
        fromName: smtpConfig.fromName,
        fromEmail: smtpConfig.fromEmail,
        replyTo: smtpConfig.replyTo
      };

      const { error } = await supabase
        .from('clubs')
        .update({
          settings: {
            ...(currentClub.settings as any),
            smtp: smtpSettings
          }
        })
        .eq('id', currentClub.id);

      if (error) throw error;

      toast({
        title: "E-Mail Einstellungen gespeichert",
        description: "SMTP-Konfiguration wurde erfolgreich gespeichert.",
      });
    } catch (error) {
      console.error('Error saving SMTP settings:', error);
      toast({
        title: "Fehler beim Speichern",
        description: "SMTP-Einstellungen konnten nicht gespeichert werden.",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleReset = async () => {
    if (!currentClub?.id) return;

    try {
      const { error } = await supabase
        .from('clubs')
        .update({
          settings: {
            ...(currentClub.settings as any),
            smtp: {
              enabled: false,
              provider: "",
              host: "",
              port: 587,
              secure: true,
              user: "",
              password: "",
              fromName: "",
              fromEmail: "",
              replyTo: ""
            }
          }
        })
        .eq('id', currentClub.id);

      if (error) throw error;

      setIsEnabled(false);
      setSelectedProvider("");
      setSmtpConfig({
        host: "",
        port: 587,
        secure: true,
        user: "",
        password: "",
        fromName: "",
        fromEmail: "",
        replyTo: ""
      });
      setTestResult(null);
      
      toast({
        title: "Einstellungen zurückgesetzt",
        description: "SMTP-Konfiguration wurde gelöscht.",
      });
    } catch (error) {
      console.error('Error resetting SMTP settings:', error);
      toast({
        title: "Fehler beim Zurücksetzen",
        description: "SMTP-Einstellungen konnten nicht zurückgesetzt werden.",
        variant: "destructive"
      });
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="p-6">
            <div className="text-center">Lade SMTP-Einstellungen...</div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            E-Mail Konfiguration
          </CardTitle>
          <CardDescription>
            Konfiguriere SMTP für ausgehende E-Mails vom Verein
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Enable SMTP */}
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Vereinseigene E-Mail verwenden</Label>
              <p className="text-sm text-muted-foreground">
                E-Mails werden von deiner eigenen Domain versendet
              </p>
            </div>
            <Switch 
              checked={isEnabled} 
              onCheckedChange={setIsEnabled}
            />
          </div>

          {isEnabled && (
            <>
              <Separator />
              
              {/* Provider Selection */}
              <div className="space-y-2">
                <Label htmlFor="smtp-provider">E-Mail Anbieter</Label>
                <Select value={selectedProvider} onValueChange={handleProviderChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Wähle deinen E-Mail Anbieter" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(SMTP_PROVIDERS).map(([key, provider]) => (
                      <SelectItem key={key} value={key}>
                        {provider.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {selectedProvider && SMTP_PROVIDERS[selectedProvider as keyof typeof SMTP_PROVIDERS] && (
                  <p className="text-sm text-muted-foreground">
                    {SMTP_PROVIDERS[selectedProvider as keyof typeof SMTP_PROVIDERS].helpText}
                  </p>
                )}
              </div>

              {/* SMTP Settings */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="smtp-host">SMTP Server</Label>
                  <Input 
                    id="smtp-host"
                    value={smtpConfig.host}
                    onChange={(e) => setSmtpConfig(prev => ({ ...prev, host: e.target.value }))}
                    placeholder="smtp.example.com"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="smtp-port">Port</Label>
                  <Input 
                    id="smtp-port"
                    type="number"
                    value={smtpConfig.port}
                    onChange={(e) => setSmtpConfig(prev => ({ ...prev, port: parseInt(e.target.value) }))}
                  />
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Sichere Verbindung (TLS/SSL)</Label>
                  <p className="text-sm text-muted-foreground">
                    Empfohlen für die meisten Anbieter
                  </p>
                </div>
                <Switch 
                  checked={smtpConfig.secure} 
                  onCheckedChange={(checked) => setSmtpConfig(prev => ({ ...prev, secure: checked }))}
                />
              </div>

              <Separator />

              {/* Authentication */}
              <div className="space-y-4">
                <h4 className="font-medium">Anmeldedaten</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="smtp-user">Benutzername / E-Mail</Label>
                    <Input 
                      id="smtp-user"
                      type="email"
                      value={smtpConfig.user}
                      onChange={(e) => setSmtpConfig(prev => ({ ...prev, user: e.target.value }))}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="smtp-password">Passwort</Label>
                    <Input 
                      id="smtp-password"
                      type="password"
                      value={smtpConfig.password}
                      onChange={(e) => setSmtpConfig(prev => ({ ...prev, password: e.target.value }))}
                      placeholder="••••••••"
                    />
                  </div>
                </div>
              </div>

              <Separator />

              {/* Sender Configuration */}
              <div className="space-y-4">
                <h4 className="font-medium">Absender-Konfiguration</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="from-name">Absender Name</Label>
                    <Input 
                      id="from-name"
                      value={smtpConfig.fromName}
                      onChange={(e) => setSmtpConfig(prev => ({ ...prev, fromName: e.target.value }))}
                      placeholder="Tennis Club München"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="from-email">Absender E-Mail</Label>
                    <Input 
                      id="from-email"
                      type="email"
                      value={smtpConfig.fromEmail}
                      onChange={(e) => setSmtpConfig(prev => ({ ...prev, fromEmail: e.target.value }))}
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="reply-to">Antwort-Adresse (optional)</Label>
                  <Input 
                    id="reply-to"
                    type="email"
                    value={smtpConfig.replyTo}
                    onChange={(e) => setSmtpConfig(prev => ({ ...prev, replyTo: e.target.value }))}
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              <Separator />

               {/* Test Connection */}
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium">Test-E-Mail senden</h4>
                  <p className="text-sm text-muted-foreground">
                    Teste die SMTP-Verbindung mit einer echten Test-E-Mail
                  </p>
                </div>
                
                <div className="space-y-3">
                  <div className="space-y-2">
                    <Label htmlFor="test-email">Test-E-Mail-Adresse</Label>
                    <Input
                      id="test-email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={testEmail}
                      onChange={(e) => setTestEmail(e.target.value)}
                      disabled={!isEnabled}
                    />
                  </div>
                  
                  <Button 
                    variant="outline" 
                    onClick={handleTestConnection}
                    disabled={isTestingConnection || !isEnabled || !testEmail || !smtpConfig.host || !smtpConfig.user}
                    className="w-full"
                  >
                    <TestTube className="mr-2 h-4 w-4" />
                    {isTestingConnection ? "Sende Test-E-Mail..." : "Test-E-Mail senden"}
                  </Button>
                </div>
                
                {testResult && (
                  <div className={`flex items-center gap-2 p-3 rounded-md ${
                    testResult === "success" 
                      ? "bg-green-50 text-green-700 border border-green-200" 
                      : "bg-red-50 text-red-700 border border-red-200"
                  }`}>
                    {testResult === "success" ? (
                      <CheckCircle className="h-4 w-4" />
                    ) : (
                      <AlertCircle className="h-4 w-4" />
                    )}
                    <span className="text-sm">
                      {testResult === "success" 
                        ? "SMTP-Verbindung erfolgreich getestet!" 
                        : "SMTP-Verbindung fehlgeschlagen. Überprüfe deine Einstellungen."}
                    </span>
                  </div>
                )}
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Save Button */}
      <div className="flex justify-between">
        <Button 
          variant="outline" 
          onClick={handleReset}
          disabled={!isEnabled && !selectedProvider && !smtpConfig.host}
        >
          Einstellungen löschen
        </Button>
        <Button onClick={handleSave} disabled={!isEnabled || isSaving}>
          <Mail className="mr-2 h-4 w-4" />
          {isSaving ? "Speichere..." : "E-Mail Einstellungen speichern"}
        </Button>
      </div>
    </div>
  );
};