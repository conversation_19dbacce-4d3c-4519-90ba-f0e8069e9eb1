import React, { createContext, useContext, useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';

export type ThemeVariant = 'klassik' | 'courtwaive-ai' | 'corporate-blue' | 'elegance' | 'nature' | 'custom';
export type ThemeCustomVariant = 'klassik-custom' | 'courtwaive-ai-custom' | 'corporate-blue-custom' | 'elegance-custom' | 'nature-custom' | 'custom';

export interface ThemeSettings {
  variant: ThemeVariant | ThemeCustomVariant;
  baseTheme?: ThemeVariant; // The original theme this is based on
  customColors?: {
    primary?: string;
    secondary?: string;
    accent?: string;
    background?: string;
    foreground?: string;
  };
  customLogo?: string;
  customFont?: string;
  animationStyle?: 'default' | 'smooth' | 'bouncy' | 'minimal';
  [key: string]: any; // Allow additional properties for JSON compatibility
}

interface ThemeContextType {
  theme: ThemeSettings;
  setTheme: (theme: ThemeSettings) => Promise<void>;
  createCustomVariant: (baseTheme: ThemeVariant, customizations: Partial<ThemeSettings>) => Promise<void>;
  isLoading: boolean;
  presetThemes: Record<ThemeVariant, ThemeSettings>;
}

const defaultTheme: ThemeSettings = {
  variant: 'klassik'
};

const presetThemes: Record<ThemeVariant, ThemeSettings> = {
  klassik: {
    variant: 'klassik'
  },
  'courtwaive-ai': {
    variant: 'courtwaive-ai'
  },
  'corporate-blue': {
    variant: 'corporate-blue'
  },
  elegance: {
    variant: 'elegance'
  },
  nature: {
    variant: 'nature'
  },
  custom: {
    variant: 'custom'
  }
};

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [theme, setThemeState] = useState<ThemeSettings>(defaultTheme);
  const [isLoading, setIsLoading] = useState(true);
  const { user } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    loadTheme();
  }, [user]);

  useEffect(() => {
    applyTheme(theme);
  }, [theme]);

  const loadTheme = async () => {
    if (!user) {
      setIsLoading(false);
      return;
    }

    try {
      // Get user's club
      const { data: membership } = await supabase
        .from('club_memberships')
        .select('club_id')
        .eq('user_id', user.id)
        .eq('is_active', true)
        .single();

      if (membership?.club_id) {
        // Load club theme settings
        const { data: club } = await supabase
          .from('clubs')
          .select('settings')
          .eq('id', membership.club_id)
          .single();

        const settings = club?.settings as any;
        const themeSettings = settings?.theme as ThemeSettings;
        if (themeSettings) {
          console.log('🎨 Loading theme from database:', themeSettings);
          setThemeState(themeSettings);
        } else {
          console.log('🎨 No theme settings found, using default');
        }
      }
    } catch (error) {
      console.error('Error loading theme:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const setTheme = async (newTheme: ThemeSettings) => {
    if (!user) return;

    try {
      // Get user's club
      const { data: membership } = await supabase
        .from('club_memberships')
        .select('club_id')
        .eq('user_id', user.id)
        .eq('is_active', true)
        .single();

      if (membership?.club_id) {
        // Get current settings first
        const { data: currentClub } = await supabase
          .from('clubs')
          .select('settings')
          .eq('id', membership.club_id)
          .single();

        const currentSettings = (currentClub?.settings as any) || {};
        
        // Update club theme settings
        const { error } = await supabase
          .from('clubs')
          .update({
            settings: {
              ...currentSettings,
              theme: newTheme
            } as any
          })
          .eq('id', membership.club_id);

        if (error) {
          throw error;
        }

        setThemeState(newTheme);
        toast({
          title: "Design gespeichert",
          description: "Die Designeinstellungen wurden erfolgreich aktualisiert."
        });
      }
    } catch (error) {
      console.error('Error saving theme:', error);
      toast({
        title: "Fehler",
        description: "Design konnte nicht gespeichert werden.",
        variant: "destructive"
      });
    }
  };

  const createCustomVariant = async (baseTheme: ThemeVariant, customizations: Partial<ThemeSettings>) => {
    const customTheme: ThemeSettings = {
      ...presetThemes[baseTheme],
      variant: `${baseTheme}-custom` as ThemeCustomVariant,
      baseTheme,
      ...customizations
    };
    await setTheme(customTheme);
  };

  const applyTheme = (theme: ThemeSettings) => {
    const root = document.documentElement;
    
    console.log('🎨 Applying theme:', theme.variant, 'Full theme object:', theme);
    
    // Remove existing theme classes
    root.className = root.className.replace(/theme-[a-z-]+/g, '');
    
    // Apply base theme or variant theme
    const baseThemeClass = theme.baseTheme || theme.variant;
    const themeClassName = `theme-${baseThemeClass}`;
    
    console.log('🎨 Adding CSS class:', themeClassName);
    root.classList.add(themeClassName);
    
    // Force refresh by toggling body class
    document.body.classList.remove('theme-applied');
    setTimeout(() => {
      document.body.classList.add('theme-applied');
    }, 10);
    
    // If it's a custom variant, also add the custom class
    if (theme.variant.includes('-custom')) {
      root.classList.add(`theme-${theme.variant}`);
    }

    console.log('🎨 Current root classes:', root.className);

    // Apply custom colors if available
    if (theme.customColors) {
      if (theme.customColors.primary) {
        root.style.setProperty('--primary', theme.customColors.primary);
      }
      if (theme.customColors.secondary) {
        root.style.setProperty('--secondary', theme.customColors.secondary);
      }
      if (theme.customColors.accent) {
        root.style.setProperty('--accent', theme.customColors.accent);
      }
      if (theme.customColors.background) {
        root.style.setProperty('--background', theme.customColors.background);
      }
      if (theme.customColors.foreground) {
        root.style.setProperty('--foreground', theme.customColors.foreground);
      }
    }

    // Apply animation style
    if (theme.animationStyle) {
      root.setAttribute('data-animation-style', theme.animationStyle);
    } else {
      root.removeAttribute('data-animation-style');
    }
  };

  return (
    <ThemeContext.Provider value={{ theme, setTheme, createCustomVariant, isLoading, presetThemes }}>
      {children}
    </ThemeContext.Provider>
  );
};