import React, { createContext, useContext, useState, useEffect } from 'react';
import { User } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';

interface Account {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  account_type: string;
  is_primary: boolean;
  title?: string;
  phone?: string;
  birth_date?: string;
}

interface AccountContextType {
  accounts: Account[];
  activeAccount: Account | null;
  switchAccount: (accountId: string) => void;
  isLoading: boolean;
  refreshAccounts: () => Promise<void>;
}

const AccountContext = createContext<AccountContextType | null>(null);

interface AccountProviderProps {
  children: React.ReactNode;
  user: User | null;
}

export const AccountProvider = ({ children, user }: AccountProviderProps) => {
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [activeAccount, setActiveAccount] = useState<Account | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const fetchAccounts = async () => {
    if (!user) {
      setAccounts([]);
      setActiveAccount(null);
      setIsLoading(false);
      return;
    }

    try {
      const { data, error } = await supabase
        .from('accounts')
        .select('*')
        .eq('user_id', user.id)
        .order('is_primary', { ascending: false })
        .order('created_at', { ascending: true });

      if (error) throw error;

      setAccounts(data || []);
      
      // Set active account to primary account or first available
      const primaryAccount = data?.find(acc => acc.is_primary) || data?.[0];
      if (primaryAccount && !activeAccount) {
        setActiveAccount(primaryAccount);
      }
    } catch (error) {
      console.error('Error fetching accounts:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const switchAccount = (accountId: string) => {
    const account = accounts.find(acc => acc.id === accountId);
    if (account) {
      setActiveAccount(account);
      // Store preference in localStorage
      localStorage.setItem('activeAccountId', accountId);
    }
  };

  const refreshAccounts = async () => {
    setIsLoading(true);
    await fetchAccounts();
  };

  useEffect(() => {
    fetchAccounts();
  }, [user?.id]);

  // Restore active account from localStorage
  useEffect(() => {
    const storedAccountId = localStorage.getItem('activeAccountId');
    if (storedAccountId && accounts.length > 0) {
      const storedAccount = accounts.find(acc => acc.id === storedAccountId);
      if (storedAccount) {
        setActiveAccount(storedAccount);
      }
    }
  }, [accounts]);

  return (
    <AccountContext.Provider
      value={{
        accounts,
        activeAccount,
        switchAccount,
        isLoading,
        refreshAccounts,
      }}
    >
      {children}
    </AccountContext.Provider>
  );
};

export const useAccount = () => {
  const context = useContext(AccountContext);
  if (!context) {
    throw new Error('useAccount must be used within an AccountProvider');
  }
  return context;
};