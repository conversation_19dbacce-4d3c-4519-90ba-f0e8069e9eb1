import React, { createContext, useContext, useEffect, useState } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';

interface AuthContextType {
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  isAdmin: boolean;
  signIn: (email: string, password: string) => Promise<{ error: any }>;
  signUp: (email: string, password: string) => Promise<{ error: any }>;
  signOut: () => Promise<void>;
  checkAdminRole: () => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [authStateTimeout, setAuthStateTimeout] = useState<NodeJS.Timeout | null>(null);

  const checkAdminRole = async (): Promise<boolean> => {
    if (!user) return false;
    
    try {
      // Use the new unified function that checks both regular admin and meta-admin roles
      const { data: hasAnyAdminRole, error } = await supabase
        .rpc('has_any_admin_role', { 
          _user_id: user.id
        });
      
      if (error) {
        console.error('Error checking admin role:', error);
        return false;
      }
      
      return hasAnyAdminRole || false;
    } catch (error) {
      console.error('Error checking admin role:', error);
      return false;
    }
  };

  const updateAuthState = (session: Session | null) => {
    // Clear any pending auth state updates to prevent flicker
    if (authStateTimeout) {
      clearTimeout(authStateTimeout);
    }
    
    // Debounce auth state updates to prevent rapid flickering
    const timeout = setTimeout(() => {
      setSession(session);
      setUser(session?.user ?? null);
      setIsAdmin(false);
    }, 10);
    
    setAuthStateTimeout(timeout);
  };

  useEffect(() => {
    let isMounted = true;

    const initializeAuth = async () => {
      try {
        // Set up auth state listener FIRST
        const { data: { subscription } } = supabase.auth.onAuthStateChange(
          (event, session) => {
            console.log('Auth state changed:', event, !!session?.user);
            if (isMounted) {
              updateAuthState(session);
              if (!isInitialized) {
                setIsInitialized(true);
                setIsLoading(false);
              }
            }
          }
        );

        // THEN check for existing session
        const { data: { session } } = await supabase.auth.getSession();
        if (isMounted) {
          updateAuthState(session);
          setIsInitialized(true);
          setIsLoading(false);
        }

        return () => {
          subscription.unsubscribe();
        };
      } catch (error) {
        console.error('Auth initialization error:', error);
        if (isMounted) {
          setIsLoading(false);
          setIsInitialized(true);
        }
      }
    };

    const cleanup = initializeAuth();

    return () => {
      isMounted = false;
      cleanup.then(fn => fn?.());
    };
  }, []);

  // Update admin status when user changes
  useEffect(() => {
    if (user) {
      checkAdminRole().then(setIsAdmin);
    }
  }, [user]);

  const signIn = async (email: string, password: string) => {
    setIsLoading(true);
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    
    if (!error) {
      // Auth state will be updated automatically via onAuthStateChange
    } else {
      setIsLoading(false);
    }
    
    return { error };
  };

  const signUp = async (email: string, password: string) => {
    setIsLoading(true);
    const redirectUrl = `${window.location.origin}/`;
    
    const { error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: redirectUrl
      }
    });
    
    if (!error) {
      // Auth state will be updated automatically via onAuthStateChange
    } else {
      setIsLoading(false);
    }
    
    return { error };
  };

  const signOut = async () => {
    setIsLoading(true);
    await supabase.auth.signOut();
    // Clear auth state immediately
    setUser(null);
    setSession(null);
    setIsAdmin(false);
    setIsLoading(false);
    // Redirect to landing page after logout
    window.location.href = '/';
  };

  const value: AuthContextType = {
    user,
    session,
    isLoading,
    isAdmin,
    signIn,
    signUp,
    signOut,
    checkAdminRole,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}