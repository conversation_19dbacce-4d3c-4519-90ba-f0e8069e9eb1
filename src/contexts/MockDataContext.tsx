import { createContext, useContext, useEffect, useState, ReactNode } from 'react';

interface MockDataContextType {
  showMockData: boolean;
  toggleMockData: () => void;
}

const MockDataContext = createContext<MockDataContextType | undefined>(undefined);

interface MockDataProviderProps {
  children: ReactNode;
}

export function MockDataProvider({ children }: MockDataProviderProps) {
  const [showMockData, setShowMockData] = useState(false);

  // Load mock data preference from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('showMockData');
    if (saved) {
      setShowMockData(JSON.parse(saved));
    }
  }, []);

  const toggleMockData = () => {
    const newValue = !showMockData;
    setShowMockData(newValue);
    localStorage.setItem('showMockData', JSON.stringify(newValue));
  };

  return (
    <MockDataContext.Provider value={{ showMockData, toggleMockData }}>
      {children}
    </MockDataContext.Provider>
  );
}

export function useMockData() {
  const context = useContext(MockDataContext);
  if (context === undefined) {
    throw new Error('useMockData must be used within a MockDataProvider');
  }
  return context;
}