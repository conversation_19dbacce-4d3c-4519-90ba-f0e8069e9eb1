import React, { createContext, useContext, useEffect, useState, useMemo, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './AuthContext';

interface ClubContextType {
  currentClubId: string | null;
  currentClub: {
    id: string;
    name: string;
    slug: string;
    timezone: string;
  } | null;
  isLoading: boolean;
  error: string | null;
}

const ClubContext = createContext<ClubContextType | undefined>(undefined);

export function ClubProvider({ children }: { children: React.ReactNode }) {
  const { user } = useAuth();
  const [currentClubId, setCurrentClubId] = useState<string | null>(null);
  const [currentClub, setCurrentClub] = useState<ClubContextType['currentClub']>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadUserClub = useCallback(async () => {
    if (!user?.id) {
      setCurrentClubId(null);
      setCurrentClub(null);
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Check if user has meta-admin roles first
      const { data: metaRoles } = await supabase
        .from('meta_admin_permissions')
        .select('role')
        .eq('user_id', user.id)
        .or('expires_at.is.null,expires_at.gt.now()')
        .limit(1);

      const isMetaAdmin = metaRoles && metaRoles.length > 0;
      console.log('ClubContext: User is meta admin:', isMetaAdmin);

      // Get user's active club membership (unified for all user types - members and guests)
      console.log('ClubContext: Looking for membership for user:', user.id);
      
      const { data: membership, error: membershipError } = await supabase
        .from('club_memberships')
        .select(`
          club_id,
          role,
          clubs!inner (
            id,
            name,
            slug,
            timezone
          )
        `)
        .eq('user_id', user.id)
        .eq('is_active', true)
        .maybeSingle();

      console.log('ClubContext: Query result:', { membership, membershipError });

      if (membershipError && membershipError.code !== 'PGRST116') {
        console.error('Error loading club membership:', membershipError);
        setError('Fehler beim Laden der Clubdaten');
        return;
      }

      if (membership?.clubs) {
        const club = membership.clubs;
        setCurrentClubId(club.id);
        setCurrentClub({
          id: club.id,
          name: club.name,
          slug: club.slug,
          timezone: club.timezone || 'Europe/Berlin'
        });
        
        console.log('ClubContext: Loaded club from membership:', club.name, '(ID:', club.id, ') - Role:', membership.role);
      } else if (isMetaAdmin) {
        // If no membership but user is meta-admin, try to resolve club from URL
        console.log('ClubContext: No membership found, but user is meta-admin. Checking URL for club...');
        
        const pathname = window.location.pathname;
        const pathMatch = pathname.match(/^\/c\/([^\/]+)/);
        
        if (pathMatch) {
          const clubSlug = pathMatch[1];
          console.log('ClubContext: Trying to resolve club by slug for meta-admin:', clubSlug);
          
          const { data: resolvedClub, error: clubError } = await supabase
            .from('clubs')
            .select('id, name, slug, timezone')
            .eq('slug', clubSlug)
            .eq('is_active', true)
            .maybeSingle();

          if (clubError) {
            console.error('Error resolving club for meta-admin:', clubError);
          } else if (resolvedClub) {
            console.log('ClubContext: Resolved club for meta-admin:', resolvedClub.name);
            setCurrentClubId(resolvedClub.id);
            setCurrentClub({
              id: resolvedClub.id,
              name: resolvedClub.name,
              slug: resolvedClub.slug,
              timezone: resolvedClub.timezone || 'Europe/Berlin'
            });
          } else {
            console.warn('ClubContext: No club found for slug:', clubSlug);
            setCurrentClubId(null);
            setCurrentClub(null);
          }
        } else {
          console.warn('ClubContext: Meta-admin but no club slug in URL');
          setCurrentClubId(null);
          setCurrentClub(null);
        }
      } else {
        console.warn('ClubContext: No active club membership found for user');
        console.log('ClubContext: Membership data:', membership);
        setCurrentClubId(null);
        setCurrentClub(null);
      }
    } catch (error) {
      console.error('Error in ClubContext:', error);
      setError('Fehler beim Laden der Clubdaten');
    } finally {
      setIsLoading(false);
    }
  }, [user?.id]);

  useEffect(() => {
    loadUserClub();
  }, [loadUserClub]);

  const value: ClubContextType = useMemo(() => ({
    currentClubId,
    currentClub,
    isLoading,
    error,
  }), [currentClubId, currentClub, isLoading, error]);

  return (
    <ClubContext.Provider value={value}>
      {children}
    </ClubContext.Provider>
  );
}

export function useClub() {
  const context = useContext(ClubContext);
  if (context === undefined) {
    throw new Error('useClub must be used within a ClubProvider');
  }
  return context;
}