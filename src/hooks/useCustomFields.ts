import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

export interface CustomField {
  id: string;
  field_key: string;
  field_name: string;
  field_type: string;
  field_options: any;
  is_required: boolean;
  is_active: boolean;
  display_order: number;
}

export const useCustomFields = (clubId?: string) => {
  const [customFields, setCustomFields] = useState<CustomField[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (clubId) {
      fetchCustomFields();
    }
  }, [clubId]);

  const fetchCustomFields = async () => {
    if (!clubId) return;

    try {
      const { data, error } = await supabase
        .from('club_custom_fields')
        .select('*')
        .eq('club_id', clubId)
        .eq('is_active', true)
        .order('display_order', { ascending: true });

      if (error) throw error;
      
      setCustomFields((data || []).map(field => ({
        ...field,
        field_options: Array.isArray(field.field_options) ? field.field_options : []
      })));
    } catch (error) {
      console.error('Error fetching custom fields:', error);
      toast.error('Fehler beim Laden der benutzerdefinierten Felder');
    } finally {
      setIsLoading(false);
    }
  };

  const validateCustomData = (customData: Record<string, any>): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];
    
    customFields.forEach(field => {
      if (field.is_required && !customData[field.field_key]) {
        errors.push(`${field.field_name} ist ein Pflichtfeld`);
      }
      
      if (field.field_type === 'tennis_lk' && customData[field.field_key]) {
        const lkValue = customData[field.field_key];
        const lkRegex = /^(LK([1-9]|1[0-9]|2[0-5])(,[0-9])?|Keine)$/;
        if (!lkRegex.test(lkValue)) {
          errors.push(`${field.field_name}: Ungültiges LK-Format (z.B. LK1, LK14,2, Keine)`);
        }
      }
      
      if (field.field_type === 'number' && customData[field.field_key]) {
        const numValue = parseFloat(customData[field.field_key]);
        if (isNaN(numValue)) {
          errors.push(`${field.field_name} muss eine Zahl sein`);
        }
      }
      
      if (field.field_type === 'select' && customData[field.field_key]) {
        const options = Array.isArray(field.field_options) ? field.field_options : [];
        if (!options.includes(customData[field.field_key])) {
          errors.push(`${field.field_name}: Ungültige Auswahl`);
        }
      }
    });
    
    return {
      isValid: errors.length === 0,
      errors
    };
  };

  return {
    customFields,
    isLoading,
    fetchCustomFields,
    validateCustomData
  };
};