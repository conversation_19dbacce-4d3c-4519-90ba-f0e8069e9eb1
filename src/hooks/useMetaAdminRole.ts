import { useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';

export type MetaAdminRole = 'SUPER_ADMIN' | 'SUPPORT_ADMIN' | 'BILLING_ADMIN' | 'READONLY';

export interface MetaAdminPermission {
  id: string;
  user_id: string;
  role: MetaAdminRole;
  granted_by: string;
  granted_at: string;
  expires_at?: string;
}

export function useMetaAdminRole() {
  const { user } = useAuth();
  const [roles, setRoles] = useState<MetaAdminRole[]>([]);
  const [permissions, setPermissions] = useState<MetaAdminPermission[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const loadedUserRef = useRef<string | null>(null);

  const loadMetaAdminRoles = useCallback(async (userId: string) => {
    // Prevent duplicate loading
    if (loadedUserRef.current === userId) {
      console.log('🔍 useMetaAdminRole: Already loaded for this user, skipping');
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      loadedUserRef.current = userId;

      console.log('🔍 useMetaAdminRole: Loading roles for user:', userId);

      const { data, error } = await supabase
        .from('meta_admin_permissions')
        .select('*')
        .eq('user_id', userId)
        .or('expires_at.is.null,expires_at.gt.now()');

      console.log('🔍 useMetaAdminRole: Query result:', { data, error });

      if (error) throw error;

      const activePermissions = data || [];
      setPermissions(activePermissions);
      setRoles(activePermissions.map(p => p.role));

      console.log('🔍 useMetaAdminRole: Set roles:', activePermissions.map(p => p.role));
    } catch (err) {
      console.error('❌ Error loading meta admin roles:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      loadedUserRef.current = null; // Reset on error
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    if (!user) {
      console.log('🔍 useMetaAdminRole: No user, clearing roles');
      setRoles([]);
      setPermissions([]);
      setIsLoading(false);
      loadedUserRef.current = null;
      return;
    }

    // Only load if we haven't loaded for this user yet
    if (loadedUserRef.current !== user.id) {
      console.log('🔍 useMetaAdminRole: User found, loading roles for:', user.id);
      loadMetaAdminRoles(user.id);
    } else {
      console.log('🔍 useMetaAdminRole: Already loaded for user, setting loading false');
      setIsLoading(false);
    }
  }, [user?.id, loadMetaAdminRoles]);

  const hasRole = useCallback((role: MetaAdminRole): boolean => {
    return roles.includes(role);
  }, [roles]);

  const hasAnyRole = useCallback((): boolean => {
    return roles.length > 0;
  }, [roles]);

  const canAccessSection = useCallback((section: string): boolean => {
    if (roles.length === 0) return false;

    // Super admins can access everything
    if (roles.includes('SUPER_ADMIN')) return true;

    // Define access rules for different sections
    const accessRules: Record<string, MetaAdminRole[]> = {
      dashboard: ['SUPER_ADMIN', 'SUPPORT_ADMIN', 'BILLING_ADMIN', 'READONLY'],
      clubs: ['SUPER_ADMIN', 'SUPPORT_ADMIN', 'READONLY'],
      domains: ['SUPER_ADMIN', 'SUPPORT_ADMIN'],
      users: ['SUPER_ADMIN', 'SUPPORT_ADMIN', 'READONLY'],
      billing: ['SUPER_ADMIN', 'BILLING_ADMIN'],
      content: ['SUPER_ADMIN', 'SUPPORT_ADMIN'],
      integrations: ['SUPER_ADMIN', 'SUPPORT_ADMIN'],
      monitoring: ['SUPER_ADMIN', 'SUPPORT_ADMIN', 'READONLY'],
      support: ['SUPER_ADMIN', 'SUPPORT_ADMIN'],
      settings: ['SUPER_ADMIN'],
    };

    const allowedRoles = accessRules[section] || [];
    return roles.some(role => allowedRoles.includes(role));
  }, [roles]);

  const canModify = useCallback((): boolean => {
    // Only SUPER_ADMIN and specific roles can modify data
    return roles.includes('SUPER_ADMIN') || roles.includes('SUPPORT_ADMIN') || roles.includes('BILLING_ADMIN');
  }, [roles]);

  const isReadOnly = useCallback((): boolean => {
    return roles.length === 1 && roles.includes('READONLY');
  }, [roles]);

  const refresh = useCallback(() => {
    if (user?.id) {
      loadedUserRef.current = null; // Reset loaded state
      loadMetaAdminRoles(user.id);
    }
  }, [user?.id, loadMetaAdminRoles]);

  return {
    roles,
    permissions,
    isLoading,
    error,
    hasRole,
    hasAnyRole,
    canAccessSection,
    canModify,
    isReadOnly,
    refresh,
  };
}