import { useState, useCallback } from 'react';

export interface ColumnConfig {
  key: string;
  label: string;
  visible: boolean;
  required?: boolean;
}

export const DEFAULT_COLUMNS: ColumnConfig[] = [
  { key: 'name', label: 'Name', visible: true, required: true },
  { key: 'email', label: 'E-Mail', visible: true },
  { key: 'gender', label: 'Geschlecht', visible: true },
  { key: 'phone', label: 'Telefon', visible: false },
  { key: 'role', label: 'Rolle', visible: true },
  { key: 'membership_type', label: 'Mitgliedschaft', visible: true },
  { key: 'birth_date', label: 'Geburtsdatum', visible: false },
  { key: 'address', label: '<PERSON>ress<PERSON>', visible: false },
  { key: 'tennis_lk', label: 'Tennis LK', visible: true },
  { key: 'joined_at', label: 'Beigetreten', visible: true },
  { key: 'actions', label: 'Aktionen', visible: true, required: true },
];

export const useColumnVisibility = (initialColumns = DEFAULT_COLUMNS) => {
  const [columns, setColumns] = useState<ColumnConfig[]>(initialColumns);

  const toggleColumn = useCallback((key: string) => {
    setColumns(prev => 
      prev.map(col => 
        col.key === key && !col.required 
          ? { ...col, visible: !col.visible }
          : col
      )
    );
  }, []);

  const resetColumns = useCallback(() => {
    setColumns(DEFAULT_COLUMNS);
  }, []);

  const visibleColumns = columns.filter(col => col.visible);

  return {
    columns,
    visibleColumns,
    toggleColumn,
    resetColumns,
    setColumns
  };
};