
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useClubTimezone } from '@/hooks/useClubTimezone';
import { toast } from 'sonner';

interface TimeConfiguration {
  match_duration_minutes: number;
  warmup_minutes: number;
  changeover_minutes: number;
  set_break_minutes: number;
  between_match_buffer_minutes: number;
  court_preparation_minutes: number;
}

interface SchedulingOptions {
  tournament_id: string;
  time_config: TimeConfiguration;
  start_date?: string;
  start_time?: string;
  available_courts?: string[];
  max_matches_per_court_per_day?: number;
  smart_scheduling_config?: any;
}

export function useTournamentScheduling() {
  const queryClient = useQueryClient();
  const { timezone } = useClubTimezone();

  // Calculate total time needed per match including all buffers
  const calculateMatchDuration = (timeConfig: TimeConfiguration) => {
    return timeConfig.warmup_minutes + 
           timeConfig.match_duration_minutes + 
           timeConfig.changeover_minutes + 
           timeConfig.set_break_minutes + 
           timeConfig.between_match_buffer_minutes + 
           timeConfig.court_preparation_minutes;
  };

  // Generate realistic match schedule based on time configuration
  const generateScheduleMutation = useMutation({
    mutationFn: async (options: SchedulingOptions) => {
      console.log('Generating schedule with time config:', options.time_config);

      // First, get tournament and court information
      const { data: tournament, error: tournamentError } = await supabase
        .from('tournaments')
        .select('*')
        .eq('id', options.tournament_id)
        .single();

      if (tournamentError || !tournament) {
        throw new Error('Turnier nicht gefunden');
      }

      // Get assigned courts for this tournament
      const { data: tournamentCourts, error: courtsError } = await supabase
        .from('tournament_courts')
        .select(`
          id,
          court_id,
          courts (
            id,
            number,
            club_id
          )
        `)
        .eq('tournament_id', options.tournament_id);

      if (courtsError || !tournamentCourts || tournamentCourts.length === 0) {
        throw new Error('Keine Plätze für dieses Turnier zugewiesen');
      }

      // Calculate match duration including all time factors
      const totalMatchDuration = calculateMatchDuration(options.time_config);
      
      // Use Edge Function to generate matches with proper time scheduling
      const { data, error } = await supabase.functions.invoke('tournament-matches', {
        body: {
          action: 'generate',
          tournament_id: options.tournament_id,
          club_timezone: timezone,
          format_config: {
            ...(tournament.format_config as object || {}),
            time_config: options.time_config,
            total_match_duration: totalMatchDuration,
            start_date: options.start_date,
            start_time: options.start_time || '09:00',
            available_courts: tournamentCourts.map(tc => tc.court_id),
            max_matches_per_court_per_day: options.max_matches_per_court_per_day || 8
          },
          smart_scheduling_config: options.smart_scheduling_config
        }
      });

      if (error) {
        console.error('Schedule generation error:', error);
        throw error;
      }

      if (!data?.success) {
        throw new Error(data?.error || 'Spielplan-Generierung fehlgeschlagen');
      }

      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['tournament-matches'] });
      queryClient.invalidateQueries({ queryKey: ['tournaments'] });
      toast.success(`Spielplan mit ${data.matches?.length || 0} Spielen erfolgreich generiert`);
    },
    onError: (error) => {
      console.error('Schedule generation error:', error);
      toast.error(`Spielplan-Generierung fehlgeschlagen: ${error.message}`);
    }
  });

  // Sync tournament time config to booking system
  const syncToBookingSystemMutation = useMutation({
    mutationFn: async ({ tournament_id, time_config }: { tournament_id: string; time_config: TimeConfiguration }) => {
      console.log('Syncing time config to booking system:', time_config);

      // Update tournament with finalized time configuration
      const { error: updateError } = await supabase
        .from('tournaments')
        .update({
          format_config: {
            time_config: time_config as any,
            updated_at: new Date().toISOString()
          } as any
        })
        .eq('id', tournament_id);

      if (updateError) {
        throw updateError;
      }

      // Get all tournament matches to update their time slots
      const { data: matches, error: matchesError } = await supabase
        .from('tournament_matches')
        .select('*')
        .eq('tournament_id', tournament_id);

      if (matchesError) {
        throw matchesError;
      }

      // Calculate new time slots for existing matches
      const totalMatchDuration = calculateMatchDuration(time_config);
      
      if (matches && matches.length > 0) {
        // Update match durations in booking system
        const updatePromises = matches.map(match => {
          if (match.scheduled_date && match.scheduled_time) {
            const startTime = new Date(`${match.scheduled_date}T${match.scheduled_time}`);
            const endTime = new Date(startTime.getTime() + totalMatchDuration * 60000);
            
            return supabase
              .from('tournament_matches')
              .update({
                estimated_duration_minutes: totalMatchDuration,
                scheduled_end_time: endTime.toTimeString().slice(0, 5)
              })
              .eq('id', match.id);
          }
          return Promise.resolve();
        });

        await Promise.all(updatePromises);
      }

      return { success: true, updated_matches: matches?.length || 0 };
    },
    onSuccess: (data) => {
      toast.success(`Zeitkonfiguration für ${data.updated_matches} Spiele synchronisiert`);
    },
    onError: (error) => {
      console.error('Sync error:', error);
      toast.error(`Synchronisation fehlgeschlagen: ${error.message}`);
    }
  });

  // Validate time configuration against court availability
  const validateTimeConfigMutation = useMutation({
    mutationFn: async ({ tournament_id, time_config }: { tournament_id: string; time_config: TimeConfiguration }) => {
      // Get tournament courts and their availability
      const { data: tournamentCourts, error: courtsError } = await supabase
        .from('tournament_courts')
        .select(`
          court_id,
          courts (
            id,
            number,
            club_id
          )
        `)
        .eq('tournament_id', tournament_id);

      if (courtsError || !tournamentCourts) {
        throw new Error('Plätze konnten nicht geladen werden');
      }

      // Check court availability for the calculated match duration
      const totalMatchDuration = calculateMatchDuration(time_config);
      
      // Get court availability rules
      const { data: availability, error: availabilityError } = await supabase
        .from('court_availability')
        .select('*')
        .in('court_id', tournamentCourts.map(tc => tc.court_id));

      if (availabilityError) {
        throw availabilityError;
      }

      // Calculate how many matches can fit per court per day
      const dailyHours = availability && availability.length > 0 
        ? Math.max(...availability.map(a => {
            const start = new Date(`2000-01-01T${a.start_time}`);
            const end = new Date(`2000-01-01T${a.end_time}`);
            return (end.getTime() - start.getTime()) / (1000 * 60 * 60);
          }))
        : 10; // Default 10 hours

      const matchesPerCourtPerDay = Math.floor((dailyHours * 60) / totalMatchDuration);

      return {
        valid: true,
        total_match_duration: totalMatchDuration,
        matches_per_court_per_day: matchesPerCourtPerDay,
        available_courts: tournamentCourts.length,
        daily_capacity: matchesPerCourtPerDay * tournamentCourts.length
      };
    }
  });

  return {
    generateScheduleMutation,
    syncToBookingSystemMutation,
    validateTimeConfigMutation,
    calculateMatchDuration
  };
}
