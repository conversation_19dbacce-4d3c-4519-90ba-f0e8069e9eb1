import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { multiTenantService } from '@/services/MultiTenantService';
import { supabase } from '@/integrations/supabase/client';

/**
 * 🏢 CLUB-SPEZIFISCHE QUERY HOOKS
 * 
 * React Query Hooks die automatisch Club-Filter anwenden
 * und Multi-Tenant-Sicherheit gewährleisten.
 */

export function useClubData<T = any>(
  table: string, 
  options?: {
    select?: string;
    filters?: Record<string, any>;
    orderBy?: { column: string; ascending?: boolean };
  }
) {
  return useQuery({
    queryKey: ['club-data', table, options],
    queryFn: async () => {
      const club = multiTenantService.getCurrentClubOrThrow();
      
      let query = supabase
        .from(table as any)
        .select(options?.select || '*')
        .eq('club_id', club.id);

      // Zusätzliche Filter anwenden
      if (options?.filters) {
        Object.entries(options.filters).forEach(([key, value]) => {
          query = query.eq(key, value);
        });
      }

      // Sortierung anwenden
      if (options?.orderBy) {
        query = query.order(options.orderBy.column, { 
          ascending: options.orderBy.ascending ?? true 
        });
      }

      const { data, error } = await query;
      
      if (error) {
        throw new Error(`Fehler beim Laden von ${table}: ${error.message}`);
      }
      
      return data as T[];
    },
    enabled: !!multiTenantService.getCurrentClubOrThrow()
  });
}

export function useClubMutation<T = any>(
  table: string,
  operation: 'insert' | 'update' | 'delete' = 'insert'
) {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (data: { id?: string; [key: string]: any }) => {
      const club = multiTenantService.getCurrentClubOrThrow();
      
      let query;
      
      switch (operation) {
        case 'insert':
          query = supabase
            .from(table as any)
            .insert(multiTenantService.addClubId(data));
          break;
          
        case 'update':
          if (!data.id) throw new Error('Update benötigt eine ID');
          // Sicherheitscheck: Ressource gehört zum Club
          const hasAccess = await multiTenantService.validateClubAccess(table, data.id);
          if (!hasAccess) {
            throw new Error('Keine Berechtigung für diese Ressource');
          }
          
          query = supabase
            .from(table as any)
            .update(data)
            .eq('id', data.id)
            .eq('club_id', club.id);
          break;
          
        case 'delete':
          if (!data.id) throw new Error('Delete benötigt eine ID');
          // Sicherheitscheck: Ressource gehört zum Club
          const canDelete = await multiTenantService.validateClubAccess(table, data.id);
          if (!canDelete) {
            throw new Error('Keine Berechtigung für diese Ressource');
          }
          
          query = supabase
            .from(table as any)
            .delete()
            .eq('id', data.id)
            .eq('club_id', club.id);
          break;
      }
      
      const { data: result, error } = await query;
      
      if (error) {
        throw new Error(`${operation} Fehler in ${table}: ${error.message}`);
      }
      
      return result;
    },
    onSuccess: () => {
      // Invalidate relevante Queries
      queryClient.invalidateQueries({ queryKey: ['club-data', table] });
    }
  });
}

export function useClubMemberships() {
  return useClubData('club_memberships', {
    orderBy: { column: 'joined_at', ascending: false }
  });
}

export function useClubBookings(date?: string) {
  const filters = date ? { booking_date: date } : undefined;
  
  return useClubData('bookings', {
    filters,
    orderBy: { column: 'start_time', ascending: true }
  });
}

export function useClubCourts() {
  return useClubData('courts', {
    orderBy: { column: 'number', ascending: true }
  });
}

export function useClubTournaments() {
  return useClubData('tournaments', {
    orderBy: { column: 'start_date', ascending: false }
  });
}