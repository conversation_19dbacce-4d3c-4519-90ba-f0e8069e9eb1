import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useClub } from '@/contexts/ClubContext';

export interface TournamentFormatConfig {
  id: string;
  club_id: string;
  tournament_id?: string;
  format_type: string;
  custom_rules: Record<string, any>;
  custom_description?: string;
  custom_name?: string;
  time_config: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export function useTournamentFormatConfig(tournamentId?: string, formatType?: string) {
  const { currentClub } = useClub();
  const queryClient = useQueryClient();

  // Fetch format configs for current club
  const { data: configs, isLoading, error } = useQuery({
    queryKey: ['tournament-format-configs', currentClub?.id, tournamentId, formatType],
    queryFn: async () => {
      if (!currentClub?.id) return [];

      // Only call the function if we have the required parameters
      if (!formatType) return [];

      const { data, error } = await supabase.functions.invoke('tournament-format-config', {
        body: { 
          action: 'get',
          club_id: currentClub.id,
          format_type: formatType,
          ...(tournamentId && { tournament_id: tournamentId })
        }
      });

      if (error) {
        console.error('Tournament format config error:', error);
        return [];
      }
      return data.data as TournamentFormatConfig[];
    },
    enabled: !!currentClub?.id && !!formatType
  });

  // Get the most specific config for a format (tournament-specific > club-wide > default)
  const getFormatConfig = (format: string, tournament?: string): TournamentFormatConfig | null => {
    if (!configs || !Array.isArray(configs)) return null;

    // First try tournament-specific config
    if (tournament) {
      const tournamentConfig = configs.find(c => 
        c.format_type === format && c.tournament_id === tournament
      );
      if (tournamentConfig) return tournamentConfig;
    }

    // Then try club-wide config
    const clubConfig = configs.find(c => 
      c.format_type === format && !c.tournament_id
    );
    if (clubConfig) return clubConfig;

    return null;
  };

  // Save or update format config
  const saveConfigMutation = useMutation({
    mutationFn: async (config: Partial<TournamentFormatConfig>) => {
      const { data, error } = await supabase.functions.invoke('tournament-format-config', {
        body: { action: 'save', ...config }
      });

      if (error) throw error;
      return data.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['tournament-format-configs', currentClub?.id]
      });
    }
  });

  // Delete format config
  const deleteConfigMutation = useMutation({
    mutationFn: async (configId: string) => {
      const { data, error } = await supabase.functions.invoke('tournament-format-config', {
        body: { action: 'delete', config_id: configId }
      });

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['tournament-format-configs', currentClub?.id]
      });
    }
  });

  return {
    configs: configs || [],
    isLoading,
    error,
    getFormatConfig,
    saveConfig: saveConfigMutation.mutateAsync,
    deleteConfig: deleteConfigMutation.mutateAsync,
    isSaving: saveConfigMutation.isPending,
    isDeleting: deleteConfigMutation.isPending
  };
}