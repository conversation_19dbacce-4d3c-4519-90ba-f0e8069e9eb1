import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

export interface Tournament {
  id: string;
  club_id: string;
  name: string;
  description?: string;
  status: 'registration' | 'active' | 'completed' | 'cancelled';
  format_type?: string;
  format_config?: any;
  registration_start?: string;
  registration_end?: string;
  tournament_start?: string;
  tournament_end?: string;
  max_participants?: number;
  allow_guests: boolean;
  created_at: string;
  updated_at: string;
}

export interface TournamentParticipant {
  id: string;
  tournament_id: string;
  club_id: string;
  user_id?: string;
  guest_name?: string;
  guest_email?: string;
  phone?: string;
  is_guest: boolean;
  seeding?: number;
  status: 'registered' | 'confirmed' | 'withdrawn' | 'disqualified';
  registration_date: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface TournamentRegistrationParams {
  tournament_id: string;
  club_id: string;
  user_id?: string;
  guest_name?: string;
  guest_email?: string;
  is_guest?: boolean;
}

/**
 * Universal Tournament Management Hook
 * Funktioniert für alle Vereine und alle Turnier-Modi
 */
export function useTournamentManagement(clubId?: string, userId?: string) {
  const queryClient = useQueryClient();

  // Fetch tournaments for a specific club
  const {
    data: tournaments,
    isLoading: tournamentsLoading,
    error: tournamentsError
  } = useQuery({
    queryKey: ['tournaments', clubId],
    queryFn: async () => {
      if (!clubId) return [];
      
      const { data, error } = await supabase
        .from('tournaments')
        .select('*')
        .eq('club_id', clubId)
        .order('created_at', { ascending: false });
      
      if (error) {
        console.error('Error fetching tournaments:', error);
        throw error;
      }
      return data as Tournament[];
    },
    enabled: !!clubId
  });

  // Fetch user's participations
  const {
    data: participations,
    isLoading: participationsLoading,
    error: participationsError
  } = useQuery({
    queryKey: ['tournament-participations', userId, clubId],
    queryFn: async () => {
      if (!userId || !clubId) return [];
      
      const { data, error } = await supabase
        .from('tournament_participants')
        .select(`
          *,
          tournaments (
            id, name, status, tournament_start, tournament_end, format_type
          )
        `)
        .eq('user_id', userId)
        .eq('club_id', clubId);
      
      if (error) {
        console.error('Error fetching participations:', error);
        throw error;
      }
      return data;
    },
    enabled: !!userId && !!clubId
  });

  // Registration mutation using Edge Function for global functionality
  const registerMutation = useMutation({
    mutationFn: async (params: TournamentRegistrationParams) => {
      console.log('Registering for tournament:', params);

      // Use Edge Function for robust, global registration logic
      const { data, error } = await supabase.functions.invoke('tournament-management', {
        body: {
          action: 'register_participant',
          payload: params
        }
      });

      if (error) {
        console.error('Registration edge function error:', error);
        throw error;
      }

      if (!data?.success) {
        console.error('Registration failed:', data);
        throw new Error(data?.error || 'Anmeldung fehlgeschlagen');
      }

      return data.data;
    },
    onSuccess: (data, variables) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['tournament-participations', userId, clubId] });
      queryClient.invalidateQueries({ queryKey: ['tournament-participants', variables.tournament_id] });
      
      toast.success('Erfolgreich für Turnier angemeldet');
      console.log('Registration successful:', data);
    },
    onError: (error) => {
      console.error('Registration error:', error);
      toast.error(`Anmeldung fehlgeschlagen: ${error.message}`);
    }
  });

  // Withdrawal mutation using Edge Function
  const withdrawMutation = useMutation({
    mutationFn: async (params: { participant_id: string; user_id: string }) => {
      console.log('Withdrawing from tournament:', params);

      const { data, error } = await supabase.functions.invoke('tournament-management', {
        body: {
          action: 'withdraw_participant',
          payload: params
        }
      });

      if (error) {
        console.error('Withdrawal edge function error:', error);
        throw error;
      }

      if (!data?.success) {
        console.error('Withdrawal failed:', data);
        throw new Error(data?.error || 'Abmeldung fehlgeschlagen');
      }

      return data;
    },
    onSuccess: () => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['tournament-participations', userId, clubId] });
      
      toast.success('Erfolgreich vom Turnier abgemeldet');
    },
    onError: (error) => {
      console.error('Withdrawal error:', error);
      toast.error(`Abmeldung fehlgeschlagen: ${error.message}`);
    }
  });

  // Direct registration mutation (fallback for simple cases)
  const directRegisterMutation = useMutation({
    mutationFn: async (params: TournamentRegistrationParams) => {
      if (!params.user_id || !params.club_id) {
        throw new Error('Benutzer-ID und Club-ID erforderlich');
      }

      // Check if already registered
      const { data: existing } = await supabase
        .from('tournament_participants')
        .select('id')
        .eq('tournament_id', params.tournament_id)
        .eq('user_id', params.user_id)
        .eq('club_id', params.club_id)
        .maybeSingle();

      if (existing) {
        throw new Error('Sie sind bereits für dieses Turnier angemeldet');
      }

      const { data, error } = await supabase
        .from('tournament_participants')
        .insert([{
          tournament_id: params.tournament_id,
          club_id: params.club_id,
          user_id: params.user_id,
          is_guest: false,
          status: 'registered'
        }])
        .select()
        .single();
      
      if (error) {
        console.error('Direct registration error:', error);
        throw error;
      }
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tournament-participations'] });
      toast.success('Erfolgreich für Turnier angemeldet');
    },
    onError: (error) => {
      console.error('Direct registration error:', error);
      toast.error(`Anmeldung fehlgeschlagen: ${error.message}`);
    }
  });

  // Helper functions
  const isRegistered = (tournamentId: string) => {
    return participations?.some(p => 
      p.tournament_id === tournamentId && 
      ['registered', 'confirmed'].includes(p.status)
    ) || false;
  };

  const canRegister = (tournament: Tournament) => {
    if (tournament.status !== 'registration') return false;
    if (isRegistered(tournament.id)) return false;
    
    const now = new Date();
    if (tournament.registration_end && new Date(tournament.registration_end) < now) return false;
    if (tournament.registration_start && new Date(tournament.registration_start) > now) return false;
    
    return true;
  };

  const getParticipation = (tournamentId: string) => {
    return participations?.find(p => p.tournament_id === tournamentId);
  };

  const getAvailableTournaments = () => {
    return tournaments?.filter(t => t.status === 'registration') || [];
  };

  const getActiveTournaments = () => {
    return tournaments?.filter(t => t.status === 'active') || [];
  };

  const getCompletedTournaments = () => {
    return tournaments?.filter(t => t.status === 'completed') || [];
  };

  const getMyTournaments = () => {
    if (!participations) return [];
    return participations.filter(p => ['registered', 'confirmed'].includes(p.status));
  };

  return {
    // Data
    tournaments,
    participations,
    
    // Loading states
    tournamentsLoading,
    participationsLoading,
    
    // Errors
    tournamentsError,
    participationsError,
    
    // Mutations
    registerMutation,
    withdrawMutation,
    directRegisterMutation,
    
    // Helper functions
    isRegistered,
    canRegister,
    getParticipation,
    getAvailableTournaments,
    getActiveTournaments,
    getCompletedTournaments,
    getMyTournaments,
    
    // Registration functions
    register: (tournamentId: string, isGuest = false, guestData?: { name: string; email: string }) => {
      const params: TournamentRegistrationParams = {
        tournament_id: tournamentId,
        club_id: clubId!,
        is_guest: isGuest
      };

      if (isGuest && guestData) {
        params.guest_name = guestData.name;
        params.guest_email = guestData.email;
      } else {
        params.user_id = userId;
      }

      // Try Edge Function first, fallback to direct registration
      try {
        return registerMutation.mutate(params);
      } catch (error) {
        console.warn('Edge function registration failed, trying direct registration:', error);
        return directRegisterMutation.mutate(params);
      }
    },
    
    withdraw: (participantId: string) => {
      if (!userId) {
        throw new Error('Benutzer-ID erforderlich');
      }
      return withdrawMutation.mutate({ participant_id: participantId, user_id: userId });
    }
  };
}