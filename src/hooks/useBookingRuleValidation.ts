import { useState, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useClub } from '@/contexts/ClubContext';
import { useClubTimezone } from '@/hooks/useClubTimezone';
import { getTodayInTimezone } from '@/lib/utils';
import { toast } from '@/hooks/use-toast';

interface BookingRule {
  id: string;
  name: string;
  rule_type: string;
  conditions: any;
  actions: any;
  is_active: boolean;
}

interface BookingData {
  club_id: string;
  booking_date: string;
  start_time: string;
  end_time: string;
  player_name?: string;
  created_by?: string;
  court_id?: string;
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export const useBookingRuleValidation = () => {
  const { timezone } = useClubTimezone();
  const [isValidating, setIsValidating] = useState(false);

  const validateBooking = useCallback(async (bookingData: BookingData): Promise<ValidationResult> => {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: []
    };

    if (!bookingData.club_id) {
      result.isValid = false;
      result.errors.push('Keine Club-ID verfügbar');
      return result;
    }

    setIsValidating(true);

    try {
      // Aktive Buchungsregeln für den Club laden
      const { data: rules, error: rulesError } = await supabase
        .from('booking_rules')
        .select('*')
        .eq('club_id', bookingData.club_id)
        .eq('is_active', true);

      if (rulesError) {
        console.error('Fehler beim Laden der Buchungsregeln:', rulesError);
        result.warnings.push('Buchungsregeln konnten nicht geladen werden');
        return result;
      }

      if (!rules || rules.length === 0) {
        return result; // Keine Regeln definiert
      }

      // Jede Regel einzeln prüfen
      for (const rule of rules) {
        const violation = await checkRule(rule, bookingData, timezone);
        if (violation) {
          result.isValid = false;
          result.errors.push(violation);
        }
      }

    } catch (error) {
      console.error('Fehler bei Regelvalidierung:', error);
      result.warnings.push('Regelvalidierung fehlgeschlagen');
    } finally {
      setIsValidating(false);
    }

    return result;
  }, [timezone]);

  const checkRule = async (rule: BookingRule, bookingData: BookingData, clubTimezone: string): Promise<string | null> => {
    const { rule_type, conditions, name } = rule;

    try {
      switch (rule_type) {
        case 'advance_booking_limit':
          return await checkAdvanceBookingLimit(conditions, bookingData, name, clubTimezone);
        
        case 'max_duration':
          return checkMaxDuration(conditions, bookingData, name);
        
        case 'min_duration':
          return checkMinDuration(conditions, bookingData, name);
        
        case 'max_bookings_per_user':
          return await checkMaxBookingsPerUser(conditions, bookingData, name);
        
        case 'booking_window':
          return checkBookingWindow(conditions, bookingData, name);
        
        default:
          return null;
      }
    } catch (error) {
      console.error(`Fehler bei Regel ${name}:`, error);
      return null;
    }
  };

  const checkAdvanceBookingLimit = async (conditions: any, bookingData: BookingData, ruleName: string, clubTimezone: string): Promise<string | null> => {
    const maxDays = conditions.max_days || conditions.max_advance_days || 7;
    const bookingDate = new Date(bookingData.booking_date);
    
    // CRITICAL: Use club timezone for "today" calculation
    const today = getTodayInTimezone(clubTimezone);
    today.setHours(0, 0, 0, 0);
    
    const daysDiff = Math.ceil((bookingDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    
    if (daysDiff > maxDays) {
      return `${ruleName}: Buchungen sind nur bis zu ${maxDays} Tage im Voraus möglich. (Aktuell: ${daysDiff} Tage)`;
    }
    
    return null;
  };

  const checkMaxDuration = (conditions: any, bookingData: BookingData, ruleName: string): string | null => {
    const maxHours = conditions.max_hours || conditions.max_duration_hours || 2;
    const startTime = bookingData.start_time;
    const endTime = bookingData.end_time;
    
    if (!startTime || !endTime) return null;
    
    const [startH, startM] = startTime.split(':').map(Number);
    const [endH, endM] = endTime.split(':').map(Number);
    
    const durationMinutes = (endH * 60 + endM) - (startH * 60 + startM);
    const durationHours = durationMinutes / 60;
    
    if (durationHours > maxHours) {
      return `${ruleName}: Maximale Buchungsdauer von ${maxHours} Stunden überschritten. (Aktuell: ${durationHours} Stunden)`;
    }
    
    return null;
  };

  const checkMinDuration = (conditions: any, bookingData: BookingData, ruleName: string): string | null => {
    const minHours = conditions.min_hours || conditions.min_duration_hours || 1;
    const startTime = bookingData.start_time;
    const endTime = bookingData.end_time;
    
    if (!startTime || !endTime) return null;
    
    const [startH, startM] = startTime.split(':').map(Number);
    const [endH, endM] = endTime.split(':').map(Number);
    
    const durationMinutes = (endH * 60 + endM) - (startH * 60 + startM);
    const durationHours = durationMinutes / 60;
    
    if (durationHours < minHours) {
      return `${ruleName}: Mindestbuchungsdauer von ${minHours} Stunden unterschritten. (Aktuell: ${durationHours} Stunden)`;
    }
    
    return null;
  };

  const checkMaxBookingsPerUser = async (conditions: any, bookingData: BookingData, ruleName: string): Promise<string | null> => {
    const maxCount = conditions.max_count || conditions.max_bookings_per_user || 3;
    
    if (!bookingData.created_by) return null;
    
    // Aktuelle Buchungen des Users zählen (zukünftige Buchungen)
    const today = getTodayInTimezone(timezone).toISOString().split('T')[0];
    
    const { data: existingBookings, error } = await supabase
      .from('bookings')
      .select('id')
      .eq('created_by', bookingData.created_by)
      .eq('club_id', bookingData.club_id)
      .gte('booking_date', today);
    
    if (error) {
      console.error('Fehler beim Prüfen der Benutzer-Buchungen:', error);
      return null;
    }
    
    const currentBookingCount = existingBookings?.length || 0;
    
    if (currentBookingCount >= maxCount) {
      return `${ruleName}: Maximale Anzahl von ${maxCount} aktiven Buchungen erreicht. (Aktuell: ${currentBookingCount})`;
    }
    
    return null;
  };

  const checkBookingWindow = (conditions: any, bookingData: BookingData, ruleName: string): string | null => {
    const startHour = conditions.start_hour || conditions.booking_start_hour || 8;
    const endHour = conditions.end_hour || conditions.booking_end_hour || 22;
    
    const startTime = bookingData.start_time;
    if (!startTime) return null;
    
    const [bookingStartH] = startTime.split(':').map(Number);
    
    if (bookingStartH < startHour || bookingStartH >= endHour) {
      return `${ruleName}: Buchungen sind nur zwischen ${startHour}:00 und ${endHour}:00 Uhr möglich. (Gewählte Zeit: ${startTime})`;
    }
    
    return null;
  };

  const validateAndShowErrors = useCallback(async (bookingData: BookingData): Promise<boolean> => {
    const result = await validateBooking(bookingData);
    
    if (!result.isValid) {
      result.errors.forEach(error => {
        toast({
          title: "Buchung nicht möglich",
          description: error,
          variant: "destructive"
        });
      });
      return false;
    }
    
    if (result.warnings.length > 0) {
      result.warnings.forEach(warning => {
        toast({
          title: "Warnung",
          description: warning,
          variant: "default"
        });
      });
    }
    
    return true;
  }, [validateBooking]);

  return {
    validateBooking,
    validateAndShowErrors,
    isValidating
  };
};