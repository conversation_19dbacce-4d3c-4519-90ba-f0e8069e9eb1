import { useState } from "react";
import { supabase } from "@/integrations/supabase/client";

interface WaitlistEntry {
  id: string;
  club_id: string;
  user_id: string;
  player_name: string;
  partner_name?: string;
  preferred_date: string;
  start_time_range: string;
  end_time_range: string;
  preferred_courts: string[];
  auto_booking_enabled: boolean;
  position: number;
  status: 'waiting' | 'converted_to_booking' | 'cancelled' | 'expired';
  created_at: string;
  updated_at: string;
}

interface AddWaitlistParams {
  clubId: string;
  preferredDate: Date;
  startTimeRange: string;
  endTimeRange: string;
  playerName: string;
  partnerName?: string | null;
  preferredCourts: string[];
  autoBookingEnabled: boolean;
}

interface Court {
  id: string;
  number: number;
  locked: boolean;
  lock_reason?: string;
}

export const useWaitlist = () => {
  const [isLoading, setIsLoading] = useState(false);

  const addToWaitlist = async (params: AddWaitlistParams): Promise<WaitlistEntry> => {
    setIsLoading(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error("Benutzer nicht authentifiziert");

      const { data, error } = await supabase
        .from('waitlist_entries')
        .insert({
          club_id: params.clubId,
          user_id: user.id,
          player_name: params.playerName,
          partner_name: params.partnerName,
          preferred_date: params.preferredDate.toISOString().split('T')[0],
          start_time_range: params.startTimeRange,
          end_time_range: params.endTimeRange,
          preferred_courts: params.preferredCourts,
          auto_booking_enabled: params.autoBookingEnabled,
          position: 0, // Will be overridden by trigger
          status: 'waiting' as const
        } as any)
        .select()
        .single();

      if (error) {
        if (error.message.includes('Auto-Zusage')) {
          throw new Error("Sie haben bereits eine aktive Auto-Zusage Warteliste.");
        }
        throw new Error(error.message);
      }

      // If auto-booking is enabled, trigger immediate check
      if (params.autoBookingEnabled) {
        try {
          await triggerAutoBookingCheck(params.clubId);
        } catch (autoBookingError) {
          console.warn('Auto-booking check failed after waitlist entry:', autoBookingError);
          // Don't fail the waitlist entry creation if auto-booking check fails
        }
      }

      return data as WaitlistEntry;
    } finally {
      setIsLoading(false);
    }
  };

  const getWaitlistEntries = async (clubId: string): Promise<WaitlistEntry[]> => {
    const { data, error } = await supabase
      .from('waitlist_entries')
      .select('*')
      .eq('club_id', clubId)
      .eq('status', 'waiting')
      .order('created_at', { ascending: true });

    if (error) throw new Error(error.message);
    return (data || []) as WaitlistEntry[];
  };

  const getUserWaitlistEntries = async (): Promise<WaitlistEntry[]> => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error("Benutzer nicht authentifiziert");

    const { data, error } = await supabase
      .from('waitlist_entries')
      .select('*')
      .eq('user_id', user.id)
      .eq('status', 'waiting')
      .order('created_at', { ascending: true });

    if (error) throw new Error(error.message);
    return (data || []) as WaitlistEntry[];
  };

  const cancelWaitlistEntry = async (entryId: string): Promise<void> => {
    setIsLoading(true);
    try {
      const { error } = await supabase
        .from('waitlist_entries')
        .update({ status: 'cancelled' })
        .eq('id', entryId);

      if (error) throw new Error(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const getCourts = async (clubId: string): Promise<Court[]> => {
    const { data, error } = await supabase
      .from('courts')
      .select('id, number, locked, lock_reason')
      .eq('club_id', clubId)
      .order('number', { ascending: true });

    if (error) throw new Error(error.message);
    return data || [];
  };

  const getWaitlistCount = async (
    clubId: string, 
    date: string, 
    startTime: string, 
    endTime: string,
    courtIds?: string[]
  ): Promise<number> => {
    let query = supabase
      .from('waitlist_entries')
      .select('id', { count: 'exact' })
      .eq('club_id', clubId)
      .eq('preferred_date', date)
      .eq('status', 'waiting')
      .lte('start_time_range', startTime)
      .gte('end_time_range', endTime);

    if (courtIds && courtIds.length > 0) {
      query = query.overlaps('preferred_courts', courtIds);
    }

    const { count, error } = await query;
    if (error) throw new Error(error.message);
    return count || 0;
  };

  const triggerAutoBookingCheck = async (clubId: string): Promise<void> => {
    setIsLoading(true);
    try {
      const { error } = await supabase.functions.invoke('waitlist-auto-checker', {
        body: { 
          action: 'check_all_pending_entries',
          club_id: clubId 
        }
      });

      if (error) throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    addToWaitlist,
    getWaitlistEntries,
    getUserWaitlistEntries,
    cancelWaitlistEntry,
    getCourts,
    getWaitlistCount,
    triggerAutoBookingCheck,
    isLoading
  };
};