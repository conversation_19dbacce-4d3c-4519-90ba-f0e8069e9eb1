import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useMetaAdminRole } from './useMetaAdminRole';
import { supabase } from '@/integrations/supabase/client';

export interface AdminCapabilities {
  // Regular admin capabilities (club-level)
  isClubAdmin: boolean;
  canManageMembers: boolean;
  canManageCourts: boolean;
  canViewReports: boolean;
  
  // Meta-admin capabilities (global system)
  isMetaAdmin: boolean;
  isSuperAdmin: boolean;
  canManageClubs: boolean;
  canManageDomains: boolean;
  canManageBilling: boolean;
  canAccessSupport: boolean;
  canModifySystem: boolean;
  
  // Combined admin status
  hasAnyAdminRole: boolean;
  isLoading: boolean;
  error: string | null;
}

export function useAdminRole(): AdminCapabilities {
  const { user } = useAuth();
  const { roles: metaRoles, isLoading: metaLoading, hasRole: hasMetaRole, canModify } = useMetaAdminRole();
  const [isClubAdmin, setIsClubAdmin] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!user) {
      setIsClubAdmin(false);
      setIsLoading(false);
      return;
    }

    const checkClubAdminRole = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Check both global admin role and club-specific admin role
        const [globalRoleResult, clubRoleResult] = await Promise.all([
          // Check global admin role
          supabase.rpc('has_role', { 
            _user_id: user.id, 
            _role: 'admin' 
          }),
          // Check club admin role in memberships
          supabase
            .from('club_memberships')
            .select('role')
            .eq('user_id', user.id)
            .eq('role', 'admin')
            .eq('is_active', true)
            .maybeSingle()
        ]);

        const hasGlobalAdmin = globalRoleResult.data || false;
        const hasClubAdmin = clubRoleResult.data !== null;
        
        console.log('🔍 useAdminRole: Admin check results:', {
          hasGlobalAdmin,
          hasClubAdmin,
          globalError: globalRoleResult.error,
          clubError: clubRoleResult.error
        });

        if (globalRoleResult.error && !globalRoleResult.error.message.includes('function "has_role" does not exist')) {
          throw globalRoleResult.error;
        }
        if (clubRoleResult.error) {
          throw clubRoleResult.error;
        }

        const isAdmin = hasGlobalAdmin || hasClubAdmin;
        setIsClubAdmin(isAdmin);
        console.log('🔍 useAdminRole: Final admin status:', isAdmin);
      } catch (err) {
        console.error('Error checking club admin role:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
        setIsClubAdmin(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkClubAdminRole();
  }, [user]);

  // Derived values
  const isMetaAdmin = metaRoles.length > 0;
  const isSuperAdmin = hasMetaRole('SUPER_ADMIN');
  const hasAnyAdminRole = isClubAdmin || isMetaAdmin;

  return {
    // Regular admin capabilities (SuperAdmin bypasses all checks)
    isClubAdmin: isClubAdmin || isSuperAdmin,
    canManageMembers: isClubAdmin || isSuperAdmin,
    canManageCourts: isClubAdmin || isSuperAdmin,
    canViewReports: isClubAdmin || isSuperAdmin,
    
    // Meta-admin capabilities
    isMetaAdmin,
    isSuperAdmin,
    canManageClubs: hasMetaRole('SUPER_ADMIN') || hasMetaRole('SUPPORT_ADMIN'),
    canManageDomains: hasMetaRole('SUPER_ADMIN') || hasMetaRole('SUPPORT_ADMIN'),
    canManageBilling: hasMetaRole('SUPER_ADMIN') || hasMetaRole('BILLING_ADMIN'),
    canAccessSupport: hasMetaRole('SUPER_ADMIN') || hasMetaRole('SUPPORT_ADMIN'),
    canModifySystem: canModify(),
    
    // Combined status
    hasAnyAdminRole,
    isLoading: isLoading || metaLoading,
    error,
  };
}