import { useClub } from '@/contexts/ClubContext';
import { resolveTimezone, timezoneService } from '@/services/TimezoneService';

/**
 * 🎯 SMART TIMEZONE HOOK: Intelligent timezone resolution with fallback hierarchy
 * 
 * Provides:
 * 1. Club-specific timezone (if available and valid)
 * 2. System default timezone (from environment config)
 * 3. Browser timezone (as final fallback)
 * 
 * This ensures consistent timezone handling across all components.
 */
export function useClubTimezone() {
  const { currentClub } = useClub();
  
  const resolvedTimezone = resolveTimezone(currentClub?.timezone);
  const isClubSpecific = !!currentClub?.timezone && currentClub.timezone === resolvedTimezone;
  
  return {
    timezone: resolvedTimezone,
    isClubTimezone: isClubSpecific,
    isSystemDefault: resolvedTimezone === timezoneService.getSystemDefaultTimezone(),
    isBrowserFallback: resolvedTimezone === timezoneService.getBrowserTimezone(),
    source: isClubSpecific ? 'club' : 
            resolvedTimezone === timezoneService.getSystemDefaultTimezone() ? 'system' : 'browser'
  };
}