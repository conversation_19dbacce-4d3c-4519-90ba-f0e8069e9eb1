import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";

interface CommunicationMessage {
  id: string;
  title: string;
  content: string;
  channel: string;
  type: string;
  priority: string;
  created_at: string;
  expires_at: string | null;
  read_by: any;
}

interface CommunicationStats {
  total_campaigns: number;
  total_emails_sent: number;
  total_push_sent: number;
  avg_engagement: number;
  by_channel: Record<string, any>;
}

export function useCommunication(clubId?: string) {
  const { toast } = useToast();
  const [messages, setMessages] = useState<CommunicationMessage[]>([]);
  const [stats, setStats] = useState<CommunicationStats | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Fetch dashboard messages for current user
  const fetchMessages = async () => {
    if (!clubId) return;

    try {
      const { data, error } = await supabase
        .from('communication_messages')
        .select('*')
        .eq('club_id', clubId)
        .in('channel', ['push', 'dashboard'])
        .order('created_at', { ascending: false })
        .limit(20);

      if (error) throw error;
      setMessages(data || []);
    } catch (error: any) {
      console.error("Error fetching messages:", error);
    }
  };

  // Fetch communication analytics
  const fetchStats = async (timeframe: 'week' | 'month' | 'year' = 'month') => {
    if (!clubId) return;

    setIsLoading(true);
    try {
      const { data, error } = await supabase.functions.invoke('communication-hub', {
        body: {
          type: 'get_analytics',
          data: { clubId, timeframe }
        }
      });

      if (error) throw error;
      setStats(data.analytics);
    } catch (error: any) {
      console.error("Error fetching stats:", error);
      toast({
        title: "Fehler",
        description: "Statistiken konnten nicht geladen werden",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Mark message as read
  const markAsRead = async (messageId: string, userId: string) => {
    try {
      const message = messages.find(m => m.id === messageId);
      if (!message) return;

      const readBy = typeof message.read_by === 'object' && message.read_by !== null ? message.read_by : {};
      const updatedReadBy = {
        ...readBy,
        [userId]: new Date().toISOString()
      };

      const { error } = await supabase
        .from('communication_messages')
        .update({ read_by: updatedReadBy })
        .eq('id', messageId);

      if (error) throw error;

      // Update local state
      setMessages(prev => 
        prev.map(m => 
          m.id === messageId 
            ? { ...m, read_by: updatedReadBy }
            : m
        )
      );
    } catch (error: any) {
      console.error("Error marking message as read:", error);
    }
  };

  // Send push notification
  const sendPushNotification = async (
    recipients: string[], 
    title: string, 
    content: string
  ) => {
    if (!clubId) return;

    try {
      const { data, error } = await supabase.functions.invoke('communication-hub', {
        body: {
          type: 'send_push',
          data: { recipients, title, content, clubId }
        }
      });

      if (error) throw error;

      toast({
        title: "Erfolg",
        description: "Push-Benachrichtigung gesendet",
      });

      // Refresh messages
      fetchMessages();
    } catch (error: any) {
      console.error("Error sending push notification:", error);
      toast({
        title: "Fehler",
        description: "Push-Benachrichtigung konnte nicht gesendet werden",
        variant: "destructive"
      });
    }
  };

  // Create smart group
  const createSmartGroup = async (name: string, criteria: any) => {
    if (!clubId) return;

    try {
      const { data, error } = await supabase.functions.invoke('communication-hub', {
        body: {
          type: 'create_smart_group',
          data: { name, criteria, clubId }
        }
      });

      if (error) throw error;

      toast({
        title: "Erfolg",
        description: `Smart Group "${name}" erstellt`,
      });

      return data.group;
    } catch (error: any) {
      console.error("Error creating smart group:", error);
      toast({
        title: "Fehler",
        description: "Smart Group konnte nicht erstellt werden",
        variant: "destructive"
      });
    }
  };

  // Set up real-time subscriptions
  useEffect(() => {
    if (!clubId) return;

    // Subscribe to new messages
    const messageChannel = supabase
      .channel('communication_messages')
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'communication_messages',
        filter: `club_id=eq.${clubId}`
      }, (payload) => {
        console.log("📨 New message received:", payload.new);
        setMessages(prev => [payload.new as CommunicationMessage, ...prev]);
        
        // Show toast for push notifications
        if (payload.new.channel === 'push') {
          toast({
            title: payload.new.title,
            description: payload.new.content,
          });
        }
      })
      .subscribe();

    return () => {
      messageChannel.unsubscribe();
    };
  }, [clubId, toast]);

  // Initial load
  useEffect(() => {
    if (clubId) {
      fetchMessages();
      fetchStats();
    }
  }, [clubId]);

  return {
    messages,
    stats,
    isLoading,
    fetchMessages,
    fetchStats,
    markAsRead,
    sendPushNotification,
    createSmartGroup
  };
}