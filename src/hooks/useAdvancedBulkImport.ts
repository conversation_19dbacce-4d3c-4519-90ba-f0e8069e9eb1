import { useState, useCallback, useMemo } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useTenant } from '@/contexts/TenantContext';
import { useCustomFields } from './useCustomFields';
import { useNameMapping } from './useNameMapping';
import { toast } from 'sonner';

export interface ParsedMember {
  id: string;
  originalData: Record<string, string>;
  mappedData: Record<string, any>;
  validationErrors: ValidationError[];
  validationWarnings: ValidationError[];
  duplicateMatches: DuplicateMatch[];
  confidenceScore: number;
  resolved: boolean;
  action: 'import' | 'merge' | 'skip';
  mergeTarget?: string;
}

export interface ValidationError {
  field: string;
  message: string;
  severity: 'error' | 'warning';
}

export interface DuplicateMatch {
  id: string;
  type: 'exact' | 'similar' | 'potential';
  field: string;
  existingMember: any;
  confidence: number;
}

export interface FieldMapping {
  csvColumn: string;
  targetField: string;
  confidence: number;
  isCustomField: boolean;
  required: boolean;
}

export interface ImportStats {
  total: number;
  imported: number;
  merged: number;
  skipped: number;
  errors: number;
}

const FIELD_PATTERNS = {
  email: /email|e-mail|mail/i,
  first_name: /first.?name|vorname|first|fname/i,
  last_name: /last.?name|nachname|surname|familienname|name(?!.*vorname)/i,
  phone: /phone|telefon|tel|mobile|handy|mobil/i,
  birth_date: /birth.?date|geboren|geburts|birthday|geburtsdatum/i,
  address_street: /street|straße|address|adresse/i,
  address_postal_code: /postal|zip|plz|postleitzahl/i,
  address_city: /city|stadt|ort/i,
  membership_type: /membership|mitgliedschaft|typ|gruppe|group/i,
  account_type: /account.?type|konto.?typ/i,
  title: /title|titel|anrede/i,
  role: /role|rolle|rechtestufe|permission|berechtigung/i,
  gender: /gender|geschlecht|sex|male|female|männlich|weiblich|diverse/i,
};

export const useAdvancedBulkImport = () => {
  const [csvData, setCsvData] = useState<string>('');
  const [headers, setHeaders] = useState<string[]>([]);
  const [rawRows, setRawRows] = useState<Record<string, string>[]>([]);
  const [fieldMappings, setFieldMappings] = useState<FieldMapping[]>([]);
  const [parsedMembers, setParsedMembers] = useState<ParsedMember[]>([]);
  const [existingMembers, setExistingMembers] = useState<any[]>([]);
  const [importProgress, setImportProgress] = useState(0);
  const [isImporting, setIsImporting] = useState(false);
  const [importStats, setImportStats] = useState<ImportStats | null>(null);
  const [currentStep, setCurrentStep] = useState<'upload' | 'mapping' | 'preview' | 'resolve' | 'import' | 'complete'>('upload');

  const { club } = useTenant();
  const { customFields, validateCustomData } = useCustomFields(club?.id);
  const { mapName, nameMapping } = useNameMapping();

  // Parse CSV data
  const parseCSV = useCallback((csvContent: string) => {
    const lines = csvContent.trim().split('\n').filter(line => line.trim());
    if (lines.length < 2) throw new Error('CSV muss mindestens eine Kopfzeile und eine Datenzeile enthalten');
    
    // Detect separator (comma or semicolon)
    const firstLine = lines[0];
    const separator = firstLine.includes(';') ? ';' : ',';
    console.log(`🔍 Detected CSV separator: "${separator}"`);
    
    const csvHeaders = firstLine.split(separator).map(h => h.trim().replace(/^["']|["']$/g, ''));
    const rows = lines.slice(1).map(line => {
      const values = line.split(separator).map(v => v.trim().replace(/^["']|["']$/g, ''));
      const row: Record<string, string> = {};
      csvHeaders.forEach((header, index) => {
        row[header] = values[index] || '';
      });
      return row;
    });

    console.log(`📊 Parsed CSV: ${csvHeaders.length} columns, ${rows.length} rows`);
    console.log(`📋 Headers:`, csvHeaders);

    setHeaders(csvHeaders);
    setRawRows(rows);
    setCsvData(csvContent);
    return { headers: csvHeaders, rows };
  }, []);

  // Smart field mapping
  const generateFieldMappings = useCallback((csvHeaders: string[]) => {
    console.log('🔍 Generating field mappings for headers:', csvHeaders);
    const mappings: FieldMapping[] = [];
    const targetFields = [
      'first_name', 'last_name', 'email', 'phone', 'birth_date',
      'address_street', 'address_postal_code', 'address_city',
      'membership_type', 'account_type', 'title', 'role', 'gender'
    ];

    // Add custom fields as potential targets
    const allTargetFields = [...targetFields, ...customFields.map(cf => cf.field_key)];
    console.log('📋 Available target fields:', allTargetFields);
    console.log('🔧 Custom fields available:', customFields);

    csvHeaders.forEach(csvColumn => {
      console.log(`🎯 Processing column: "${csvColumn}"`);
      let bestMatch: { field: string; confidence: number; isCustomField: boolean } | null = null;

      // Check against standard fields
      for (const targetField of targetFields) {
        const pattern = FIELD_PATTERNS[targetField as keyof typeof FIELD_PATTERNS];
        if (pattern && pattern.test(csvColumn)) {
          const confidence = 0.9;
          console.log(`✅ Pattern match for "${csvColumn}" -> "${targetField}" (confidence: ${confidence})`);
          if (!bestMatch || confidence > bestMatch.confidence) {
            bestMatch = { field: targetField, confidence, isCustomField: false };
          }
        } else {
          console.log(`❌ No pattern match for "${csvColumn}" against "${targetField}"`);
        }
      }

      // Check against custom fields
      for (const customField of customFields) {
        if (customField.field_name.toLowerCase().includes(csvColumn.toLowerCase()) ||
            csvColumn.toLowerCase().includes(customField.field_name.toLowerCase())) {
          const confidence = 0.8;
          if (!bestMatch || confidence > bestMatch.confidence) {
            bestMatch = { field: customField.field_key, confidence, isCustomField: true };
          }
        }
      }

      // Exact name match
      if (allTargetFields.includes(csvColumn)) {
        console.log(`🎯 Exact match for "${csvColumn}"`);
        bestMatch = { field: csvColumn, confidence: 1.0, isCustomField: customFields.some(cf => cf.field_key === csvColumn) };
      }

      if (bestMatch && bestMatch.confidence > 0.5) { // Lowered from 0.6 to 0.5 for more suggestions
        console.log(`✨ Final mapping for "${csvColumn}": ${bestMatch.field} (confidence: ${bestMatch.confidence})`);
        mappings.push({
          csvColumn,
          targetField: bestMatch.field,
          confidence: bestMatch.confidence,
          isCustomField: bestMatch.isCustomField,
          required: ['first_name', 'last_name', 'email', 'gender'].includes(bestMatch.field)
        });
      } else {
        console.log(`❌ No suitable mapping found for "${csvColumn}" (best confidence: ${bestMatch?.confidence || 0})`);
      }
    });

    console.log('🎉 Generated mappings:', mappings);
    setFieldMappings(mappings);
    return mappings;
  }, [customFields]);

  // Load existing members for duplicate detection
  const loadExistingMembers = useCallback(async () => {
    if (!club) return;

    const { data, error } = await supabase
      .from('club_memberships')
      .select('*')
      .eq('club_id', club.id)
      .eq('is_active', true);

    if (error) {
      console.error('Error loading existing members:', error);
      return;
    }

    setExistingMembers(data || []);
  }, [club]);

  // Detect duplicates
  const detectDuplicates = useCallback((memberData: Record<string, any>) => {
    const matches: DuplicateMatch[] = [];

    existingMembers.forEach(existingMember => {
      // Email exact match
      if (memberData.email && existingMember.email === memberData.email) {
        matches.push({
          id: existingMember.id,
          type: 'exact',
          field: 'email',
          existingMember,
          confidence: 1.0
        });
      }

      // Name similarity
      if (memberData.first_name && memberData.last_name) {
        const newName = `${memberData.first_name} ${memberData.last_name}`;
        const existingName = `${existingMember.first_name} ${existingMember.last_name}`;
        const mappedName = mapName(existingName);
        
        if (newName.toLowerCase() === existingName.toLowerCase() || 
            newName.toLowerCase() === mappedName.toLowerCase()) {
          matches.push({
            id: existingMember.id,
            type: 'exact',
            field: 'name',
            existingMember,
            confidence: 1.0
          });
        } else if (similarityScore(newName, existingName) > 0.8 || 
                   similarityScore(newName, mappedName) > 0.8) {
          matches.push({
            id: existingMember.id,
            type: 'similar',
            field: 'name',
            existingMember,
            confidence: Math.max(similarityScore(newName, existingName), similarityScore(newName, mappedName))
          });
        }
      }

      // Phone match
      if (memberData.phone && existingMember.phone && 
          normalizePhone(memberData.phone) === normalizePhone(existingMember.phone)) {
        matches.push({
          id: existingMember.id,
          type: 'exact',
          field: 'phone',
          existingMember,
          confidence: 0.9
        });
      }
    });

    return matches.sort((a, b) => b.confidence - a.confidence);
  }, [existingMembers, mapName]);

  // Validate member data
  const validateMemberData = useCallback((mappedData: Record<string, any>, originalData: Record<string, string>) => {
    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];

    // Required field validation with specific messages
    if (!mappedData.first_name) {
      errors.push({ 
        field: 'first_name', 
        message: `Vorname fehlt. Original-Zeile: "${Object.values(originalData).join('; ')}"`, 
        severity: 'error' 
      });
    }
    if (!mappedData.last_name) {
      errors.push({ 
        field: 'last_name', 
        message: `Nachname fehlt. Original-Zeile: "${Object.values(originalData).join('; ')}"`, 
        severity: 'error' 
      });
    }
    if (!mappedData.email) {
      errors.push({ 
        field: 'email', 
        message: `E-Mail fehlt. Name: "${mappedData.first_name || ''} ${mappedData.last_name || ''}"`, 
        severity: 'error' 
      });
    }
    if (!mappedData.gender) {
      errors.push({ 
        field: 'gender', 
        message: `Geschlecht fehlt. Name: "${mappedData.first_name || ''} ${mappedData.last_name || ''}"`, 
        severity: 'error' 
      });
    }

    // Gender validation
    if (mappedData.gender && !['male', 'female', 'diverse'].includes(mappedData.gender.toLowerCase())) {
      errors.push({ 
        field: 'gender', 
        message: `Ungültiges Geschlecht: "${mappedData.gender}". Gültige Werte: male, female, diverse`, 
        severity: 'error' 
      });
    }

    // Email format validation
    if (mappedData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(mappedData.email)) {
      errors.push({ 
        field: 'email', 
        message: `Ungültige E-Mail: "${mappedData.email}". Muss Format "<EMAIL>" haben.`, 
        severity: 'error' 
      });
    }

    // Date validation with specific format guidance
    if (mappedData.birth_date && !/^\d{4}-\d{2}-\d{2}$/.test(mappedData.birth_date)) {
      warnings.push({ 
        field: 'birth_date', 
        message: `Geburtsdatum "${mappedData.birth_date}" sollte Format YYYY-MM-DD haben (z.B. 1990-12-25)`, 
        severity: 'warning' 
      });
    }

    // Phone number validation
    if (mappedData.phone && mappedData.phone.length < 6) {
      warnings.push({ 
        field: 'phone', 
        message: `Telefonnummer "${mappedData.phone}" scheint zu kurz zu sein`, 
        severity: 'warning' 
      });
    }

    // Role validation
    if (mappedData.role && !['admin', 'member', 'trainer', 'guest'].includes(mappedData.role.toLowerCase())) {
      warnings.push({ 
        field: 'role', 
        message: `Unbekannte Rolle: "${mappedData.role}". Gültige Werte: admin, member, trainer, guest`, 
        severity: 'warning' 
      });
    }

    // Account type validation
    if (mappedData.account_type && !['Member', 'Guest', 'Trainer', 'Admin'].includes(mappedData.account_type)) {
      warnings.push({ 
        field: 'account_type', 
        message: `Unbekannter Kontotyp: "${mappedData.account_type}". Gültige Werte: Member, Guest, Trainer, Admin`, 
        severity: 'warning' 
      });
    }

    // Custom field validation with detailed errors
    try {
      const customValidation = validateCustomData(mappedData);
      if (customValidation?.errors && Array.isArray(customValidation.errors)) {
        customValidation.errors.forEach(error => {
          if (typeof error === 'string') {
            errors.push({ field: 'custom', message: `Benutzerdefiniertes Feld: ${error}`, severity: 'error' });
          } else if (error && typeof error === 'object') {
            errors.push({ 
              field: (error as any).field || 'custom', 
              message: `Benutzerdefiniertes Feld "${(error as any).field}": ${(error as any).message || 'Validation error'}`, 
              severity: 'error' 
            });
          }
        });
      }
    } catch (customError) {
      console.warn('Custom validation error:', customError);
    }

    return { errors, warnings };
  }, [validateCustomData]);

  // Process members with validation and duplicate detection
  const processMembers = useCallback(async () => {
    if (!club) return;

    await loadExistingMembers();

    const processed: ParsedMember[] = rawRows.map((rawData, index) => {
      // Map fields
      const mappedData: Record<string, any> = {};
      fieldMappings.forEach(mapping => {
        if (rawData[mapping.csvColumn]) {
          mappedData[mapping.targetField] = rawData[mapping.csvColumn];
        }
      });

      // Add club-specific data
      mappedData.club_id = club.id;
      mappedData.role = mappedData.account_type === 'admin' ? 'admin' : 'member';
      mappedData.is_active = true;

      // Validate
      const validation = validateMemberData(mappedData, rawData);
      
      // Detect duplicates
      const duplicates = detectDuplicates(mappedData);
      
      // Calculate confidence score
      const confidenceScore = calculateConfidenceScore(mappedData, validation, duplicates);

      return {
        id: `import-${index}`,
        originalData: rawData,
        mappedData,
        validationErrors: validation.errors,
        validationWarnings: validation.warnings,
        duplicateMatches: duplicates,
        confidenceScore,
        resolved: validation.errors.length === 0 && duplicates.length === 0,
        action: duplicates.length > 0 ? 'merge' : 'import',
        mergeTarget: duplicates.length > 0 ? duplicates[0].id : undefined
      };
    });

    setParsedMembers(processed);
    setCurrentStep('preview');
  }, [club, rawRows, fieldMappings, loadExistingMembers, validateMemberData, detectDuplicates]);

  // Execute import
  const executeImport = useCallback(async () => {
    if (!club) return;

    setIsImporting(true);
    setImportProgress(0);

    const stats: ImportStats = {
      total: parsedMembers.length,
      imported: 0,
      merged: 0,
      skipped: 0,
      errors: 0
    };

    for (let i = 0; i < parsedMembers.length; i++) {
      const member = parsedMembers[i];
      setImportProgress((i / parsedMembers.length) * 100);

      try {
        if (member.action === 'skip') {
          stats.skipped++;
        } else if (member.action === 'merge' && member.mergeTarget) {
          // Update existing member
          const { error } = await supabase
            .from('club_memberships')
            .update(member.mappedData)
            .eq('id', member.mergeTarget);

          if (error) throw error;
          stats.merged++;
        } else if (member.action === 'import') {
          // Create new member
          const membershipData = {
            ...member.mappedData,
            user_id: '00000000-0000-0000-0000-000000000000', // Placeholder
            club_id: club.id
          };
          
          const { error } = await supabase
            .from('club_memberships')
            .insert(membershipData);

          if (error) throw error;
          stats.imported++;
        }
      } catch (error) {
        console.error('Import error for member:', member, error);
        stats.errors++;
      }
    }

    setImportProgress(100);
    setImportStats(stats);
    setCurrentStep('complete');
    setIsImporting(false);

    toast.success(`Import abgeschlossen: ${stats.imported} importiert, ${stats.merged} zusammengeführt`);
  }, [parsedMembers, club]);

  // Reset function for clearing all data
  const resetAllData = useCallback(() => {
    setCsvData('');
    setHeaders([]);
    setRawRows([]);
    setFieldMappings([]);
    setParsedMembers([]);
    setExistingMembers([]);
    setImportProgress(0);
    setIsImporting(false);
    setImportStats(null);
    setCurrentStep('upload');
  }, []);

  return {
    // State
    csvData,
    headers,
    rawRows,
    fieldMappings,
    parsedMembers,
    importProgress,
    isImporting,
    importStats,
    currentStep,

    // Actions
    parseCSV,
    generateFieldMappings,
    setFieldMappings,
    processMembers,
    setParsedMembers,
    executeImport,
    setCurrentStep,
    resetAllData,

    // Computed
    isReadyForMapping: headers.length > 0,
    isReadyForPreview: fieldMappings.length > 0,
    isReadyForImport: parsedMembers.length > 0,
  };
};

// Helper functions
function similarityScore(str1: string, str2: string): number {
  const longer = str1.length > str2.length ? str1 : str2;
  const shorter = str1.length > str2.length ? str2 : str1;
  
  if (longer.length === 0) return 1.0;
  
  const editDistance = levenshteinDistance(longer.toLowerCase(), shorter.toLowerCase());
  return (longer.length - editDistance) / longer.length;
}

function levenshteinDistance(str1: string, str2: string): number {
  const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
  
  for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
  for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;
  
  for (let j = 1; j <= str2.length; j++) {
    for (let i = 1; i <= str1.length; i++) {
      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[j][i] = Math.min(
        matrix[j][i - 1] + 1,
        matrix[j - 1][i] + 1,
        matrix[j - 1][i - 1] + indicator
      );
    }
  }
  
  return matrix[str2.length][str1.length];
}

function normalizePhone(phone: string): string {
  return phone.replace(/\D/g, '');
}

function calculateConfidenceScore(
  mappedData: Record<string, any>, 
  validation: { errors: ValidationError[]; warnings: ValidationError[] },
  duplicates: DuplicateMatch[]
): number {
  let score = 1.0;
  
  // Penalize for validation errors
  score -= validation.errors.length * 0.3;
  score -= validation.warnings.length * 0.1;
  
  // Penalize for duplicates
  score -= duplicates.length * 0.2;
  
  // Bonus for complete data
  const completedFields = Object.values(mappedData).filter(v => v && v.toString().trim()).length;
  score += (completedFields / 10) * 0.1;
  
  return Math.max(0, Math.min(1, score));
}