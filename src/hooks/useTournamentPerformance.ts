import { useCallback, useRef, useMemo } from 'react';
import { useQueryClient } from '@tanstack/react-query';

interface ConflictResult {
  hasConflict: boolean;
  conflicts: any[];
}

export function useTournamentPerformance() {
  const queryClient = useQueryClient();
  const debounceRefs = useRef<Map<string, NodeJS.Timeout>>(new Map());

  // Debounced conflict checking
  const debouncedConflictCheck = useCallback(
    (
      key: string,
      checkFn: () => Promise<ConflictResult>,
      delay: number = 300
    ): Promise<ConflictResult> => {
      return new Promise((resolve) => {
        // Clear existing timeout for this key
        const existingTimeout = debounceRefs.current.get(key);
        if (existingTimeout) {
          clearTimeout(existingTimeout);
        }

        // Set new timeout
        const newTimeout = setTimeout(async () => {
          try {
            const result = await checkFn();
            resolve(result);
          } catch (error) {
            console.error('Conflict check error:', error);
            resolve({ hasConflict: false, conflicts: [] });
          }
          debounceRefs.current.delete(key);
        }, delay);

        debounceRefs.current.set(key, newTimeout);
      });
    },
    []
  );

  // Batch invalidate queries for performance
  const batchInvalidateQueries = useCallback(
    (queryKeys: string[][]) => {
      const promises = queryKeys.map(queryKey => 
        queryClient.invalidateQueries({ queryKey })
      );
      return Promise.all(promises);
    },
    [queryClient]
  );

  // Optimized data fetching with selective updates
  const optimizedUpdate = useCallback(
    async (updateFn: () => Promise<any>, relatedQueryKeys: string[][]) => {
      try {
        const result = await updateFn();
        // Only invalidate specific related queries, not all
        await batchInvalidateQueries(relatedQueryKeys);
        return result;
      } catch (error) {
        console.error('Optimized update failed:', error);
        throw error;
      }
    },
    [batchInvalidateQueries]
  );

  // Performance metrics tracking
  const performanceMetrics = useMemo(() => {
    const startTime = performance.now();
    
    return {
      measureOperation: (operationName: string) => {
        const start = performance.now();
        return {
          end: () => {
            const duration = performance.now() - start;
            console.debug(`${operationName} took ${duration.toFixed(2)}ms`);
            return duration;
          }
        };
      },
      getSessionTime: () => performance.now() - startTime
    };
  }, []);

  return {
    debouncedConflictCheck,
    batchInvalidateQueries,
    optimizedUpdate,
    performanceMetrics
  };
}