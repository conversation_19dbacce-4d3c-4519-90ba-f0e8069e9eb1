import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { SidebarProvider } from "@/components/ui/sidebar";
import { TenantProvider } from "@/contexts/TenantContext";
import { ClubProvider } from "@/contexts/ClubContext";
import { MockDataProvider } from "@/contexts/MockDataContext";
import Index from "./pages/Index";
import Auth from "./pages/Auth";
import Register from "./pages/Register";
import ClubRegister from "./pages/ClubRegister";
import ClubAuth from "./pages/club/ClubAuth";
import ClubRegisterPage from "./pages/club/ClubRegister";
import ClubLanding from "./pages/club/ClubLanding";
import NotFound from "./pages/NotFound";
import LandingPage from "./pages/LandingPage";
import { PublicTournamentView } from "./pages/PublicTournamentView";
import Impressum from "./pages/Impressum";
import AdminDashboardPage from "./pages/admin/AdminDashboardPage";
import AdminMembersPage from "./pages/admin/AdminMembersPage";
import AdminCourtsPage from "./pages/admin/AdminCourtsPage";
import AdminBookingsPage from "./pages/admin/AdminBookingsPage";
import AdminSettingsPage from "./pages/admin/AdminSettingsPage";
import AdminWorkServicesPage from "./pages/admin/AdminWorkServicesPage";
import AdminClubPage from "./pages/admin/AdminClubPage";
import SuperAdminDashboardPage from "./pages/admin/SuperAdminDashboardPage";
import { AdminRedirect } from "./components/AdminRedirect";
import MemberLayout from "./layouts/MemberLayout";
import TrainerLayout from "./layouts/TrainerLayout";
import GuestLayout from "./layouts/GuestLayout";
import MemberDashboardPage from "./pages/member/MemberDashboardPage";
import MemberBookingPage from "./pages/member/MemberBookingPage";
import MemberBookingsPage from "./pages/member/MemberBookingsPage";
import MemberProfilePage from "./pages/member/MemberProfilePage";
import MemberPaymentsPage from "./pages/member/MemberPaymentsPage";
import MemberTournamentsPage from "./pages/member/MemberTournamentsPage";
import MemberCoursesPage from "./pages/member/MemberCoursesPage";
import MemberStatsPage from "./pages/member/MemberStatsPage";
import MemberWorkServicePage from "./pages/member/MemberWorkServicePage";
import GuestDashboardPage from "./pages/guest/GuestDashboardPage";
import GuestBookingPage from "./pages/guest/GuestBookingPage";
import GuestInfoPage from "./pages/guest/GuestInfoPage";
import GuestProfilePage from "./pages/guest/GuestProfilePage";
import TrainerDashboardPage from "./pages/trainer/TrainerDashboardPage";
import TrainerCoursesPage from "./pages/trainer/TrainerCoursesPage";
import TrainerParticipantsPage from "./pages/trainer/TrainerParticipantsPage";
import TrainerTournamentsPage from "./pages/trainer/TrainerTournamentsPage";
import TrainerBookingPage from "./pages/trainer/TrainerBookingPage";
import TrainerReportsPage from "./pages/trainer/TrainerReportsPage";
import MetaAdminDashboardPage from "./pages/meta-admin/MetaAdminDashboardPage";
import MetaAdminClubsPage from "./pages/meta-admin/MetaAdminClubsPage";
import MetaAdminUsersPage from "./pages/meta-admin/MetaAdminUsersPage";
import MetaAdminDomainsPage from "./pages/meta-admin/MetaAdminDomainsPage";
import MetaAdminBillingPage from "./pages/meta-admin/MetaAdminBillingPage";
import MetaAdminSupportPage from "./pages/meta-admin/MetaAdminSupportPage";
import MetaAdminContentPage from "./pages/meta-admin/MetaAdminContentPage";
import MetaAdminMonitoringPage from "./pages/meta-admin/MetaAdminMonitoringPage";
import MetaAdminSettingsPage from "./pages/meta-admin/MetaAdminSettingsPage";
import MetaAdminTournamentsPage from "./pages/meta-admin/MetaAdminTournamentsPage";
import AdminTournamentsPage from "./pages/admin/AdminTournamentsPage";
import AdminCommunicationPage from "./pages/admin/AdminCommunicationPage";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <TenantProvider>
          <ClubProvider>
            <MockDataProvider>
            <Routes>
          {/* Marketing Landing Page */}
          <Route path="/" element={<LandingPage />} />
          
          {/* Authentication and registration */}
          <Route path="/auth" element={<Auth />} />
          <Route path="/login" element={<Auth />} />
          <Route path="/register" element={<Register />} />
          <Route path="/club-register" element={<ClubRegister />} />
          
          {/* Redirect old admin routes to meta-admin */}
          <Route path="/admin" element={<AdminRedirect />} />
          <Route path="/admin/*" element={<AdminRedirect />} />

          {/* Meta Admin Routes */}
        <Route path="/meta-admin" element={<MetaAdminDashboardPage />} />
        <Route path="/meta-admin/clubs" element={<MetaAdminClubsPage />} />
        <Route path="/meta-admin/clubs/new" element={<MetaAdminClubsPage />} />
        <Route path="/meta-admin/users" element={<MetaAdminUsersPage />} />
        <Route path="/meta-admin/users/meta-admins" element={<MetaAdminUsersPage />} />
        <Route path="/meta-admin/domains" element={<MetaAdminDomainsPage />} />
        <Route path="/meta-admin/billing/plans" element={<MetaAdminBillingPage />} />
        <Route path="/meta-admin/billing/subscriptions" element={<MetaAdminBillingPage />} />
        <Route path="/meta-admin/billing/invoices" element={<MetaAdminBillingPage />} />
        <Route path="/meta-admin/content/email-templates" element={<MetaAdminContentPage />} />
        <Route path="/meta-admin/content/branding" element={<MetaAdminContentPage />} />
        <Route path="/meta-admin/monitoring/logs" element={<MetaAdminMonitoringPage />} />
        <Route path="/meta-admin/monitoring/performance" element={<MetaAdminMonitoringPage />} />
        <Route path="/meta-admin/support/tickets" element={<MetaAdminSupportPage />} />
        <Route path="/meta-admin/tournaments" element={<MetaAdminTournamentsPage />} />
        <Route path="/meta-admin/settings" element={<MetaAdminSettingsPage />} />

          {/* Club-specific routes only */}
          <Route path="/c/:clubSlug" element={<ClubLanding />} />
          <Route path="/c/:clubSlug/auth" element={<ClubAuth />} />
          <Route path="/c/:clubSlug/register" element={<ClubRegisterPage />} />
          <Route path="/c/:clubSlug/admin" element={<AdminDashboardPage />} />
          <Route path="/c/:clubSlug/admin/members" element={<AdminMembersPage />} />
          <Route path="/c/:clubSlug/admin/courts" element={<AdminCourtsPage />} />
          <Route path="/c/:clubSlug/admin/bookings" element={<AdminBookingsPage />} />
          <Route path="/c/:clubSlug/admin/management" element={<AdminClubPage />} />
          <Route path="/c/:clubSlug/admin/work-services" element={<AdminWorkServicesPage />} />
          <Route path="/c/:clubSlug/admin/tournaments" element={<AdminTournamentsPage />} />
          <Route path="/c/:clubSlug/admin/communication" element={<AdminCommunicationPage />} />
          <Route path="/c/:clubSlug/admin/settings" element={<AdminSettingsPage />} />
          
          <Route path="/c/:clubSlug/member" element={<MemberLayout />}>
            <Route index element={<MemberDashboardPage />} />
            <Route path="booking" element={<MemberBookingPage />} />
            <Route path="bookings" element={<MemberBookingsPage />} />
            <Route path="statistiken" element={<MemberStatsPage />} />
            <Route path="profile" element={<MemberProfilePage />} />
            <Route path="payments" element={<MemberPaymentsPage />} />
            <Route path="tournaments" element={<MemberTournamentsPage />} />
            <Route path="courses" element={<MemberCoursesPage />} />
            <Route path="work-service" element={<MemberWorkServicePage />} />
          </Route>

          <Route path="/c/:clubSlug/trainer" element={<TrainerLayout />}>
            <Route index element={<TrainerDashboardPage />} />
            <Route path="courses" element={<TrainerCoursesPage />} />
            <Route path="participants" element={<TrainerParticipantsPage />} />
            <Route path="tournaments" element={<TrainerTournamentsPage />} />
            <Route path="booking" element={<TrainerBookingPage />} />
            <Route path="reports" element={<TrainerReportsPage />} />
          </Route>

          <Route path="/c/:clubSlug/guest" element={<GuestLayout />}>
            <Route index element={<GuestDashboardPage />} />
            <Route path="booking" element={<GuestBookingPage />} />
            <Route path="profile" element={<GuestProfilePage />} />
            <Route path="info" element={<GuestInfoPage />} />
          </Route>
          
          {/* Public Tournament View */}
          <Route path="/tournament/:tournamentId" element={<PublicTournamentView />} />
          
          {/* Legal Pages */}
          <Route path="/impressum" element={<Impressum />} />
          
          {/* Catch-all route */}
          <Route path="*" element={<NotFound />} />
            </Routes>
          </MockDataProvider>
          </ClubProvider>
        </TenantProvider>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
