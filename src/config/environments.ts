export type Environment = 'development' | 'test' | 'production';

export interface EnvironmentConfig {
  supabaseUrl: string;
  supabaseAnonKey: string;
  environment: Environment;
  debug: boolean;
  apiBaseUrl: string;
  timezone: string;
  locale: string;
}

const environments: Record<Environment, EnvironmentConfig> = {
  development: {
    supabaseUrl: "https://qotcxsnnuzaupxqjihsw.supabase.co",
    supabaseAnonKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFvdGN4c25udXphdXB4cWppaHN3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ0ODI0MjcsImV4cCI6MjA3MDA1ODQyN30.CwbwglK7q8A9Xctxm9OGv4Cyp-441uYAOXRx_TM6rTM",
    environment: 'development',
    debug: true,
    apiBaseUrl: '/api',
    timezone: 'Europe/Berlin',
    locale: 'de-DE'
  },
  test: {
    supabaseUrl: "REPLACE_WITH_TEST_SUPABASE_URL",
    supabaseAnonKey: "REPLACE_WITH_TEST_SUPABASE_ANON_KEY",
    environment: 'test',
    debug: true,
    apiBaseUrl: '/api',
    timezone: 'Europe/Berlin',
    locale: 'de-DE'
  },
  production: {
    supabaseUrl: "https://qotcxsnnuzaupxqjihsw.supabase.co",
    supabaseAnonKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFvdGN4c25udXphdXB4cWppaHN3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ0ODI0MjcsImV4cCI6MjA3MDA1ODQyN30.CwbwglK7q8A9Xctxm9OGv4Cyp-441uYAOXRx_TM6rTM",
    environment: 'production',
    debug: false,
    apiBaseUrl: '/api',
    timezone: 'Europe/Berlin',
    locale: 'de-DE'
  }
};

// Determine current environment based on hostname or other factors
function getCurrentEnvironment(): Environment {
  if (typeof window === 'undefined') return 'development';
  
  const hostname = window.location.hostname;
  
  // Treat Lovable project domains as development - IMPORTANT: includes all Lovable domains!
  if (hostname.includes('localhost') || 
      hostname.includes('127.0.0.1') || 
      hostname.includes('lovableproject.com') ||
      hostname.includes('lovable.app') ||
      hostname.includes('sandbox.lovable.dev')) {  // This fixes the sandbox preview issue!
    return 'development';
  }
  
  if (hostname.includes('test') || hostname.includes('staging')) {
    return 'test';
  }
  
  // Production domains - courtwaive.de and its subdomains
  if (hostname === 'courtwaive.de' || hostname.endsWith('.courtwaive.de')) {
    return 'production';
  }
  
  return 'production';
}

export const currentEnvironment = getCurrentEnvironment();
export const config = environments[currentEnvironment];

export function getEnvironmentConfig(env?: Environment): EnvironmentConfig {
  return environments[env || currentEnvironment];
}