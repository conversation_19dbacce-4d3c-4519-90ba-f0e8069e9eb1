import { supabase } from '@/integrations/supabase/client';
import { tenantResolver } from '@/lib/tenant';

/**
 * 🏢 MULTI-TENANT SERVICE: Zentrale Verwaltung der Mandantenfähigkeit
 * 
 * Stellt sicher, dass alle Datenbankoperationen club-spezifisch sind
 * und verhindert Cross-Tenant-Datenlecks.
 */
export class MultiTenantService {
  private static instance: MultiTenantService;
  
  private constructor() {}
  
  static getInstance(): MultiTenantService {
    if (!MultiTenantService.instance) {
      MultiTenantService.instance = new MultiTenantService();
    }
    return MultiTenantService.instance;
  }

  /**
   * Fügt automatisch club_id Filter zu Supabase Queries hinzu
   */
  async withClubFilter<T>(query: any): Promise<T> {
    const club = tenantResolver.getCurrentClub();
    if (!club) {
      throw new Error('Kein Club-Kontext verfügbar. Multi-Tenant-Operation fehlgeschlagen.');
    }
    
    return query.eq('club_id', club.id);
  }

  /**
   * Erstellt Club-spezifische Supabase Query Builder
   */
  getClubQuery(table: string) {
    const club = tenantResolver.getCurrentClub();
    if (!club) {
      throw new Error(`Kein Club-Kontext für Tabelle ${table} verfügbar.`);
    }
    
    return supabase
      .from(table as any)
      .select('*')
      .eq('club_id', club.id);
  }

  /**
   * Fügt club_id zu Insert-Daten hinzu
   */
  addClubId<T extends Record<string, any>>(data: T): T & { club_id: string } {
    const club = tenantResolver.getCurrentClub();
    if (!club) {
      throw new Error('Kein Club-Kontext für Insert-Operation verfügbar.');
    }
    
    return {
      ...data,
      club_id: club.id
    };
  }

  /**
   * Validiert ob eine Ressource zum aktuellen Club gehört
   */
  async validateClubAccess(table: string, resourceId: string): Promise<boolean> {
    const club = tenantResolver.getCurrentClub();
    if (!club) return false;
    
    const { data, error } = await supabase
      .from(table as any)
      .select('club_id')
      .eq('id', resourceId)
      .single();
    
    if (error || !data) return false;
    
    return (data as any).club_id === club.id;
  }

  /**
   * Gibt den aktuellen Club zurück oder wirft Fehler
   */
  getCurrentClubOrThrow() {
    const club = tenantResolver.getCurrentClub();
    if (!club) {
      throw new Error('Multi-Tenant-Fehler: Kein Club-Kontext verfügbar.');
    }
    return club;
  }

  /**
   * Prüft ob aktueller Club aktiv ist
   */
  isCurrentClubActive(): boolean {
    const club = tenantResolver.getCurrentClub();
    return club?.is_active === true;
  }

  /**
   * Erstellt Club-spezifische URL für Navigation
   */
  buildClubUrl(path: string): string {
    return tenantResolver.buildClubUrl(path);
  }

  /**
   * Gibt Club-spezifische Einstellungen zurück
   */
  getClubSettings<T = any>(): T | null {
    const club = tenantResolver.getCurrentClub();
    return club?.settings || null;
  }
}

export const multiTenantService = MultiTenantService.getInstance();

// Utility Hooks für React Components
export function useMultiTenant() {
  return {
    service: multiTenantService,
    currentClub: tenantResolver.getCurrentClub(),
    isClubActive: multiTenantService.isCurrentClubActive(),
    buildClubUrl: multiTenantService.buildClubUrl.bind(multiTenantService),
    addClubId: multiTenantService.addClubId.bind(multiTenantService),
    withClubFilter: multiTenantService.withClubFilter.bind(multiTenantService)
  };
}