import { config } from '@/config/environments';

/**
 * 🌍 CRITICAL: Smart Timezone Management Service
 * 
 * Provides intelligent timezone resolution with fallback hierarchy:
 * 1. Club-specific timezone (if available)
 * 2. System default timezone (from environment config)
 * 3. Browser/User timezone (as final fallback)
 * 
 * This service ensures consistent timezone handling across the entire application
 * while maintaining club-specific customization capabilities.
 */
export class TimezoneService {
  private static instance: TimezoneService;
  private browserTimezone: string;

  private constructor() {
    // Detect browser timezone once on initialization
    this.browserTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  }

  public static getInstance(): TimezoneService {
    if (!TimezoneService.instance) {
      TimezoneService.instance = new TimezoneService();
    }
    return TimezoneService.instance;
  }

  /**
   * Get the system default timezone from environment configuration
   * 🔒 KEEP HARDCODED: This is system-level configuration for stability
   */
  public getSystemDefaultTimezone(): string {
    return config.timezone; // 'Europe/Berlin' from environments.ts
  }

  /**
   * Get browser/user detected timezone
   */
  public getBrowserTimezone(): string {
    return this.browserTimezone;
  }

  /**
   * 🎯 SMART RESOLUTION: Get the best timezone for a given club
   * Implements fallback hierarchy: Club → System → Browser
   */
  public resolveClubTimezone(clubTimezone?: string | null): string {
    // 1. Priority: Club-specific timezone
    if (clubTimezone && this.isValidTimezone(clubTimezone)) {
      return clubTimezone;
    }

    // 2. Fallback: System default timezone
    const systemDefault = this.getSystemDefaultTimezone();
    if (this.isValidTimezone(systemDefault)) {
      return systemDefault;
    }

    // 3. Final fallback: Browser timezone
    return this.getBrowserTimezone();
  }

  /**
   * Get all available timezones with human-readable labels
   * 🔒 KEEP STABLE: Common timezone options for UI components
   */
  public getTimezoneOptions(): Array<{ value: string; label: string }> {
    return [
      { value: "Europe/Berlin", label: "Europe/Berlin (CET/CEST)" },
      { value: "Europe/Vienna", label: "Europe/Vienna (CET/CEST)" },
      { value: "Europe/Zurich", label: "Europe/Zurich (CET/CEST)" },
      { value: "Europe/Paris", label: "Europe/Paris (CET/CEST)" },
      { value: "Europe/London", label: "Europe/London (GMT/BST)" },
      { value: "Europe/Amsterdam", label: "Europe/Amsterdam (CET/CEST)" },
      { value: "Europe/Rome", label: "Europe/Rome (CET/CEST)" },
      { value: "Europe/Madrid", label: "Europe/Madrid (CET/CEST)" },
      { value: "UTC", label: "UTC (Coordinated Universal Time)" },
    ];
  }

  /**
   * Validate if a timezone string is valid
   */
  private isValidTimezone(timezone: string): boolean {
    try {
      Intl.DateTimeFormat(undefined, { timeZone: timezone });
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Format current time in a specific timezone
   */
  public formatCurrentTimeInTimezone(timezone: string): string {
    try {
      return new Date().toLocaleTimeString('de-DE', {
        timeZone: timezone,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch {
      return new Date().toLocaleTimeString('de-DE');
    }
  }

  /**
   * Get timezone display name
   */
  public getTimezoneDisplayName(timezone: string): string {
    const option = this.getTimezoneOptions().find(opt => opt.value === timezone);
    return option?.label || timezone;
  }
}

// Export singleton instance
export const timezoneService = TimezoneService.getInstance();

// Export convenience functions for common use cases
export const getSystemTimezone = () => timezoneService.getSystemDefaultTimezone();
export const resolveTimezone = (clubTimezone?: string | null) => timezoneService.resolveClubTimezone(clubTimezone);
export const getTimezoneOptions = () => timezoneService.getTimezoneOptions();