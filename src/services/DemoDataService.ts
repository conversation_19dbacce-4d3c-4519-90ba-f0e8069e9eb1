import { config } from '@/config/environments';

/**
 * 🎭 DEMO DATA SERVICE: Centralized management of development and test data
 * 
 * Provides environment-aware demo data and ensures separation between
 * development/testing data and production environments.
 * 
 * This service replaces scattered hardcoded demo values throughout the codebase
 * with centralized, environment-aware data management.
 */
export class DemoDataService {
  private static instance: DemoDataService;

  private constructor() {}

  public static getInstance(): DemoDataService {
    if (!DemoDataService.instance) {
      DemoDataService.instance = new DemoDataService();
    }
    return DemoDataService.instance;
  }

  /**
   * Check if we're in a development/test environment where demo data should be shown
   */
  public isDemoEnvironment(): boolean {
    return config.debug; // true for development/test, false for production
  }

  /**
   * Get demo club names for development/testing
   * 🔒 KEEP CENTRALIZED: This prevents scattered demo club references
   */
  public getDemoClubNames(): string[] {
    if (!this.isDemoEnvironment()) {
      return []; // No demo data in production
    }

    return [
      'Demo Tennis Club',
      'Test Vereinshaus',
      'Development Sports Club',
      'Sample Tennis Academy',
      'Example Racquet Club'
    ];
  }

  /**
   * Get demo member data for UI development
   */
  public getDemoMembers() {
    if (!this.isDemoEnvironment()) {
      return [];
    }

    return [
      {
        id: 'demo-member-1',
        name: 'Max Mustermann',
        email: '<EMAIL>',
        role: 'member',
        status: 'active'
      },
      {
        id: 'demo-member-2', 
        name: 'Anna Beispiel',
        email: '<EMAIL>',
        role: 'admin',
        status: 'active'
      },
      {
        id: 'demo-member-3',
        name: 'Demo Trainer',
        email: '<EMAIL>', 
        role: 'trainer',
        status: 'active'
      }
    ];
  }

  /**
   * Get demo tournament data
   */
  public getDemoTournaments() {
    if (!this.isDemoEnvironment()) {
      return [];
    }

    return [
      {
        id: 'demo-tournament-1',
        name: 'Sommerturnier 2024',
        date: new Date('2024-07-15'),
        status: 'upcoming',
        participants: 24
      },
      {
        id: 'demo-tournament-2',
        name: 'Vereinsmeisterschaft',
        date: new Date('2024-09-01'),
        status: 'registration',
        participants: 16
      }
    ];
  }

  /**
   * Get demo communication statistics
   */
  public getDemoCommunicationStats() {
    if (!this.isDemoEnvironment()) {
      return null;
    }

    return {
      emails_sent: 1247,
      push_notifications: 342,
      campaigns: 18,
      engagement_rate: 73.2
    };
  }

  /**
   * Get demo target groups for communication
   */
  public getDemoTargetGroups() {
    if (!this.isDemoEnvironment()) {
      return [];
    }

    return [
      { id: 'all_members', label: 'Alle Mitglieder', count: 247 },
      { id: 'active_players', label: 'Aktive Spieler', count: 189 },
      { id: 'tournament_players', label: 'Turnierspieler', count: 73 },
      { id: 'junior_members', label: 'Jugendliche', count: 54 },
      { id: 'board_members', label: 'Vorstand', count: 8 }
    ];
  }

  /**
   * Get demo campaign suggestions
   */
  public getDemoCampaignSuggestions() {
    if (!this.isDemoEnvironment()) {
      return [];
    }

    return [
      {
        id: 1,
        category: "Engagement",
        title: "Inaktive Mitglieder reaktivieren",
        description: "23 Mitglieder haben seit 3 Monaten nicht gebucht",
        action: "Personalisierte Re-Engagement Kampagne erstellen",
        priority: "high"
      },
      {
        id: 2,
        category: "Timing",
        title: "Optimaler Versandzeitpunkt",
        description: "E-Mails um 18:30 haben 40% höhere Öffnungsrate",
        action: "Zukünftige Kampagnen automatisch optimieren",
        priority: "medium"
      }
    ];
  }

  /**
   * Check if a given club ID or name appears to be demo/test data
   */
  public isDemoClub(clubIdOrName: string): boolean {
    if (!clubIdOrName) return false;
    
    const demoIdentifiers = [
      'demo',
      'test', 
      'example',
      'sample',
      'development',
      'current-club-id' // Legacy hardcoded ID
    ];

    const lowerInput = clubIdOrName.toLowerCase();
    return demoIdentifiers.some(identifier => lowerInput.includes(identifier));
  }

  /**
   * Get environment-appropriate placeholder text
   */
  public getPlaceholderText(type: 'club' | 'member' | 'email' | 'generic'): string {
    const placeholders = {
      club: this.isDemoEnvironment() ? 'Demo Tennis Club' : 'Ihr Vereinsname',
      member: this.isDemoEnvironment() ? 'Max Mustermann' : 'Mitgliedername',
      email: this.isDemoEnvironment() ? '<EMAIL>' : '<EMAIL>',
      generic: this.isDemoEnvironment() ? 'Demo Wert' : 'Ihr Wert'
    };

    return placeholders[type] || placeholders.generic;
  }

  /**
   * Sanitize data for production (remove demo indicators)
   */
  public sanitizeForProduction<T extends Record<string, any>>(data: T): T {
    if (this.isDemoEnvironment()) {
      return data; // Keep as is in development
    }

    // In production, filter out obvious demo data
    const sanitized = { ...data };
    
    Object.keys(sanitized).forEach(key => {
      const value = sanitized[key];
      if (typeof value === 'string' && this.isDemoClub(value)) {
        delete sanitized[key];
      }
    });

    return sanitized;
  }
}

// Export singleton instance
export const demoDataService = DemoDataService.getInstance();

// Export convenience functions
export const isDemoEnvironment = () => demoDataService.isDemoEnvironment();
export const getDemoTargetGroups = () => demoDataService.getDemoTargetGroups();
export const getDemoCommunicationStats = () => demoDataService.getDemoCommunicationStats();