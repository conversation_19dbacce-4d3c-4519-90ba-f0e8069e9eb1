@tailwind base;
@tailwind components;
@tailwind utilities;

/* TennisClub Booker Design System - Tennis-inspired colors and styling
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    
    /* Tennis-themed color palette */
    --tennis-green: 142 69% 32%;
    --tennis-green-light: 142 50% 45%;
    --tennis-green-dark: 142 80% 25%;
    --court-clay: 25 85% 65%;
    --court-white: 0 0% 98%;
    --net-gray: 220 13% 91%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: var(--tennis-green);
    --primary-foreground: 0 0% 98%;

    --secondary: var(--court-white);
    --secondary-foreground: var(--tennis-green);

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: var(--tennis-green-light);
    --accent-foreground: 0 0% 98%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: var(--net-gray);
    --input: var(--net-gray);
    --ring: var(--tennis-green);
    
    /* Custom gradients and effects */
    --gradient-tennis: linear-gradient(135deg, hsl(var(--tennis-green)), hsl(var(--tennis-green-light)));
    --gradient-court: linear-gradient(180deg, hsl(var(--court-white)), hsl(var(--background)));
    --shadow-tennis: 0 10px 30px -10px hsl(var(--tennis-green) / 0.3);
    --shadow-card: 0 4px 20px -4px hsl(222.2 84% 4.9% / 0.1);
    
    /* Animations */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* Marketing Design System Colors - Modern Clean Style */
    --brand: 140 60% 35%; /* Court Green */
    --brand-light: 140 50% 45%;
    --accent-purple: 259 100% 65%; /* Modern purple accent */
    --accent-blue: 220 90% 60%; /* Modern blue accent */
    --fg-strong: 0 0% 4%; /* #0A0A0A */
    --fg-muted: 0 0% 45%; /* #737373 */
    --fg-light: 0 0% 60%; /* #999999 */
    --bg: 0 0% 100%; /* #FFFFFF */
    --bg-alt: 210 17% 98%; /* #FAFBFC */
    --bg-subtle: 210 20% 96%; /* #F4F5F7 */

    /* Marketing Typography & Spacing */
    --marketing-h1: 4rem; /* 64px */
    --marketing-h2: 2.75rem; /* 44px */
    --marketing-h3: 1.875rem; /* 30px */
    --marketing-body: 1.125rem; /* 18px */
    --marketing-body-sm: 1rem; /* 16px */
    --marketing-micro: 0.875rem; /* 14px */

    /* Enhanced Background Colors for Glassmorphism */
    --bg-gradient: linear-gradient(135deg, hsl(210 40% 98%), hsl(220 60% 97%));
    --bg-glass: hsl(0 0% 100% / 0.7);
    --bg-glass-alt: hsl(210 40% 98% / 0.8);
    --bg-glass-subtle: hsl(220 60% 97% / 0.6);

    /* Marketing Gradients */
    --gradient-subtle: linear-gradient(180deg, hsl(var(--bg)), hsl(var(--bg-alt)));
    --gradient-brand: linear-gradient(135deg, hsl(var(--brand)), hsl(var(--brand-light)));
    --gradient-purple: linear-gradient(135deg, hsl(var(--accent-purple)), hsl(var(--accent-blue)));
    --gradient-hero: linear-gradient(135deg, hsl(210 60% 97%), hsl(220 70% 96%));
    --gradient-section: linear-gradient(135deg, hsl(210 40% 99%), hsl(220 60% 98%));

    /* Marketing Shadows */
    --shadow-elegant: 0 10px 30px -10px hsl(var(--brand) / 0.15);
    --shadow-card: 0 4px 24px -4px hsl(220 15% 15% / 0.08);
    --shadow-button: 0 4px 12px -2px hsl(var(--accent-purple) / 0.3);
    --shadow-glass: 0 8px 32px 0 hsl(220 20% 20% / 0.1);

    /* Enhanced Glassmorphism Effects */
    --glass-border: hsl(0 0% 100% / 0.18);
    --glass-backdrop: blur(10px);
    --glass-bg: hsl(0 0% 100% / 0.25);
    --glass-bg-strong: hsl(0 0% 100% / 0.4);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Info semantic color (blue for highlights) */
    --info: 225 73% 57%;
    --info-foreground: 0 0% 100%;
  }

  /* Courtwaive AI Theme - Gradient Enhanced */
  .theme-courtwaive-ai {
    --primary: 240 90% 55% !important;
    --primary-foreground: 0 0% 100% !important;
    --secondary: 240 20% 92% !important;
    --secondary-foreground: 240 60% 25% !important;
    --accent: 260 70% 60% !important;
    --accent-foreground: 0 0% 100% !important;
    --muted: 240 30% 95% !important;
    --muted-foreground: 240 10% 40% !important;
    --border: 240 30% 85% !important;
    --ring: 240 90% 55% !important;
    --sidebar-primary: 240 90% 55% !important;
    --sidebar-accent: 240 30% 95% !important;
    --gradient-tennis: linear-gradient(135deg, hsl(240, 90%, 55%), hsl(220, 85%, 50%)) !important;
    --gradient-primary: linear-gradient(135deg, hsl(240, 90%, 55%), hsl(260, 70%, 60%)) !important;
    --gradient-secondary: linear-gradient(135deg, hsl(220, 85%, 50%), hsl(240, 80%, 55%)) !important;
    --gradient-accent: linear-gradient(135deg, hsl(260, 70%, 60%), hsl(280, 65%, 65%)) !important;
    --gradient-subtle: linear-gradient(135deg, hsl(240, 30%, 98%), hsl(220, 40%, 97%)) !important;
    --shadow-tennis: 0 10px 30px -10px hsl(240 90% 55% / 0.3) !important;
    --shadow-glow: 0 0 40px hsl(240 90% 55% / 0.2) !important;
  }

  /* Light button styling for Courtwaive AI theme */
  .theme-courtwaive-ai .bg-primary {
    background: linear-gradient(135deg, hsl(240, 21%, 97%), hsl(260, 18%, 96%)) !important;
    color: hsl(240, 90%, 55%) !important;
    border: 1px solid hsl(240, 90%, 55%) !important;
    box-shadow: 0 0 0 1px hsl(240 90% 55% / 0.2), 0 3px 8px hsl(240 90% 55% / 0.14) !important;
  }
  
  .theme-courtwaive-ai .bg-primary:hover {
    background: linear-gradient(135deg, hsl(240, 25%, 95%), hsl(260, 21%, 94%)) !important;
    box-shadow: 0 0 0 2px hsl(240 90% 55% / 0.28), 0 4px 11px hsl(240 90% 55% / 0.21) !important;
  }
  
  .theme-courtwaive-ai .bg-secondary {
    background: hsl(240, 15%, 98%) !important;
    color: hsl(240, 60%, 25%) !important;
    border: 1px solid hsl(240, 30%, 85%) !important;
    box-shadow: 0 0 0 1px hsl(240 30% 85% / 0.5) !important;
  }
  
  .theme-courtwaive-ai .bg-secondary:hover {
    background: hsl(240, 15%, 96%) !important;
    box-shadow: 0 0 0 2px hsl(240 30% 85% / 0.7) !important;
  }
  
  .theme-courtwaive-ai .bg-card {
    background: hsl(240, 15%, 99%) !important;
    border: 1px solid hsl(240, 20%, 92%) !important;
    box-shadow: 0 2px 8px hsl(240 20% 80% / 0.1) !important;
  }
  
  .theme-courtwaive-ai .border-primary {
    border-color: hsl(240 90% 55%) !important;
    box-shadow: 0 0 0 1px hsl(240 90% 55% / 0.2) !important;
  }

  .theme-courtwaive-ai .text-primary {
    color: hsl(240, 90%, 55%) !important;
  }

  /* Special gradient text class for headings */
  .theme-courtwaive-ai .text-gradient {
    background: linear-gradient(135deg, hsl(240, 90%, 55%), hsl(260, 70%, 60%)) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
  }

  /* Lighter icon styling for Courtwaive AI theme */
  .theme-courtwaive-ai svg {
    filter: drop-shadow(0 0 3px hsl(240 90% 55% / 0.2)) !important;
  }

  /* Progress bar gradient for Courtwaive AI theme */
  .theme-courtwaive-ai .bg-primary.flex-1 {
    background: linear-gradient(90deg, hsl(240, 90%, 55%), hsl(260, 70%, 60%), hsl(280, 50%, 75%), hsl(300, 30%, 90%)) !important;
  }
  
  .theme-courtwaive-ai .text-primary svg {
    color: hsl(240, 90%, 55%) !important;
    filter: drop-shadow(0 0 4px hsl(240 90% 55% / 0.25)) !important;
  }
  
  .theme-courtwaive-ai [data-sidebar] svg {
    color: hsl(240, 90%, 55%) !important;
    filter: drop-shadow(0 0 2px hsl(240 90% 55% / 0.15)) !important;
    transition: all 0.3s ease !important;
  }

  /* Sidebar trigger hover for Courtwaive AI theme */
  .theme-courtwaive-ai .bg-background button[data-sidebar="trigger"]:hover,
  .theme-courtwaive-ai button[aria-label*="Toggle"]:hover,
  .theme-courtwaive-ai button.mr-2:hover,
  .theme-courtwaive-ai header button[data-state]:hover,
  .theme-courtwaive-ai header .bg-background button:hover {
    background: hsl(240, 25%, 95%) !important;
    color: hsl(240, 90%, 55%) !important;
  }

  /* Status indicator dots for Courtwaive AI theme */
  .theme-courtwaive-ai .bg-tennis-green {
    background: hsl(240, 90%, 55%) !important;
  }

  /* Ensure all buttons use Courtwaive AI styling */
  .theme-courtwaive-ai button:not(.bg-secondary):not([variant="outline"]):not([variant="ghost"]):not([variant="link"]) {
    background: linear-gradient(135deg, hsl(240, 21%, 97%), hsl(260, 18%, 96%)) !important;
    color: hsl(240, 90%, 55%) !important;
    border: 1px solid hsl(240, 90%, 55%) !important;
    box-shadow: 0 0 0 1px hsl(240 90% 55% / 0.2), 0 3px 8px hsl(240 90% 55% / 0.14) !important;
  }

  /* Switch/Toggle styling for Courtwaive AI theme */
  .theme-courtwaive-ai [data-state="checked"] {
    background: hsl(240, 90%, 55%) !important;
  }
  
  .theme-courtwaive-ai [data-state="checked"]:focus-visible {
    ring-color: hsl(240, 90%, 55%) !important;
  }

  .theme-courtwaive-ai button:not(.bg-secondary):not([variant="outline"]):not([variant="ghost"]):not([variant="link"]):hover {
    background: linear-gradient(135deg, hsl(240, 25%, 95%), hsl(260, 21%, 94%)) !important;
    box-shadow: 0 0 0 2px hsl(240 90% 55% / 0.28), 0 4px 11px hsl(240 90% 55% / 0.21) !important;
  }
  
  .theme-courtwaive-ai [data-sidebar]:hover svg {
    filter: drop-shadow(0 0 6px hsl(240 90% 55% / 0.3)) !important;
    transform: scale(1.03) !important;
  }

  /* Corporate Blue Theme */
  .theme-corporate-blue {
    --primary: 220 90% 60%;
    --primary-foreground: 0 0% 100%;
    --secondary: 220 70% 50%;
    --secondary-foreground: 0 0% 100%;
    --accent: 220 80% 70%;
    --accent-foreground: 0 0% 100%;
    --gradient-tennis: linear-gradient(135deg, hsl(220, 90%, 60%), hsl(220, 70%, 50%));
    --shadow-tennis: 0 10px 30px -10px hsl(220 90% 60% / 0.3);
  }

  /* Elegance Theme */
  .theme-elegance {
    --primary: 0 0% 10%;
    --primary-foreground: 45 100% 50%;
    --secondary: 45 100% 50%;
    --secondary-foreground: 0 0% 10%;
    --accent: 0 0% 20%;
    --accent-foreground: 45 100% 50%;
    --gradient-tennis: linear-gradient(135deg, hsl(0, 0%, 10%), hsl(45, 100%, 50%));
    --shadow-tennis: 0 10px 30px -10px hsl(45 100% 50% / 0.3);
  }

  /* Nature Theme */
  .theme-nature {
    --primary: 25 50% 45%;
    --primary-foreground: 0 0% 100%;
    --secondary: 60 30% 60%;
    --secondary-foreground: 0 0% 10%;
    --accent: 120 25% 35%;
    --accent-foreground: 0 0% 100%;
    --gradient-tennis: linear-gradient(135deg, hsl(25, 50%, 45%), hsl(120, 25%, 35%));
    --shadow-tennis: 0 10px 30px -10px hsl(25 50% 45% / 0.3);
  }

  /* Custom theme variants */
  .theme-klassik-custom {
    /* Inherits from .theme-klassik but allows overrides via CSS custom properties */
  }

  .theme-courtwaive-ai-custom {
    /* Inherits from .theme-courtwaive-ai but allows overrides */
  }

  .theme-corporate-blue-custom {
    /* Inherits from .theme-corporate-blue but allows overrides */
  }

  .theme-elegance-custom {
    /* Inherits from .theme-elegance but allows overrides */
  }

  .theme-nature-custom {
    /* Inherits from .theme-nature but allows overrides */
  }

  /* Animation style variations */
  [data-animation-style="smooth"] {
    --transition-smooth: all 0.4s ease-in-out;
  }

  [data-animation-style="smooth"] * {
    transition-timing-function: ease-in-out;
    transition-duration: 0.4s;
  }

  [data-animation-style="bouncy"] {
    --transition-smooth: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }

  [data-animation-style="bouncy"] * {
    transition-timing-function: cubic-bezier(0.68, -0.55, 0.265, 1.55);
    transition-duration: 0.3s;
  }

  [data-animation-style="minimal"] {
    --transition-smooth: all 0.1s linear;
  }

  [data-animation-style="minimal"] * {
    transition-duration: 0.1s;
    transition-timing-function: linear;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Info semantic color (blue for highlights) */
    --info: 225 73% 57%;
    --info-foreground: 0 0% 100%;

    /* Removed glassmorphism effects */
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }

  /* Animation delays for staggered effects */
  .animation-delay-100 {
    animation-delay: 0.1s;
  }
  
  .animation-delay-200 {
    animation-delay: 0.2s;
  }
  
  .animation-delay-300 {
    animation-delay: 0.3s;
  }

  .animation-delay-400 {
    animation-delay: 0.4s;
  }

  .animation-delay-500 {
    animation-delay: 0.5s;
  }

  /* Glassmorphism utilities */
  .bg-glass {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
  }

  .bg-glass-hover {
    background: var(--glass-bg-strong);
  }

  .bg-glass-subtle {
    background: var(--bg-glass-subtle);
  }

  .border-glass {
    border-color: var(--glass-border);
  }
}

/* CRITICAL FIX: React Day Picker dropdown duplicate text issue - v8.10.1 */
.rdp-dropdown select {
  /* Remove duplicate text by hiding select content and using proper styling */
  color: transparent !important;
  background: hsl(var(--background)) !important;
  border: 1px solid hsl(var(--border)) !important;
  border-radius: 6px !important;
  padding: 8px 12px !important;
  min-width: 80px !important;
  position: relative;
  appearance: none;
  cursor: pointer;
}

.rdp-dropdown select option {
  color: hsl(var(--foreground)) !important;
  background: hsl(var(--background)) !important;
}

/* Show proper text using shadow/overlay approach */
.rdp-dropdown {
  position: relative;
}

.rdp-dropdown::after {
  content: attr(title);
  position: absolute;
  top: 50%;
  left: 12px;
  transform: translateY(-50%);
  pointer-events: none;
  color: hsl(var(--foreground));
  font-size: 14px;
  z-index: 1;
}