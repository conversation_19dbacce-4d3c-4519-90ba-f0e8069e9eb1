import { supabase } from '@/integrations/supabase/client';

export interface Club {
  id: string;
  name: string;
  slug: string;
  custom_domain?: string;
  subdomain?: string;
  logo_url?: string;
  description?: string;
  settings?: any;
  is_active: boolean;
}

export interface TenantContext {
  club: Club | null;
  isLoading: boolean;
  error: string | null;
}

class TenantResolver {
  private currentClub: Club | null = null;
  private isLoading = false;

  async resolveClub(): Promise<Club | null> {
    if (typeof window === 'undefined') return null;
    
    this.isLoading = true;
    const hostname = window.location.hostname;
    const pathname = window.location.pathname;
    
    console.log('🔍 TenantResolver.resolveClub() - hostname:', hostname, 'pathname:', pathname);

    try {
      // 1. Check for custom domain
      const customDomainClub = await this.getClubByCustomDomain(hostname);
      if (customDomainClub) {
        this.currentClub = customDomainClub;
        return customDomainClub;
      }

      // 2. Check for subdomain (verein.courtwaive.de)
      const subdomainMatch = hostname.match(/^([^.]+)\.courtwaive\.de$/);
      if (subdomainMatch) {
        const subdomain = subdomainMatch[1];
        // Skip meta admin subdomain
        if (subdomain !== 'admin') {
          const subdomainClub = await this.getClubBySubdomain(subdomain);
          if (subdomainClub) {
            this.currentClub = subdomainClub;
            return subdomainClub;
          }
        }
      }

      // 3. Check for path-based routing (/c/{club})
      const pathMatch = pathname.match(/^\/c\/([^\/]+)/);
      console.log('TenantResolver: pathname:', pathname, 'pathMatch:', pathMatch);
      if (pathMatch) {
        const clubSlug = pathMatch[1];
        console.log('TenantResolver: trying to resolve club by slug:', clubSlug);
        const pathClub = await this.getClubBySlug(clubSlug);
        if (pathClub) {
          console.log('TenantResolver: resolved club by path:', pathClub.name);
          this.currentClub = pathClub;
          return pathClub;
        } else {
          console.log('TenantResolver: no club found for slug:', clubSlug);
        }
      }

      // 4. No club found
      this.currentClub = null;
      return null;
    } catch (error) {
      console.error('Error resolving tenant:', error);
      this.currentClub = null;
      return null;
    } finally {
      this.isLoading = false;
    }
  }

  private async getClubByCustomDomain(domain: string): Promise<Club | null> {
    const { data, error } = await supabase
      .from('clubs')
      .select('*')
      .eq('custom_domain', domain)
      .eq('is_active', true)
      .maybeSingle();

    if (error) {
      console.error('Error fetching club by custom domain:', error);
      return null;
    }

    return data;
  }

  private async getClubBySubdomain(subdomain: string): Promise<Club | null> {
    const { data, error } = await supabase
      .from('clubs')
      .select('*')
      .eq('subdomain', subdomain)
      .eq('is_active', true)
      .maybeSingle();

    if (error) {
      console.error('Error fetching club by subdomain:', error);
      return null;
    }

    return data;
  }

  private async getClubBySlug(slug: string): Promise<Club | null> {
    const { data, error } = await supabase
      .from('clubs')
      .select('*')
      .eq('slug', slug)
      .eq('is_active', true)
      .maybeSingle();

    if (error) {
      console.error('Error fetching club by slug:', error);
      return null;
    }

    return data;
  }

  getCurrentClub(): Club | null {
    return this.currentClub;
  }

  isMetaAdmin(): boolean {
    if (typeof window === 'undefined') return false;
    const hostname = window.location.hostname;
    const pathname = window.location.pathname;
    
    // Multi-Tenant Meta-Admin Zugriff
    return hostname === 'admin.courtwaive.de' || 
           hostname.startsWith('admin.') || 
           pathname.startsWith('/meta-admin');
  }

  getClubBasePath(): string {
    if (!this.currentClub) return '';
    
    const hostname = window.location.hostname;
    const pathname = window.location.pathname;
    
    // Custom domain or subdomain - no prefix needed
    if (this.currentClub.custom_domain === hostname || 
        hostname.startsWith(`${this.currentClub.subdomain}.`)) {
      return '';
    }
    
    // Path-based routing
    if (pathname.startsWith(`/c/${this.currentClub.slug}`)) {
      return `/c/${this.currentClub.slug}`;
    }
    
    return '';
  }

  buildClubUrl(path: string = ''): string {
    const club = this.getCurrentClub();
    if (!club) {
      console.log('🚫 buildClubUrl: No club found, returning path as-is:', path);
      return path;
    }

    const basePath = this.getClubBasePath();
    const result = `${basePath}${path}`;
    console.log('🔗 buildClubUrl:', { path, basePath, result, clubSlug: club.slug });
    return result;
  }
}

export const tenantResolver = new TenantResolver();

// Utility function to check if current user is super admin
export async function isSuperAdmin(): Promise<boolean> {
  const { data: userRoles, error } = await supabase
    .from('user_roles')
    .select('role')
    .eq('user_id', (await supabase.auth.getUser()).data.user?.id)
    .eq('role', 'admin'); // Use regular admin role for club-level admin checks

  return !error && userRoles && userRoles.length > 0;
}

// Set tenant context for database operations (using PostgreSQL directly)
export async function setTenantContext(clubId: string | null) {
  if (clubId) {
    // This would need to be set via direct SQL if needed
    // For now, we'll handle tenant context in application logic
    console.log('Setting tenant context for club:', clubId);
  }
}