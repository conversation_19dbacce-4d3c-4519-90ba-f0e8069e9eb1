// Smart Tournament Scheduler - Intelligent match scheduling logic
export interface SchedulingConfig {
  tournament_start_time: string;
  daily_start_time: string;
  daily_end_time: string;
  minimum_break_between_matches: number;
  warmup_time: number;
  changeover_time: number;
  court_preparation_time: number;
  max_matches_per_player_per_day: number;
  max_consecutive_matches: number;
  mandatory_rest_after_consecutive: number;
  use_main_court_for_finals: boolean;
  court_preference_order: string[];
  avoid_surface_changes: boolean;
  delay_handling: 'reschedule' | 'compress' | 'extend_day';
  rain_delay_buffer: number;
  parallel_scheduling_enabled: boolean;
  round_gap_hours: number;
  group_stage_parallel: boolean;
  knockout_sequential: boolean;
}

export interface Match {
  id: string;
  tournament_id: string;
  participant1_id: string;
  participant2_id?: string;
  round_number: number;
  match_number: number;
  status: string;
  estimated_duration_minutes: number;
  dependencies?: string[]; // IDs of matches that must complete before this one
  earliest_start?: Date;
  latest_start?: Date;
  format_constraints?: Record<string, any>;
}

export interface Court {
  id: string;
  number: number;
  surface_type?: string;
  court_group?: string;
  is_main_court?: boolean;
  availability_windows?: Array<{
    start: Date;
    end: Date;
  }>;
}

export interface ScheduledMatch extends Match {
  scheduled_date: string;
  scheduled_time: string;
  court_id: string;
  scheduled_at: Date;
  ends_at: Date;
}

export interface SchedulingResult {
  scheduled_matches: ScheduledMatch[];
  warnings: string[];
  total_duration_days: number;
  court_utilization: Record<string, number>;
  player_load: Record<string, Array<{
    date: string;
    match_count: number;
  }>>;
  conflicts: Array<{
    type: string;
    message: string;
    matches: string[];
  }>;
}

export class SmartScheduler {
  private config: SchedulingConfig;
  private courts: Court[];
  private tournamentFormat: string;

  constructor(config: SchedulingConfig, courts: Court[], tournamentFormat: string) {
    this.config = config;
    this.courts = courts;
    this.tournamentFormat = tournamentFormat;
  }

  public scheduleMatches(matches: Match[], tournamentStartDate: Date): SchedulingResult {
    const result: SchedulingResult = {
      scheduled_matches: [],
      warnings: [],
      total_duration_days: 0,
      court_utilization: {},
      player_load: {},
      conflicts: []
    };

    // Sort courts by preference
    const sortedCourts = this.sortCourtsByPreference();
    
    // Group matches by format-specific logic
    const matchGroups = this.groupMatchesBySchedulingLogic(matches);
    
    // Initialize scheduling state
    const courtSchedules = this.initializeCourtSchedules(sortedCourts, tournamentStartDate);
    const playerSchedules = new Map<string, Array<{ start: Date; end: Date; matchId: string }>>();
    
    let currentDate = new Date(tournamentStartDate);
    
    // Schedule each group
    for (const group of matchGroups) {
      const groupResult = this.scheduleMatchGroup(
        group, 
        currentDate, 
        courtSchedules, 
        playerSchedules
      );
      
      result.scheduled_matches.push(...groupResult.scheduled_matches);
      result.warnings.push(...groupResult.warnings);
      result.conflicts.push(...groupResult.conflicts);
      
      // Update current date for next group
      if (groupResult.scheduled_matches.length > 0) {
        const lastMatch = groupResult.scheduled_matches
          .sort((a, b) => a.ends_at.getTime() - b.ends_at.getTime())
          .pop();
        
        if (lastMatch) {
          currentDate = new Date(lastMatch.ends_at);
          
          // Add round gap for knockout tournaments
          if (this.tournamentFormat === 'knockout' && this.config.knockout_sequential) {
            currentDate.setHours(currentDate.getHours() + this.config.round_gap_hours);
          }
        }
      }
    }

    // Calculate statistics
    result.total_duration_days = this.calculateTotalDays(result.scheduled_matches, tournamentStartDate);
    result.court_utilization = this.calculateCourtUtilization(result.scheduled_matches, sortedCourts);
    result.player_load = this.calculatePlayerLoad(result.scheduled_matches);

    return result;
  }

  private sortCourtsByPreference(): Court[] {
    const preferenceOrder = this.config.court_preference_order;
    
    return this.courts.sort((a, b) => {
      const aIndex = preferenceOrder.indexOf(a.id);
      const bIndex = preferenceOrder.indexOf(b.id);
      
      if (aIndex === -1 && bIndex === -1) {
        // Both not in preference list, sort by number
        return a.number - b.number;
      }
      
      if (aIndex === -1) return 1;
      if (bIndex === -1) return -1;
      
      return aIndex - bIndex;
    });
  }

  private groupMatchesBySchedulingLogic(matches: Match[]): Match[][] {
    switch (this.tournamentFormat) {
      case 'knockout':
        return this.groupKnockoutMatches(matches);
      case 'round_robin':
        return this.groupRoundRobinMatches(matches);
      case 'team_tie':
        return this.groupTeamTieMatches(matches);
      default:
        // Group by round
        const rounds = new Map<number, Match[]>();
        matches.forEach(match => {
          if (!rounds.has(match.round_number)) {
            rounds.set(match.round_number, []);
          }
          rounds.get(match.round_number)!.push(match);
        });
        return Array.from(rounds.values());
    }
  }

  private groupKnockoutMatches(matches: Match[]): Match[][] {
    if (this.config.knockout_sequential) {
      // Strict round-by-round scheduling
      const rounds = new Map<number, Match[]>();
      matches.forEach(match => {
        if (!rounds.has(match.round_number)) {
          rounds.set(match.round_number, []);
        }
        rounds.get(match.round_number)!.push(match);
      });
      
      return Array.from(rounds.entries())
        .sort(([a], [b]) => a - b)
        .map(([_, matches]) => matches);
    } else {
      // Allow some overlap between rounds
      return [matches]; // Schedule all at once with dependency checking
    }
  }

  private groupRoundRobinMatches(matches: Match[]): Match[][] {
    if (this.config.group_stage_parallel) {
      // All round robin matches can be scheduled in parallel
      return [matches];
    } else {
      // Group by participant to avoid conflicts
      const groups: Match[][] = [];
      const scheduledMatches = new Set<string>();
      
      for (const match of matches) {
        if (scheduledMatches.has(match.id)) continue;
        
        const currentGroup: Match[] = [match];
        scheduledMatches.add(match.id);
        
        // Find non-conflicting matches
        for (const otherMatch of matches) {
          if (scheduledMatches.has(otherMatch.id)) continue;
          
          if (!this.matchesHaveCommonPlayer(match, otherMatch, currentGroup)) {
            currentGroup.push(otherMatch);
            scheduledMatches.add(otherMatch.id);
          }
        }
        
        groups.push(currentGroup);
      }
      
      return groups;
    }
  }

  private groupTeamTieMatches(matches: Match[]): Match[][] {
    // Team ties require specific sequencing (e.g., Singles → Doubles → Singles)
    const groups: Match[][] = [];
    const matchesByTeamTie = new Map<string, Match[]>();
    
    // Group by team tie
    matches.forEach(match => {
      const teamTieId = match.format_constraints?.team_tie_id || 'default';
      if (!matchesByTeamTie.has(teamTieId)) {
        matchesByTeamTie.set(teamTieId, []);
      }
      matchesByTeamTie.get(teamTieId)!.push(match);
    });
    
    // Schedule each team tie sequentially
    matchesByTeamTie.forEach(tieMatches => {
      // Sort by rubber order (singles first, then doubles, then more singles)
      const sortedMatches = tieMatches.sort((a, b) => {
        const aOrder = this.getRubberOrder(a.format_constraints?.rubber || '');
        const bOrder = this.getRubberOrder(b.format_constraints?.rubber || '');
        return aOrder - bOrder;
      });
      
      groups.push(sortedMatches);
    });
    
    return groups;
  }

  private getRubberOrder(rubber: string): number {
    const order = {
      'singles1': 1,
      'singles2': 2,
      'doubles': 3,
      'singles3': 4,
      'singles4': 5
    };
    return order[rubber as keyof typeof order] || 99;
  }

  private matchesHaveCommonPlayer(match1: Match, match2: Match, groupMatches: Match[]): boolean {
    const match1Players = [match1.participant1_id, match1.participant2_id].filter(Boolean);
    const match2Players = [match2.participant1_id, match2.participant2_id].filter(Boolean);
    
    // Check direct conflict
    const hasDirectConflict = match1Players.some(p1 => match2Players.includes(p1));
    if (hasDirectConflict) return true;
    
    // Check conflicts with other matches in the group
    for (const groupMatch of groupMatches) {
      const groupPlayers = [groupMatch.participant1_id, groupMatch.participant2_id].filter(Boolean);
      const hasGroupConflict = match2Players.some(p2 => groupPlayers.includes(p2));
      if (hasGroupConflict) return true;
    }
    
    return false;
  }

  private initializeCourtSchedules(courts: Court[], startDate: Date) {
    const schedules = new Map<string, Array<{ start: Date; end: Date; matchId: string }>>();
    
    courts.forEach(court => {
      schedules.set(court.id, []);
    });
    
    return schedules;
  }

  private scheduleMatchGroup(
    matches: Match[],
    groupStartDate: Date,
    courtSchedules: Map<string, Array<{ start: Date; end: Date; matchId: string }>>,
    playerSchedules: Map<string, Array<{ start: Date; end: Date; matchId: string }>>
  ) {
    const result = {
      scheduled_matches: [] as ScheduledMatch[],
      warnings: [] as string[],
      conflicts: [] as Array<{ type: string; message: string; matches: string[] }>
    };

    // Sort matches by priority (finals first if using main court preference)
    const sortedMatches = this.sortMatchesByPriority(matches);
    
    for (const match of sortedMatches) {
      const schedulingResult = this.scheduleIndividualMatch(
        match,
        groupStartDate,
        courtSchedules,
        playerSchedules
      );
      
      if (schedulingResult.success) {
        result.scheduled_matches.push(schedulingResult.scheduledMatch!);
        
        // Update schedules
        this.updateCourtSchedule(courtSchedules, schedulingResult.scheduledMatch!);
        this.updatePlayerSchedules(playerSchedules, schedulingResult.scheduledMatch!);
      } else {
        result.conflicts.push({
          type: 'scheduling_failed',
          message: `Spiel ${match.match_number} konnte nicht terminiert werden: ${schedulingResult.reason}`,
          matches: [match.id]
        });
      }
    }

    return result;
  }

  private sortMatchesByPriority(matches: Match[]): Match[] {
    return matches.sort((a, b) => {
      // Finals first if using main court preference
      if (this.config.use_main_court_for_finals) {
        const aIsFinal = this.isMatchFinal(a);
        const bIsFinal = this.isMatchFinal(b);
        
        if (aIsFinal && !bIsFinal) return -1;
        if (!aIsFinal && bIsFinal) return 1;
      }
      
      // Then by round number (later rounds first for better court allocation)
      if (a.round_number !== b.round_number) {
        return b.round_number - a.round_number;
      }
      
      // Then by match number
      return a.match_number - b.match_number;
    });
  }

  private isMatchFinal(match: Match): boolean {
    // This would need to be determined based on tournament structure
    // For now, assume the highest round number with match_number 1 is the final
    return match.format_constraints?.is_final === true;
  }

  private scheduleIndividualMatch(
    match: Match,
    earliestStart: Date,
    courtSchedules: Map<string, Array<{ start: Date; end: Date; matchId: string }>>,
    playerSchedules: Map<string, Array<{ start: Date; end: Date; matchId: string }>>
  ) {
    const totalDuration = match.estimated_duration_minutes + this.config.court_preparation_time;
    
    // Find best court and time slot
    for (const court of this.courts) {
      const courtId = court.id;
      const courtSchedule = courtSchedules.get(courtId) || [];
      
      // Check if this is main court and if we should reserve it for finals
      if (this.config.use_main_court_for_finals && court.is_main_court && !this.isMatchFinal(match)) {
        // Only use main court for finals
        continue;
      }
      
      // Find earliest available time slot
      const earliestSlot = this.findEarliestAvailableSlot(
        courtSchedule,
        earliestStart,
        totalDuration,
        match,
        playerSchedules
      );
      
      if (earliestSlot) {
        const scheduledMatch: ScheduledMatch = {
          ...match,
          scheduled_date: earliestSlot.start.toISOString().split('T')[0],
          scheduled_time: earliestSlot.start.toTimeString().substring(0, 5),
          court_id: courtId,
          scheduled_at: earliestSlot.start,
          ends_at: earliestSlot.end
        };
        
        return {
          success: true,
          scheduledMatch,
          reason: null
        };
      }
    }
    
    return {
      success: false,
      scheduledMatch: null,
      reason: 'Kein verfügbarer Zeitslot gefunden'
    };
  }

  private findEarliestAvailableSlot(
    courtSchedule: Array<{ start: Date; end: Date; matchId: string }>,
    earliestStart: Date,
    durationMinutes: number,
    match: Match,
    playerSchedules: Map<string, Array<{ start: Date; end: Date; matchId: string }>>
  ) {
    let currentTime = new Date(Math.max(earliestStart.getTime(), this.getEarliestDailyStart(earliestStart).getTime()));
    const maxDaysToCheck = 30; // Prevent infinite loops
    let daysChecked = 0;
    
    while (daysChecked < maxDaysToCheck) {
      // Check if within daily hours
      const dayStart = this.getDailyStart(currentTime);
      const dayEnd = this.getDailyEnd(currentTime);
      
      if (currentTime < dayStart) {
        currentTime = dayStart;
      }
      
      // Check if slot fits in the day
      const slotEnd = new Date(currentTime.getTime() + durationMinutes * 60000);
      if (slotEnd > dayEnd) {
        // Move to next day
        currentTime = this.getNextDayStart(currentTime);
        daysChecked++;
        continue;
      }
      
      // Check court availability
      if (this.isCourtAvailable(courtSchedule, currentTime, slotEnd)) {
        // Check player availability
        if (this.arePlayersAvailable(match, currentTime, slotEnd, playerSchedules)) {
          return {
            start: new Date(currentTime),
            end: new Date(slotEnd)
          };
        }
      }
      
      // Move to next possible slot
      currentTime = this.findNextAvailableSlot(courtSchedule, currentTime, durationMinutes);
      if (!currentTime) {
        // No more slots today, move to next day
        currentTime = this.getNextDayStart(earliestStart);
        daysChecked++;
      }
    }
    
    return null;
  }

  private getEarliestDailyStart(date: Date): Date {
    const [hours, minutes] = this.config.daily_start_time.split(':').map(Number);
    const result = new Date(date);
    result.setHours(hours, minutes, 0, 0);
    return result;
  }

  private getDailyStart(date: Date): Date {
    const [hours, minutes] = this.config.daily_start_time.split(':').map(Number);
    const result = new Date(date);
    result.setHours(hours, minutes, 0, 0);
    return result;
  }

  private getDailyEnd(date: Date): Date {
    const [hours, minutes] = this.config.daily_end_time.split(':').map(Number);
    const result = new Date(date);
    result.setHours(hours, minutes, 0, 0);
    return result;
  }

  private getNextDayStart(date: Date): Date {
    const nextDay = new Date(date);
    nextDay.setDate(nextDay.getDate() + 1);
    return this.getDailyStart(nextDay);
  }

  private isCourtAvailable(
    courtSchedule: Array<{ start: Date; end: Date; matchId: string }>,
    start: Date,
    end: Date
  ): boolean {
    return !courtSchedule.some(slot => 
      (start < slot.end && end > slot.start)
    );
  }

  private arePlayersAvailable(
    match: Match,
    start: Date,
    end: Date,
    playerSchedules: Map<string, Array<{ start: Date; end: Date; matchId: string }>>
  ): boolean {
    const players = [match.participant1_id, match.participant2_id].filter(Boolean);
    
    for (const playerId of players) {
      const playerSchedule = playerSchedules.get(playerId) || [];
      
      // Check for conflicts with minimum break time
      const conflictFound = playerSchedule.some(slot => {
        const breakStart = new Date(slot.end.getTime() - this.config.minimum_break_between_matches * 60000);
        const breakEnd = new Date(slot.start.getTime() + this.config.minimum_break_between_matches * 60000);
        
        return (start < breakEnd && end > breakStart);
      });
      
      if (conflictFound) return false;
      
      // Check daily match limits
      const dayMatches = playerSchedule.filter(slot => 
        slot.start.toDateString() === start.toDateString()
      );
      
      if (dayMatches.length >= this.config.max_matches_per_player_per_day) {
        return false;
      }
    }
    
    return true;
  }

  private findNextAvailableSlot(
    courtSchedule: Array<{ start: Date; end: Date; matchId: string }>,
    currentTime: Date,
    durationMinutes: number
  ): Date | null {
    // Find the next end time that could provide a slot
    const futureSlots = courtSchedule
      .filter(slot => slot.end > currentTime)
      .sort((a, b) => a.end.getTime() - b.end.getTime());
    
    if (futureSlots.length > 0) {
      return futureSlots[0].end;
    }
    
    return null;
  }

  private updateCourtSchedule(
    courtSchedules: Map<string, Array<{ start: Date; end: Date; matchId: string }>>,
    match: ScheduledMatch
  ) {
    const schedule = courtSchedules.get(match.court_id) || [];
    schedule.push({
      start: match.scheduled_at,
      end: match.ends_at,
      matchId: match.id
    });
    
    // Keep sorted
    schedule.sort((a, b) => a.start.getTime() - b.start.getTime());
    courtSchedules.set(match.court_id, schedule);
  }

  private updatePlayerSchedules(
    playerSchedules: Map<string, Array<{ start: Date; end: Date; matchId: string }>>,
    match: ScheduledMatch
  ) {
    const players = [match.participant1_id, match.participant2_id].filter(Boolean);
    
    players.forEach(playerId => {
      const schedule = playerSchedules.get(playerId) || [];
      schedule.push({
        start: match.scheduled_at,
        end: match.ends_at,
        matchId: match.id
      });
      
      // Keep sorted
      schedule.sort((a, b) => a.start.getTime() - b.start.getTime());
      playerSchedules.set(playerId, schedule);
    });
  }

  private calculateTotalDays(matches: ScheduledMatch[], startDate: Date): number {
    if (matches.length === 0) return 0;
    
    const lastMatch = matches
      .sort((a, b) => a.ends_at.getTime() - b.ends_at.getTime())
      .pop();
    
    if (!lastMatch) return 0;
    
    const daysDiff = Math.ceil((lastMatch.ends_at.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    return Math.max(1, daysDiff);
  }

  private calculateCourtUtilization(matches: ScheduledMatch[], courts: Court[]): Record<string, number> {
    const utilization: Record<string, number> = {};
    
    courts.forEach(court => {
      const courtMatches = matches.filter(m => m.court_id === court.id);
      const totalMinutes = courtMatches.reduce((sum, match) => 
        sum + (match.ends_at.getTime() - match.scheduled_at.getTime()) / (1000 * 60), 0
      );
      
      // Calculate percentage based on daily available hours
      const dailyMinutes = this.calculateDailyAvailableMinutes();
      const days = this.calculateTotalDays(matches, new Date());
      const totalAvailableMinutes = dailyMinutes * days;
      
      utilization[court.id] = totalAvailableMinutes > 0 ? (totalMinutes / totalAvailableMinutes) * 100 : 0;
    });
    
    return utilization;
  }

  private calculateDailyAvailableMinutes(): number {
    const [startHours, startMinutes] = this.config.daily_start_time.split(':').map(Number);
    const [endHours, endMinutes] = this.config.daily_end_time.split(':').map(Number);
    
    const startTotalMinutes = startHours * 60 + startMinutes;
    const endTotalMinutes = endHours * 60 + endMinutes;
    
    return endTotalMinutes - startTotalMinutes;
  }

  private calculatePlayerLoad(matches: ScheduledMatch[]): Record<string, Array<{ date: string; match_count: number }>> {
    const playerLoad: Record<string, Array<{ date: string; match_count: number }>> = {};
    
    matches.forEach(match => {
      const date = match.scheduled_date;
      const players = [match.participant1_id, match.participant2_id].filter(Boolean);
      
      players.forEach(playerId => {
        if (!playerLoad[playerId]) {
          playerLoad[playerId] = [];
        }
        
        const existingEntry = playerLoad[playerId].find(entry => entry.date === date);
        if (existingEntry) {
          existingEntry.match_count++;
        } else {
          playerLoad[playerId].push({ date, match_count: 1 });
        }
      });
    });
    
    return playerLoad;
  }
}
