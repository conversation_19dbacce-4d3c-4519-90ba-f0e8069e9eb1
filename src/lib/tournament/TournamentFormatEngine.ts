// Tournament Format Engine - Zentrale Logik für alle Turnier-Formate
interface TimeConfiguration {
  match_duration_minutes: number;
  warmup_minutes: number;
  changeover_minutes: number;
  set_break_minutes: number;
  between_match_buffer_minutes: number;
  court_preparation_minutes: number;
}

export interface TournamentFormat {
  type: string;
  name: string;
  description: string;
  config: Record<string, any>;
  default_time_config: TimeConfiguration;
}

export interface Participant {
  id: string;
  name: string;
  seed: number;
  rating?: number;
  isGuest?: boolean;
}

export interface Match {
  id: string;
  round: number;
  matchNumber: number;
  participant1?: Participant;
  participant2?: Participant;
  winnerId?: string;
  status: 'scheduled' | 'in_progress' | 'completed' | 'walkover';
  scores?: Array<{ player1: number; player2: number }>;
  metadata?: Record<string, any>;
}

export interface DrawResult {
  matches: Match[];
  rounds: Array<{
    number: number;
    name: string;
    matches: Match[];
  }>;
  metadata: Record<string, any>;
}

// Base Handler Interface
export interface FormatHandler {
  generateDraw(participants: Participant[], config: Record<string, any>): DrawResult;
  validateConfig(config: Record<string, any>): boolean;
  getRequiredParticipants(config: Record<string, any>): { min: number; max?: number };
  advanceWinner(match: Match, winnerId: string, state: Record<string, any>): DrawResult | null;
}

// Knockout (Single Elimination) Handler
export class KnockoutHandler implements FormatHandler {
  generateDraw(participants: Participant[], config: Record<string, any>): DrawResult {
    const { seeding = 'random', consolation_plate = false } = config;
    
    // Seed participants
    let seededParticipants = [...participants];
    if (seeding === 'random') {
      seededParticipants = this.shuffleArray(participants);
    } else if (seeding === 'rating') {
      seededParticipants.sort((a, b) => (b.rating || 0) - (a.rating || 0));
    }

    // Calculate rounds needed
    const totalRounds = Math.ceil(Math.log2(participants.length));
    const firstRoundMatches = Math.ceil(participants.length / 2);
    
    const matches: Match[] = [];
    const rounds: DrawResult['rounds'] = [];

    // Generate first round
    for (let i = 0; i < firstRoundMatches; i++) {
      const p1 = seededParticipants[i * 2];
      const p2 = seededParticipants[i * 2 + 1];
      
      matches.push({
        id: `match-1-${i + 1}`,
        round: 1,
        matchNumber: i + 1,
        participant1: p1,
        participant2: p2,
        status: 'scheduled'
      });
    }

    // Generate subsequent rounds (empty matches)
    for (let round = 2; round <= totalRounds; round++) {
      const matchesInRound = Math.pow(2, totalRounds - round);
      for (let i = 0; i < matchesInRound; i++) {
        matches.push({
          id: `match-${round}-${i + 1}`,
          round,
          matchNumber: i + 1,
          status: 'scheduled'
        });
      }
    }

    // Group matches by round
    for (let round = 1; round <= totalRounds; round++) {
      const roundMatches = matches.filter(m => m.round === round);
      const roundName = this.getRoundName(round, totalRounds);
      rounds.push({
        number: round,
        name: roundName,
        matches: roundMatches
      });
    }

    return {
      matches,
      rounds,
      metadata: { format: 'knockout', totalRounds, consolation_plate }
    };
  }

  validateConfig(config: Record<string, any>): boolean {
    return true; // Knockout has minimal config requirements
  }

  getRequiredParticipants(): { min: number; max?: number } {
    return { min: 2, max: 128 };
  }

  advanceWinner(match: Match, winnerId: string, state: Record<string, any>): DrawResult | null {
    // Logic to advance winner to next round
    // This would update the tournament state and return new matches if needed
    return null; // Simplified for now
  }

  private shuffleArray<T>(array: T[]): T[] {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }

  public getRoundName(round: number, totalRounds: number): string {
    const remaining = totalRounds - round + 1;
    if (remaining === 1) return 'Finale';
    if (remaining === 2) return 'Halbfinale';
    if (remaining === 3) return 'Viertelfinale';
    if (remaining === 4) return 'Achtelfinale';
    return `Runde ${round}`;
  }
}

// Round Robin Handler
export class RoundRobinHandler implements FormatHandler {
  generateDraw(participants: Participant[], config: Record<string, any>): DrawResult {
    const { groups = 1, tiebreaker = ['head_to_head', 'set_ratio'] } = config;
    
    if (groups === 1) {
      return this.generateSingleRoundRobin(participants, config);
    } else {
      return this.generateGroupRoundRobin(participants, config);
    }
  }

  private generateSingleRoundRobin(participants: Participant[], config: Record<string, any>): DrawResult {
    const matches: Match[] = [];
    let matchCounter = 1;

    // Generate all possible pairings
    for (let i = 0; i < participants.length; i++) {
      for (let j = i + 1; j < participants.length; j++) {
        matches.push({
          id: `match-rr-${matchCounter}`,
          round: 1,
          matchNumber: matchCounter,
          participant1: participants[i],
          participant2: participants[j],
          status: 'scheduled'
        });
        matchCounter++;
      }
    }

    return {
      matches,
      rounds: [{
        number: 1,
        name: 'Round Robin',
        matches
      }],
      metadata: { format: 'round_robin', groups: 1, tiebreaker: config.tiebreaker }
    };
  }

  private generateGroupRoundRobin(participants: Participant[], config: Record<string, any>): DrawResult {
    const { groups, group_size } = config;
    const actualGroupSize = group_size || Math.ceil(participants.length / groups);
    
    // Distribute participants into groups
    const groupedParticipants: Participant[][] = [];
    for (let i = 0; i < groups; i++) {
      groupedParticipants.push([]);
    }

    participants.forEach((participant, index) => {
      const groupIndex = index % groups;
      groupedParticipants[groupIndex].push(participant);
    });

    const matches: Match[] = [];
    const rounds: DrawResult['rounds'] = [];
    let matchCounter = 1;

    // Generate matches for each group
    groupedParticipants.forEach((group, groupIndex) => {
      const groupMatches: Match[] = [];
      
      for (let i = 0; i < group.length; i++) {
        for (let j = i + 1; j < group.length; j++) {
          const match: Match = {
            id: `match-g${groupIndex + 1}-${matchCounter}`,
            round: groupIndex + 1,
            matchNumber: matchCounter,
            participant1: group[i],
            participant2: group[j],
            status: 'scheduled',
            metadata: { group: groupIndex + 1 }
          };
          matches.push(match);
          groupMatches.push(match);
          matchCounter++;
        }
      }

      rounds.push({
        number: groupIndex + 1,
        name: `Gruppe ${String.fromCharCode(65 + groupIndex)}`,
        matches: groupMatches
      });
    });

    return {
      matches,
      rounds,
      metadata: { format: 'round_robin', groups, group_size: actualGroupSize }
    };
  }

  validateConfig(config: Record<string, any>): boolean {
    const { groups = 1, group_size } = config;
    return groups >= 1 && (!group_size || group_size >= 3);
  }

  getRequiredParticipants(config: Record<string, any>): { min: number; max?: number } {
    const { groups = 1 } = config;
    return { min: groups * 3, max: 64 };
  }

  advanceWinner(): DrawResult | null {
    return null; // Round Robin doesn't advance players during matches
  }
}

// UTS (Universal Tennis Scoring) Handler
export class UTSHandler implements FormatHandler {
  generateDraw(participants: Participant[], config: Record<string, any>): DrawResult {
    const { seeding = 'random', best_of = 3, sets_to_win = 2 } = config;
    
    let seededParticipants = [...participants];
    if (seeding === 'random') {
      seededParticipants = this.shuffleArray(participants);
    } else if (seeding === 'rating') {
      seededParticipants.sort((a, b) => (b.rating || 0) - (a.rating || 0));
    }

    const totalRounds = Math.ceil(Math.log2(participants.length));
    const firstRoundMatches = Math.ceil(participants.length / 2);
    const matches: Match[] = [];
    const rounds: DrawResult['rounds'] = [];

    // Generate first round with UTS scoring format
    for (let i = 0; i < firstRoundMatches; i++) {
      const p1 = seededParticipants[i * 2];
      const p2 = seededParticipants[i * 2 + 1];
      
      matches.push({
        id: `uts-match-1-${i + 1}`,
        round: 1,
        matchNumber: i + 1,
        participant1: p1,
        participant2: p2,
        status: 'scheduled',
        metadata: { 
          scoring: 'uts', 
          first_to: 4, 
          sudden_death: true,
          no_ad: true,
          sets_to_win 
        }
      });
    }

    // Generate subsequent rounds
    for (let round = 2; round <= totalRounds; round++) {
      const matchesInRound = Math.pow(2, totalRounds - round);
      for (let i = 0; i < matchesInRound; i++) {
        matches.push({
          id: `uts-match-${round}-${i + 1}`,
          round,
          matchNumber: i + 1,
          status: 'scheduled',
          metadata: { scoring: 'uts', first_to: 4, sudden_death: true, no_ad: true, sets_to_win }
        });
      }
    }

    // Group matches by round
    for (let round = 1; round <= totalRounds; round++) {
      const roundMatches = matches.filter(m => m.round === round);
      rounds.push({
        number: round,
        name: `UTS Runde ${round}`,
        matches: roundMatches
      });
    }

    return {
      matches,
      rounds,
      metadata: { format: 'uts', scoring: 'first_to_4_games', sudden_death: true, best_of }
    };
  }

  validateConfig(config: Record<string, any>): boolean {
    const { best_of = 3 } = config;
    return [1, 3, 5].includes(best_of);
  }

  getRequiredParticipants(): { min: number; max?: number } {
    return { min: 2, max: 64 };
  }

  advanceWinner(): DrawResult | null {
    return null;
  }

  private shuffleArray<T>(array: T[]): T[] {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }
}

// Compass Draw Handler (E/W/N/S placement)
export class CompassHandler implements FormatHandler {
  generateDraw(participants: Participant[], config: Record<string, any>): DrawResult {
    const matches: Match[] = [];
    const rounds: DrawResult['rounds'] = [];
    
    // Main draw (First round)
    const mainDrawSize = Math.floor(participants.length / 2) * 2;
    const mainParticipants = participants.slice(0, mainDrawSize);
    
    for (let i = 0; i < mainParticipants.length; i += 2) {
      matches.push({
        id: `compass-main-${i/2 + 1}`,
        round: 1,
        matchNumber: i/2 + 1,
        participant1: mainParticipants[i],
        participant2: mainParticipants[i + 1],
        status: 'scheduled',
        metadata: { draw: 'main' }
      });
    }

    // Create subsequent compass draws (E/W/N/S)
    const compassDraws = ['east', 'west', 'north', 'south'];
    compassDraws.forEach((direction, idx) => {
      rounds.push({
        number: idx + 2,
        name: `${direction.toUpperCase()} Draw`,
        matches: []
      });
    });

    rounds.unshift({
      number: 1,
      name: 'Main Draw',
      matches: matches.filter(m => m.metadata?.draw === 'main')
    });

    return {
      matches,
      rounds,
      metadata: { format: 'compass', draws: ['main', ...compassDraws] }
    };
  }

  validateConfig(): boolean {
    return true;
  }

  getRequiredParticipants(): { min: number; max?: number } {
    return { min: 8, max: 32 };
  }

  advanceWinner(): DrawResult | null {
    return null;
  }
}

// Team Tie Handler (Davis Cup style)
export class TeamTieHandler implements FormatHandler {
  generateDraw(participants: Participant[], config: Record<string, any>): DrawResult {
    const { rubbers = ['singles1', 'singles2', 'doubles', 'singles3', 'singles4'] } = config;
    const matches: Match[] = [];
    
    // Generate team matchups
    for (let i = 0; i < participants.length; i += 2) {
      const team1 = participants[i];
      const team2 = participants[i + 1];
      
      rubbers.forEach((rubber, rubberIndex) => {
        matches.push({
          id: `team-tie-${i/2 + 1}-${rubber}`,
          round: 1,
          matchNumber: rubberIndex + 1,
          participant1: team1,
          participant2: team2,
          status: 'scheduled',
          metadata: { rubber, team_match: i/2 + 1, format: rubber.includes('doubles') ? 'doubles' : 'singles' }
        });
      });
    }

    return {
      matches,
      rounds: [{
        number: 1,
        name: 'Team Ties',
        matches
      }],
      metadata: { format: 'team_tie', rubbers, scoring: 'best_of_5_rubbers' }
    };
  }

  validateConfig(config: Record<string, any>): boolean {
    const { rubbers = [] } = config;
    return rubbers.length >= 3 && rubbers.length <= 5;
  }

  getRequiredParticipants(): { min: number; max?: number } {
    return { min: 2, max: 16 };
  }

  advanceWinner(): DrawResult | null {
    return null;
  }
}

// Challenge Ladder Handler
export class ChallengeLadderHandler implements FormatHandler {
  generateDraw(participants: Participant[], config: Record<string, any>): DrawResult {
    const { challenge_window_days = 7, max_challenges_per_week = 2 } = config;
    
    // Sort by rating/seed for initial ladder positioning
    const sortedParticipants = [...participants].sort((a, b) => (b.rating || 0) - (a.rating || 0));
    
    return {
      matches: [], // Matches generated dynamically when challenges are made
      rounds: [{
        number: 1,
        name: 'Challenge Ladder',
        matches: []
      }],
      metadata: { 
        format: 'challenge_ladder', 
        ladder_positions: sortedParticipants.map((p, idx) => ({ participant: p, position: idx + 1 })),
        challenge_window_days,
        max_challenges_per_week
      }
    };
  }

  validateConfig(): boolean {
    return true;
  }

  getRequiredParticipants(): { min: number; max?: number } {
    return { min: 4, max: 100 };
  }

  advanceWinner(): DrawResult | null {
    return null;
  }
}

// Timeboxed Tournament Handler
export class TimeboxedHandler implements FormatHandler {
  generateDraw(participants: Participant[], config: Record<string, any>): DrawResult {
    const { time_limit_minutes = 60, scoring = 'games', tiebreak_at = 6 } = config;
    const matches: Match[] = [];
    
    // Generate round robin style but with time limits
    for (let i = 0; i < participants.length; i++) {
      for (let j = i + 1; j < participants.length; j++) {
        matches.push({
          id: `timeboxed-${i}-${j}`,
          round: 1,
          matchNumber: matches.length + 1,
          participant1: participants[i],
          participant2: participants[j],
          status: 'scheduled',
          metadata: { 
            time_limit_minutes, 
            scoring,
            tiebreak_at,
            format: 'timeboxed'
          }
        });
      }
    }

    return {
      matches,
      rounds: [{
        number: 1,
        name: `Timeboxed (${time_limit_minutes}min)`,
        matches
      }],
      metadata: { format: 'timeboxed', time_limit_minutes, scoring }
    };
  }

  validateConfig(config: Record<string, any>): boolean {
    const { time_limit_minutes = 60 } = config;
    return time_limit_minutes >= 15 && time_limit_minutes <= 180;
  }

  getRequiredParticipants(): { min: number; max?: number } {
    return { min: 4, max: 16 };
  }

  advanceWinner(): DrawResult | null {
    return null;
  }
}

// Social Mixer Handler (Partner rotation)
export class SocialMixerHandler implements FormatHandler {
  generateDraw(participants: Participant[], config: Record<string, any>): DrawResult {
    const { rounds = 6, partner_rotation = true } = config;
    const matches: Match[] = [];
    const drawRounds: DrawResult['rounds'] = [];
    
    // Ensure even number of participants
    const evenParticipants = participants.length % 2 === 0 
      ? participants 
      : [...participants, { id: 'bye', name: 'BYE', seed: 999 }];
    
    for (let round = 1; round <= rounds; round++) {
      const roundMatches: Match[] = [];
      const shuffled = this.shuffleArray(evenParticipants);
      
      for (let i = 0; i < shuffled.length; i += 2) {
        if (shuffled[i].id !== 'bye' && shuffled[i + 1].id !== 'bye') {
          const match: Match = {
            id: `mixer-r${round}-${i/2 + 1}`,
            round,
            matchNumber: i/2 + 1,
            participant1: shuffled[i],
            participant2: shuffled[i + 1],
            status: 'scheduled',
            metadata: { format: 'social_mixer', round }
          };
          matches.push(match);
          roundMatches.push(match);
        }
      }
      
      drawRounds.push({
        number: round,
        name: `Runde ${round}`,
        matches: roundMatches
      });
    }

    return {
      matches,
      rounds: drawRounds,
      metadata: { format: 'social_mixer', total_rounds: rounds, partner_rotation }
    };
  }

  validateConfig(config: Record<string, any>): boolean {
    const { rounds = 6 } = config;
    return rounds >= 3 && rounds <= 10;
  }

  getRequiredParticipants(): { min: number; max?: number } {
    return { min: 6, max: 20 };
  }

  advanceWinner(): DrawResult | null {
    return null;
  }

  private shuffleArray<T>(array: T[]): T[] {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }
}

// Pro Set Handler (First to 8/9 games)
export class ProSetHandler implements FormatHandler {
  generateDraw(participants: Participant[], config: Record<string, any>): DrawResult {
    const { games_to_win = 8, tiebreak_at = 8, seeding = 'random' } = config;
    
    let seededParticipants = [...participants];
    if (seeding === 'random') {
      seededParticipants = this.shuffleArray(participants);
    } else if (seeding === 'rating') {
      seededParticipants.sort((a, b) => (b.rating || 0) - (a.rating || 0));
    }

    const matches: Match[] = [];
    const totalRounds = Math.ceil(Math.log2(participants.length));
    
    // Generate knockout structure with pro set scoring
    for (let round = 1; round <= totalRounds; round++) {
      const matchesInRound = round === 1 
        ? Math.ceil(participants.length / 2)
        : Math.pow(2, totalRounds - round);
      
      for (let i = 0; i < matchesInRound; i++) {
        const match: Match = {
          id: `proset-${round}-${i + 1}`,
          round,
          matchNumber: i + 1,
          status: 'scheduled',
          metadata: { 
            scoring: 'pro_set', 
            games_to_win, 
            tiebreak_at,
            format: `First to ${games_to_win} games`
          }
        };
        
        if (round === 1 && i < seededParticipants.length / 2) {
          match.participant1 = seededParticipants[i * 2];
          match.participant2 = seededParticipants[i * 2 + 1];
        }
        
        matches.push(match);
      }
    }

    const rounds: DrawResult['rounds'] = [];
    for (let round = 1; round <= totalRounds; round++) {
      const roundMatches = matches.filter(m => m.round === round);
      rounds.push({
        number: round,
        name: round === totalRounds ? 'Pro Set Finale' : `Pro Set Runde ${round}`,
        matches: roundMatches
      });
    }

    return {
      matches,
      rounds,
      metadata: { format: 'pro_set', games_to_win, tiebreak_at }
    };
  }

  validateConfig(config: Record<string, any>): boolean {
    const { games_to_win = 8 } = config;
    return [6, 8, 10].includes(games_to_win);
  }

  getRequiredParticipants(): { min: number; max?: number } {
    return { min: 2, max: 32 };
  }

  advanceWinner(): DrawResult | null {
    return null;
  }

  private shuffleArray<T>(array: T[]): T[] {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }
}

// Main Tournament Format Engine
export class TournamentFormatEngine {
  private handlers: Map<string, FormatHandler> = new Map();

  constructor() {
    // Register all format handlers
    this.handlers.set('knockout', new KnockoutHandler());
    this.handlers.set('round_robin', new RoundRobinHandler());
    this.handlers.set('uts', new UTSHandler());
    this.handlers.set('compass', new CompassHandler());
    this.handlers.set('team_tie', new TeamTieHandler());
    this.handlers.set('challenge_ladder', new ChallengeLadderHandler());
    this.handlers.set('timeboxed', new TimeboxedHandler());
    this.handlers.set('social_mixer', new SocialMixerHandler());
    this.handlers.set('pro_set', new ProSetHandler());
  }

  getAvailableFormats(): TournamentFormat[] {
    return [
      // Standard Formats
      {
        type: 'knockout',
        name: 'K.o.-System (Single Elimination)',
        description: 'Klassisches Ausscheidungsturnier - wer verliert, ist raus',
        config: { seeding: 'random', consolation_plate: false },
        default_time_config: {
          match_duration_minutes: 120,
          warmup_minutes: 5,
          changeover_minutes: 3,
          set_break_minutes: 2,
          between_match_buffer_minutes: 15,
          court_preparation_minutes: 10
        }
      },
      {
        type: 'round_robin',
        name: 'Round Robin (Jeder gegen jeden)',
        description: 'Jeder spielt gegen jeden - Tabellenmodus',
        config: { groups: 1, tiebreaker: ['head_to_head', 'set_ratio'] },
        default_time_config: {
          match_duration_minutes: 90,
          warmup_minutes: 5,
          changeover_minutes: 2,
          set_break_minutes: 1,
          between_match_buffer_minutes: 10,
          court_preparation_minutes: 5
        }
      },
      {
        type: 'compass',
        name: 'Compass Draw (E/W/N/S)',
        description: 'Vollständige Platzierung mit East/West/North/South Draws',
        config: { main_draw_size: 16, seeding: 'rating' },
        default_time_config: {
          match_duration_minutes: 120,
          warmup_minutes: 5,
          changeover_minutes: 3,
          set_break_minutes: 2,
          between_match_buffer_minutes: 15,
          court_preparation_minutes: 10
        }
      },
      
      // Innovative Formats
      {
        type: 'uts',
        name: 'UTS (Universal Tennis Scoring)',
        description: 'Revolutionäres Format: First-to-4 Games, Sudden Death bei 3:3',
        config: { best_of: 3, seeding: 'rating', sets_to_win: 2 },
        default_time_config: {
          match_duration_minutes: 90,
          warmup_minutes: 5,
          changeover_minutes: 1,
          set_break_minutes: 2,
          between_match_buffer_minutes: 15,
          court_preparation_minutes: 5
        }
      },
      
      // Team Formats
      {
        type: 'team_tie',
        name: 'Team Tie (Davis Cup Style)',
        description: 'Team-Wettkampf mit mehreren Einzeln und Doppel',
        config: { rubbers: ['singles1', 'singles2', 'doubles', 'singles3', 'singles4'] },
        default_time_config: {
          match_duration_minutes: 180,
          warmup_minutes: 10,
          changeover_minutes: 5,
          set_break_minutes: 3,
          between_match_buffer_minutes: 30,
          court_preparation_minutes: 15
        }
      },
      
      // Time-based Formats
      {
        type: 'timeboxed',
        name: 'Timeboxed Tournament',
        description: 'Zeitbegrenzte Matches mit flexiblem Scoring',
        config: { time_limit_minutes: 60, scoring: 'games', tiebreak_at: 6 },
        default_time_config: {
          match_duration_minutes: 60,
          warmup_minutes: 3,
          changeover_minutes: 2,
          set_break_minutes: 1,
          between_match_buffer_minutes: 5,
          court_preparation_minutes: 5
        }
      },
      
      // Fun Formats
      {
        type: 'social_mixer',
        name: 'Social Mixer (Partner Rotation)',
        description: 'Geselliges Format mit wechselnden Partnern und Gegnern',
        config: { rounds: 6, partner_rotation: true },
        default_time_config: {
          match_duration_minutes: 30,
          warmup_minutes: 2,
          changeover_minutes: 1,
          set_break_minutes: 1,
          between_match_buffer_minutes: 5,
          court_preparation_minutes: 3
        }
      },
      {
        type: 'pro_set',
        name: 'Pro Set Event',
        description: 'Schnelles Format: First-to-8 Games mit Tiebreak',
        config: { games_to_win: 8, tiebreak_at: 8, seeding: 'random' },
        default_time_config: {
          match_duration_minutes: 60,
          warmup_minutes: 3,
          changeover_minutes: 2,
          set_break_minutes: 1,
          between_match_buffer_minutes: 5,
          court_preparation_minutes: 5
        }
      },
      
      // Ladder Systems
      {
        type: 'challenge_ladder',
        name: 'Challenge Ladder',
        description: 'Flexible Herausforderungsleiter - spiele wann du willst',
        config: { challenge_window_days: 7, max_challenges_per_week: 2 },
        default_time_config: {
          match_duration_minutes: 90,
          warmup_minutes: 5,
          changeover_minutes: 3,
          set_break_minutes: 2,
          between_match_buffer_minutes: 10,
          court_preparation_minutes: 5
        }
      }
    ];
  }

  generateDraw(format: string, participants: Participant[], config: Record<string, any>): DrawResult {
    const handler = this.handlers.get(format);
    if (!handler) {
      throw new Error(`Unknown tournament format: ${format}`);
    }

    if (!handler.validateConfig(config)) {
      throw new Error(`Invalid configuration for format: ${format}`);
    }

    const requirements = handler.getRequiredParticipants(config);
    if (participants.length < requirements.min) {
      throw new Error(`Minimum ${requirements.min} participants required for ${format}`);
    }
    if (requirements.max && participants.length > requirements.max) {
      throw new Error(`Maximum ${requirements.max} participants allowed for ${format}`);
    }

    return handler.generateDraw(participants, config);
  }

  validateFormat(format: string, config: Record<string, any>): boolean {
    const handler = this.handlers.get(format);
    return handler ? handler.validateConfig(config) : false;
  }

  getRequiredParticipants(format: string, config: Record<string, any>): { min: number; max?: number } {
    const handler = this.handlers.get(format);
    if (!handler) {
      throw new Error(`Unknown tournament format: ${format}`);
    }
    return handler.getRequiredParticipants(config);
  }
}

// Singleton instance
export const tournamentFormatEngine = new TournamentFormatEngine();