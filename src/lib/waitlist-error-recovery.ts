import { supabase } from '@/integrations/supabase/client';

export interface RecoveryAction {
  type: 'rollback_booking' | 'retry_notification' | 'manual_intervention' | 'compensate_user';
  description: string;
  data?: any;
}

export interface ErrorRecoveryResult {
  success: boolean;
  actions: RecoveryAction[];
  requiresManualIntervention: boolean;
  compensationOffered?: boolean;
}

export class WaitlistErrorRecovery {
  
  /**
   * Comprehensive error recovery for failed auto-booking
   */
  static async recoverFromAutoBookingFailure(
    entryId: string,
    error: Error,
    bookingId?: string
  ): Promise<ErrorRecoveryResult> {
    const actions: RecoveryAction[] = [];
    let requiresManualIntervention = false;
    let compensationOffered = false;

    try {
      console.error('Auto-booking failure recovery initiated:', {
        entryId,
        bookingId,
        error: error.message
      });

      // 1. If booking was created but notification failed
      if (bookingId && error.message.includes('notification')) {
        const notificationRecovery = await this.recoverNotificationFailure(entryId, bookingId);
        actions.push(...notificationRecovery.actions);
        
        return {
          success: true,
          actions,
          requiresManualIntervention: false
        };
      }

      // 2. If booking creation failed after waitlist entry was marked as converted
      if (error.message.includes('booking') && !bookingId) {
        const bookingRecovery = await this.recoverBookingCreationFailure(entryId);
        actions.push(...bookingRecovery.actions);
        requiresManualIntervention = bookingRecovery.requiresManualIntervention;
      }

      // 3. If database constraint violation
      if (error.message.includes('constraint') || error.message.includes('unique')) {
        const constraintRecovery = await this.recoverConstraintViolation(entryId);
        actions.push(...constraintRecovery.actions);
      }

      // 4. If timeout or connection error
      if (error.message.includes('timeout') || error.message.includes('connection')) {
        const connectionRecovery = await this.recoverConnectionFailure(entryId);
        actions.push(...connectionRecovery.actions);
        requiresManualIntervention = connectionRecovery.requiresManualIntervention;
      }

      // 5. Log error for analysis
      await this.logErrorForAnalysis(entryId, error);

      return {
        success: actions.length > 0,
        actions,
        requiresManualIntervention,
        compensationOffered
      };

    } catch (recoveryError) {
      console.error('Error recovery failed:', recoveryError);
      
      return {
        success: false,
        actions: [{
          type: 'manual_intervention',
          description: 'Automatische Wiederherstellung fehlgeschlagen. Manuelle Intervention erforderlich.'
        }],
        requiresManualIntervention: true
      };
    }
  }

  /**
   * Recover from notification failures
   */
  private static async recoverNotificationFailure(
    entryId: string,
    bookingId: string
  ): Promise<{ actions: RecoveryAction[] }> {
    const actions: RecoveryAction[] = [];

    try {
      // Retry notification with exponential backoff
      const retryResult = await this.retryNotificationWithBackoff(entryId, bookingId);
      
      if (retryResult.success) {
        actions.push({
          type: 'retry_notification',
          description: 'Benachrichtigung erfolgreich nachgesendet.',
          data: { bookingId, attempts: retryResult.attempts }
        });
      } else {
        // Mark for manual notification
        await this.markForManualNotification(entryId, bookingId);
        actions.push({
          type: 'manual_intervention',
          description: 'Benachrichtigung wird manuell nachgesendet.'
        });
      }

    } catch (error) {
      console.error('Notification recovery failed:', error);
      actions.push({
        type: 'manual_intervention',
        description: 'Benachrichtigungswiederherstellung fehlgeschlagen.'
      });
    }

    return { actions };
  }

  /**
   * Recover from booking creation failures
   */
  private static async recoverBookingCreationFailure(
    entryId: string
  ): Promise<{ actions: RecoveryAction[]; requiresManualIntervention: boolean }> {
    const actions: RecoveryAction[] = [];
    let requiresManualIntervention = false;

    try {
      // First, reset waitlist entry status
      const { error: resetError } = await supabase
        .from('waitlist_entries')
        .update({ 
          status: 'waiting',
          updated_at: new Date().toISOString()
        })
        .eq('id', entryId);

      if (resetError) {
        console.error('Failed to reset waitlist entry:', resetError);
        requiresManualIntervention = true;
        actions.push({
          type: 'manual_intervention',
          description: 'Wartelisten-Eintrag konnte nicht zurückgesetzt werden.'
        });
      } else {
        actions.push({
          type: 'rollback_booking',
          description: 'Wartelisten-Eintrag wurde zurückgesetzt für erneuten Versuch.'
        });

        // Recalculate positions
        await this.recalculateWaitlistPositions(entryId);
      }

    } catch (error) {
      console.error('Booking creation recovery failed:', error);
      requiresManualIntervention = true;
      actions.push({
        type: 'manual_intervention',
        description: 'Buchungserstellung-Wiederherstellung fehlgeschlagen.'
      });
    }

    return { actions, requiresManualIntervention };
  }

  /**
   * Recover from constraint violations
   */
  private static async recoverConstraintViolation(
    entryId: string
  ): Promise<{ actions: RecoveryAction[] }> {
    const actions: RecoveryAction[] = [];

    try {
      // Get entry details
      const { data: entry, error } = await supabase
        .from('waitlist_entries')
        .select('*')
        .eq('id', entryId)
        .single();

      if (error || !entry) {
        actions.push({
          type: 'manual_intervention',
          description: 'Wartelisten-Eintrag nicht gefunden.'
        });
        return { actions };
      }

      // Check if booking slot is now taken
      const { data: existingBooking } = await supabase
        .from('bookings')
        .select('id, player_name')
        .eq('club_id', entry.club_id)
        .eq('booking_date', entry.preferred_date)
        .overlaps('time_range', `[${entry.start_time_range},${entry.end_time_range})`);

      if (existingBooking && existingBooking.length > 0) {
        // Slot is taken, offer alternative
        await this.offerAlternativeSlots(entryId);
        actions.push({
          type: 'compensate_user',
          description: 'Zeitslot bereits vergeben. Alternative Zeiten werden angeboten.'
        });
      } else {
        // Unknown constraint issue, manual intervention needed
        actions.push({
          type: 'manual_intervention',
          description: 'Unbekannter Datenbankkonflikt. Manuelle Prüfung erforderlich.'
        });
      }

    } catch (error) {
      console.error('Constraint violation recovery failed:', error);
      actions.push({
        type: 'manual_intervention',
        description: 'Constraint-Wiederherstellung fehlgeschlagen.'
      });
    }

    return { actions };
  }

  /**
   * Recover from connection failures
   */
  private static async recoverConnectionFailure(
    entryId: string
  ): Promise<{ actions: RecoveryAction[]; requiresManualIntervention: boolean }> {
    const actions: RecoveryAction[] = [];
    let requiresManualIntervention = false;

    try {
      // Schedule retry with exponential backoff
      const retryScheduled = await this.scheduleRetryWithBackoff(entryId);
      
      if (retryScheduled) {
        actions.push({
          type: 'retry_notification',
          description: 'Auto-Buchung wird automatisch wiederholt.'
        });
      } else {
        requiresManualIntervention = true;
        actions.push({
          type: 'manual_intervention',
          description: 'Verbindungsfehler - manuelle Bearbeitung erforderlich.'
        });
      }

    } catch (error) {
      console.error('Connection failure recovery failed:', error);
      requiresManualIntervention = true;
      actions.push({
        type: 'manual_intervention',
        description: 'Verbindungswiederherstellung fehlgeschlagen.'
      });
    }

    return { actions, requiresManualIntervention };
  }

  /**
   * Retry notification with exponential backoff
   */
  private static async retryNotificationWithBackoff(
    entryId: string,
    bookingId: string,
    maxAttempts: number = 3
  ): Promise<{ success: boolean; attempts: number }> {
    let attempts = 0;
    
    while (attempts < maxAttempts) {
      attempts++;
      const delay = Math.pow(2, attempts - 1) * 1000; // 1s, 2s, 4s
      
      try {
        await new Promise(resolve => setTimeout(resolve, delay));
        
        // Attempt to resend notification
        const { error } = await supabase.functions.invoke('send-waitlist-notification', {
          body: {
            entryId,
            bookingId,
            isRetry: true,
            attempt: attempts
          }
        });
        
        if (!error) {
          return { success: true, attempts };
        }
        
        console.warn(`Notification retry ${attempts} failed:`, error);
        
      } catch (error) {
        console.warn(`Notification retry ${attempts} error:`, error);
      }
    }
    
    return { success: false, attempts };
  }

  /**
   * Mark entry for manual notification
   */
  private static async markForManualNotification(
    entryId: string,
    bookingId: string
  ): Promise<void> {
    try {
      await supabase
        .from('waitlist_entries')
        .update({
          status: 'converted_to_booking',
          notes: `Manual notification required for booking ${bookingId}`,
          updated_at: new Date().toISOString()
        })
        .eq('id', entryId);
    } catch (error) {
      console.error('Failed to mark for manual notification:', error);
    }
  }

  /**
   * Recalculate waitlist positions after rollback
   */
  private static async recalculateWaitlistPositions(entryId: string): Promise<void> {
    try {
      // Get the entry to find club and date
      const { data: entry } = await supabase
        .from('waitlist_entries')
        .select('club_id, preferred_date')
        .eq('id', entryId)
        .single();

      if (!entry) return;

      // Trigger position recalculation by updating the entry
      await supabase
        .from('waitlist_entries')
        .update({ updated_at: new Date().toISOString() })
        .eq('id', entryId);

    } catch (error) {
      console.error('Failed to recalculate positions:', error);
    }
  }

  /**
   * Offer alternative time slots to user
   */
  private static async offerAlternativeSlots(entryId: string): Promise<void> {
    try {
      // This would integrate with a more sophisticated slot recommendation system
      await supabase
        .from('waitlist_entries')
        .update({
          notes: 'Alternative Zeitslots werden angeboten',
          updated_at: new Date().toISOString()
        })
        .eq('id', entryId);
    } catch (error) {
      console.error('Failed to offer alternatives:', error);
    }
  }

  /**
   * Schedule retry with backoff
   */
  private static async scheduleRetryWithBackoff(entryId: string): Promise<boolean> {
    try {
      // Mark for retry (would integrate with job queue in production)
      await supabase
        .from('waitlist_entries')
        .update({
          notes: 'Scheduled for retry due to connection failure',
          updated_at: new Date().toISOString()
        })
        .eq('id', entryId);
      
      return true;
    } catch (error) {
      console.error('Failed to schedule retry:', error);
      return false;
    }
  }

  /**
   * Log error for analysis and pattern detection
   */
  private static async logErrorForAnalysis(entryId: string, error: Error): Promise<void> {
    try {
      await supabase
        .from('audit_logs')
        .insert({
          resource_type: 'waitlist_entry',
          resource_id: entryId,
          action: 'auto_booking_failure',
          old_values: {},
          new_values: {
            error_message: error.message,
            error_stack: error.stack,
            timestamp: new Date().toISOString(),
            recovery_attempted: true
          }
        });
    } catch (logError) {
      console.error('Failed to log error:', logError);
    }
  }
}