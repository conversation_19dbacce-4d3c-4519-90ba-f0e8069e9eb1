import { supabase } from '@/integrations/supabase/client';
import { TimezoneAwareWaitlist } from './timezone-aware-waitlist';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface WaitlistValidationParams {
  clubId: string;
  userId: string;
  preferredDate: Date;
  startTimeRange: string;
  endTimeRange: string;
  preferredCourts: string[];
  autoBookingEnabled: boolean;
  playerName: string;
  partnerName?: string;
}

export class WaitlistValidationEngine {
  
  /**
   * Comprehensive validation for waitlist entries
   */
  static async validateWaitlistEntry(
    params: WaitlistValidationParams
  ): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // 1. Basic data validation
      const basicValidation = this.validateBasicData(params);
      errors.push(...basicValidation.errors);
      warnings.push(...basicValidation.warnings);

      // 2. User membership validation
      const membershipValidation = await this.validateUserMembership(
        params.userId, 
        params.clubId
      );
      errors.push(...membershipValidation.errors);

      // 3. Club settings validation
      const clubValidation = await this.validateClubSettings(params);
      errors.push(...clubValidation.errors);
      warnings.push(...clubValidation.warnings);

      // 4. Time and timezone validation
      const timeValidation = await this.validateTimeRanges(params);
      errors.push(...timeValidation.errors);
      warnings.push(...timeValidation.warnings);

      // 5. Court availability validation
      const courtValidation = await this.validateCourtAvailability(params);
      errors.push(...courtValidation.errors);
      warnings.push(...courtValidation.warnings);

      // 6. Auto-booking constraints validation
      if (params.autoBookingEnabled) {
        const autoBookingValidation = await this.validateAutoBookingConstraints(params);
        errors.push(...autoBookingValidation.errors);
        warnings.push(...autoBookingValidation.warnings);
      }

      // 7. Partner validation
      if (params.partnerName) {
        const partnerValidation = await this.validatePartner(params);
        warnings.push(...partnerValidation.warnings);
      }

      // 8. Booking rules validation
      const rulesValidation = await this.validateBookingRules(params);
      errors.push(...rulesValidation.errors);
      warnings.push(...rulesValidation.warnings);

      return {
        isValid: errors.length === 0,
        errors: [...new Set(errors)], // Remove duplicates
        warnings: [...new Set(warnings)]
      };

    } catch (error) {
      console.error('Validation error:', error);
      return {
        isValid: false,
        errors: ['Validierung fehlgeschlagen. Bitte versuchen Sie es erneut.'],
        warnings: []
      };
    }
  }

  /**
   * Basic data validation
   */
  private static validateBasicData(params: WaitlistValidationParams): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Date validation
    if (!params.preferredDate || params.preferredDate < new Date()) {
      errors.push('Das gewünschte Datum muss in der Zukunft liegen.');
    }

    // Time format validation
    const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
    if (!timeRegex.test(params.startTimeRange)) {
      errors.push('Ungültiges Startzeit-Format (HH:MM erwartet).');
    }
    if (!timeRegex.test(params.endTimeRange)) {
      errors.push('Ungültiges Endzeit-Format (HH:MM erwartet).');
    }

    // Time logic validation
    if (params.startTimeRange >= params.endTimeRange) {
      errors.push('Die Endzeit muss nach der Startzeit liegen.');
    }

    // Player name validation
    if (!params.playerName?.trim()) {
      errors.push('Spielername ist erforderlich.');
    }

    // Preferred courts validation
    if (params.preferredCourts.length === 0) {
      warnings.push('Keine bevorzugten Plätze ausgewählt. Alle verfügbaren Plätze werden berücksichtigt.');
    }

    return { isValid: errors.length === 0, errors, warnings };
  }

  /**
   * Validate user membership status
   */
  private static async validateUserMembership(
    userId: string, 
    clubId: string
  ): Promise<ValidationResult> {
    const errors: string[] = [];

    try {
      const { data: membership, error } = await supabase
        .from('club_memberships')
        .select('is_active, role')
        .eq('user_id', userId)
        .eq('club_id', clubId)
        .single();

      if (error || !membership) {
        errors.push('Keine gültige Mitgliedschaft gefunden.');
        return { isValid: false, errors, warnings: [] };
      }

      if (!membership.is_active) {
        errors.push('Ihre Mitgliedschaft ist nicht aktiv.');
      }

    } catch (error) {
      console.error('Membership validation error:', error);
      errors.push('Fehler bei der Mitgliedschaftsprüfung.');
    }

    return { isValid: errors.length === 0, errors, warnings: [] };
  }

  /**
   * Validate club settings and timezone
   */
  private static async validateClubSettings(
    params: WaitlistValidationParams
  ): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      const { data: club, error } = await supabase
        .from('clubs')
        .select('timezone, is_active, settings')
        .eq('id', params.clubId)
        .single();

      if (error || !club) {
        errors.push('Club nicht gefunden.');
        return { isValid: false, errors, warnings };
      }

      if (!club.is_active) {
        errors.push('Club ist nicht aktiv.');
      }

      // Cache timezone for later use
      if (club.timezone) {
        TimezoneAwareWaitlist.setCachedClubTimezone(params.clubId, club.timezone);
      }

    } catch (error) {
      console.error('Club validation error:', error);
      errors.push('Fehler bei der Club-Validierung.');
    }

    return { isValid: errors.length === 0, errors, warnings };
  }

  /**
   * Validate time ranges with timezone awareness
   */
  private static async validateTimeRanges(
    params: WaitlistValidationParams
  ): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      const clubTimezone = TimezoneAwareWaitlist.getCachedClubTimezone(params.clubId) || 'Europe/Berlin';

      // Check for DST transitions
      const startDst = TimezoneAwareWaitlist.handleDstTransition(
        params.startTimeRange, 
        params.preferredDate, 
        clubTimezone
      );
      
      const endDst = TimezoneAwareWaitlist.handleDstTransition(
        params.endTimeRange, 
        params.preferredDate, 
        clubTimezone
      );

      if (startDst.isDstTransition) {
        warnings.push(`Sommerzeitumstellung erkannt. Startzeit wurde angepasst: ${startDst.adjustedTime}`);
      }

      if (endDst.isDstTransition) {
        warnings.push(`Sommerzeitumstellung erkannt. Endzeit wurde angepasst: ${endDst.adjustedTime}`);
      }

      // Validate minimum and maximum duration
      const { utcDateTime: startUtc } = TimezoneAwareWaitlist.clubTimeToUtc(
        params.startTimeRange, 
        params.preferredDate, 
        clubTimezone
      );
      
      const { utcDateTime: endUtc } = TimezoneAwareWaitlist.clubTimeToUtc(
        params.endTimeRange, 
        params.preferredDate, 
        clubTimezone
      );

      const durationMinutes = (endUtc.getTime() - startUtc.getTime()) / (1000 * 60);
      
      if (durationMinutes < 30) {
        errors.push('Mindestbuchungsdauer beträgt 30 Minuten.');
      }
      
      if (durationMinutes > 240) {
        warnings.push('Buchungsdauer über 4 Stunden ungewöhnlich lang.');
      }

    } catch (error) {
      console.error('Time validation error:', error);
      errors.push('Fehler bei der Zeitvalidierung.');
    }

    return { isValid: errors.length === 0, errors, warnings };
  }

  /**
   * Validate court availability and compatibility
   */
  private static async validateCourtAvailability(
    params: WaitlistValidationParams
  ): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Check if preferred courts exist and are not locked
      if (params.preferredCourts.length > 0) {
        const { data: courts, error } = await supabase
          .from('courts')
          .select('id, number, locked, lock_reason, surface_type')
          .eq('club_id', params.clubId)
          .in('id', params.preferredCourts);

        if (error) {
          warnings.push('Fehler beim Laden der Platzinformationen.');
        } else {
          const lockedCourts = courts?.filter(court => court.locked) || [];
          if (lockedCourts.length > 0) {
            warnings.push(`Folgende Plätze sind gesperrt: ${lockedCourts.map(c => c.number).join(', ')}`);
          }
        }
      }

    } catch (error) {
      console.error('Court validation error:', error);
      warnings.push('Fehler bei der Platzvalidierung.');
    }

    return { isValid: errors.length === 0, errors, warnings };
  }

  /**
   * Validate auto-booking specific constraints
   */
  private static async validateAutoBookingConstraints(
    params: WaitlistValidationParams
  ): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Check if user already has an active auto-booking entry
      const { data: existingAutoBooking, error } = await supabase
        .from('waitlist_entries')
        .select('id')
        .eq('user_id', params.userId)
        .eq('auto_booking_enabled', true)
        .eq('status', 'waiting');

      if (error) {
        warnings.push('Fehler bei der Auto-Buchung-Validierung.');
      } else if (existingAutoBooking && existingAutoBooking.length > 0) {
        errors.push('Sie haben bereits eine aktive Auto-Zusage Warteliste.');
      }

    } catch (error) {
      console.error('Auto-booking validation error:', error);
      errors.push('Fehler bei der Auto-Buchung-Validierung.');
    }

    return { isValid: errors.length === 0, errors, warnings };
  }

  /**
   * Validate partner availability (soft check)
   */
  private static async validatePartner(
    params: WaitlistValidationParams
  ): Promise<ValidationResult> {
    const warnings: string[] = [];

    try {
      if (params.partnerName) {
        // Check if partner has conflicting bookings
        const { data: conflicts, error } = await supabase
          .from('bookings')
          .select('id')
          .eq('club_id', params.clubId)
          .eq('booking_date', params.preferredDate.toISOString().split('T')[0])
          .or(`player_name.eq.${params.partnerName},partner_name.eq.${params.partnerName}`)
          .overlaps('time_range', `[${params.startTimeRange},${params.endTimeRange})`);

        if (!error && conflicts && conflicts.length > 0) {
          warnings.push(`${params.partnerName} hat möglicherweise bereits eine Buchung zu dieser Zeit.`);
        }
      }

    } catch (error) {
      console.error('Partner validation error:', error);
      warnings.push('Fehler bei der Partner-Validierung.');
    }

    return { isValid: true, errors: [], warnings };
  }

  /**
   * Validate against booking rules
   */
  private static async validateBookingRules(
    params: WaitlistValidationParams
  ): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Get active booking rules for the club
      const { data: rules, error } = await supabase
        .from('booking_rules')
        .select('name, rule_type, conditions, actions')
        .eq('club_id', params.clubId)
        .eq('is_active', true);

      if (error) {
        warnings.push('Fehler beim Laden der Buchungsregeln.');
        return { isValid: true, errors, warnings };
      }

      // Process each rule (simplified - would need full rule engine implementation)
      for (const rule of rules || []) {
        if (rule.rule_type === 'advance_booking_limit') {
          const conditions = rule.conditions as any;
          const maxDaysAdvance = conditions?.max_days_advance || 7;
          
          const daysDifference = Math.ceil(
            (params.preferredDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
          );
          
          if (daysDifference > maxDaysAdvance) {
            errors.push(`Buchungen sind nur ${maxDaysAdvance} Tage im Voraus möglich.`);
          }
        }
      }

    } catch (error) {
      console.error('Booking rules validation error:', error);
      warnings.push('Fehler bei der Regelvalidierung.');
    }

    return { isValid: errors.length === 0, errors, warnings };
  }
}