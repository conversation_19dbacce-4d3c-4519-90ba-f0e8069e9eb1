import { format, parseISO, isValid } from 'date-fns';
import { formatInTimeZone, toZonedTime, fromZonedTime } from 'date-fns-tz';

interface ClubTimezoneConfig {
  clubId: string;
  timezone: string;
}

export class TimezoneAwareWaitlist {
  private static clubTimezones = new Map<string, string>();

  /**
   * Convert local club time to UTC for database storage
   */
  static clubTimeToUtc(
    localTime: string, 
    date: Date, 
    clubTimezone: string
  ): { utcTime: string; utcDateTime: Date } {
    try {
      // Combine date and time in club timezone
      const dateStr = format(date, 'yyyy-MM-dd');
      const localDateTime = parseISO(`${dateStr}T${localTime}`);
      
      // Convert to UTC
      const utcDateTime = fromZonedTime(localDateTime, clubTimezone);
      const utcTime = format(utcDateTime, 'HH:mm:ss');
      
      return { utcTime, utcDateTime };
    } catch (error) {
      console.error('Error converting club time to UTC:', error);
      throw new Error(`Zeitkonvertierung fehlgeschlagen: ${error}`);
    }
  }

  /**
   * Convert UTC time to local club time for display
   */
  static utcToClubTime(
    utcTime: string, 
    date: Date, 
    clubTimezone: string
  ): { localTime: string; localDateTime: Date } {
    try {
      const dateStr = format(date, 'yyyy-MM-dd');
      const utcDateTime = parseISO(`${dateStr}T${utcTime}Z`);
      
      // Convert to club timezone
      const localDateTime = toZonedTime(utcDateTime, clubTimezone);
      const localTime = format(localDateTime, 'HH:mm:ss');
      
      return { localTime, localDateTime };
    } catch (error) {
      console.error('Error converting UTC to club time:', error);
      throw new Error(`Zeitkonvertierung fehlgeschlagen: ${error}`);
    }
  }

  /**
   * Validate if time ranges overlap considering timezone
   */
  static timeRangesOverlap(
    range1Start: string,
    range1End: string,
    range2Start: string,
    range2End: string,
    date: Date,
    clubTimezone: string
  ): boolean {
    try {
      const dateStr = format(date, 'yyyy-MM-dd');
      
      const start1 = parseISO(`${dateStr}T${range1Start}`);
      const end1 = parseISO(`${dateStr}T${range1End}`);
      const start2 = parseISO(`${dateStr}T${range2Start}`);
      const end2 = parseISO(`${dateStr}T${range2End}`);
      
      // Convert all to UTC for comparison
      const utcStart1 = fromZonedTime(start1, clubTimezone);
      const utcEnd1 = fromZonedTime(end1, clubTimezone);
      const utcStart2 = fromZonedTime(start2, clubTimezone);
      const utcEnd2 = fromZonedTime(end2, clubTimezone);
      
      // Check overlap: not (end1 <= start2 || start1 >= end2)
      return !(utcEnd1 <= utcStart2 || utcStart1 >= utcEnd2);
    } catch (error) {
      console.error('Error checking time overlap:', error);
      return false;
    }
  }

  /**
   * Check if booking time is within business hours
   */
  static isWithinBusinessHours(
    startTime: string,
    endTime: string,
    date: Date,
    clubTimezone: string,
    businessHours: { start: string; end: string }
  ): boolean {
    try {
      const { localTime: localStart } = this.utcToClubTime(startTime, date, clubTimezone);
      const { localTime: localEnd } = this.utcToClubTime(endTime, date, clubTimezone);
      
      return localStart >= businessHours.start && localEnd <= businessHours.end;
    } catch (error) {
      console.error('Error checking business hours:', error);
      return false;
    }
  }

  /**
   * Handle daylight saving time transitions
   */
  static handleDstTransition(
    time: string,
    date: Date,
    clubTimezone: string
  ): { isDstTransition: boolean; adjustedTime?: string } {
    try {
      const dateStr = format(date, 'yyyy-MM-dd');
      const localDateTime = parseISO(`${dateStr}T${time}`);
      
      // Check if this time exists in the target timezone
      const utcTime = fromZonedTime(localDateTime, clubTimezone);
      const backToLocal = toZonedTime(utcTime, clubTimezone);
      
      const isDstTransition = Math.abs(
        localDateTime.getTime() - backToLocal.getTime()
      ) > 1000; // More than 1 second difference indicates DST issue
      
      if (isDstTransition) {
        const adjustedTime = format(backToLocal, 'HH:mm:ss');
        return { isDstTransition: true, adjustedTime };
      }
      
      return { isDstTransition: false };
    } catch (error) {
      console.error('Error handling DST transition:', error);
      return { isDstTransition: false };
    }
  }

  /**
   * Format time for display in club timezone
   */
  static formatTimeForClub(
    utcTime: string,
    date: Date,
    clubTimezone: string
  ): string {
    try {
      const { localDateTime } = this.utcToClubTime(utcTime, date, clubTimezone);
      return formatInTimeZone(localDateTime, clubTimezone, 'HH:mm');
    } catch (error) {
      console.error('Error formatting time for club:', error);
      return utcTime;
    }
  }

  /**
   * Cache club timezone for performance
   */
  static setCachedClubTimezone(clubId: string, timezone: string): void {
    this.clubTimezones.set(clubId, timezone);
  }

  static getCachedClubTimezone(clubId: string): string | undefined {
    return this.clubTimezones.get(clubId);
  }
}