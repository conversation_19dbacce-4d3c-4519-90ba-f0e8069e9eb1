import { format, parseISO, isValid } from 'date-fns';
import { formatInTimeZone, toZonedTime, fromZonedTime } from 'date-fns-tz';
import { TimezoneAwareWaitlist } from './timezone-aware-waitlist';

/**
 * Formatiere ein Datum in der Club-Zeitzone
 */
export function formatDateInClubTZ(
  date: Date | string, 
  clubTimezone: string, 
  options?: Intl.DateTimeFormatOptions
): string {
  const targetDate = typeof date === 'string' ? parseISO(date) : date;
  if (!isValid(targetDate)) {
    throw new Error('Invalid date provided');
  }
  
  return formatInTimeZone(targetDate, clubTimezone, 'dd.MM.yyyy', options);
}

/**
 * Formatiere eine Zeit in der Club-Zeitzone
 */
export function formatTimeInClubTZ(
  date: Date | string, 
  clubTimezone: string, 
  options?: Intl.DateTimeFormatOptions
): string {
  const targetDate = typeof date === 'string' ? parseISO(date) : date;
  if (!isValid(targetDate)) {
    throw new Error('Invalid date provided');
  }
  
  return formatInTimeZone(targetDate, clubTimezone, 'HH:mm', options);
}

/**
 * Formatiere Datum und Zeit in der Club-Zeitzone
 */
export function formatDateTimeInClubTZ(
  date: Date | string, 
  clubTimezone: string, 
  options?: Intl.DateTimeFormatOptions
): string {
  const targetDate = typeof date === 'string' ? parseISO(date) : date;
  if (!isValid(targetDate)) {
    throw new Error('Invalid date provided');
  }
  
  return formatInTimeZone(targetDate, clubTimezone, 'dd.MM.yyyy HH:mm', options);
}

/**
 * Konvertiere ein Datum in die Club-Zeitzone
 */
export function toClubTimezone(date: Date | string, clubTimezone: string): Date {
  const targetDate = typeof date === 'string' ? parseISO(date) : date;
  if (!isValid(targetDate)) {
    throw new Error('Invalid date provided');
  }
  
  return toZonedTime(targetDate, clubTimezone);
}

/**
 * Konvertiere lokale Club-Zeit zu UTC - uses TimezoneAwareWaitlist for consistency
 */
export function clubTimeToUTC(localTimeString: string, date: Date, clubTimezone: string): Date {
  const { utcDateTime } = TimezoneAwareWaitlist.clubTimeToUtc(localTimeString, date, clubTimezone);
  return utcDateTime;
}

/**
 * Konvertiere UTC Zeit zu Club-Zeit - uses TimezoneAwareWaitlist for consistency
 */
export function utcToClubTime(utcTime: string, date: Date, clubTimezone: string): Date {
  const { localDateTime } = TimezoneAwareWaitlist.utcToClubTime(utcTime, date, clubTimezone);
  return localDateTime;
}

/**
 * Prüfe ob ein Zeitslot in der Vergangenheit liegt (Club-Zeitzone)
 */
export function isPastSlotInClubTZ(date: Date, time: string, clubTimezone: string): boolean {
  try {
    const dateStr = format(date, 'yyyy-MM-dd');
    const localDateTime = parseISO(`${dateStr}T${time}`);
    
    if (!isValid(localDateTime)) {
      return false;
    }
    
    // Use TimezoneAwareWaitlist for consistent timezone conversion
    const { utcDateTime } = TimezoneAwareWaitlist.clubTimeToUtc(time, date, clubTimezone);
    const nowUtc = new Date();
    
    return utcDateTime < nowUtc;
  } catch (error) {
    console.error('Error checking if slot is in past:', error);
    return false;
  }
}

/**
 * Format time for display in club timezone
 */
export function formatTimeForClub(utcTime: string, date: Date, clubTimezone: string): string {
  return TimezoneAwareWaitlist.formatTimeForClub(utcTime, date, clubTimezone);
}

/**
 * Check if time ranges overlap in club timezone
 */
export function timeRangesOverlapInClubTZ(
  range1Start: string,
  range1End: string, 
  range2Start: string,
  range2End: string,
  date: Date,
  clubTimezone: string
): boolean {
  return TimezoneAwareWaitlist.timeRangesOverlap(
    range1Start, range1End, range2Start, range2End, date, clubTimezone
  );
}

/**
 * Check if booking time is within business hours
 */
export function isWithinBusinessHoursInClubTZ(
  startTime: string,
  endTime: string,
  date: Date,
  clubTimezone: string,
  businessHours: { start: string; end: string }
): boolean {
  return TimezoneAwareWaitlist.isWithinBusinessHours(
    startTime, endTime, date, clubTimezone, businessHours
  );
}