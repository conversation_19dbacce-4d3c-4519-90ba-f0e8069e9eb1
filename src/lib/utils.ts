import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import { config } from "@/config/environments"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// ===========================================
// EINHEITLICHE ZEITZONE- & DATUM-FUNKTIONEN
// ===========================================

/**
 * Konvertiert ein Datum/Zeit-String in die angegebene Zeitzone
 */
export function toTimezone(date: Date | string, timezone: string = config.timezone): Date {
  const d = typeof date === 'string' ? new Date(date) : date
  return new Date(d.toLocaleString('en-US', { timeZone: timezone }))
}

/**
 * Konvertiert ein Datum/Zeit-String in die Systemzeitzone (Europe/Berlin)
 * @deprecated Use toTimezone with explicit timezone instead
 */
export function toSystemTimezone(date: Date | string): Date {
  return toTimezone(date, config.timezone)
}

/**
 * Formatiert ein Datum im deutschen Format (TT.MM.JJJJ)
 */
export function formatDate(date: Date | string, options?: Intl.DateTimeFormatOptions, timezone?: string): string {
  const d = typeof date === 'string' ? new Date(date) : date
  const tz = timezone || config.timezone
  const defaultOptions: Intl.DateTimeFormatOptions = {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    timeZone: tz
  }
  return d.toLocaleDateString(config.locale, { ...defaultOptions, ...options })
}

/**
 * Formatiert eine Zeit im deutschen Format (HH:MM)
 */
export function formatTime(date: Date | string, options?: Intl.DateTimeFormatOptions, timezone?: string): string {
  const d = typeof date === 'string' ? new Date(date) : date
  const tz = timezone || config.timezone
  const defaultOptions: Intl.DateTimeFormatOptions = {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false,
    timeZone: tz
  }
  return d.toLocaleTimeString(config.locale, { ...defaultOptions, ...options })
}

/**
 * Formatiert Datum und Zeit zusammen im deutschen Format (TT.MM.JJJJ, HH:MM)
 */
export function formatDateTime(date: Date | string, options?: Intl.DateTimeFormatOptions, timezone?: string): string {
  const d = typeof date === 'string' ? new Date(date) : date
  const tz = timezone || config.timezone
  const defaultOptions: Intl.DateTimeFormatOptions = {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    hour12: false,
    timeZone: tz
  }
  return d.toLocaleString(config.locale, { ...defaultOptions, ...options })
}

/**
 * Erstellt ein neues Datum für heute in der angegebenen Zeitzone
 */
export function getTodayInTimezone(timezone?: string): Date {
  return toTimezone(new Date(), timezone || config.timezone)
}

/**
 * Erstellt ein neues Datum für heute in der Systemzeitzone
 * @deprecated Use getTodayInTimezone with explicit timezone instead
 */
export function getTodayInSystemTimezone(): Date {
  return getTodayInTimezone(config.timezone)
}

/**
 * Erstellt ein ISO-String für die Datenbank (immer UTC)
 * aber respektiert die Eingabe aus der Systemzeitzone
 */
export function toUTCISOString(date: Date | string): string {
  const d = typeof date === 'string' ? new Date(date) : date
  return d.toISOString()
}

/**
 * Parst einen ISO-String aus der Datenbank und konvertiert zur Systemzeitzone
 */
export function fromUTCISOString(isoString: string): Date {
  return toSystemTimezone(new Date(isoString))
}

/**
 * Debug-Funktion: Zeigt alle Zeitzonen-Informationen
 */
export function debugTimezone(date?: Date | string, timezone?: string) {
  const d = date ? (typeof date === 'string' ? new Date(date) : date) : new Date()
  const tz = timezone || config.timezone
  console.log('🕒 Timezone Debug:', {
    systemTimezone: config.timezone,
    currentTimezone: tz,
    locale: config.locale,
    originalDate: d.toISOString(),
    inSystemTZ: formatDateTime(d, undefined, config.timezone),
    inCurrentTZ: formatDateTime(d, undefined, tz),
    UTCOffset: d.getTimezoneOffset(),
    actualTZ: Intl.DateTimeFormat().resolvedOptions().timeZone
  })
}
