import { Navigate, useLocation, useParams } from "react-router-dom";
import AdminHeader from "@/components/admin/AdminHeader";
import AdminSidebar from "@/components/admin/AdminSidebar";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext";
import { useAdminRole } from "@/hooks/useAdminRole";
import { useTenant } from "@/contexts/TenantContext";
import tennisCourtCorner from "@/assets/tennis-court-corner.jpg";

interface AdminLayoutProps {
  children: React.ReactNode;
}

const AdminLayout = ({ children }: AdminLayoutProps) => {
  const { user, isLoading } = useAuth();
  const { isClubAdmin, isLoading: adminLoading } = useAdminRole();
  const { club, isLoading: tenantLoading } = useTenant();
  const location = useLocation();
  const { clubSlug } = useParams();

  // Show loading state
  if (isLoading || adminLoading || tenantLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-tennis-green mx-auto mb-4"></div>
          <p className="text-muted-foreground">Authentifizierung wird geladen...</p>
        </div>
      </div>
    );
  }

  // Redirect if user is not logged in
  if (!user) {
    return <Navigate to="/auth" state={{ from: location }} replace />;
  }

  // Redirect if user is not admin
  if (!isClubAdmin) {
    toast.error('Zugriff verweigert: Admin-Berechtigung erforderlich');
    return <Navigate to="/" replace />;
  }

  // For club-specific routes, validate that the club in URL matches resolved club
  if (clubSlug && club && club.slug !== clubSlug) {
    toast.error('Zugriff auf diesen Club nicht erlaubt');
    return <Navigate to={`/c/${club.slug}/admin`} replace />;
  }

  // If no club context but trying to access club-specific route, redirect to auth
  if (clubSlug && !club) {
    toast.error('Club nicht gefunden oder Zugriff verweigert');
    return <Navigate to="/auth" replace />;
  }

  return (
    <div 
      className="flex h-screen bg-background relative"
      style={{
        backgroundImage: `url(${tennisCourtCorner})`,
        backgroundSize: 'cover',
        backgroundPosition: 'bottom right',
        backgroundRepeat: 'no-repeat',
        backgroundAttachment: 'fixed'
      }}
    >
      {/* Overlay to make background more subtle */}
      <div className="absolute inset-0 bg-background" />
      
      <div className="relative z-10 flex h-screen w-full">
        <AdminSidebar />
        <div className="flex-1 flex flex-col overflow-hidden">
          <AdminHeader />
          <main className="flex-1 overflow-y-auto">
            <div className="container mx-auto px-6 py-8">
              {children}
            </div>
          </main>
        </div>
      </div>
    </div>
  );
};

export default AdminLayout;