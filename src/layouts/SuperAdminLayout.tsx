import { Navigate, useLocation } from "react-router-dom";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext";
import { useAdminRole } from "@/hooks/useAdminRole";
import { Shield, LayoutDashboard, Building2, Users, Globe, CreditCard, MessageSquare, Settings, Monitor } from "lucide-react";
import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import tennisCourtCorner from "@/assets/tennis-court-corner.jpg";

interface SuperAdminLayoutProps {
  children: React.ReactNode;
}

const SuperAdminLayout = ({ children }: SuperAdminLayoutProps) => {
  const { user, isLoading } = useAuth();
  const { isSuperAdmin, isLoading: adminLoading } = useAdminRole();
  const location = useLocation();

  // Show loading state
  if (isLoading || adminLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Authentifizierung wird geladen...</p>
        </div>
      </div>
    );
  }

  // Redirect if user is not logged in
  if (!user) {
    return <Navigate to="/auth" state={{ from: location }} replace />;
  }

  // Redirect if user is not super admin
  if (!isSuperAdmin) {
    toast.error('Zugriff verweigert: SuperAdmin-Berechtigung erforderlich');
    return <Navigate to="/" replace />;
  }

  const superAdminNavigation = [
    { name: "Dashboard", href: "/admin", icon: LayoutDashboard },
    { name: "Clubs", href: "/meta-admin/clubs", icon: Building2 },
    { name: "Benutzer", href: "/meta-admin/users", icon: Users },
    { name: "Domains", href: "/meta-admin/domains", icon: Globe },
    { name: "Billing", href: "/meta-admin/billing/plans", icon: CreditCard },
    { name: "Support", href: "/meta-admin/support/tickets", icon: MessageSquare },
    { name: "Monitoring", href: "/meta-admin/monitoring/logs", icon: Monitor },
    { name: "Einstellungen", href: "/meta-admin/settings", icon: Settings },
  ];

  return (
    <div 
      className="flex h-screen bg-background relative"
      style={{
        backgroundImage: `url(${tennisCourtCorner})`,
        backgroundSize: 'cover',
        backgroundPosition: 'bottom right',
        backgroundRepeat: 'no-repeat',
        backgroundAttachment: 'fixed'
      }}
    >
      {/* Overlay to make background more subtle */}
      <div className="absolute inset-0 bg-background" />
      
      <div className="relative z-10 flex h-screen w-full">
        {/* Sidebar */}
        <div className="w-64 bg-card border-r border-border flex flex-col">
          <div className="p-6">
            <div className="flex items-center gap-2">
              <Shield className="h-6 w-6 text-primary" />
              <span className="font-semibold text-lg">SuperAdmin</span>
            </div>
          </div>
          
          <Separator />
          
          <nav className="flex-1 p-4 space-y-2">
            {superAdminNavigation.map((item) => {
              const isActive = location.pathname === item.href || 
                              (item.href !== '/admin' && location.pathname.startsWith(item.href));
              
              return (
                <Button
                  key={item.name}
                  variant={isActive ? "default" : "ghost"}
                  size="sm"
                  className="w-full justify-start"
                  asChild
                >
                  <Link to={item.href}>
                    <item.icon className="h-4 w-4 mr-2" />
                    {item.name}
                  </Link>
                </Button>
              );
            })}
          </nav>
          
          <Separator />
          
          <div className="p-4">
            <p className="text-xs text-muted-foreground">
              Angemeldet als SuperAdmin
            </p>
            <p className="text-sm font-medium truncate">{user.email}</p>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          <header className="bg-card border-b border-border px-6 py-4">
            <div className="flex items-center justify-between">
              <h1 className="text-xl font-semibold">SuperAdmin Panel</h1>
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">Vollzugriff auf alle Bereiche</span>
                <Shield className="h-4 w-4 text-primary" />
              </div>
            </div>
          </header>
          
          <main className="flex-1 overflow-y-auto">
            <div className="container mx-auto px-6 py-8">
              {children}
            </div>
          </main>
        </div>
      </div>
    </div>
  );
};

export default SuperAdminLayout;