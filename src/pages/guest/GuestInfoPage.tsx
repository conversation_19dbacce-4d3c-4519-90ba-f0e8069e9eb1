import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Clock, MapPin, Phone, Mail } from "lucide-react";

const GuestInfoPage = () => {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-foreground mb-2">Club Informationen</h1>
        <p className="text-muted-foreground">
          Erfahren Sie mehr über unseren Club und unsere Angebote.
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Öffnungszeiten
            </CardTitle>
            <CardDescription>
              Wann Sie unsere Anlagen nutzen können
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between">
              <span>Montag - Freitag:</span>
              <span>06:00 - 22:00</span>
            </div>
            <div className="flex justify-between">
              <span>Samstag - Sonntag:</span>
              <span>08:00 - 20:00</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Standort
            </CardTitle>
            <CardDescription>
              Wo Sie uns finden
            </CardDescription>
          </CardHeader>
          <CardContent>
            <address className="not-italic text-sm">
              Musterstraße 123<br />
              12345 Musterstadt<br />
              Deutschland
            </address>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Phone className="h-5 w-5" />
              Kontakt
            </CardTitle>
            <CardDescription>
              So erreichen Sie uns
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex items-center gap-2">
              <Phone className="h-4 w-4" />
              <span>+49 123 456 789</span>
            </div>
            <div className="flex items-center gap-2">
              <Mail className="h-4 w-4" />
              <span><EMAIL></span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Preise</CardTitle>
            <CardDescription>
              Unsere aktuellen Tarife
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between">
              <span>Platz pro Stunde (Hauptzeit):</span>
              <span>25,00 €</span>
            </div>
            <div className="flex justify-between">
              <span>Platz pro Stunde (Nebenzeit):</span>
              <span>20,00 €</span>
            </div>
            <div className="text-xs text-muted-foreground mt-2">
              Hauptzeit: Mo-Fr 16:00-20:00, Sa-So 10:00-18:00
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default GuestInfoPage;