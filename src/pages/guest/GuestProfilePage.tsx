import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { User, Mail, Phone, MapPin, Calendar, Settings, UserPlus } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { useNavigate, useLocation } from "react-router-dom";

const GuestProfilePage = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Extract club slug from current path
  const pathParts = location.pathname.split('/');
  const clubSlug = pathParts[2];

  const handleLogin = () => {
    navigate(`/c/${clubSlug}/auth`);
  };

  const handleRegister = () => {
    navigate(`/c/${clubSlug}/register`);
  };

  // If user is logged in, show basic profile info
  if (user) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Mein Profil</h1>
          <p className="text-muted-foreground mt-2">
            Ihre Benutzerinformationen und Einstellungen
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Benutzerinformationen
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                  <User className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <p className="font-medium text-foreground">{user.email}</p>
                  <Badge variant="secondary">Gast</Badge>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Mail className="h-4 w-4" />
                  {user.email}
                </div>
                
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Calendar className="h-4 w-4" />
                  Angemeldet seit: {new Date(user.created_at).toLocaleDateString('de-DE')}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Zugriffsberechtigungen
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-foreground">Buchungskalender einsehen</span>
                  <Badge variant="outline" className="text-green-600 border-green-600">
                    Verfügbar
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-foreground">Club News lesen</span>
                  <Badge variant="outline" className="text-green-600 border-green-600">
                    Verfügbar
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-foreground">Plätze buchen</span>
                  <Badge variant="outline" className="text-red-600 border-red-600">
                    Nur für Mitglieder
                  </Badge>
                </div>
              </div>
              
              <div className="pt-4 border-t">
                <p className="text-sm text-muted-foreground mb-3">
                  Für erweiterte Funktionen registrieren Sie sich als Mitglied.
                </p>
                <Button 
                  onClick={handleRegister}
                  className="w-full"
                  variant="outline"
                >
                  <UserPlus className="h-4 w-4 mr-2" />
                  Mitgliedschaft beantragen
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // If user is not logged in, show login prompt
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-foreground">Mein Profil</h1>
        <p className="text-muted-foreground mt-2">
          Melden Sie sich an, um Ihre Profilinformationen zu verwalten
        </p>
      </div>

      <Card className="max-w-md mx-auto">
        <CardHeader className="text-center">
          <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
            <User className="h-8 w-8 text-muted-foreground" />
          </div>
          <CardTitle>Anmeldung erforderlich</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 text-center">
          <p className="text-muted-foreground">
            Um Ihr Profil zu verwalten und alle Club-Features zu nutzen, melden Sie sich bitte an oder registrieren Sie sich.
          </p>
          
          <div className="space-y-2">
            <Button onClick={handleLogin} className="w-full">
              Anmelden
            </Button>
            <Button onClick={handleRegister} variant="outline" className="w-full">
              <UserPlus className="h-4 w-4 mr-2" />
              Registrieren
            </Button>
          </div>
          
          <div className="pt-4 border-t">
            <p className="text-xs text-muted-foreground">
              Als Gast können Sie bereits den Buchungskalender einsehen und Club-News lesen.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default GuestProfilePage;