import { Helmet } from "react-helmet";
import MarketingHeader from "@/components/marketing/MarketingHeader";
import Hero from "@/components/marketing/Hero";
import FeaturesForClubs from "@/components/marketing/FeaturesForClubs";
import FeaturesForMembers from "@/components/marketing/FeaturesForMembers";
import AIFeatures from "@/components/marketing/AIFeatures";
import Pricing from "@/components/marketing/Pricing";
import FAQ from "@/components/marketing/FAQ";
import MarketingFooter from "@/components/marketing/MarketingFooter";

const LandingPage = () => {
  return (
    <>
      <Helmet>
        <title>Courtwaive – Moderne Tennis-Management-Plattform mit KI</title>
        <meta 
          name="description" 
          content="Mitgliederverwaltung, smartes Platz-Booking, Arbeitsdienste, Gebühren & Kommunikation – alles in einer Plattform. 14 Tage gratis testen." 
        />
        <meta property="og:title" content="Courtwaive – Tennis-Management, das mitdenkt" />
        <meta 
          property="og:description" 
          content="Weniger Verwaltung, mehr Tennis – KI setzt eure Regeln um und hält alles fair und transparent." 
        />
        <meta property="og:type" content="website" />
        <link rel="canonical" href="https://courtwaive.de" />
      </Helmet>

      <div className="min-h-screen bg-bg">
        <MarketingHeader />
        
        <main>
          <Hero />
          
          <section id="features">
            <FeaturesForClubs />
            <FeaturesForMembers />
          </section>
          
          <section id="ai-features">
            <AIFeatures />
          </section>
          
          <section id="screens-gallery">
            <div className="py-24 lg:py-32 bg-bg-subtle">
              <div className="max-w-7xl mx-auto px-4">
                <div className="text-center mb-16">
                  <div className="mb-6">
                    <span className="text-marketing-micro font-semibold text-accent-purple uppercase tracking-wider">
                      Screens & Features
                    </span>
                  </div>
                  <h2 className="text-marketing-h2 lg:text-4xl font-bold mb-6 text-fg-strong leading-tight">
                    Alles was ihr braucht, an einem Ort
                  </h2>
                  <p className="text-marketing-body text-fg-muted max-w-3xl mx-auto">
                    Von der KI-gestützten Regelerstellung bis zum Member-Dashboard – entdeckt die wichtigsten Features in Aktion.
                  </p>
                </div>
                
                {/* Clubverwaltung Features */}
                <div className="mb-12">
                  <div className="mb-8">
                    <h3 className="text-xl font-semibold text-fg-strong mb-2">Für die Clubverwaltung</h3>
                    <p className="text-fg-muted">Intelligente Tools für effiziente Vereinsführung</p>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {[
                      { 
                        icon: "🏆", 
                        title: "Admin-Dashboard", 
                        description: "Alle wichtigen Vereinskennzahlen, anstehende Buchungen und Systemaktivitäten in einem übersichtlichen Dashboard. Mit Echtzeit-Updates und intelligenten Benachrichtigungen.",
                        hasAI: false
                      },
                      { 
                        icon: "🤖", 
                        title: "Buchungsregeln", 
                        description: "Einfach in natürlicher Sprache beschreiben, welche Buchungsregeln gelten sollen. Unsere KI wandelt eure Anforderungen automatisch in technische Regeln um.",
                        hasAI: true
                      },
                      { 
                        icon: "🏟️", 
                        title: "Intelligente Platzverwaltung", 
                        description: "Verwaltet eure Tennisplätze mit flexiblen Verfügbarkeitszeiten, Wartungszyklen und dynamischen Preismodellen. Automatische Optimierung der Platzauslastung.",
                        hasAI: true
                      },
                      { 
                        icon: "👥", 
                        title: "Mitgliederverwaltung", 
                        description: "Komplette Verwaltung aller Vereinsmitglieder mit Rollen, Berechtigungen, Mitgliedschaftstypen und automatischen Benachrichtigungen. Integrierte Team-Zuordnung.",
                        hasAI: false
                      },
                      { 
                        icon: "💳", 
                        title: "Gebühren & Beiträge", 
                        description: "Flexible Gebührenmodelle für verschiedene Mitgliedschaftstypen, automatische Beitragsabrechnung und transparente Kostenübersicht für Mitglieder.",
                        hasAI: false
                      },
                      { 
                        icon: "🛠️", 
                        title: "Arbeitsdienste", 
                        description: "KI-gestützte Vorschläge für Vereinstätigkeiten, automatische Erstellung und Verwaltung von Arbeitsdiensten sowie Jahresübersicht geleisteter Tätigkeiten.",
                        hasAI: true
                      }
                    ].map((item, index) => (
                      <div key={index} className="group">
                        <div className="flex flex-col items-start space-y-4 p-6 rounded-2xl backdrop-blur-sm border border-blue-100/30 bg-blue-50/20 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
                          <div className="flex items-center gap-3">
                            <div className="text-3xl">{item.icon}</div>
                            <div className="flex flex-col">
                              <div className="flex items-center gap-2">
                                <h4 className="font-semibold text-fg-strong text-lg">
                                  {item.title}
                                </h4>
                                {item.hasAI && (
                                  <div 
                                    className="p-0.5 rounded-full"
                                    style={{ background: 'linear-gradient(135deg, #8b5cf6, #3b82f6)' }}
                                  >
                                    <div 
                                      className="px-2.5 py-0.5 rounded-full text-xs font-semibold bg-white"
                                    >
                                      <span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                                        KI
                                      </span>
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                          <p className="text-sm text-fg-muted leading-relaxed">
                            {item.description}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Mitglieder Features */}
                <div>
                  <div className="mb-8">
                    <h3 className="text-xl font-semibold text-fg-strong mb-2">Für ihre Mitglieder</h3>
                    <p className="text-fg-muted">Einfache Bedienung, faire Buchungen, bessere Kommunikation</p>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {[
                      { 
                        icon: "⚡", 
                        title: "Member-Dashboard", 
                        description: "Persönlicher Bereich für Mitglieder mit Wartelisten-Management, Buchungsübersicht, Arbeitsdienst-Status und schnellem Zugang zu allen wichtigen Funktionen.",
                        hasAI: true
                      },
                      { 
                        icon: "⏰", 
                        title: "Wartelisten-Management", 
                        description: "Automatische Benachrichtigungen bei freien Plätzen, intelligente Vergabe nach fairen Regeln und transparente Wartelisten-Position für alle Mitglieder.",
                        hasAI: true
                      },
                      { 
                        icon: "💬", 
                        title: "Team-Kommunikation", 
                        description: "Nachrichten-Austausch innerhalb von Mannschaften, Gruppen-Chats für Events und direkte Kommunikation zwischen Spielpartnern.",
                        hasAI: false
                      },
                      { 
                        icon: "🎾", 
                        title: "Schnelle Buchungen", 
                        description: "Intuitive Platz-Buchung mit wenigen Klicks, Match-Pool zum Finden von Spielpartnern und spontane Buchungen über verfügbare Zeitfenster.",
                        hasAI: false
                      }
                    ].map((item, index) => (
                      <div key={index} className="group">
                        <div className="flex flex-col items-start space-y-4 p-6 rounded-2xl backdrop-blur-sm border border-green-100/30 bg-green-50/20 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
                          <div className="flex items-center gap-3">
                            <div className="text-3xl">{item.icon}</div>
                            <div className="flex flex-col">
                              <div className="flex items-center gap-2">
                                <h4 className="font-semibold text-fg-strong text-lg">
                                  {item.title}
                                </h4>
                                {item.hasAI && (
                                  <div 
                                    className="p-0.5 rounded-full"
                                    style={{ background: 'linear-gradient(135deg, #8b5cf6, #3b82f6)' }}
                                  >
                                    <div 
                                      className="px-2.5 py-0.5 rounded-full text-xs font-semibold bg-white"
                                    >
                                      <span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                                        KI
                                      </span>
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                          <p className="text-sm text-fg-muted leading-relaxed">
                            {item.description}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </section>
          
          <section id="pricing">
            <Pricing />
          </section>
          
          <section id="faq">
            <FAQ />
          </section>
        </main>
        
        <MarketingFooter />
      </div>
    </>
  );
};

export default LandingPage;