import BookingCalendar from "@/components/BookingCalendar";
import MemberDashboardOverview from "@/components/member/dashboard/MemberDashboardOverview";
import { WaitlistSection } from "@/components/member/dashboard/WaitlistSection";
import { WorkServiceProgress } from "@/components/member/dashboard/WorkServiceProgress";

const MemberDashboardPage = () => {
  return (
    <div className="space-y-2">
      <div className="mb-2">
        <h1 className="text-xl font-bold text-foreground mb-0">Dashboard</h1>
        <p className="text-xs text-muted-foreground">
          Willkommen im Mitgliederbereich
        </p>
      </div>

      {/* Overview section with all 3 cards in one row */}
      <div className="grid gap-2 md:grid-cols-3 mb-2">
        <MemberDashboardOverview />
        <WorkServiceProgress />
      </div>

      {/* Waitlist section */}
      <WaitlistSection />

      {/* Unified booking calendar */}
      <div>
        <BookingCalendar />
      </div>
    </div>
  );
};

export default MemberDashboardPage;