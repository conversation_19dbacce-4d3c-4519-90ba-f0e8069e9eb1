import { useEffect, useState, use<PERSON>emo } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { supabase } from "@/integrations/supabase/client";
import { Calendar, Clock, MapPin, Users, Filter, BarChart3 } from "lucide-react";
import { format, isAfter, isBefore, startOfDay, parse } from "date-fns";
import { de } from "date-fns/locale";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import {
  BarChart,
  Bar,
  CartesianGrid,
  XAxis,
  YAxis,
} from "recharts";

interface Booking {
  id: string;
  booking_date: string;
  start_time: string;
  end_time: string;
  court_id: string;
  player_name: string;
  partner_name?: string | null;
  created_by?: string | null;
  created_at: string;
}

interface Court {
  id: string;
  number: number;
  surface_type?: string;
}

const MemberBookingsPage = () => {
  const [userId, setUserId] = useState<string | null>(null);
  const [fullName, setFullName] = useState<string>("");
  const [allBookings, setAllBookings] = useState<Booking[]>([]);
  const [courts, setCourts] = useState<Court[]>([]);
  const [loading, setLoading] = useState(true);
  const [courtsMap, setCourtsMap] = useState<Record<string, Court>>({});
  const [joinInfo, setJoinInfo] = useState<{ joinDate?: string; category?: string }>({});

  const loadData = async () => {
    try {
      const { data: userRes } = await supabase.auth.getUser();
      const uid = userRes.user?.id || null;
      setUserId(uid);

      // Load user profile to get full name and membership info
      let name = "";
      if (uid) {
        const { data: profile } = await supabase
          .from("profiles")
          .select("first_name, last_name, join_date, membership_category")
          .eq("id", uid)
          .maybeSingle();
        if (profile?.first_name || profile?.last_name) {
          name = `${profile.first_name ?? ""} ${profile.last_name ?? ""}`.trim();
        }
        setJoinInfo({ joinDate: profile?.join_date, category: profile?.membership_category });
      }
      setFullName(name);

      // Load courts
      const { data: courtsData } = await supabase
        .from("courts")
        .select("id, number, surface_type")
        .order("number");
      setCourts(courtsData || []);
      
      // Create courts map for quick lookup
      const cMap: Record<string, Court> = {};
      (courtsData || []).forEach(c => { cMap[c.id] = c; });
      setCourtsMap(cMap);

      // Load all bookings where user is involved
      if (name) {
        const { data: bookingsData } = await supabase
          .from("bookings")
          .select("*")
          .or(`player_name.eq.${name},partner_name.eq.${name}`)
          .order("booking_date", { ascending: false })
          .order("start_time", { ascending: false });

        setAllBookings(bookingsData || []);
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  const getCourtInfo = (courtId: string) => {
    return courts.find(c => c.id === courtId);
  };

  const getBookingStatus = (booking: Booking) => {
    const bookingDate = new Date(`${booking.booking_date}T${booking.start_time}`);
    const now = new Date();
    
    if (isAfter(bookingDate, now)) {
      return { status: "upcoming", label: "Anstehend", variant: "default" as const };
    } else {
      return { status: "completed", label: "Gespielt", variant: "secondary" as const };
    }
  };

  const upcomingBookings = allBookings.filter(b => {
    const bookingDate = new Date(`${b.booking_date}T${b.start_time}`);
    return isAfter(bookingDate, new Date());
  });

  const pastBookings = allBookings.filter(b => {
    const bookingDate = new Date(`${b.booking_date}T${b.start_time}`);
    return isBefore(bookingDate, new Date());
  });

  const stats = {
    total: allBookings.length,
    upcoming: upcomingBookings.length,
    thisMonth: allBookings.filter(b => {
      const bookingDate = new Date(b.booking_date);
      const now = new Date();
      return bookingDate.getMonth() === now.getMonth() && 
             bookingDate.getFullYear() === now.getFullYear();
    }).length,
    asPlayer: allBookings.filter(b => b.player_name === fullName).length,
    asPartner: allBookings.filter(b => b.partner_name === fullName).length
  };

  // Calculate total playing time
  const totalPlayingMinutes = useMemo(() => {
    return allBookings.reduce((total, booking) => {
      const start = parse(booking.start_time, 'HH:mm:ss', new Date());
      const end = parse(booking.end_time, 'HH:mm:ss', new Date());
      const durationMs = end.getTime() - start.getTime();
      const durationMinutes = durationMs / (1000 * 60);
      return total + durationMinutes;
    }, 0);
  }, [allBookings]);

  const playingTimeStats = useMemo(() => {
    const hours = Math.floor(totalPlayingMinutes / 60);
    const minutes = totalPlayingMinutes % 60;
    return { hours, minutes, total: totalPlayingMinutes };
  }, [totalPlayingMinutes]);

  // Statistics calculations (from the old stats page)
  const toDateTime = (b: Booking) => parse(`${b.booking_date} ${b.start_time}`, "yyyy-MM-dd HH:mm:ss", new Date());

  const last12Months = useMemo(() => {
    const arr: { key: string; label: string }[] = [];
    const now = new Date();
    for (let i = 11; i >= 0; i--) {
      const d = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const key = format(d, "yyyy-MM");
      const label = format(d, "MMM yy", { locale: de });
      arr.push({ key, label });
    }
    return arr;
  }, []);

  const monthlyData = useMemo(() => {
    const counts = new Map<string, number>();
    for (const b of allBookings) {
      const key = b.booking_date.slice(0, 7); // yyyy-MM
      counts.set(key, (counts.get(key) || 0) + 1);
    }
    return last12Months.map(({ key, label }) => ({ month: label, count: counts.get(key) || 0 }));
  }, [allBookings, last12Months]);

  const weekdayOrder = ['Mo', 'Di', 'Mi', 'Do', 'Fr', 'Sa', 'So'];
  const weekdayData = useMemo(() => {
    const counts = new Map<string, number>(weekdayOrder.map(d => [d, 0]));
    for (const b of allBookings) {
      const d = toDateTime(b);
      const lbl = format(d, 'EEE', { locale: de });
      const short = lbl.charAt(0).toUpperCase() + lbl.slice(1, 2); // Mo, Di, ...
      counts.set(short, (counts.get(short) || 0) + 1);
    }
    return weekdayOrder.map(day => ({ day, count: counts.get(day) || 0 }));
  }, [allBookings]);

  const hourData = useMemo(() => {
    const counts = Array.from({ length: 24 }, () => 0);
    for (const b of allBookings) {
      const h = parse(b.start_time, 'HH:mm:ss', new Date()).getHours();
      counts[h]++;
    }
    return counts.map((c, h) => ({ hour: `${h.toString().padStart(2, '0')}:00`, count: c }));
  }, [allBookings]);

  const topPartners = useMemo(() => {
    if (!fullName) return [] as { name: string; count: number }[];
    const counts = new Map<string, number>();
    for (const b of allBookings) {
      const other = b.player_name === fullName ? (b.partner_name || '') : (b.partner_name === fullName ? b.player_name : '');
      if (!other) continue;
      counts.set(other, (counts.get(other) || 0) + 1);
    }
    return Array.from(counts.entries()).map(([name, count]) => ({ name, count })).sort((a, b) => b.count - a.count).slice(0, 5);
  }, [allBookings, fullName]);

  const courtStats = useMemo(() => {
    const counts = new Map<string, number>();
    for (const b of allBookings) {
      counts.set(b.court_id, (counts.get(b.court_id) || 0) + 1);
    }
    const items = Array.from(counts.entries()).map(([courtId, count]) => ({
      courtId,
      courtLabel: courtsMap[courtId]?.number ? `Platz ${courtsMap[courtId].number}` : 'Platz',
      count,
    })).sort((a, b) => b.count - a.count);
    return items;
  }, [allBookings, courtsMap]);

  const favoriteCourt = courtStats[0]?.courtLabel;

  const chartConfig = {
    bookings: {
      label: "Buchungen",
      color: "hsl(var(--primary))",
    },
  } as const;

  const BookingCard = ({ booking }: { booking: Booking }) => {
    const court = getCourtInfo(booking.court_id);
    const status = getBookingStatus(booking);
    const isPlayerBooking = booking.player_name === fullName;
    const partner = isPlayerBooking ? booking.partner_name : booking.player_name;

    return (
      <Card className="mb-4">
        <CardContent className="p-4">
          <div className="flex justify-between items-start mb-3">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="font-medium">
                {format(new Date(booking.booking_date), "dd.MM.yyyy", { locale: de })}
              </span>
            </div>
            <Badge variant={status.variant}>{status.label}</Badge>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">
                {booking.start_time.slice(0, 5)} - {booking.end_time.slice(0, 5)}
              </span>
            </div>
            
            <div className="flex items-center gap-2">
              <MapPin className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">
                Platz {court?.number || "?"}
                {court?.surface_type && (
                  <span className="text-muted-foreground ml-1">({court.surface_type})</span>
                )}
              </span>
            </div>
            
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">
                {partner || "Einzelspiel"}
              </span>
            </div>
            
            <div className="text-xs text-muted-foreground">
              Gebucht: {format(new Date(booking.created_at), "dd.MM.yy", { locale: de })}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-foreground mb-2">Meine Buchungen</h1>
        <p className="text-muted-foreground">
          Übersicht über alle Ihre Buchungen
        </p>
      </div>

      {loading ? (
        <div className="text-center py-8">
          <p className="text-muted-foreground">Buchungen werden geladen...</p>
        </div>
      ) : (
        <>
          {/* Statistics Cards */}
          <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-primary">{stats.total}</div>
                <div className="text-xs text-muted-foreground">Gesamt</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-green-600">{stats.upcoming}</div>
                <div className="text-xs text-muted-foreground">Anstehend</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-blue-600">{stats.thisMonth}</div>
                <div className="text-xs text-muted-foreground">Diesen Monat</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-purple-600">{stats.asPlayer}</div>
                <div className="text-xs text-muted-foreground">Als Spieler</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-orange-600">{stats.asPartner}</div>
                <div className="text-xs text-muted-foreground">Als Partner</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-lg font-bold text-teal-600">
                  {playingTimeStats.hours}h {playingTimeStats.minutes}m
                </div>
                <div className="text-xs text-muted-foreground">Spielzeit</div>
              </CardContent>
            </Card>
          </div>

          {/* Bookings Tabs */}
          <Tabs defaultValue="all" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="all">Alle ({stats.total})</TabsTrigger>
              <TabsTrigger value="upcoming">Anstehend ({stats.upcoming})</TabsTrigger>
              <TabsTrigger value="past">Vergangen ({pastBookings.length})</TabsTrigger>
              <TabsTrigger value="statistics">Statistiken</TabsTrigger>
            </TabsList>
            
            <TabsContent value="all" className="mt-6">
              {allBookings.length === 0 ? (
                <Card>
                  <CardContent className="p-8 text-center">
                    <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2">Keine Buchungen gefunden</h3>
                    <p className="text-muted-foreground">
                      Sie haben noch keine Buchungen vorgenommen.
                    </p>
                  </CardContent>
                </Card>
              ) : (
                <div>
                  {allBookings.map(booking => (
                    <BookingCard key={booking.id} booking={booking} />
                  ))}
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="upcoming" className="mt-6">
              {upcomingBookings.length === 0 ? (
                <Card>
                  <CardContent className="p-8 text-center">
                    <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2">Keine anstehenden Buchungen</h3>
                    <p className="text-muted-foreground">
                      Sie haben derzeit keine anstehenden Buchungen.
                    </p>
                  </CardContent>
                </Card>
              ) : (
                <div>
                  {upcomingBookings.map(booking => (
                    <BookingCard key={booking.id} booking={booking} />
                  ))}
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="past" className="mt-6">
              {pastBookings.length === 0 ? (
                <Card>
                  <CardContent className="p-8 text-center">
                    <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2">Keine vergangenen Buchungen</h3>
                    <p className="text-muted-foreground">
                      Sie haben noch keine vergangenen Buchungen.
                    </p>
                  </CardContent>
                </Card>
              ) : (
                <div>
                  {pastBookings.map(booking => (
                    <BookingCard key={booking.id} booking={booking} />
                  ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="statistics" className="mt-6">
              <div className="space-y-6">
                {/* Charts row 1 */}
                <div className="grid gap-6 md:grid-cols-2">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Buchungen pro Monat</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ChartContainer config={chartConfig} className="h-64 w-full">
                        <BarChart data={monthlyData}>
                          <CartesianGrid vertical={false} strokeDasharray="3 3" />
                          <XAxis dataKey="month" tickLine={false} axisLine={false} />
                          <YAxis allowDecimals={false} tickLine={false} axisLine={false} />
                          <ChartTooltip cursor={{ fill: 'hsl(var(--muted))' }} content={<ChartTooltipContent nameKey="Buchungen" />} />
                          <Bar dataKey="count" fill="var(--color-bookings)" radius={[6, 6, 0, 0]} />
                        </BarChart>
                      </ChartContainer>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Buchungen nach Wochentag</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ChartContainer config={chartConfig} className="h-64 w-full">
                        <BarChart data={weekdayData}>
                          <CartesianGrid vertical={false} strokeDasharray="3 3" />
                          <XAxis dataKey="day" tickLine={false} axisLine={false} />
                          <YAxis allowDecimals={false} tickLine={false} axisLine={false} />
                          <ChartTooltip cursor={{ fill: 'hsl(var(--muted))' }} content={<ChartTooltipContent nameKey="Buchungen" />} />
                          <Bar dataKey="count" fill="var(--color-bookings)" radius={[6, 6, 0, 0]} />
                        </BarChart>
                      </ChartContainer>
                    </CardContent>
                  </Card>
                </div>

                {/* Charts row 2 */}
                <div className="grid gap-6 md:grid-cols-2">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Buchungen nach Uhrzeit</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ChartContainer config={chartConfig} className="h-64 w-full">
                        <BarChart data={hourData}>
                          <CartesianGrid vertical={false} strokeDasharray="3 3" />
                          <XAxis dataKey="hour" tickLine={false} axisLine={false} interval={2} />
                          <YAxis allowDecimals={false} tickLine={false} axisLine={false} />
                          <ChartTooltip cursor={{ fill: 'hsl(var(--muted))' }} content={<ChartTooltipContent nameKey="Buchungen" />} />
                          <Bar dataKey="count" fill="var(--color-bookings)" radius={[6, 6, 0, 0]} />
                        </BarChart>
                      </ChartContainer>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Top‑Mitspieler & Lieblingsplatz</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid gap-6 md:grid-cols-2">
                        <div>
                          <h3 className="text-sm font-semibold mb-2">Top 5 Mitspieler</h3>
                          {topPartners.length === 0 ? (
                            <p className="text-sm text-muted-foreground">Noch keine Daten</p>
                          ) : (
                            <ul className="space-y-2">
                              {topPartners.map((p) => (
                                <li key={p.name} className="flex items-center justify-between text-sm">
                                  <span className="truncate mr-2">{p.name}</span>
                                  <span className="text-muted-foreground">{p.count}x</span>
                                </li>
                              ))}
                            </ul>
                          )}
                        </div>
                        <div>
                          <h3 className="text-sm font-semibold mb-2">Lieblingsplatz</h3>
                          {favoriteCourt ? (
                            <>
                              <p className="text-sm">{favoriteCourt}</p>
                              {courtStats.length > 1 && (
                                <ul className="mt-3 space-y-1">
                                  {courtStats.slice(0, 3).map((c) => (
                                    <li key={c.courtId} className="flex items-center justify-between text-sm">
                                      <span className="truncate mr-2">{c.courtLabel}</span>
                                      <span className="text-muted-foreground">{c.count}x</span>
                                    </li>
                                  ))}
                                </ul>
                              )}
                            </>
                          ) : (
                            <p className="text-sm text-muted-foreground">Noch keine Daten</p>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Playing Time & Membership Info */}
                <div className="grid gap-6 md:grid-cols-2">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Spielzeit</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-center">
                        <div className="text-4xl font-bold text-teal-600 mb-2">
                          {playingTimeStats.hours}h {playingTimeStats.minutes}m
                        </div>
                        <p className="text-sm text-muted-foreground">Gesamte Spielzeit</p>
                        <div className="mt-4 text-xs text-muted-foreground">
                          <div>Durchschnitt pro Buchung: {
                            stats.total > 0 
                              ? `${Math.round(totalPlayingMinutes / stats.total)} Min.`
                              : "0 Min."
                          }</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Mitgliedschaft</CardTitle>
                    </CardHeader>
                    <CardContent>
                      {joinInfo.joinDate || joinInfo.category ? (
                        <div className="flex flex-wrap gap-6 text-sm">
                          {joinInfo.category && (
                            <div>
                              <span className="text-muted-foreground">Kategorie: </span>
                              <span className="font-medium">{joinInfo.category}</span>
                            </div>
                          )}
                          {joinInfo.joinDate && (
                            <div>
                              <span className="text-muted-foreground">Mitglied seit: </span>
                              <span className="font-medium">{format(new Date(joinInfo.joinDate), 'dd.MM.yyyy')}</span>
                            </div>
                          )}
                        </div>
                      ) : (
                        <p className="text-sm text-muted-foreground">Keine Angaben verfügbar</p>
                      )}
                    </CardContent>
                  </Card>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </>
      )}
    </div>
  );
};

export default MemberBookingsPage;