import { useState, useEffect } from "react";
import { Calendar, Clock, CheckCircle2, AlertCircle, Users, ListChecks, Plus } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { supabase } from "@/integrations/supabase/client";
import { useClub } from "@/contexts/ClubContext";
import AvailableActivitiesTable from "@/components/member/work-service/AvailableActivitiesTable";
import MyAssignedActivities from "@/components/member/work-service/MyAssignedActivities";
import SuggestActivityDialog from "@/components/member/work-service/SuggestActivityDialog";

interface WorkTarget {
  id: string;
  year: number;
  target_hours: number;
  completed_hours: number;
  is_manually_exempt: boolean;
  is_auto_exempt: boolean;
  exemption_reason?: string;
  auto_exemption_reason?: string;
}

interface Activity {
  id: string;
  name: string;
  description?: string | null;
  hourly_rate: number | null;
  status: string;
}

const MemberWorkServicePage = () => {
  const { currentClubId } = useClub();
  const [workTarget, setWorkTarget] = useState<WorkTarget | null>(null);
  const [availableActivities, setAvailableActivities] = useState<Activity[]>([]);
  const [isLoadingTarget, setIsLoadingTarget] = useState(true);
  const [isLoadingActivities, setIsLoadingActivities] = useState(true);
  const [isSuggestDialogOpen, setIsSuggestDialogOpen] = useState(false);
  const currentYear = new Date().getFullYear();

  useEffect(() => {
    if (currentClubId) {
      loadWorkTarget();
      loadAvailableActivities();
    }
  }, [currentClubId]);

  const loadWorkTarget = async () => {
    if (!currentClubId) return;

    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // Load work target for current year
      const { data: targetData } = await supabase
        .from('member_work_service_targets')
        .select('*')
        .eq('user_id', user.id)
        .eq('club_id', currentClubId)
        .eq('year', currentYear)
        .maybeSingle();

      if (targetData) {
        setWorkTarget(targetData);
      }
    } catch (error) {
      console.error('Error loading work target:', error);
    } finally {
      setIsLoadingTarget(false);
    }
  };

  const loadAvailableActivities = async () => {
    if (!currentClubId) return;

    setIsLoadingActivities(true);
    try {
      // Get activities that are not currently assigned to any member (or have completed/cancelled assignments)
      const { data: activitiesData, error } = await supabase
        .from('activities')
        .select(`
          id,
          name,
          description,
          hourly_rate,
          status,
          activity_assignments!left (
            id,
            status
          )
        `)
        .eq('club_id', currentClubId)
        .eq('status', 'approved')
        .order('name');

      if (error) {
        console.error('Error loading activities:', error);
      } else if (activitiesData) {
        // Filter out activities that have active assignments
        const available = activitiesData.filter(activity => {
          const hasActiveAssignment = activity.activity_assignments?.some((assignment: any) => 
            assignment.status === 'assigned'
          );
          return !hasActiveAssignment;
        });
        
        setAvailableActivities(available);
      }
    } catch (error) {
      console.error('Error loading available activities:', error);
    } finally {
      setIsLoadingActivities(false);
    }
  };

  const handleActivityAssigned = () => {
    // Refresh both work target and available activities when an activity is assigned
    loadWorkTarget();
    loadAvailableActivities();
  };

  if (isLoadingTarget) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Lade Arbeitsdienst-Daten...</p>
        </div>
      </div>
    );
  }

  const progressPercentage = workTarget 
    ? Math.min((workTarget.completed_hours / workTarget.target_hours) * 100, 100)
    : 0;

  const isExempt = workTarget?.is_manually_exempt || workTarget?.is_auto_exempt;
  const remainingHours = workTarget 
    ? Math.max(workTarget.target_hours - workTarget.completed_hours, 0)
    : 0;

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-foreground mb-2">Arbeitsdienst</h1>
        <p className="text-muted-foreground">
          Ihre Arbeitsstunden und verfügbare Tätigkeiten für {currentYear}
        </p>
      </div>

      {/* Work Progress Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Arbeitsstunden-Übersicht {currentYear}
          </CardTitle>
          <CardDescription>
            Ihr aktueller Fortschritt bei der Ableistung der Arbeitsstunden
          </CardDescription>
        </CardHeader>
        <CardContent>
          {workTarget ? (
            <div className="space-y-4">
              {isExempt ? (
                <div className="flex items-center gap-2 p-4 bg-muted rounded-lg">
                  <CheckCircle2 className="h-5 w-5 text-green-600" />
                  <div>
                    <p className="font-medium">Sie sind vom Arbeitsdienst befreit</p>
                    <p className="text-sm text-muted-foreground">
                      {workTarget.exemption_reason || workTarget.auto_exemption_reason}
                    </p>
                  </div>
                </div>
              ) : (
                <>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Fortschritt</span>
                    <span className="text-sm text-muted-foreground">
                      {workTarget.completed_hours} von {workTarget.target_hours} Stunden
                    </span>
                  </div>
                  <Progress value={progressPercentage} className="w-full" />
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">
                      Noch {remainingHours} Stunden zu leisten
                    </span>
                    <span className="font-medium">
                      {progressPercentage.toFixed(1)}% abgeschlossen
                    </span>
                  </div>
                </>
              )}
            </div>
          ) : (
            <div className="flex items-center gap-2 p-4 bg-muted rounded-lg">
              <AlertCircle className="h-5 w-5 text-yellow-600" />
              <p className="text-sm">
                Für das Jahr {currentYear} wurde noch kein Arbeitsstunden-Ziel festgelegt.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Activity Management Tabs */}
      <Tabs defaultValue="available" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="available" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Verfügbare Tätigkeiten
          </TabsTrigger>
          <TabsTrigger value="assigned" className="flex items-center gap-2">
            <ListChecks className="h-4 w-4" />
            Meine Tätigkeiten
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="available" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle>Verfügbare Tätigkeiten</CardTitle>
                  <CardDescription>
                    Wählen Sie Tätigkeiten aus, die Sie übernehmen möchten. Die Stunden werden automatisch Ihrem Arbeitsdienst gutgeschrieben.
                  </CardDescription>
                </div>
                <Button 
                  onClick={() => setIsSuggestDialogOpen(true)}
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  Tätigkeit vorschlagen
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <AvailableActivitiesTable 
                activities={availableActivities}
                isLoading={isLoadingActivities}
                onActivityAssigned={handleActivityAssigned}
              />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="assigned" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Meine übernommenen Tätigkeiten</CardTitle>
              <CardDescription>
                Verwalten Sie Ihre übernommenen Tätigkeiten und markieren Sie diese als abgeschlossen.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <MyAssignedActivities onActivityStatusChanged={handleActivityAssigned} />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Information Card */}
      <Card>
        <CardHeader>
          <CardTitle>Hinweise zum Arbeitsdienst</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <p className="text-sm text-muted-foreground">
            • Übernehmen Sie Tätigkeiten direkt über die "Verfügbare Tätigkeiten" Liste
          </p>
          <p className="text-sm text-muted-foreground">
            • Schlagen Sie neue Tätigkeiten vor, die Sie gerne für den Verein erledigen möchten
          </p>
          <p className="text-sm text-muted-foreground">
            • Geleistete Stunden werden automatisch bei der Übernahme gutgeschrieben
          </p>
          <p className="text-sm text-muted-foreground">
            • Markieren Sie Tätigkeiten als abgeschlossen, wenn Sie diese erledigt haben
          </p>
          <p className="text-sm text-muted-foreground">
            • Bei Fragen zum Arbeitsdienst kontaktieren Sie bitte die Vereinsadministration
          </p>
        </CardContent>
      </Card>

      {/* Suggest Activity Dialog */}
      <SuggestActivityDialog
        isOpen={isSuggestDialogOpen}
        onOpenChange={setIsSuggestDialogOpen}
        onActivitySuggested={handleActivityAssigned}
      />
    </div>
  );
};

export default MemberWorkServicePage;
