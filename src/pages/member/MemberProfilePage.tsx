import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import CancellationForm from "@/components/member/cancellations/CancellationForm";
import GroupManager from "@/components/member/group/GroupManager";
import { UserPreferences } from "@/components/UserPreferences";
import { supabase } from "@/integrations/supabase/client";

const MemberProfilePage = () => {
  const [isBillingMember, setIsBillingMember] = useState(false);

  // Check if user is billing member of at least one group (RLS filters to own groups)
  useEffect(() => {
    const checkGroups = async () => {
      const { data, error } = await supabase
        .from("groups")
        .select("id")
        .limit(1);

      if (error) {
        console.error("Error checking billing groups:", error);
        return;
      }
      setIsBillingMember(!!data && data.length > 0);
    };
    checkGroups();
  }, []);

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-foreground mb-2">Mein Profil</h1>
        <p className="text-muted-foreground">
          Verwalten Sie Ihre persönlichen Daten und Mitgliedschaft
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Profilbearbeitung</CardTitle>
          <CardDescription>
            Profilverwaltung wird hier implementiert
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-lg border bg-card text-card-foreground p-8 text-center">
            <h2 className="text-xl font-semibold mb-2">Profilbearbeitung</h2>
            <p className="text-muted-foreground">
              Profilverwaltung wird hier implementiert
            </p>
          </div>
        </CardContent>
      </Card>

      {/* User Preferences for Admins */}
      <UserPreferences />

      {/* Personal cancellation */}
      <CancellationForm mode="individual" />

      {/* Group management and group cancellation only for billing members */}
      {isBillingMember && <GroupManager />}
    </div>
  );
};

export default MemberProfilePage;
