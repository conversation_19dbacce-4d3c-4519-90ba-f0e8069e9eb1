import { useEffect, useMemo, useState } from "react";
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { supabase } from "@/integrations/supabase/client";
import { format, parse } from "date-fns";
import { de } from "date-fns/locale";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import {
  BarChart,
  Bar,
  CartesianGrid,
  XAxis,
  YAxis,
} from "recharts";

interface Booking {
  id: string;
  booking_date: string; // yyyy-MM-dd
  start_time: string;   // HH:mm:ss
  end_time: string;     // HH:mm:ss
  court_id: string;
  player_name: string;
  partner_name?: string | null;
}

interface Court { id: string; number: number }

const MemberStatsPage = () => {
  const [fullName, setFullName] = useState<string>("");
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [courtsMap, setCourtsMap] = useState<Record<string, Court>>({});
  const [joinInfo, setJoinInfo] = useState<{ joinDate?: string; category?: string }>({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // SEO: Title, Description, Canonical
    document.title = "Statistiken – Mitgliederbereich";
    const desc = "Statistiken zu meinen Platzbuchungen, Mitspielern und Lieblingsplatz.";
    let meta = document.querySelector('meta[name="description"]');
    if (!meta) {
      meta = document.createElement('meta');
      meta.setAttribute('name', 'description');
      document.head.appendChild(meta);
    }
    meta.setAttribute('content', desc);

    let canonical = document.querySelector('link[rel="canonical"]') as HTMLLinkElement | null;
    if (!canonical) {
      canonical = document.createElement('link');
      canonical.setAttribute('rel', 'canonical');
      document.head.appendChild(canonical);
    }
    canonical.setAttribute('href', `${window.location.origin}/member/statistiken`);
  }, []);

  useEffect(() => {
    const load = async () => {
      try {
        const { data: userRes } = await supabase.auth.getUser();
        const userId = userRes.user?.id;

        let name = "";
        if (userId) {
          const { data: profile } = await supabase
            .from("profiles")
            .select("first_name, last_name, join_date, membership_category")
            .eq("id", userId)
            .maybeSingle();
          if (profile?.first_name || profile?.last_name) {
            name = `${profile.first_name ?? ""} ${profile.last_name ?? ""}`.trim();
          }
          setJoinInfo({ joinDate: profile?.join_date, category: profile?.membership_category });
        }
        setFullName(name);

        // Courts for label mapping
        const { data: courts } = await supabase
          .from("courts")
          .select("id, number")
          .order("number");
        const cMap: Record<string, Court> = {};
        (courts || []).forEach(c => { cMap[c.id] = c; });
        setCourtsMap(cMap);

        // last 365 days
        const since = new Date();
        since.setDate(since.getDate() - 365);
        const sinceStr = format(since, "yyyy-MM-dd");

        const { data: allBookings } = await supabase
          .from("bookings")
          .select("*")
          .gte("booking_date", sinceStr)
          .order("booking_date", { ascending: true });

        const belongsToUser = (b: Booking) => name && (b.player_name === name || b.partner_name === name);
        setBookings((allBookings || []).filter(belongsToUser));
      } finally {
        setLoading(false);
      }
    };
    load();
  }, []);

  const toDateTime = (b: Booking) => parse(`${b.booking_date} ${b.start_time}`, "yyyy-MM-dd HH:mm:ss", new Date());

  // Helpers
  const last12Months = useMemo(() => {
    const arr: { key: string; label: string }[] = [];
    const now = new Date();
    for (let i = 11; i >= 0; i--) {
      const d = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const key = format(d, "yyyy-MM");
      const label = format(d, "MMM yy", { locale: de });
      arr.push({ key, label });
    }
    return arr;
  }, []);

  const monthlyData = useMemo(() => {
    const counts = new Map<string, number>();
    for (const b of bookings) {
      const key = b.booking_date.slice(0, 7); // yyyy-MM
      counts.set(key, (counts.get(key) || 0) + 1);
    }
    return last12Months.map(({ key, label }) => ({ month: label, count: counts.get(key) || 0 }));
  }, [bookings, last12Months]);

  const weekdayOrder = ['Mo', 'Di', 'Mi', 'Do', 'Fr', 'Sa', 'So'];
  const weekdayData = useMemo(() => {
    const counts = new Map<string, number>(weekdayOrder.map(d => [d, 0]));
    for (const b of bookings) {
      const d = toDateTime(b);
      const lbl = format(d, 'EEE', { locale: de });
      const short = lbl.charAt(0).toUpperCase() + lbl.slice(1, 2); // Mo, Di, ...
      counts.set(short, (counts.get(short) || 0) + 1);
    }
    return weekdayOrder.map(day => ({ day, count: counts.get(day) || 0 }));
  }, [bookings]);

  const hourData = useMemo(() => {
    const counts = Array.from({ length: 24 }, () => 0);
    for (const b of bookings) {
      const h = parse(b.start_time, 'HH:mm:ss', new Date()).getHours();
      counts[h]++;
    }
    return counts.map((c, h) => ({ hour: `${h.toString().padStart(2, '0')}:00`, count: c }));
  }, [bookings]);

  const topPartners = useMemo(() => {
    if (!fullName) return [] as { name: string; count: number }[];
    const counts = new Map<string, number>();
    for (const b of bookings) {
      const other = b.player_name === fullName ? (b.partner_name || '') : (b.partner_name === fullName ? b.player_name : '');
      if (!other) continue;
      counts.set(other, (counts.get(other) || 0) + 1);
    }
    return Array.from(counts.entries()).map(([name, count]) => ({ name, count })).sort((a, b) => b.count - a.count).slice(0, 5);
  }, [bookings, fullName]);

  const courtStats = useMemo(() => {
    const counts = new Map<string, number>();
    for (const b of bookings) {
      counts.set(b.court_id, (counts.get(b.court_id) || 0) + 1);
    }
    const items = Array.from(counts.entries()).map(([courtId, count]) => ({
      courtId,
      courtLabel: courtsMap[courtId]?.number ? `Platz ${courtsMap[courtId].number}` : 'Platz',
      count,
    })).sort((a, b) => b.count - a.count);
    return items;
  }, [bookings, courtsMap]);

  const favoriteCourt = courtStats[0]?.courtLabel;

  const chartConfig = {
    bookings: {
      label: "Buchungen",
      color: "hsl(var(--primary))",
    },
  } as const;

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-foreground mb-2">Statistiken</h1>
        <p className="text-muted-foreground">Übersicht Ihrer persönlichen Buchungsstatistiken</p>
      </div>

      {/* Charts row 1 */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Buchungen pro Monat</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <p className="text-sm text-muted-foreground">Laden…</p>
            ) : (
              <ChartContainer config={chartConfig} className="h-64 w-full">
                <BarChart data={monthlyData}>
                  <CartesianGrid vertical={false} strokeDasharray="3 3" />
                  <XAxis dataKey="month" tickLine={false} axisLine={false} />
                  <YAxis allowDecimals={false} tickLine={false} axisLine={false} />
                  <ChartTooltip cursor={{ fill: 'hsl(var(--muted))' }} content={<ChartTooltipContent nameKey="Buchungen" />} />
                  <Bar dataKey="count" fill="var(--color-bookings)" radius={[6, 6, 0, 0]} />
                </BarChart>
              </ChartContainer>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Buchungen nach Wochentag</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <p className="text-sm text-muted-foreground">Laden…</p>
            ) : (
              <ChartContainer config={chartConfig} className="h-64 w-full">
                <BarChart data={weekdayData}>
                  <CartesianGrid vertical={false} strokeDasharray="3 3" />
                  <XAxis dataKey="day" tickLine={false} axisLine={false} />
                  <YAxis allowDecimals={false} tickLine={false} axisLine={false} />
                  <ChartTooltip cursor={{ fill: 'hsl(var(--muted))' }} content={<ChartTooltipContent nameKey="Buchungen" />} />
                  <Bar dataKey="count" fill="var(--color-bookings)" radius={[6, 6, 0, 0]} />
                </BarChart>
              </ChartContainer>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Charts row 2 */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Buchungen nach Uhrzeit</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <p className="text-sm text-muted-foreground">Laden…</p>
            ) : (
              <ChartContainer config={chartConfig} className="h-64 w-full">
                <BarChart data={hourData}>
                  <CartesianGrid vertical={false} strokeDasharray="3 3" />
                  <XAxis dataKey="hour" tickLine={false} axisLine={false} interval={2} />
                  <YAxis allowDecimals={false} tickLine={false} axisLine={false} />
                  <ChartTooltip cursor={{ fill: 'hsl(var(--muted))' }} content={<ChartTooltipContent nameKey="Buchungen" />} />
                  <Bar dataKey="count" fill="var(--color-bookings)" radius={[6, 6, 0, 0]} />
                </BarChart>
              </ChartContainer>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Top‑Mitspieler & Lieblingsplatz</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <p className="text-sm text-muted-foreground">Laden…</p>
            ) : (
              <div className="grid gap-6 md:grid-cols-2">
                <div>
                  <h3 className="text-sm font-semibold mb-2">Top 5 Mitspieler</h3>
                  {topPartners.length === 0 ? (
                    <p className="text-sm text-muted-foreground">Noch keine Daten</p>
                  ) : (
                    <ul className="space-y-2">
                      {topPartners.map((p) => (
                        <li key={p.name} className="flex items-center justify-between text-sm">
                          <span className="truncate mr-2">{p.name}</span>
                          <span className="text-muted-foreground">{p.count}x</span>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
                <div>
                  <h3 className="text-sm font-semibold mb-2">Lieblingsplatz</h3>
                  {favoriteCourt ? (
                    <>
                      <p className="text-sm">{favoriteCourt}</p>
                      {courtStats.length > 1 && (
                        <ul className="mt-3 space-y-1">
                          {courtStats.slice(0, 3).map((c) => (
                            <li key={c.courtId} className="flex items-center justify-between text-sm">
                              <span className="truncate mr-2">{c.courtLabel}</span>
                              <span className="text-muted-foreground">{c.count}x</span>
                            </li>
                          ))}
                        </ul>
                      )}
                    </>
                  ) : (
                    <p className="text-sm text-muted-foreground">Noch keine Daten</p>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Membership info */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Mitgliedschaft</CardTitle>
        </CardHeader>
        <CardContent>
          {joinInfo.joinDate || joinInfo.category ? (
            <div className="flex flex-wrap gap-6 text-sm">
              {joinInfo.category && (
                <div>
                  <span className="text-muted-foreground">Kategorie: </span>
                  <span className="font-medium">{joinInfo.category}</span>
                </div>
              )}
              {joinInfo.joinDate && (
                <div>
                  <span className="text-muted-foreground">Mitglied seit: </span>
                  <span className="font-medium">{format(new Date(joinInfo.joinDate), 'dd.MM.yyyy')}</span>
                </div>
              )}
            </div>
          ) : (
            <p className="text-sm text-muted-foreground">Keine Angaben verfügbar</p>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default MemberStatsPage;
