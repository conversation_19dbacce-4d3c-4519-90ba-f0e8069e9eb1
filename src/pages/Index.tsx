import { useEffect } from 'react';
import { useParams, Navigate, useNavigate } from 'react-router-dom';
import Navigation from "@/components/Navigation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useTenant } from "@/contexts/TenantContext";
import { useAuth } from "@/contexts/AuthContext";
import { LogIn, UserPlus } from "lucide-react";

const Index = () => {
  const { clubSlug } = useParams();
  const { club, isLoading, error } = useTenant();
  const { user } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (clubSlug) {
      console.log('Club-specific landing page for:', clubSlug);
    }
  }, [clubSlug]);

  // This component should only be used for club-specific routes (/c/:clubSlug)
  if (!clubSlug) {
    return <Navigate to="/not-found" replace />;
  }

  // Show loading while tenant is being resolved
  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Lade Vereinsdaten...</p>
        </div>
      </div>
    );
  }

  // Show error if tenant resolution failed or club not found
  if (error || !club) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <p className="text-destructive mb-4">Verein nicht gefunden</p>
          <p className="text-muted-foreground">
            {error || `Der Verein "${clubSlug}" konnte nicht gefunden werden.`}
          </p>
        </div>
      </div>
    );
  }

  // Club-specific landing page
  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      <section id="home">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold mb-4">
              Willkommen bei {club.name}
            </h1>
            {club.description && (
              <p className="text-xl text-muted-foreground mb-8">
                {club.description}
              </p>
            )}
            
            {/* Authentication Section for non-logged in users */}
            {!user && (
              <div className="max-w-2xl mx-auto mb-8">
                <Card>
                  <CardHeader>
                    <CardTitle>Mitglied werden oder Anmelden</CardTitle>
                    <CardDescription>
                      Werden Sie Teil von {club.name} oder melden Sie sich mit Ihrem bestehenden Account an
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                      <Button
                        onClick={() => navigate(`/c/${clubSlug}/register`)}
                        size="lg"
                        className="flex items-center gap-2"
                      >
                        <UserPlus className="h-5 w-5" />
                        Jetzt registrieren
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => navigate(`/c/${clubSlug}/auth`)}
                        size="lg"
                        className="flex items-center gap-2"
                      >
                        <LogIn className="h-5 w-5" />
                        Anmelden
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Member Dashboard Link for logged in users */}
            {user && (
              <div className="max-w-xl mx-auto mb-8">
                <Card>
                  <CardHeader>
                    <CardTitle>Willkommen zurück!</CardTitle>
                    <CardDescription>
                      Zugang zu Ihrem Mitgliederbereich
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button
                      onClick={() => navigate(`/c/${clubSlug}/member`)}
                      size="lg"
                      className="w-full"
                    >
                      Zum Mitgliederbereich
                    </Button>
                  </CardContent>
                </Card>
              </div>
            )}
            
            <div className="bg-muted p-4 rounded-lg">
              <p className="text-sm text-muted-foreground">
                Verein: {club.name} | Slug: {club.slug}
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Index;
