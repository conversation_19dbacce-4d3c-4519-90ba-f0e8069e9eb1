import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/contexts/AuthContext";
import { useClub } from "@/contexts/ClubContext";
import { ArrowLeft, Loader2 } from "lucide-react";

const Auth = () => {
  const [loading, setLoading] = useState(false);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user, signIn } = useAuth();
  const { currentClub } = useClub();

  // Redirect based on user preferences and role
  useEffect(() => {
    if (user) {
      const determineRedirect = async () => {
        try {
          // Check if user is a meta-admin first (highest priority)
          const { data: isMetaAdmin, error: metaAdminError } = await supabase
            .rpc('is_meta_admin', { _user_id: user.id });
          
          if (metaAdminError) {
            console.error('Error checking meta admin role:', metaAdminError);
          }

          // Get user's club membership directly (don't wait for context)
          const { data: membership } = await supabase
            .from('club_memberships')
            .select(`
              role,
              clubs (
                id,
                name,
                slug
              )
            `)
            .eq('user_id', user.id)
            .eq('is_active', true)
            .maybeSingle();

          const userClub = membership?.clubs;
          console.log('Auth: User club found:', userClub);

          // If user is meta-admin and no club context, redirect to meta-admin area
          if (isMetaAdmin && !userClub) {
            console.log('Auth: Redirecting to meta-admin (no club)');
            navigate('/meta-admin');
            return;
          }

          // Get user's preferred landing page
          const { data: preferredPage, error: prefError } = await supabase
            .rpc('get_user_default_landing_page', { _user_id: user.id });
          
          if (prefError) {
            console.error('Error getting user preference:', prefError);
          }

          // Check if user has any club admin role
          const { data: isAdminUser, error: adminError } = await supabase
            .rpc('has_any_admin_role', { _user_id: user.id });
          
          if (adminError) {
            console.error('Error checking admin role:', adminError);
            navigate('/');
            return;
          }

          // Determine redirect destination
          const defaultPage = preferredPage || 'member';
          console.log('Auth: User preferences:', { defaultPage, isAdminUser, userClub });
          
          if (userClub) {
            // User has club context - redirect to club-specific routes
            if (isAdminUser && defaultPage === 'admin') {
              console.log('Auth: Redirecting to club admin:', `/c/${userClub.slug}/admin`);
              navigate(`/c/${userClub.slug}/admin`);
            } else {
              console.log('Auth: Redirecting to club member:', `/c/${userClub.slug}/member`);
              navigate(`/c/${userClub.slug}/member`);
            }
          } else if (isMetaAdmin) {
            // Meta admin without club context
            console.log('Auth: Redirecting to meta-admin');
            navigate('/meta-admin');
          } else {
            // Fallback to generic routes
            const fallbackRoute = isAdminUser && defaultPage === 'admin' ? '/admin' : '/member';
            console.log('Auth: Fallback redirect to:', fallbackRoute);
            navigate(fallbackRoute);
          }
        } catch (error) {
          console.error('Error determining redirect:', error);
          navigate('/');
        }
      };
      
      determineRedirect();
    }
  }, [user, navigate]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const { error } = await signIn(email, password);

      if (error) {
        if (error.message.includes('Invalid login credentials')) {
          toast({
            variant: "destructive",
            title: "Anmeldung fehlgeschlagen",
            description: "Ungültige E-Mail oder Passwort. Bitte überprüfen Sie Ihre Eingaben.",
          });
        } else {
          toast({
            variant: "destructive",
            title: "Anmeldung fehlgeschlagen",
            description: error.message,
          });
        }
      } else {
        toast({
          title: "Erfolgreich angemeldet",
          description: "Willkommen zurück!",
        });
        // Redirect will be handled by the useEffect above
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Fehler",
        description: "Ein unerwarteter Fehler ist aufgetreten.",
      });
    }

    setLoading(false);
  };

  return (
    <div className="min-h-screen relative overflow-hidden bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50">
      {/* AI-style animated background elements */}
      <div className="absolute inset-0">
        {/* Floating gradient orbs */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-purple-400 to-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
        <div className="absolute top-3/4 right-1/4 w-96 h-96 bg-gradient-to-r from-blue-400 to-indigo-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>
        <div className="absolute bottom-1/4 left-1/3 w-96 h-96 bg-gradient-to-r from-indigo-400 to-purple-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000"></div>
        
        {/* Grid pattern */}
        <div className="absolute inset-0 bg-grid-slate-100 bg-[size:60px_60px] opacity-30" />
      </div>
      
      <div className="relative z-10 flex items-center justify-center min-h-screen p-4">
        <div className="w-full max-w-md space-y-6">
          {/* Back Button - AI Style */}
          <Button 
            variant="outline" 
            onClick={() => navigate("/")}
            className="bg-white/80 backdrop-blur-sm border-purple-200/50 text-purple-700 hover:text-purple-800 hover:bg-white/90 hover:border-purple-300 hover:scale-105 transition-all duration-300 shadow-lg shadow-purple-500/20"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Zurück
          </Button>
          
          {/* Main Auth Card - AI Interface Style */}
          <div className="relative">
            {/* Glowing background effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-blue-500 rounded-2xl blur-xl opacity-20"></div>
            
            {/* Main card */}
            <Card className="relative bg-white/90 backdrop-blur-lg border border-purple-200/50 rounded-2xl overflow-hidden shadow-2xl shadow-purple-500/10">
              {/* Header with AI-style branding */}
              <CardHeader className="text-center py-8 relative">
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500/5 to-blue-500/5"></div>
                <div className="relative space-y-4">
                  {/* Courtwaive Logo */}
                  <div className="flex justify-center">
                    <h1 className="text-4xl font-bold text-fg-strong">
                      Courtw<span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">ai</span>ve
                    </h1>
                  </div>
                  
                  <CardTitle className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                    Demo Club
                  </CardTitle>
                </div>
              </CardHeader>
              
              <CardContent className="px-8 pb-8 pt-2 space-y-6 relative">
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500/5 to-blue-500/5"></div>
                <div className="relative">
                  <form onSubmit={handleLogin} className="space-y-4">
                    {/* Email Field - AI Style */}
                    <div className="space-y-3">
                      <Label htmlFor="email" className="text-foreground font-medium flex items-center gap-2">
                        <div className="w-2 h-2 bg-gradient-to-r from-purple-400 to-purple-500 rounded-full animate-pulse"></div>
                        E-Mail
                      </Label>
                      <div className="relative">
                        <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-blue-500/10 rounded-lg blur-sm"></div>
                        <Input
                          id="email"
                          type="email"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          required
                          className="relative h-12 bg-white/80 backdrop-blur-sm border-purple-200/50 text-foreground placeholder:text-muted-foreground focus:border-purple-400 focus:ring-purple-400/20 transition-all duration-300"
                          placeholder="<EMAIL>"
                        />
                      </div>
                    </div>
                    
                    {/* Password Field - AI Style */}
                    <div className="space-y-3">
                      <Label htmlFor="password" className="text-foreground font-medium flex items-center gap-2">
                        <div className="w-2 h-2 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full animate-pulse delay-75"></div>
                        Passwort
                      </Label>
                      <div className="relative">
                        <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-blue-500/10 rounded-lg blur-sm"></div>
                        <Input
                          id="password"
                          type="password"
                          value={password}
                          onChange={(e) => setPassword(e.target.value)}
                          required
                          className="relative h-12 bg-white/80 backdrop-blur-sm border-purple-200/50 text-foreground placeholder:text-muted-foreground focus:border-purple-400 focus:ring-purple-400/20 transition-all duration-300"
                          placeholder="••••••••"
                        />
                      </div>
                    </div>
                    
                    {/* Submit Button - AI Interface Style */}
                    <Button 
                      type="submit" 
                      size="lg"
                      className="relative w-full h-12 overflow-hidden bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-600/90 hover:to-blue-600/90 text-white border-0 shadow-lg transition-all duration-300 hover:shadow-xl hover:shadow-purple-500/25 hover:scale-105"
                      disabled={loading}
                    >
                      <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 translate-x-[-100%] hover:translate-x-[100%] transition-transform duration-700 ease-in-out"></div>
                      <div className="relative flex items-center justify-center gap-3">
                        {loading ? (
                          <>
                            <Loader2 className="h-5 w-5 animate-spin" />
                            <span className="font-medium">Anmelden...</span>
                          </>
                        ) : (
                           <span className="font-medium">Anmelden</span>
                        )}
                      </div>
                    </Button>
                  </form>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Auth;