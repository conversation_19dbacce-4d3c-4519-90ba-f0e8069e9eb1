import { useState, useEffect } from "react";
import ClubAdminLayout from "@/layouts/ClubAdminLayout";
import UnifiedMemberManagement from "@/components/admin/UnifiedMemberManagement";
import ClubRoleManagement from "@/components/admin/ClubRoleManagement";
import CustomFieldsManager from "@/components/admin/CustomFieldsManager";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { RefreshCw, Settings2 } from "lucide-react";
import AdvancedMemberBulkImport from "@/components/admin/AdvancedMemberBulkImport";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useTenant } from "@/contexts/TenantContext";
import { useCustomFields } from "@/hooks/useCustomFields";

const AdminMembersPage = () => {
  const [activeRoles, setActiveRoles] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const { user, isLoading: authLoading, isAdmin } = useAuth();
  const { club } = useTenant();
  const { customFields, isLoading: customFieldsLoading } = useCustomFields(club?.id);

  useEffect(() => {
    if (!authLoading && user) {
      fetchActiveRoles();
    }
  }, [authLoading, user]);

  const fetchActiveRoles = async () => {
    try {
      const { data, error } = await supabase
        .from("accounts")
        .select("account_type")
        .not("account_type", "is", null);

      if (error) throw error;

      // Get unique roles
      const uniqueRoles = [...new Set(data?.map(account => account.account_type) || [])];
      setActiveRoles(uniqueRoles);
    } catch (error) {
      console.error("Error fetching roles:", error);
    } finally {
      setLoading(false);
    }
  };

  const getRolePlural = (role: string): string => {
    const pluralMap: { [key: string]: string } = {
      "Member": "Mitglieder",
      "Guest": "Gäste", 
      "Trainer": "Trainer",
      "Admin": "Admins"
    };
    return pluralMap[role] || `${role}s`;
  };

  // Define the desired order of tabs
  const tabOrder = ["Member", "Guest", "Trainer", "Admin"];
  const orderedRoles = tabOrder.filter(role => activeRoles.includes(role));
  
  // Filter custom fields to only show select type fields
  const selectCustomFields = customFields.filter(field => field.field_type === 'select' && field.field_options);
  
  // Get unique values for select custom fields from the database
  const getCustomFieldOptions = (field: any) => {
    if (field.field_options) {
      try {
        return Array.isArray(field.field_options) ? field.field_options : JSON.parse(field.field_options);
      } catch {
        return [];
      }
    }
    return [];
  };

  // Show loading while auth is being established
  if (authLoading) {
    return (
    <ClubAdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="flex items-center gap-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
            <span>Authentifizierung wird geladen...</span>
          </div>
        </div>
      </ClubAdminLayout>
    );
  }

  // Show error state if not authenticated
  if (!user) {
    return (
      <ClubAdminLayout>
        <div className="flex flex-col items-center justify-center h-64 space-y-3">
          <div className="text-center">
            <p className="text-muted-foreground">
              Nicht authentifiziert. Bitte melden Sie sich an.
            </p>
          </div>
        </div>
      </ClubAdminLayout>
    );
  }

  if (loading) {
    return (
      <ClubAdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="flex items-center gap-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
            <span>Lade Rollen...</span>
          </div>
        </div>
      </ClubAdminLayout>
    );
  }

  const totalTabs = 2 + activeRoles.length; // "Alle Benutzer" + "Benutzerdefinierte Felder" + roles

  return (
    <ClubAdminLayout>
        <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-foreground">Benutzerverwaltung</h1>
            <p className="text-muted-foreground">Verwalte alle Benutzer des Vereins und benutzerdefinierte Felder</p>
          </div>
          <div className="flex items-center gap-2">
                <AdvancedMemberBulkImport />
                <ClubRoleManagement />
          </div>
        </div>
        
        <Card>
          <CardContent className="pt-6">
            <Tabs defaultValue="all" className="w-full">
              <TabsList className={`grid w-full ${
                totalTabs <= 2 ? 'grid-cols-2' :
                totalTabs === 3 ? 'grid-cols-3' :
                totalTabs === 4 ? 'grid-cols-4' :
                totalTabs === 5 ? 'grid-cols-5' :
                totalTabs === 6 ? 'grid-cols-6' :
                'grid-cols-7'
              }`}>
                <TabsTrigger value="all">Alle Benutzer</TabsTrigger>
                <TabsTrigger value="custom-fields">Benutzerdefinierte Felder</TabsTrigger>
                {orderedRoles.map((role) => (
                  <TabsTrigger key={role} value={role.toLowerCase()}>
                    {getRolePlural(role)}
                  </TabsTrigger>
                ))}
              </TabsList>
              
              <TabsContent value="all" className="mt-6">
                <UnifiedMemberManagement />
              </TabsContent>
              
              <TabsContent value="custom-fields" className="mt-6">
                <div className="space-y-6">
                  {/* Custom Fields Configuration */}
                  <CustomFieldsManager />
                  
                  {/* Custom Fields Filtering */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Settings2 className="h-5 w-5" />
                        Nach benutzerdefinierten Feldern filtern
                      </CardTitle>
                      <CardDescription>
                        Filtere Benutzer nach Werten in benutzerdefinierten Feldern
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      {selectCustomFields.length > 0 ? (
                        <Tabs defaultValue={selectCustomFields[0]?.field_key} className="w-full">
                          <TabsList className="grid w-full" style={{ gridTemplateColumns: `repeat(${selectCustomFields.length}, 1fr)` }}>
                            {selectCustomFields.map((field) => (
                              <TabsTrigger key={field.field_key} value={field.field_key}>
                                {field.field_name}
                              </TabsTrigger>
                            ))}
                          </TabsList>
                          
                          {selectCustomFields.map((field) => (
                            <TabsContent key={field.field_key} value={field.field_key} className="mt-6">
                              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                {getCustomFieldOptions(field).map((option: string) => (
                                  <Card key={option} className="p-4">
                                    <div className="flex items-center justify-between mb-2">
                                      <h4 className="font-medium">{option}</h4>
                                       <Badge variant="secondary">
                                         {/* We'll calculate this later when we have access to all members */}
                                         Filter: {option}
                                       </Badge>
                                    </div>
                                    <UnifiedMemberManagement customFieldFilter={{ fieldKey: field.field_key, value: option }} />
                                  </Card>
                                ))}
                              </div>
                            </TabsContent>
                          ))}
                        </Tabs>
                      ) : (
                        <div className="text-center text-muted-foreground py-8">
                          <Settings2 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                          <p>Keine benutzerdefinierten Auswahlfelder konfiguriert</p>
                          <p className="text-sm">Erstelle benutzerdefinierte Felder vom Typ "Auswahl" um hier Filter zu verwenden</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
              
              {orderedRoles.map((role) => (
                <TabsContent key={role} value={role.toLowerCase()} className="mt-6">
                  <UnifiedMemberManagement defaultFilter={role.toLowerCase() as any} />
                </TabsContent>
              ))}
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </ClubAdminLayout>
  );
};

export default AdminMembersPage;