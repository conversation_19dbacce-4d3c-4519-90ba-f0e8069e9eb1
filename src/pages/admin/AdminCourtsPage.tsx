import ClubAdminLayout from "@/layouts/ClubAdminLayout";
import CourtSetup from "@/components/admin/CourtSetup";
import CourtConfigurationImproved from "@/components/admin/CourtConfigurationImproved";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

const AdminCourtsPage = () => {
  return (
    <ClubAdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Platzverwaltung</h1>
          <p className="text-muted-foreground">Verwalte Tennisplätze, Standard-Spielzeiten und individuelle Konfigurationen</p>
        </div>
        
        <Tabs defaultValue="setup" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="setup">Platz-Setup & Standard-Zeiten</TabsTrigger>
            <TabsTrigger value="configure">Individuelle Platz-Konfiguration</TabsTrigger>
          </TabsList>
          
          <TabsContent value="setup" className="space-y-0">
            <CourtSetup />
          </TabsContent>
          
          <TabsContent value="configure" className="space-y-0">
            <CourtConfigurationImproved />
          </TabsContent>
        </Tabs>
      </div>
    </ClubAdminLayout>
  );
};

export default AdminCourtsPage;