import SuperAdminLayout from "@/layouts/SuperAdminLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Building2, Users, Globe, Activity, ArrowUpRight, Shield, Database, Settings } from "lucide-react";
import { SuperAdminTestPanel } from "@/components/admin/SuperAdminTestPanel";
import { Link } from "react-router-dom";

const SuperAdminDashboardPage = () => {
  // Mock data für die Übersicht
  const stats = [
    { title: "Gesamt Clubs", value: "47", change: "+3", icon: Building2, color: "text-blue-600" },
    { title: "Aktive Benutzer", value: "1,247", change: "+12%", icon: Users, color: "text-green-600" },
    { title: "Custom Domains", value: "23", change: "+2", icon: Globe, color: "text-purple-600" },
    { title: "System Status", value: "Online", change: "99.9%", icon: Activity, color: "text-emerald-600" },
  ];

  const quickActions = [
    { title: "Club Management", description: "Alle Clubs verwalten", href: "/meta-admin/clubs", icon: Building2 },
    { title: "Benutzer verwalten", description: "User & Rollen verwalten", href: "/meta-admin/users", icon: Users },
    { title: "Domain Setup", description: "Custom Domains konfigurieren", href: "/meta-admin/domains", icon: Globe },
    { title: "System Settings", description: "Globale Einstellungen", href: "/meta-admin/settings", icon: Settings },
  ];

  const recentClubs = [
    { name: "TC Musterstadt", slug: "tc-musterstadt", members: 145, status: "Aktiv", created: "vor 2 Tagen" },
    { name: "Tennis Club Berlin", slug: "tc-berlin", members: 89, status: "Aktiv", created: "vor 5 Tagen" },
    { name: "Sportclub Hamburg", slug: "sc-hamburg", members: 203, status: "Aktiv", created: "vor 1 Woche" },
  ];

  const systemComponents = [
    { name: "API Gateway", status: "Online", uptime: "99.9%" },
    { name: "Database", status: "Online", uptime: "100%" },
    { name: "Auth Service", status: "Online", uptime: "99.8%" },
    { name: "Storage", status: "Online", uptime: "99.9%" },
  ];

  return (
    <SuperAdminLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">SuperAdmin Dashboard</h1>
            <p className="text-muted-foreground">
              Vollständiger Überblick und Kontrolle über alle Systembereiche
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Shield className="h-5 w-5 text-primary" />
            <Badge variant="secondary">Vollzugriff</Badge>
          </div>
        </div>

        {/* Development Test Panel */}
        <SuperAdminTestPanel />

        {/* Stats Overview */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {stats.map((stat) => (
            <Card key={stat.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
                <stat.icon className={`h-4 w-4 ${stat.color}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <p className="text-xs text-muted-foreground">
                  {stat.change} zum Vormonat
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Schnellzugriff</CardTitle>
            <CardDescription>
              Direkter Zugang zu allen wichtigen Verwaltungsbereichen
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              {quickActions.map((action) => (
                <Button
                  key={action.title}
                  variant="outline"
                  className="h-auto flex-col space-y-2 p-4"
                  asChild
                >
                  <Link to={action.href}>
                    <action.icon className="h-6 w-6" />
                    <div className="text-center">
                      <div className="font-medium">{action.title}</div>
                      <div className="text-xs text-muted-foreground">{action.description}</div>
                    </div>
                  </Link>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        <div className="grid gap-6 lg:grid-cols-2">
          {/* Recent Clubs */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Neueste Clubs</CardTitle>
                <CardDescription>Kürzlich registrierte Tennis-Clubs</CardDescription>
              </div>
              <Button variant="outline" size="sm" asChild>
                <Link to="/meta-admin/clubs">
                  Alle anzeigen
                  <ArrowUpRight className="h-4 w-4 ml-1" />
                </Link>
              </Button>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentClubs.map((club) => (
                  <div key={club.slug} className="flex items-center justify-between p-3 rounded-lg border">
                    <div className="flex items-center space-x-3">
                      <Building2 className="h-8 w-8 text-primary" />
                      <div>
                        <p className="font-medium">{club.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {club.members} Mitglieder • {club.created}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">{club.status}</Badge>
                      <Button variant="ghost" size="sm" asChild>
                        <Link to={`/c/${club.slug}/admin`}>
                          Verwalten
                        </Link>
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* System Status */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>System Status</CardTitle>
                <CardDescription>Aktueller Status aller Systemkomponenten</CardDescription>
              </div>
              <Button variant="outline" size="sm" asChild>
                <Link to="/meta-admin/monitoring/performance">
                  Details
                  <ArrowUpRight className="h-4 w-4 ml-1" />
                </Link>
              </Button>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {systemComponents.map((component) => (
                  <div key={component.name} className="flex items-center justify-between p-3 rounded-lg border">
                    <div className="flex items-center space-x-3">
                      <Database className="h-6 w-6 text-primary" />
                      <div>
                        <p className="font-medium">{component.name}</p>
                        <p className="text-sm text-muted-foreground">
                          Uptime: {component.uptime}
                        </p>
                      </div>
                    </div>
                    <Badge variant="secondary" className="bg-green-100 text-green-800">
                      {component.status}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* All Pages Access */}
        <Card>
          <CardHeader>
            <CardTitle>Vollzugriff auf alle Bereiche</CardTitle>
            <CardDescription>
              Als SuperAdmin haben Sie uneingeschränkten Zugriff auf alle Seiten und Funktionen
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-3 md:grid-cols-3 lg:grid-cols-4">
              <Button variant="outline" asChild>
                <Link to="/meta-admin/clubs">Club Management</Link>
              </Button>
              <Button variant="outline" asChild>
                <Link to="/meta-admin/users">Benutzer verwalten</Link>
              </Button>
              <Button variant="outline" asChild>
                <Link to="/meta-admin/domains">Domain Management</Link>
              </Button>
              <Button variant="outline" asChild>
                <Link to="/meta-admin/billing/plans">Billing & Pläne</Link>
              </Button>
              <Button variant="outline" asChild>
                <Link to="/meta-admin/support/tickets">Support Tickets</Link>
              </Button>
              <Button variant="outline" asChild>
                <Link to="/meta-admin/monitoring/logs">System Logs</Link>
              </Button>
              <Button variant="outline" asChild>
                <Link to="/meta-admin/content/email-templates">Email Templates</Link>
              </Button>
              <Button variant="outline" asChild>
                <Link to="/meta-admin/settings">Global Settings</Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </SuperAdminLayout>
  );
};

export default SuperAdminDashboardPage;