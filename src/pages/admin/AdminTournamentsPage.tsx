import AdminLayout from '@/layouts/AdminLayout';
import { TournamentManagement } from '@/components/admin/TournamentManagement';
import { TournamentCalendar } from '@/components/admin/TournamentCalendar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { TournamentValidationPanel } from '@/components/admin/TournamentValidationPanel';
import { EventManagement } from '@/components/admin/EventManagement';
import { useState } from 'react';

export default function AdminTournamentsPage() {
  const [selectedTournamentId, setSelectedTournamentId] = useState<string | null>(null);
  const [selectedTournamentName, setSelectedTournamentName] = useState<string>('');
  const [activeTab, setActiveTab] = useState('tournaments');
  const [tournamentsActiveTab, setTournamentsActiveTab] = useState('overview');

  const handleTournamentSelect = (tournamentId: string, tournamentName: string) => {
    setSelectedTournamentId(tournamentId);
    setSelectedTournamentName(tournamentName);
    setTournamentsActiveTab('calendar');
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList>
            <TabsTrigger value="tournaments">Turniere</TabsTrigger>
            <TabsTrigger value="events">Events & Veranstaltungen</TabsTrigger>
          </TabsList>

          <TabsContent value="tournaments">
            <Tabs value={tournamentsActiveTab} onValueChange={setTournamentsActiveTab} className="space-y-4">
              <TabsList>
                <TabsTrigger value="overview">Übersicht</TabsTrigger>
                <TabsTrigger value="calendar">Turnierkalender</TabsTrigger>
                <TabsTrigger value="formats">Format-Konfiguration</TabsTrigger>
                <TabsTrigger value="validation">Validierung & Tests</TabsTrigger>
              </TabsList>
              
              <TabsContent value="overview">
                <TournamentManagement onTournamentSelect={handleTournamentSelect} />
              </TabsContent>
              
              <TabsContent value="calendar">
                <div className="space-y-4">
                  {selectedTournamentId ? (
                    <TournamentCalendar 
                      tournamentId={selectedTournamentId} 
                      tournamentName={selectedTournamentName} 
                    />
                  ) : (
                    <div className="text-center py-8">
                      <h3 className="text-lg font-medium mb-2">Kein Turnier ausgewählt</h3>
                      <p className="text-muted-foreground">
                        Wählen Sie zuerst ein Turnier in der Übersicht aus, um den Kalender anzuzeigen.
                      </p>
                    </div>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="formats">
                <div className="text-center py-8">
                  <h3 className="text-lg font-medium mb-2">Format-Konfiguration</h3>
                  <p className="text-muted-foreground">
                    Format-Konfiguration wird hier implementiert.
                  </p>
                </div>
              </TabsContent>

              <TabsContent value="validation">
                <TournamentValidationPanel 
                  tournamentId="demo-tournament-id" 
                  onMatchSelect={(matchId) => console.log('Selected match:', matchId)} 
                />
              </TabsContent>
            </Tabs>
          </TabsContent>

          <TabsContent value="events">
            <EventManagement />
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
}