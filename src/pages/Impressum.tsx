import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { useNavigate } from "react-router-dom";

const Impressum = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-md border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <Button 
            variant="ghost" 
            onClick={() => navigate(-1)}
            className="flex items-center gap-2 text-gray-600 hover:text-gray-800"
          >
            <ArrowLeft className="w-4 h-4" />
            Zurück
          </Button>
        </div>
      </header>

      {/* Content */}
      <main className="max-w-4xl mx-auto px-4 py-12">
        <div className="bg-white rounded-lg shadow-sm p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">Impressum</h1>
          
          <div className="space-y-8">
            {/* Diensteanbieter */}
            <section>
              <h2 className="text-xl font-semibold text-gray-800 mb-4">Diensteanbieter</h2>
              <div className="space-y-2 text-gray-600">
                <p><strong>Courtwaive</strong></p>
                <p>Landgrafenstr. 56B</p>
                <p>61350 Bad Homburg</p>
                <p>Deutschland</p>
              </div>
            </section>

            {/* Kontakt */}
            <section>
              <h2 className="text-xl font-semibold text-gray-800 mb-4">Kontakt</h2>
              <div className="space-y-2 text-gray-600">
                <p><strong>E-Mail:</strong> <EMAIL></p>
                <p><strong>Website:</strong> courtwaive.com</p>
              </div>
            </section>

            {/* Vertreten durch */}
            <section>
              <h2 className="text-xl font-semibold text-gray-800 mb-4">Vertreten durch</h2>
              <div className="space-y-2 text-gray-600">
                <p>Dennis Lampe</p>
                <p>Inhaber</p>
              </div>
            </section>

            {/* Handelsregister */}
            <section>
              <h2 className="text-xl font-semibold text-gray-800 mb-4">Handelsregister</h2>
              <div className="space-y-2 text-gray-600">
                <p><strong>Registereintrag:</strong> [Registergericht und Registernummer]</p>
                <p><strong>Umsatzsteuer-ID:</strong> [USt-IdNr. gemäß §27a UStG]</p>
              </div>
            </section>

            {/* EU-Streitschlichtung */}
            <section>
              <h2 className="text-xl font-semibold text-gray-800 mb-4">EU-Streitschlichtung</h2>
              <div className="text-gray-600">
                <p>
                  Die Europäische Kommission stellt eine Plattform zur Online-Streitbeilegung (OS) bereit: 
                  <a href="https://ec.europa.eu/consumers/odr/" target="_blank" rel="noopener noreferrer" 
                     className="text-blue-600 hover:text-blue-800 underline ml-1">
                    https://ec.europa.eu/consumers/odr/
                  </a>
                </p>
                <p className="mt-2">
                  Unsere E-Mail-Adresse finden Sie oben im Impressum.
                </p>
              </div>
            </section>

            {/* Verbraucherstreitbeilegung */}
            <section>
              <h2 className="text-xl font-semibold text-gray-800 mb-4">Verbraucherstreitbeilegung/Universalschlichtungsstelle</h2>
              <div className="text-gray-600">
                <p>
                  Wir sind nicht bereit oder verpflichtet, an Streitbeilegungsverfahren vor einer 
                  Verbraucherschlichtungsstelle teilzunehmen.
                </p>
              </div>
            </section>

            {/* Haftung für Inhalte */}
            <section>
              <h2 className="text-xl font-semibold text-gray-800 mb-4">Haftung für Inhalte</h2>
              <div className="text-gray-600">
                <p>
                  Als Diensteanbieter sind wir gemäß § 7 Abs.1 TMG für eigene Inhalte auf diesen Seiten nach den 
                  allgemeinen Gesetzen verantwortlich. Nach §§ 8 bis 10 TMG sind wir als Diensteanbieter jedoch nicht 
                  unter der Verpflichtung, übermittelte oder gespeicherte fremde Informationen zu überwachen oder nach 
                  Umständen zu forschen, die auf eine rechtswidrige Tätigkeit hinweisen.
                </p>
              </div>
            </section>

            {/* Haftung für Links */}
            <section>
              <h2 className="text-xl font-semibold text-gray-800 mb-4">Haftung für Links</h2>
              <div className="text-gray-600">
                <p>
                  Unser Angebot enthält Links zu externen Websites Dritter, auf deren Inhalte wir keinen Einfluss haben. 
                  Deshalb können wir für diese fremden Inhalte auch keine Gewähr übernehmen. Für die Inhalte der 
                  verlinkten Seiten ist stets der jeweilige Anbieter oder Betreiber der Seiten verantwortlich.
                </p>
              </div>
            </section>

            {/* Urheberrecht */}
            <section>
              <h2 className="text-xl font-semibold text-gray-800 mb-4">Urheberrecht</h2>
              <div className="text-gray-600">
                <p>
                  Die durch die Seitenbetreiber erstellten Inhalte und Werke auf diesen Seiten unterliegen dem deutschen 
                  Urheberrecht. Die Vervielfältigung, Bearbeitung, Verbreitung und jede Art der Verwertung außerhalb der 
                  Grenzen des Urheberrechtes bedürfen der schriftlichen Zustimmung des jeweiligen Autors bzw. Erstellers.
                </p>
              </div>
            </section>
          </div>

          <div className="mt-12 pt-8 border-t border-gray-200">
            <p className="text-sm text-gray-500">
              Stand: {new Date().toLocaleDateString('de-DE')}
            </p>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Impressum;