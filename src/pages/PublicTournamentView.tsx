import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Trophy, Calendar, Users, MapPin, Clock, ExternalLink } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { TournamentBracket } from '@/components/tournaments/TournamentBracket';

interface Tournament {
  id: string;
  name: string;
  description?: string;
  status: 'registration' | 'active' | 'completed' | 'cancelled';
  registration_start?: string;
  registration_end?: string;
  tournament_start?: string;
  tournament_end?: string;
  max_participants?: number;
  allow_guests: boolean;
  created_at: string;
}

interface Participant {
  id: string;
  user_name: string;
  is_guest: boolean;
  seeding?: number;
  status: string;
}

interface Match {
  id: string;
  participant1?: { id: string; user_name: string };
  participant2?: { id: string; user_name: string };
  sets?: Array<{ player1_score: number; player2_score: number }>;
  winner_id?: string;
  status: 'scheduled' | 'in_progress' | 'completed' | 'walkover';
  round_number: number;
  match_number: number;
}

export function PublicTournamentView() {
  const { tournamentId } = useParams<{ tournamentId: string }>();
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Fetch tournament details
  const { data: tournament, isLoading: tournamentLoading } = useQuery({
    queryKey: ['public-tournament', tournamentId],
    queryFn: async () => {
      if (!tournamentId) throw new Error('Tournament ID required');
      
      const { data, error } = await supabase
        .from('tournaments')
        .select('*')
        .eq('id', tournamentId)
        .single();
      
      if (error) throw error;
      return data as Tournament;
    },
    enabled: !!tournamentId
  });

  // Fetch participants
  const { data: participants } = useQuery({
    queryKey: ['tournament-participants-public', tournamentId],
    queryFn: async () => {
      if (!tournamentId) return [];
      
      const { data, error } = await supabase
        .from('tournament_participants')
        .select('id, guest_name, guest_email, seeding, status')
        .eq('tournament_id', tournamentId)
        .eq('status', 'confirmed')
        .order('seeding');
      
      if (error) throw error;
      return data?.map(p => ({ 
        ...p, 
        user_name: p.guest_name || 'Unknown',
        is_guest: !!p.guest_email
      })) as Participant[];
    },
    enabled: !!tournamentId
  });

  // Fetch matches with auto-refresh
  const { data: matches, refetch: refetchMatches } = useQuery({
    queryKey: ['tournament-matches-public', tournamentId],
    queryFn: async () => {
      if (!tournamentId) return [];
      
      const { data, error } = await supabase
        .from('tournament_matches')
        .select(`
          id, round_number, match_number, status, score_json, winner_id,
          participant1_id, participant2_id
        `)
        .eq('tournament_id', tournamentId)
        .order('round_number')
        .order('match_number');
      
      if (error) throw error;
      
      // Fetch participants and map them to matches
      const participantData = await supabase
        .from('tournament_participants')
        .select('id, guest_name')
        .eq('tournament_id', tournamentId);
      
      const participantMap = (participantData.data || []).reduce((acc, p) => {
        acc[p.id] = { id: p.id, user_name: p.guest_name || 'Unknown' };
        return acc;
      }, {} as Record<string, { id: string; user_name: string }>);
      
      return data?.map(match => ({
        ...match,
        sets: match.score_json as Array<{ player1_score: number; player2_score: number }> || [],
        participant1: match.participant1_id ? participantMap[match.participant1_id] : undefined,
        participant2: match.participant2_id ? participantMap[match.participant2_id] : undefined
      })) as Match[];
    },
    enabled: !!tournamentId,
    refetchInterval: autoRefresh ? 30000 : false // Refresh every 30 seconds
  });

  const getStatusBadge = (status: string) => {
    const statusMap = {
      'registration': { label: 'Anmeldung', variant: 'secondary' as const, color: 'bg-blue-100 text-blue-800' },
      'active': { label: 'Aktiv', variant: 'default' as const, color: 'bg-green-100 text-green-800' },
      'completed': { label: 'Abgeschlossen', variant: 'outline' as const, color: 'bg-gray-100 text-gray-800' },
      'cancelled': { label: 'Abgesagt', variant: 'destructive' as const, color: 'bg-red-100 text-red-800' }
    };
    
    const statusInfo = statusMap[status as keyof typeof statusMap] || { 
      label: status, 
      variant: 'secondary' as const, 
      color: 'bg-gray-100 text-gray-800' 
    };
    
    return (
      <Badge variant={statusInfo.variant} className={statusInfo.color}>
        {statusInfo.label}
      </Badge>
    );
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'TBD';
    return new Date(dateString).toLocaleDateString('de-DE', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (tournamentLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 p-4">
        <div className="max-w-7xl mx-auto">
          <div className="animate-pulse space-y-8">
            <div className="h-32 bg-white rounded-lg"></div>
            <div className="h-96 bg-white rounded-lg"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!tournament) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center">
        <Card>
          <CardContent className="text-center py-12">
            <Trophy className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
            <h2 className="text-2xl font-bold mb-2">Turnier nicht gefunden</h2>
            <p className="text-muted-foreground">
              Das angeforderte Turnier existiert nicht oder ist nicht öffentlich verfügbar.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center gap-3 mb-2">
                <Trophy className="h-8 w-8 text-yellow-500" />
                <h1 className="text-3xl font-bold text-foreground">{tournament.name}</h1>
                {getStatusBadge(tournament.status)}
              </div>
              {tournament.description && (
                <p className="text-muted-foreground text-lg">{tournament.description}</p>
              )}
            </div>
            
            <div className="flex items-center gap-4">
              <Button
                variant={autoRefresh ? "default" : "outline"}
                size="sm"
                onClick={() => setAutoRefresh(!autoRefresh)}
              >
                <Clock className="h-4 w-4 mr-2" />
                Auto-Refresh {autoRefresh ? 'AN' : 'AUS'}
              </Button>
              <Button variant="outline" size="sm" onClick={() => refetchMatches()}>
                <ExternalLink className="h-4 w-4 mr-2" />
                Aktualisieren
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto p-4 space-y-8">
        {/* Tournament Info */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg">
                <Calendar className="h-5 w-5 text-blue-500" />
                Termine
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {tournament.registration_start && tournament.registration_end && (
                <div>
                  <div className="text-sm font-medium text-muted-foreground">Anmeldung</div>
                  <div className="text-sm">
                    {formatDate(tournament.registration_start)} - {formatDate(tournament.registration_end)}
                  </div>
                </div>
              )}
              {tournament.tournament_start && (
                <div>
                  <div className="text-sm font-medium text-muted-foreground">Turnier Start</div>
                  <div className="text-sm">{formatDate(tournament.tournament_start)}</div>
                </div>
              )}
              {tournament.tournament_end && (
                <div>
                  <div className="text-sm font-medium text-muted-foreground">Turnier Ende</div>
                  <div className="text-sm">{formatDate(tournament.tournament_end)}</div>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg">
                <Users className="h-5 w-5 text-green-500" />
                Teilnehmer
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {participants?.length || 0}
                {tournament.max_participants && `/${tournament.max_participants}`}
              </div>
              <div className="text-sm text-muted-foreground">
                Bestätigte Anmeldungen
              </div>
              {tournament.allow_guests && (
                <Badge variant="outline" className="mt-2">
                  Gäste erlaubt
                </Badge>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg">
                <Trophy className="h-5 w-5 text-yellow-500" />
                Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div>Status: {getStatusBadge(tournament.status)}</div>
                {matches && (
                  <div className="text-sm text-muted-foreground">
                    {matches.filter(m => m.status === 'completed').length} von {matches.length} Spielen beendet
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Tournament Bracket */}
        {matches && matches.length > 0 ? (
          <Card>
            <CardHeader>
              <CardTitle>Turnierbaum</CardTitle>
            </CardHeader>
            <CardContent>
              <TournamentBracket 
                matches={matches} 
                tournamentName={tournament.name}
              />
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardContent className="text-center py-12">
              <Calendar className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">Noch keine Spiele</h3>
              <p className="text-muted-foreground">
                Das Turnier hat noch keine generierten Spiele. 
                Schauen Sie später wieder vorbei!
              </p>
            </CardContent>
          </Card>
        )}

        {/* Participants List */}
        {participants && participants.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Teilnehmerliste</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {participants.map((participant) => (
                  <div key={participant.id} className="flex items-center justify-between p-3 border rounded">
                    <div>
                      <div className="font-medium">{participant.user_name}</div>
                      <div className="text-sm text-muted-foreground">
                        Seed #{participant.seeding || 'N/A'}
                      </div>
                    </div>
                    <Badge variant={participant.is_guest ? 'outline' : 'secondary'}>
                      {participant.is_guest ? 'Gast' : 'Mitglied'}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}

export default PublicTournamentView;