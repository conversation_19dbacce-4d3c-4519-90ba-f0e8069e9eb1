import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import Navigation from "@/components/Navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { AdminDataStep } from "@/components/onboarding/AdminDataStep";
import { ClubDataStep } from "@/components/onboarding/ClubDataStep";
import { CourtSetupStep } from "@/components/onboarding/CourtSetupStep";
import { PlayingTimesStep } from "@/components/onboarding/PlayingTimesStep";
import { ConfirmationStep } from "@/components/onboarding/ConfirmationStep";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { ArrowLeft, ArrowRight, CheckCircle, LogOut } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";

export interface AdminData {
  firstName: string;
  lastName: string;
  birthDate: Date | undefined;
  street: string;
  houseNumber: string;
  city: string;
  postalCode: string;
  email: string;
  password: string;
  passwordConfirm: string;
}

export interface ClubData {
  name: string;
  street: string;
  houseNumber: string;
  city: string;
  postalCode: string;
  website?: string;
}

export interface CourtSetup {
  numberOfCourts: number;
  skipForNow: boolean;
}

export interface PlayingTimes {
  startTime: string;
  endTime: string;
  selectedDays: string[];
  skipForNow: boolean;
}

const STEPS = [
  { id: 1, title: "Admin-Daten", description: "Ihre persönlichen Informationen" },
  { id: 2, title: "Vereinsdaten", description: "Informationen zu Ihrem Verein" },
  { id: 3, title: "Plätze", description: "Anzahl der Tennisplätze" },
  { id: 4, title: "Spielzeiten", description: "Buchungszeiten definieren" },
  { id: 5, title: "Bestätigung", description: "Übersicht und Aktivierung" },
];

export default function ClubRegister() {
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);
  const [currentUser, setCurrentUser] = useState<any>(null);
  const navigate = useNavigate();
  const { toast } = useToast();

  const [adminData, setAdminData] = useState<AdminData>({
    firstName: "",
    lastName: "",
    birthDate: undefined,
    street: "",
    houseNumber: "",
    city: "",
    postalCode: "",
    email: "",
    password: "",
    passwordConfirm: "",
  });

  const [clubData, setClubData] = useState<ClubData>({
    name: "",
    street: "",
    houseNumber: "",
    city: "",
    postalCode: "",
    website: "",
  });

  const [courtSetup, setCourtSetup] = useState<CourtSetup>({
    numberOfCourts: 1,
    skipForNow: false,
  });

  const [playingTimes, setPlayingTimes] = useState<PlayingTimes>({
    startTime: "08:00",
    endTime: "22:00",
    selectedDays: ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"],
    skipForNow: false,
  });

  // Check if user is already signed in
  useEffect(() => {
    const checkAuthState = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      setCurrentUser(user);
      setIsCheckingAuth(false);
    };
    
    checkAuthState();
  }, []);

  const handleSignOut = async () => {
    await supabase.auth.signOut();
    setCurrentUser(null);
    toast({
      title: "Abgemeldet",
      description: "Sie können jetzt einen neuen Verein registrieren.",
    });
  };

  const progress = (currentStep / STEPS.length) * 100;

  const nextStep = () => {
    if (currentStep < STEPS.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const validateCurrentStep = (): boolean => {
    switch (currentStep) {
      case 1:
        return !!(
          adminData.firstName &&
          adminData.lastName &&
          adminData.email &&
          adminData.password &&
          adminData.password === adminData.passwordConfirm &&
          adminData.street &&
          adminData.houseNumber &&
          adminData.city &&
          adminData.postalCode
        );
      case 2:
        return !!(
          clubData.name &&
          clubData.street &&
          clubData.houseNumber &&
          clubData.city &&
          clubData.postalCode
        );
      case 3:
      case 4:
        return true; // These steps can be skipped
      default:
        return true;
    }
  };

  const submitRegistration = async () => {
    setIsLoading(true);
    try {
      console.log('Starting club registration...');
      
      // Prepare data for edge function
      const registrationData = {
        adminData: {
          email: adminData.email,
          password: adminData.password,
          firstName: adminData.firstName,
          lastName: adminData.lastName,
          birthDate: adminData.birthDate ? adminData.birthDate.toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
          street: adminData.street,
          houseNumber: adminData.houseNumber,
          postalCode: adminData.postalCode,
          city: adminData.city,
          membershipCategory: 'Erwachsener',
        },
        clubData: {
          name: clubData.name,
          description: `Tennis Club ${clubData.name}`,
          logoUrl: null,
          customDomain: null,
          subdomain: clubData.name
            .toLowerCase()
            .replace(/[^a-z0-9]/g, '-')
            .replace(/-+/g, '-')
            .replace(/^-|-$/g, ''),
        },
        courtSetup: {
          courts: courtSetup.skipForNow ? [] : Array.from({ length: courtSetup.numberOfCourts }, (_, i) => ({
            number: i + 1,
            surfaceType: 'Hartplatz',
            courtGroup: null,
          })),
        },
        playingTimes: {
          availability: (playingTimes.skipForNow || playingTimes.selectedDays.length === 0) ? [] : [{
            startTime: playingTimes.startTime,
            endTime: playingTimes.endTime,
            daysOfWeek: playingTimes.selectedDays,
          }],
        },
      };

      console.log('Registration data prepared:', registrationData);

      // Call the edge function to handle club registration
      console.log('Calling register-club edge function...');
      const { data, error } = await supabase.functions.invoke('register-club', {
        body: registrationData,
      });

      console.log('Edge function response received:');
      console.log('Data:', data);
      console.log('Error:', error);

      if (error) {
        console.error('Edge function error details:', error);
        throw error;
      }

      if (error) {
        throw error;
      }

      if (!data.success) {
        throw new Error(data.error || 'Club-Registrierung fehlgeschlagen');
      }

      console.log('Club registration completed successfully');
      
      toast({
        title: "Vereinsregistrierung erfolgreich!",
        description: `Club "${data.club.name}" wurde erfolgreich erstellt!`,
      });
      
      // Sign in the user after successful registration
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email: adminData.email,
        password: adminData.password,
      });

      if (signInError) {
        console.error('Auto sign-in failed:', signInError);
        toast({
          variant: "destructive",
          title: "Anmeldung fehlgeschlagen",
          description: "Club wurde erstellt, aber automatische Anmeldung fehlgeschlagen. Bitte melden Sie sich manuell an.",
        });
        // Redirect to auth page for manual login
        navigate('/auth');
      } else {
        // Check admin role and redirect accordingly
        const { data: isAdminUser, error: roleError } = await supabase
          .rpc('has_any_admin_role', { _user_id: (await supabase.auth.getUser()).data.user?.id });
        
        if (roleError) {
          console.error('Error checking admin role:', roleError);
          navigate('/auth');
        } else if (isAdminUser) {
          // Navigate to admin dashboard
          navigate('/admin');
        } else {
          // Fallback to member area
          navigate('/member');
        }
      }
    } catch (error) {
      console.error('Registration error:', error);
      toast({
        variant: "destructive",
        title: "Registrierung fehlgeschlagen",
        description: error instanceof Error ? error.message : "Ein unbekannter Fehler ist aufgetreten.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return <AdminDataStep data={adminData} onChange={setAdminData} />;
      case 2:
        return <ClubDataStep data={clubData} onChange={setClubData} />;
      case 3:
        return <CourtSetupStep data={courtSetup} onChange={setCourtSetup} />;
      case 4:
        return <PlayingTimesStep data={playingTimes} onChange={setPlayingTimes} />;
      case 5:
        return (
          <ConfirmationStep
            adminData={adminData}
            clubData={clubData}
            courtSetup={courtSetup}
            playingTimes={playingTimes}
          />
        );
      default:
        return null;
    }
  };

  if (isCheckingAuth) {
    return (
      <div className="min-h-screen bg-background">
        <Navigation />
        <main className="pt-24 pb-16">
          <div className="container mx-auto px-4 max-w-4xl">
            <div className="text-center">
              <h1 className="text-2xl font-bold">Lade...</h1>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      <main className="pt-24 pb-16">
        <div className="container mx-auto px-4 max-w-4xl">
          <div className="text-center mb-8">
            <h1 className="text-3xl md:text-4xl font-bold tracking-tight mb-2">
              Verein registrieren
            </h1>
            <p className="text-muted-foreground">
              Erstellen Sie Ihren Tennis-Verein in wenigen Schritten
            </p>
          </div>

          {currentUser && (
            <Alert className="mb-6">
              <LogOut className="h-4 w-4" />
              <AlertDescription>
                Sie sind bereits als {currentUser.email} angemeldet. Um einen neuen Verein zu registrieren, müssen Sie sich zuerst abmelden.
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSignOut}
                  className="ml-4"
                >
                  Abmelden
                </Button>
              </AlertDescription>
            </Alert>
          )}

          {currentUser && (
            <div className="text-center">
              <p className="text-muted-foreground">
                Melden Sie sich ab, um fortzufahren.
              </p>
            </div>
          )}

          {!currentUser && (
            <>

          {/* Progress Bar */}
          <div className="mb-8 relative">
            {/* AI-themed gradient background */}
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-blue-500/10 rounded-xl blur-sm"></div>
            
            <div className="relative p-6 bg-white/50 backdrop-blur-sm border border-purple-200/30 rounded-xl">
              <div className="flex justify-between items-center mb-6">
                {STEPS.map((step, index) => (
                  <div
                    key={step.id}
                    className={`flex items-center ${
                      index < STEPS.length - 1 ? 'flex-1' : ''
                    }`}
                  >
                    <div
                      className={`flex items-center justify-center w-10 h-10 rounded-full text-sm font-semibold transition-all duration-300 ${
                        step.id <= currentStep
                          ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-lg shadow-purple-500/25'
                          : 'bg-white border-2 border-purple-200 text-purple-400'
                      }`}
                    >
                      {step.id < currentStep ? (
                        <CheckCircle className="w-5 h-5" />
                      ) : (
                        step.id
                      )}
                    </div>
                    {index < STEPS.length - 1 && (
                      <div className="flex-1 h-2 mx-4 bg-purple-100 rounded-full overflow-hidden">
                        <div
                          className={`h-full transition-all duration-500 ${
                            step.id < currentStep
                              ? 'bg-gradient-to-r from-purple-600 to-blue-600'
                              : 'bg-transparent'
                          }`}
                        />
                      </div>
                    )}
                  </div>
                ))}
              </div>
              
              {/* Enhanced Progress Bar */}
              <div className="relative mb-4">
                <div className="w-full h-3 bg-purple-100 rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-gradient-to-r from-purple-600 to-blue-600 transition-all duration-500 ease-out"
                    style={{ width: `${progress}%` }}
                  />
                </div>
                <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-white/30 to-transparent rounded-full" />
              </div>
              
              <div className="flex justify-between text-sm">
                {STEPS.map((step) => (
                  <span
                    key={step.id}
                    className={`font-medium transition-colors ${
                      step.id === currentStep 
                        ? 'text-purple-700 font-semibold' 
                        : step.id < currentStep
                        ? 'text-purple-600'
                        : 'text-purple-400'
                    }`}
                  >
                    {step.title}
                  </span>
                ))}
              </div>
            </div>
          </div>

          {/* Step Content */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>{STEPS[currentStep - 1].title}</CardTitle>
              <CardDescription>
                {STEPS[currentStep - 1].description}
              </CardDescription>
            </CardHeader>
            <CardContent>{renderCurrentStep()}</CardContent>
          </Card>

          {/* Navigation Buttons */}
          <div className="flex justify-between">
            <Button
              variant="outline"
              onClick={prevStep}
              disabled={currentStep === 1}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="w-4 h-4" />
              Zurück
            </Button>

            {currentStep < STEPS.length ? (
              <Button
                onClick={nextStep}
                disabled={!validateCurrentStep()}
                className="flex items-center gap-2 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white border-0 shadow-lg shadow-purple-500/25 hover:shadow-purple-500/40 transition-all duration-300 hover:scale-105 disabled:from-gray-400 disabled:to-gray-500 disabled:shadow-none disabled:scale-100"
              >
                Weiter
                <ArrowRight className="w-4 h-4" />
              </Button>
            ) : (
              <Button
                onClick={submitRegistration}
                disabled={isLoading}
                className="flex items-center gap-2"
              >
                {isLoading ? 'Wird erstellt...' : 'Verein erstellen'}
                <CheckCircle className="w-4 h-4" />
              </Button>
            )}
          </div>
            </>
          )}
        </div>
      </main>
    </div>
  );
}