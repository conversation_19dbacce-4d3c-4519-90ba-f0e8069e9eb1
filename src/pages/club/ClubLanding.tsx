import { useState, useEffect } from "react";
import { useNavigate, useParams, Link } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/contexts/AuthContext";
import { useTenant } from "@/contexts/TenantContext";
import { ArrowLeft, Loader2, Users, UserPlus, Shield, GraduationCap, Bot, Trophy, Calendar, Settings } from "lucide-react";
import BookingCalendarPublic from "@/components/BookingCalendarPublic";

const ClubLanding = () => {
  const [activeTab, setActiveTab] = useState("login");
  const [loading, setLoading] = useState(false);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const navigate = useNavigate();
  const { clubSlug } = useParams();
  const { toast } = useToast();
  const { user, signIn } = useAuth();
  const { club } = useTenant();

  // Redirect logged in users based on their role
  useEffect(() => {
    if (user && clubSlug) {
      // Check if user is admin and redirect to admin dashboard
      if (user) {
        navigate(`/c/${clubSlug}/admin`);
      }
    }
  }, [user, clubSlug, navigate]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const { error } = await signIn(email, password);

      if (error) {
        if (error.message.includes('Invalid login credentials')) {
          toast({
            variant: "destructive",
            title: "Anmeldung fehlgeschlagen",
            description: "Ungültige E-Mail oder Passwort. Bitte überprüfen Sie Ihre Eingaben.",
          });
        } else {
          toast({
            variant: "destructive",
            title: "Anmeldung fehlgeschlagen",
            description: error.message,
          });
        }
      } else {
        toast({
          title: "Erfolgreich angemeldet",
          description: `Willkommen bei ${club?.name || 'dem Tennisclub'}!`,
        });
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Fehler",
        description: "Ein unerwarteter Fehler ist aufgetreten.",
      });
    }

    setLoading(false);
  };

  const userTypeFeatures = {
    admin: {
      icon: Shield,
      title: "Vereinsverwalter",
      description: "Vollständige Kontrolle über Ihren Tennisverein",
      features: [
        { icon: Users, text: "Mitgliederverwaltung" },
        { icon: Calendar, text: "Platz & Buchungsmanagement" },
        { icon: Settings, text: "Vereinseinstellungen" },
        { icon: Bot, text: "KI-gestützte Automatisierung" }
      ]
    },
    member: {
      icon: Users,
      title: "Vereinsmitglieder",
      description: "Alles was Sie als Mitglied benötigen",
      features: [
        { icon: Calendar, text: "Plätze buchen & verwalten" },
        { icon: Trophy, text: "Turniere & Events" },
        { icon: Users, text: "Mitgliederverzeichnis" },
        { icon: Bot, text: "KI-Assistent für Fragen" }
      ]
    },
    guest: {
      icon: UserPlus,
      title: "Gäste",
      description: "Einfacher Zugang für Gastspieler",
      features: [
        { icon: Calendar, text: "Gastbuchungen" },
        { icon: Users, text: "Kontakt zum Verein" },
        { icon: Trophy, text: "Offene Turniere" },
        { icon: Bot, text: "Informationen per KI" }
      ]
    },
    trainer: {
      icon: GraduationCap,
      title: "Trainer",
      description: "Professionelle Tools für Trainer",
      features: [
        { icon: Calendar, text: "Trainingstermine verwalten" },
        { icon: Users, text: "Schülerverwaltung" },
        { icon: Trophy, text: "Turniervorbereitung" },
        { icon: Bot, text: "Trainings-KI Unterstützung" }
      ]
    }
  };

  return (
    <div className="min-h-screen relative overflow-hidden bg-gradient-to-br from-bg via-bg-alt to-bg-subtle">
      {/* AI-enhanced animated background */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-brand/20 to-accent-purple/20 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob"></div>
        <div className="absolute top-3/4 right-1/4 w-96 h-96 bg-gradient-to-r from-accent-blue/20 to-brand-light/20 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-2000"></div>
        <div className="absolute bottom-1/4 left-1/3 w-96 h-96 bg-gradient-to-r from-accent-purple/20 to-accent-blue/20 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-4000"></div>
        <div className="absolute inset-0 bg-grid-slate-100 bg-[size:60px_60px] opacity-20" />
      </div>
      
      <div className="relative z-10 min-h-screen">
        {/* Header */}
        <header className="p-6">
          <div className="flex items-center justify-between max-w-7xl mx-auto">
            <div className="flex items-center gap-4">
              <Button 
                variant="outline" 
                onClick={() => navigate("/")}
                className="bg-glass border-glass hover:bg-glass-hover transition-all duration-300 shadow-lg"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Zurück zur Hauptseite
              </Button>
              
              <div>
                <h1 className="text-2xl font-bold text-fg-strong">
                  {club?.name || 'Tennisclub'}
                </h1>
                <p className="text-fg-muted">KI-gestütztes Vereinsmanagement</p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-gradient-to-r from-brand to-brand-light rounded-full animate-pulse"></div>
              <span className="text-fg-muted text-sm">Courtw<span className="text-brand font-semibold">ai</span>ve</span>
            </div>
          </div>
        </header>

        <div className="max-w-7xl mx-auto px-6 pb-12">
          {/* Hero Section with Calendar and Auth */}
          <div className="grid lg:grid-cols-3 gap-8 mb-12">
            {/* Left Side - Booking Calendar (2/3 width) */}
            <div className="lg:col-span-2">
              <div className="text-center lg:text-left mb-6">
                <h2 className="text-3xl font-bold text-fg-strong mb-3">
                  Aktuelle Platzbelegung
                </h2>
                <p className="text-lg text-fg-muted">
                  Sehen Sie verfügbare Plätze in Echtzeit - ohne Anmeldung
                </p>
              </div>
              <Card className="bg-glass border-glass shadow-glass backdrop-blur-lg">
                <CardContent className="p-6">
                  <BookingCalendarPublic />
                </CardContent>
              </Card>
            </div>

            {/* Right Side - Auth Form (1/3 width) */}
            <div className="lg:col-span-1">
              <Card className="bg-glass border-glass shadow-glass backdrop-blur-lg sticky top-6">
                <CardHeader className="text-center">
                  <div className="flex justify-center mb-4">
                    <div className="p-3 bg-gradient-to-r from-brand/10 to-accent-purple/10 rounded-full">
                      <Bot className="h-8 w-8 text-brand" />
                    </div>
                  </div>
                  <CardTitle className="text-2xl text-fg-strong">Club-Zugang</CardTitle>
                  <CardDescription className="text-fg-muted">
                    Anmelden oder als neues Mitglied registrieren
                  </CardDescription>
                </CardHeader>
                
                <CardContent className="space-y-6">
                  <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                    <TabsList className="grid grid-cols-2 w-full bg-bg-subtle">
                      <TabsTrigger value="login" className="data-[state=active]:bg-bg data-[state=active]:text-fg-strong">
                        Anmelden
                      </TabsTrigger>
                      <TabsTrigger value="register" className="data-[state=active]:bg-bg data-[state=active]:text-fg-strong">
                        Registrieren
                      </TabsTrigger>
                    </TabsList>
                    
                    <TabsContent value="login" className="space-y-4 mt-6">
                      <form onSubmit={handleLogin} className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="email" className="text-fg-strong font-medium flex items-center gap-2">
                            <div className="w-2 h-2 bg-gradient-to-r from-brand to-accent-purple rounded-full animate-pulse"></div>
                            E-Mail
                          </Label>
                          <Input
                            id="email"
                            type="email"
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            required
                            className="bg-bg border-border focus:border-brand focus:ring-brand/20"
                            placeholder="<EMAIL>"
                          />
                        </div>
                        
                        <div className="space-y-2">
                          <Label htmlFor="password" className="text-fg-strong font-medium flex items-center gap-2">
                            <div className="w-2 h-2 bg-gradient-to-r from-accent-blue to-accent-purple rounded-full animate-pulse delay-75"></div>
                            Passwort
                          </Label>
                          <Input
                            id="password"
                            type="password"
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                            required
                            className="bg-bg border-border focus:border-brand focus:ring-brand/20"
                            placeholder="••••••••"
                          />
                        </div>
                        
                        <Button 
                          type="submit" 
                          size="lg"
                          className="w-full bg-gradient-to-r from-brand to-accent-purple hover:from-brand/90 hover:to-accent-purple/90 text-white shadow-button"
                          disabled={loading}
                        >
                          {loading ? (
                            <>
                              <Loader2 className="h-4 w-4 animate-spin mr-2" />
                              Anmelden...
                            </>
                          ) : (
                            "Anmelden"
                          )}
                        </Button>
                      </form>
                    </TabsContent>
                    
                    <TabsContent value="register" className="space-y-4 mt-6">
                      <div className="text-center space-y-4">
                        <p className="text-fg-muted">
                          Wählen Sie Ihren Registrierungstyp:
                        </p>
                        
                        <div className="grid gap-3">
                          <Link to={`/c/${clubSlug}/register`}>
                            <Button 
                              variant="outline" 
                              className="w-full justify-start bg-bg border-border hover:bg-bg-alt hover:border-brand transition-all duration-300"
                            >
                              <Users className="h-4 w-4 mr-3 text-brand" />
                              Als Mitglied registrieren
                            </Button>
                          </Link>
                          
                          <Link to={`/c/${clubSlug}/register`}>
                            <Button 
                              variant="outline" 
                              className="w-full justify-start bg-bg border-border hover:bg-bg-alt hover:border-brand transition-all duration-300"
                            >
                              <UserPlus className="h-4 w-4 mr-3 text-brand" />
                              Als Gast registrieren
                            </Button>
                          </Link>
                          
                          <Link to={`/c/${clubSlug}/register`}>
                            <Button 
                              variant="outline" 
                              className="w-full justify-start bg-bg border-border hover:bg-bg-alt hover:border-brand transition-all duration-300"
                            >
                              <GraduationCap className="h-4 w-4 mr-3 text-brand" />
                              Als Trainer registrieren
                            </Button>
                          </Link>
                        </div>
                        
                        <div className="pt-4 border-t border-border">
                          <p className="text-sm text-fg-muted">
                            Bereits registriert?{" "}
                            <button 
                              onClick={() => setActiveTab("login")}
                              className="text-brand hover:text-brand/80 font-medium"
                            >
                              Hier anmelden
                            </button>
                          </p>
                        </div>
                      </div>
                    </TabsContent>
                  </Tabs>
                </CardContent>
              </Card>
              
              {/* AI Features Highlight */}
              <div className="mt-6 p-4 bg-glass border-glass rounded-lg backdrop-blur-lg">
                <div className="flex items-center gap-3 mb-3">
                  <Bot className="h-5 w-5 text-brand" />
                  <span className="font-medium text-fg-strong">KI-gestützte Features</span>
                </div>
                <div className="grid grid-cols-1 gap-2 text-sm text-fg-muted">
                  <div>• Intelligente Buchungen</div>
                  <div>• Automatische Planung</div>
                  <div>• Chat-Assistent</div>
                  <div>• Optimierte Abläufe</div>
                </div>
              </div>
            </div>
          </div>

          <div className="grid lg:grid-cols-1 gap-12 items-start">
            {/* Club Information Section */}
            <div className="space-y-8">
              <div className="text-center lg:text-left">
                <h2 className="text-4xl lg:text-5xl font-bold text-fg-strong mb-4">
                  Willkommen beim
                  <span className="block bg-gradient-to-r from-brand to-accent-purple bg-clip-text text-transparent">
                    {club?.name || 'Demo Club'}
                  </span>
                </h2>
                <p className="text-xl text-fg-muted mb-8">
                  Modernste KI-Technologie für Ihren Tennisverein. 
                  Melden Sie sich an oder registrieren Sie sich für vollen Zugang.
                </p>
              </div>

              {/* User Type Features */}
              <div className="grid gap-6">
                {Object.entries(userTypeFeatures).map(([key, userType]) => {
                  const IconComponent = userType.icon;
                  return (
                    <Card key={key} className="bg-glass border-glass hover:bg-glass-hover transition-all duration-300 shadow-card">
                      <CardHeader className="pb-3">
                        <div className="flex items-center gap-3">
                          <div className="p-2 bg-gradient-to-r from-brand/10 to-accent-purple/10 rounded-lg">
                            <IconComponent className="h-5 w-5 text-brand" />
                          </div>
                          <div>
                            <CardTitle className="text-lg text-fg-strong">{userType.title}</CardTitle>
                            <CardDescription className="text-fg-muted">{userType.description}</CardDescription>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <div className="grid grid-cols-2 gap-2">
                          {userType.features.map((feature, idx) => {
                            const FeatureIcon = feature.icon;
                            return (
                              <div key={idx} className="flex items-center gap-2 text-sm text-fg-muted">
                                <FeatureIcon className="h-3 w-3 text-brand" />
                                <span>{feature.text}</span>
                              </div>
                            );
                          })}
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClubLanding;