# .github/scripts/gpt_review.py
import os

MODEL = os.getenv("MODEL", "gpt-5")
MAX_CHUNK_CHARS = 12000

SYSTEM_PROMPT = (
    "Rolle: Senior Staff Engineer. Liefere eine präzise Änderungs-Checkliste, "
    "ohne Smalltalk, im Format:\n"
    "## TL;DR (max. 5 Zeilen)\n"
    "## Änderungen (priorisiert)\n"
    "- [P{1-3}] Datei:Zeile → Problem → Konkrete Änderung\n"
    "  Patch:\n```diff\n...Unified-Diff...\n```\n"
    "## Tests\n"
    "- Kurze Testfälle (Unit/Integration) je Änderung\n"
    "## Risiken/Follow-ups\n"
    "- Mögliche Nebenwirkungen, Migration, Monitoring\n"
    "Nur Inhalte aus dem Diff adressieren; falls Kontext fehlt, notiere Annahmen."
)

USER_CONTEXT = os.getenv("PROJECT_CONTEXT", "Stack unbekannt")
STEP_SUMMARY = os.getenv("GITHUB_STEP_SUMMARY")

def load_diff():
    p = "diff.patch"
    if not os.path.exists(p):
        return ""
    return open(p, "r", encoding="utf-8", errors="ignore").read()

def chunk_text(s, size):
    return [s[i:i+size] for i in range(0, len(s), size)]

def openai_review(diff):
    from openai import OpenAI
    client = OpenAI(api_key=os.environ["OPENAI_API_KEY"])
    resp = client.responses.create(
        model=MODEL,
        input=[
            {"role": "system", "content": SYSTEM_PROMPT},
            {"role": "user", "content": f"Projektkontext: {USER_CONTEXT}\nUnified-Diff:\n```diff\n{diff}\n```"}
        ],
    )
    return getattr(resp, "output_text", None) or str(resp)

def main():
    diff = load_diff().strip()
    if not diff:
        out = "## TL;DR\nKein Diff erkannt – nichts zu prüfen."
    else:
        parts = chunk_text(diff, MAX_CHUNK_CHARS)
        sections = []
        for idx, part in enumerate(parts, 1):
            review = openai_review(part)
            sections.append(f"### Teil {idx}/{len(parts)}\n{review}")
        out = "# 🤖 GPT Änderungs-Report\n" + "\n\n---\n".join(sections)

    # Artefakt speichern
    with open("gpt_review.md", "w", encoding="utf-8") as f:
        f.write(out)

    # Step Summary für Actions UI
    if STEP_SUMMARY:
        with open(STEP_SUMMARY, "w", encoding="utf-8") as f:
            f.write(out)

if __name__ == "__main__":
    main()
