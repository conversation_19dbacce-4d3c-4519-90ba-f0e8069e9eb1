name: GPT Code Read Report
on:
  pull_request:
    types: [opened, synchronize, reopened]
  workflow_dispatch: {}

permissions:
  contents: read

jobs:
  review:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Create diff (PR oder letzter Commit)
        run: |
          if [ "${{ github.event_name }}" = "pull_request" ]; then
            BASE="${{ github.event.pull_request.base.sha }}"
            HEAD="${{ github.event.pull_request.head.sha }}"
          else
            BASE="$(git rev-parse HEAD~1 2>/dev/null || echo "")"
            HEAD="$(git rev-parse HEAD)"
          fi
          if [ -n "$BASE" ]; then
            git diff --unified=0 --no-color "$BASE...$HEAD" > diff.patch || true
          else
            : > diff.patch
          fi
          echo "Diff bytes:" $(wc -c < diff.patch)

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install OpenAI SDK
        run: pip install openai

      - name: Run GPT read-only review
        env:
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          MODEL: gpt-5
          PROJECT_CONTEXT: "Vite/React, TypeScript, Tailwind, Supabase"
        run: python .github/scripts/gpt_review.py

      - name: Upload report artifact
        uses: actions/upload-artifact@v4
        with:
          name: gpt-review
          path: gpt_review.md
